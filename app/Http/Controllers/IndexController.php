<?php

namespace App\Http\Controllers;

use App\Jobs\DeleteVoiceFiles;
use App\Jobs\TextToVoiceJob;
use App\Models\Activity;
use App\Models\ActorSlide;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;
use DateTime;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class IndexController extends Controller
{
    public function index()
    {
        $date = Carbon::now()->format('d-m-Y');
        return view('xo-so', ['date' => $date]);
    }

    public function listXSMT()
    {
        $date = Carbon::now()->format('d-m-Y');
        return view('xo-so-mt', ['date' => $date]);
    }

    public function listXSMTBACKUP()
    {
        $date = Carbon::now()->format('d-m-Y');
        return view('xo-so-mt-backup', ['date' => $date]);
    }


    public function getLiveResults(): \Illuminate\Http\JsonResponse
    {
        // Gọi và lấy dữ liệu đã xử lý từ file js
        $rawResponse = $this->fetchKQXS('mn');
        $data = $rawResponse->getData(true);

        $cacheKey = 'xskt_mien_nam_' . now()->format('Ymd');
        $cached = Cache::get($cacheKey);

        if (!$data || !isset($data['tinh']) || !isset($data['kq'])) {
            return response()->json($cached);
        }

        // Danh sách mã tỉnh cố định
        $fixedCodes = ['11', '22', '33', '44'];

        // Danh sách mã tỉnh thực tế hôm nay
        $todayCodes = explode(',', $data['tinh']);

        // Kết quả cuối cùng
        $results = [];

        foreach ($todayCodes as $index => $realCode) {
            $fixedCode = $fixedCodes[$index] ?? null;

            if (!$fixedCode || !isset($data['kq'][$realCode])) continue;

            $kq = $data['kq'][$realCode];

            // Gán theo dạng từng giải từ G0 -> G8
            $results[$fixedCode] = [
                "G0" => isset($kq["0"]) ? [$kq["0"]] : [],
                "G1" => isset($kq["1"]) ? [$kq["1"]] : [],
                "G2" => isset($kq["2"]) ? (array)$kq["2"] : [],
                "G3" => isset($kq["3"]) ? (array)$kq["3"] : [],
                "G4" => isset($kq["4"]) ? (array)$kq["4"] : [],
                "G5" => isset($kq["5"]) ? (array)$kq["5"] : [],
                "G6" => isset($kq["6"]) ? (array)$kq["6"] : [],
                "G7" => isset($kq["7"]) ? (array)$kq["7"] : [],
                "G8" => isset($kq["8"]) ? [$kq["8"]] : [],
            ];
        }

        // So sánh kết quả mới với cache hiện tại

        $hasChanged = true;

        if ($cached && isset($cached['provinces'])) {
            // So sánh từng tỉnh và từng giải
            $hasChanged = false;

            foreach ($results as $code => $newData) {
                if (!isset($cached['provinces'][$code])) {
                    $hasChanged = true;
                    break;
                }

                $oldData = $cached['provinces'][$code];

                // So sánh từng giải từ G0 -> G8
                foreach (range(0, 8) as $i) {
                    $key = 'G' . $i;
                    $newValue = $newData[$key] ?? [];
                    $oldValue = $oldData[$key] ?? [];

                    // So sánh giá trị theo kiểu JSON để phát hiện mọi thay đổi
                    if (json_encode($newValue) !== json_encode($oldValue)) {
                        $hasChanged = true;
                        break 2; // Thoát cả 2 vòng nếu có thay đổi
                    }
                }
            }
        }

        if (!$hasChanged) {
            return response()->json($cached);
        }

        // Nếu có thay đổi, lưu cache mới
        $finalResult = [
            "date" => now()->format('Y-m-d'),
            "time" => now()->format('H:i:s'),
            "region" => "Miền Nam",
            "provinces" => $results
        ];

        Cache::put($cacheKey, $finalResult, now()->addMinutes(120));

        //lấy kết quả để tạo dọng nói

        $spoken = []; // Lưu các kết quả đã xử lý tránh phát lại

        foreach ($results as $code => $data) {
            foreach (range(0, 8) as $i) {
                $key = 'G' . $i;
                $values = $data[$key] ?? [];

                if (is_array($values)) {
                    foreach ($values as $val) {
                        if (is_numeric($val)) {
                            $uniqueKey = "{$code}_{$key}_{$val}";
                            if (!in_array($uniqueKey, $spoken)) {
                                $spoken[] = $uniqueKey; // đánh dấu đã xử lý
                                $voiceData = [
                                    'code' => $code,
                                    'giai' => $key,
                                    'value' => $val
                                ];
                                $this->testVoice($voiceData);

                            }
                        }
                    }
                } else {
                    if (is_numeric($values)) {
                        $uniqueKey = "{$code}_{$key}_{$values}";
                        if (!in_array($uniqueKey, $spoken)) {
                            $spoken[] = $uniqueKey;
                            $voiceData = [
                                'code' => $code,
                                'giai' => $key,
                                'value' => $values
                            ];
                            $this->testVoice($voiceData);

                        }
                    }
                }
            }
        }

        return response()->json($finalResult);
    }

    // Kết quả miền trung

    public function getLiveResultMienTrung()
    {
        $rawResponse = $this->fetchKQXS('mt');
        $data = $rawResponse->getData(true);

        $cacheKey = 'xskt_mien_trung_' . now()->format('Ymd');
        $cached = Cache::get($cacheKey);

        if (!$data || !isset($data['tinh'], $data['kq'])) {
            return response()->json($cached);
        }

        $fixedCodes = ['11', '22', '33', '44'];
        $todayCodes = explode(',', $data['tinh']);
        $resultKeys = ['0', '1', '2', '3', '4', '5', '6', '7', '8'];

        $results = [];

        foreach ($todayCodes as $index => $realCode) {
            $fixedCode = $fixedCodes[$index] ?? null;
            if (!$fixedCode || !isset($data['kq'][$realCode])) continue;

            $kq = $data['kq'][$realCode];

            foreach ($resultKeys as $k) {
                $results[$fixedCode]["G{$k}"] = isset($kq[$k])
                    ? (is_array($kq[$k]) ? $kq[$k] : [$kq[$k]])
                    : [];
            }
        }

        $hasChanged = false;

        if ($cached && isset($cached['provinces'])) {
            foreach ($results as $code => $newData) {
                $oldData = $cached['provinces'][$code] ?? [];

                foreach ($newData as $key => $newValue) {
                    $oldValue = $oldData[$key] ?? [];

                    if (json_encode($newValue) !== json_encode($oldValue)) {
                        $hasChanged = true;
                        break 2; // thoát khỏi cả 2 vòng nếu có thay đổi
                    }
                }
            }
        } else {
            $hasChanged = true; // nếu chưa có cache thì chắc chắn có thay đổi
        }

        if (!$hasChanged) {
            return response()->json($cached);
        }

        // Cập nhật cache mới nếu có thay đổi
        $finalResult = [
            "date" => now()->format('Y-m-d'),
            "time" => now()->format('H:i:s'),
            "region" => "Miền Nam",
            "provinces" => $results
        ];

        Cache::put($cacheKey, $finalResult, now()->addMinutes(120));

        return response()->json($finalResult);
    }


    public function fetchKQXS($mien)
    {
        $test = true;
        $timestamp = round(microtime(true) * 1000); // Timestamp dạng milliseconds
        if ($mien === 'mn') {
            if ($test) {
                $url = "http://mobile.vtc.vn/tool/inside/aspnet_client/auto/xoso/Live.aspx";
            } else {
                $url = "https://data.minhngoc.net/O0O/0/xstt/js_m1.js?_=$timestamp";
            }
        } else {
            $url = "https://data.minhngoc.net/O0O/0/xstt/js_m3.js?_=$timestamp";
        }

        $response = Http::withOptions(['verify' => false])->get($url);
        $body = $response->body();
        if ($test) {
            $result = json_decode($body, true);
        } else {
            $jsonLike = $this->convertJSObjectToJson($body, $mien === 'mn' ? 'mn' : 'mt');
            $result = json_decode($jsonLike, true);
        }

        return response()->json($result);
    }

    private function convertJSObjectToJson($js, $regionKey = 'mn')
    {
        // Loại bỏ tiền tố ví dụ: kqxs.mt= hoặc kqxs.mn=
        $pattern = "/^kqxs\\.$regionKey\\s*=\\s*/";
        $js = preg_replace($pattern, '', trim($js));
        $js = rtrim($js, ';');

        // Thay nháy đơn → nháy kép (nếu có)
        $js = str_replace("'", '"', $js);

        // Bọc key không có dấu nháy thành nháy kép
        $js = preg_replace('/([{,])\s*([a-zA-Z0-9_]+)\s*:/', '$1"$2":', $js);

        return $js;
    }

    // Api công ty
    public function getMN()
    {
        $date = Carbon::now()->format('d-m-Y');
        return view('xo-so-mn-backup', ['date' => $date]);
    }

    public function lichQuayThuongMN(): array
    {
        return getlichQuayThuongMN();
    }

    public function lichQuayThuongMT(): array
    {
        return getlichQuayThuongMT();
    }


    public function getLiveResultMn()
    {
        $rawResponse = $this->fetchKQXS2();
        $tinhHomNay = $this->lichQuayThuongMN();
        $maTinhHomNay = array_column($tinhHomNay, 'code');

        $data = $rawResponse->getData(true)['data'] ?? [];
        $rawData = array_filter($data, fn($item) => in_array($item['Region'], $maTinhHomNay));

        // Lấy bản ghi mới nhất theo Region + PrizeNumber
        $grouped = [];
        foreach ($rawData as $item) {
            $region = (int)$item['Region'];
            $prize = (int)$item['PrizeNumber'];
            $updateTime = $item['LotteyUpdateTime'];

            if (!isset($grouped[$region][$prize]) || $updateTime > $grouped[$region][$prize]['LotteyUpdateTime']) {
                $grouped[$region][$prize] = $item;
            }
        }

        // Danh sách các giải cần xuất
        $prizeKeys = [
            'G0' => 0, 'G1' => 1, 'G2' => 2, 'G3' => 3, 'G4' => 4,
            'G5' => 5, 'G6' => 6, 'G7' => 7, 'G8' => 8, 'G9' => 9
        ];

        // Xử lý đầu ra
        $output = [];
        foreach ($tinhHomNay as $tinh) {
            $regionCode = (int)$tinh['code'];
            $regionData = $grouped[$regionCode] ?? [];

            $province = [
                'code' => $regionCode,
                'name' => $tinh['name'],
            ];

            foreach ($prizeKeys as $key => $number) {
                $value = $regionData[$number]['PrizeString'] ?? null;
                $province[$key] = $value ? [$value] : [];
            }

            // Xử lý tách chuỗi cho các giải nhiều số
            foreach (['G3', 'G4', 'G6'] as $multiKey) {
                if (!empty($province[$multiKey])) {
                    $province[$multiKey] = explode('-', $province[$multiKey][0]);
                }
            }

            $output[] = $province;
        }

        return response()->json($output);

    }

    private function time()
    {
        $date = DateTime::createFromFormat('Y-m-d H:i:s.u', date('Y-m-d') . ' 00:00:00.000');
        $formatted = $date->format('YmdHisv');
        return $formatted;
    }

    public function fetchKQXS2()
    {
        $timestamp = (int)$this->time();

        $sign = md5($timestamp . '118100' . 'XOSOTV_LIVE' . 'Aq61abti3s658o969685H');

        $url = "http://api.lottery.vtcmobile.com.vn/LotteryResult/api/LotteryResult/GetResultByTimeSync?updateTime=$timestamp&region=1&province=18&limit=100&service=XOSOTV_LIVE&sign=$sign";

        $response = Http::withOptions(['verify' => false])->get($url);

        $body = $response->body();

        $result = json_decode($body, true);

        return response()->json($result);
    }

    public function getLiveResultMienTrungBackup()
    {
        $rawResponse = $this->fetchKQXS2();
        $tinhHomNay = $this->lichQuayThuongMT();
        $maTinhHomNay = array_column($tinhHomNay, 'code');

        $data = $rawResponse->getData(true)['data'] ?? [];
        $rawData = array_filter($data, fn($item) => in_array($item['Region'], $maTinhHomNay));

        // Lấy bản ghi mới nhất theo Region + PrizeNumber
        $grouped = [];
        foreach ($rawData as $item) {
            $region = (int)$item['Region'];
            $prize = (int)$item['PrizeNumber'];
            $updateTime = $item['LotteyUpdateTime'];

            if (!isset($grouped[$region][$prize]) || $updateTime > $grouped[$region][$prize]['LotteyUpdateTime']) {
                $grouped[$region][$prize] = $item;
            }
        }

        // Danh sách các giải cần xuất
        $prizeKeys = [
            'G0' => 0, 'G1' => 1, 'G2' => 2, 'G3' => 3, 'G4' => 4,
            'G5' => 5, 'G6' => 6, 'G7' => 7, 'G8' => 8, 'G9' => 9
        ];

        // Xử lý đầu ra
        $output = [];
        foreach ($tinhHomNay as $tinh) {
            $regionCode = (int)$tinh['code'];
            $regionData = $grouped[$regionCode] ?? [];

            $province = [
                'code' => $regionCode,
                'name' => $tinh['name'],
            ];

            foreach ($prizeKeys as $key => $number) {
                $value = $regionData[$number]['PrizeString'] ?? null;
                $province[$key] = $value ? [$value] : [];
            }

            // Xử lý tách chuỗi cho các giải nhiều số
            foreach (['G3', 'G4', 'G6'] as $multiKey) {
                if (!empty($province[$multiKey])) {
                    $province[$multiKey] = explode('-', $province[$multiKey][0]);
                }
            }

            $output[] = $province;
        }

        return response()->json($output);

    }


    // đọc thoại
    public function testVoice($newVoices): bool
    {
        $code = $newVoices['code'];
        $giai = $newVoices['giai'];
        $value = $newVoices['value'];

        if (!empty($newVoices)) {
            $xskt_mien_nam_da_tao = Cache::get('xskt_mien_nam_da_tao' . now()->format('Ymd'), []);

            $indexUnique = '';
            $ketQuaHienTai = Cache::get('xskt_mien_nam_' . now()->format('Ymd'));

            if ($ketQuaHienTai && isset($ketQuaHienTai['provinces'][$code][$giai])) {
                $kq = $ketQuaHienTai['provinces'][$code][$giai];

                if (is_array($kq)) {
                    if (count($kq) > 1) {
                        $index = array_search($value, $kq);
                        if ($index !== false) {
                            $indexUnique = $index + 1;
                        }
                    }
                }
            }

            $filename = "{$code}_{$giai}_{$indexUnique}_{$value}.wav";

            if (!in_array($filename, $xskt_mien_nam_da_tao)) {
                TextToVoiceJob::dispatch($newVoices)->onQueue('voice');
                Log::info('TextToVoiceJob dispatched', [
                    'code' => $newVoices['code'] ?? null,
                    'giai' => $newVoices['giai'] ?? null,
                    'value' => $newVoices['value'] ?? null,
                    'timestamp' => now()->toDateTimeString(),
                ]);
            }
        }
        return true;
    }

    public function playVoice()
    {

        $files = collect(Storage::disk('public')->files('voice'))
            ->filter(function ($file) {
                return str_ends_with($file, '.wav');
            })
            ->sort()
            ->values();

        $voiceUrls = $files->map(function ($file) {
            return asset("storage /{
                $file}");
        });

        if ($files->count() > 0) {
            DeleteVoiceFiles::dispatch()->delay(now()->addSeconds(5));
        }

        return response()->json(['voices' => $voiceUrls]);
    }

    // xổ số miền trung

    public function daiHomNayMt()
    {
        $lichQuayThuong = [
            'Monday' => [
                ['name' => 'Thừa Thiên Huế', 'code' => 'XX'],  // Bạn có thể thay 'XX' bằng mã tỉnh thực tế nếu có
                ['name' => 'Phú Yên', 'code' => 'XX'],
            ],
            'Tuesday' => [
                ['name' => 'Đắk Lắk', 'code' => 'XX'],
                ['name' => 'Quảng Nam', 'code' => 'XX'],
            ],
            'Wednesday' => [
                ['name' => 'Đà Nẵng', 'code' => 'XX'],
                ['name' => 'Khánh Hòa', 'code' => 'XX'],
            ],
            'Thursday' => [
                ['name' => 'Bình Định', 'code' => 'XX'],
                ['name' => 'Quảng Trị', 'code' => 'XX'],
                ['name' => 'Quảng Bình', 'code' => 'XX'],
            ],
            'Friday' => [
                ['name' => 'Gia Lai', 'code' => 'XX'],
                ['name' => 'Ninh Thuận', 'code' => 'XX'],
            ],
            'Saturday' => [
                ['name' => 'Đà Nẵng', 'code' => 'XX'],
                ['name' => 'Quảng Ngãi', 'code' => 'XX'],
                ['name' => 'Đắk Nông', 'code' => 'XX'],
            ],
            'Sunday' => [
                ['name' => 'Khánh Hòa', 'code' => 'XX'],
                ['name' => 'Kon Tum', 'code' => 'XX'],
            ],
        ];

        $homNay = Carbon::now()->englishDayOfWeek;
        $tinhHomNay = $lichQuayThuong[$homNay] ?? [];

        return $tinhHomNay;
    }

}
