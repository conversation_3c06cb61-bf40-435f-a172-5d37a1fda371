<?php

namespace App\Http\Controllers;

use App\Jobs\DeleteVoiceFiles;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class TextToSpeechController extends Controller
{
    public function getVoices()
    {



        $now = now()->format('Y-m-d');
        $voicePath = "voice/{$now}";

        $cacheKey = 'xskt_' . $mien . '_da_chay';
        
        $files = collect(Storage::disk('public')->files($voicePath))
            ->filter(function ($file) {
                return str_ends_with($file, '.wav');
            })
            ->sort()
            ->values();

        $voiceUrls = $files->map(function ($file) {
            return [
                'url' => asset("storage/{$file}"),
                'filename' => basename($file),
            ];
        });

        if ($files->count() > 0) {
            DeleteVoiceFiles::dispatch($voicePath)->delay(now()->addSeconds(5));
        }

        return response()->json(['voices' => $voiceUrls]);
    }
}
