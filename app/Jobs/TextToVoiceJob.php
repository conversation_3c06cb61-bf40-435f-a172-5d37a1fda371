<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class TextToVoiceJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */

    protected $newVoices;

    public function __construct($newVoices)
    {
        $this->newVoices = $newVoices;
    }

    /**
     * Execute the job.
     *
     * @return string
     */
    public function handle(): string
    {
        try {
            $tinhSoXo = ["Phú Yên"];
            $xskt_mien_nam_da_tao = Cache::get('xskt_mien_nam_da_tao' . now()->format('Ymd'), []);

            if ($this->newVoices) {
                $mapIndex = [
                    '11' => 0,
                    '22' => 1,
                    '33' => 2,
                    '44' => 3,
                ];

                $code = $this->newVoices['code'];
                $giai = $this->newVoices['giai'];
                $value = $this->newVoices['value'];
                $valueRead = implode(' ', str_split($value));

                // Lấy tên tỉnh
                if (isset($mapIndex[$code])) {
                    $tenTinh = getlichQuayThuongMT()[$mapIndex[$code]]['name'];
                }

                // Xác định từ ngữ mở đầu
                if (in_array($tenTinh, $tinhSoXo)) {
                    $textFirst = "Sở xổ số $tenTinh giải";
                } else {
                    $textFirst = "Kết quả xổ số $tenTinh giải";
                }

                // Xóa ký tự 'G' ở đầu tên giải
                $giaiText = substr($giai, 1);

                // Xác định lần thứ mấy nếu có nhiều kết quả
                $lanThu = '';
                $indexUnique = '';
                $ketQuaHienTai = Cache::get('xskt_mien_nam_' . now()->format('Ymd'));

                if ($ketQuaHienTai && isset($ketQuaHienTai['provinces'][$code][$giai])) {
                    $kq = $ketQuaHienTai['provinces'][$code][$giai];

                    if (is_array($kq)) {
                        if (count($kq) > 1) {
                            $index = array_search($value, $kq);
                            if ($index !== false) {
                                $lanThu = "lần thứ " . ($index + 1);
                                $indexUnique = $index + 1;
                            }
                        }
                    }
                }
                if ($lanThu) {
                    $text = "$textFirst $giaiText có kết quả quay $lanThu là $valueRead";
                } else {
                    $text = "$textFirst $giaiText có kết quả là $valueRead";
                }

                $filename = "{$code}_{$giai}_{$indexUnique}_{$value}.wav";

                if (in_array($filename, $xskt_mien_nam_da_tao)) {
                    return 'OK';
                }

                $this->textToVoice($text, $filename);
            }

            $xskt_mien_nam_da_tao[] = $filename;
            Cache::put('xskt_mien_nam_da_tao' . now()->format('Ymd'), $xskt_mien_nam_da_tao);

            Log::info('TextToVoiceJob xử lý thành công', [
                'result' => $text,
                'time' => now()->format('H:i:s'),
                'data' => $this->newVoices
            ]);
        } catch (\Throwable $e) {
            Log::error('TextToVoiceJob bị lỗi', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $this->newVoices,
                'time' => now()->format('H:i:s'),
            ]);
        }

        return 'OK';
    }

    function textToVoice($text, $filename)
    {
        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
        ])->withOptions([
            'verify' => false,
        ])->post('https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-tts:generateContent?key=AIzaSyCfezDi1_FvvmZ6QiQuE0bwqhjQRjy0AIU', [
            'model' => 'gemini-2.5-flash-preview-tts',
            'contents' => [[
                'parts' => [['text' => $text]]
            ]],
            'generationConfig' => [
                'responseModalities' => ['AUDIO'],
                'speechConfig' => [
                    'voiceConfig' => [
                        'prebuiltVoiceConfig' => [
                            'voiceName' => "Charon"
                        ]
                    ]
                ]
            ]
        ]);

        $data = $response->json();
        $base64Pcm = $data['candidates'][0]['content']['parts'][0]['inlineData']['data'] ?? null;

        if (!$base64Pcm) {
            return false;
        }

        $pcmData = base64_decode($base64Pcm);
        $channels = 1;
        $sampleRate = 24000;
        $bitsPerSample = 16;
        $byteRate = $sampleRate * $channels * ($bitsPerSample / 8);
        $blockAlign = $channels * ($bitsPerSample / 8);
        $subchunk2Size = strlen($pcmData);
        $chunkSize = 36 + $subchunk2Size;

        $header =
            pack('A4V', 'RIFF', $chunkSize) .
            pack('A4', 'WAVE') .
            pack('A4VvvVVvv', 'fmt ', 16, 1, $channels, $sampleRate, $byteRate, $blockAlign, $bitsPerSample) .
            pack('A4V', 'data', $subchunk2Size);

        $wavData = $header . $pcmData;

        $date = now()->format('Y-m-d');
        $voiceDir = storage_path("app/public/voice/{$date}");

        if (!file_exists($voiceDir)) {
            mkdir($voiceDir, 0755, true);
        }

        file_put_contents("{$voiceDir}/{$filename}", $wavData);

        return true;
    }
}
