<?php

use Carbon\Carbon;

if (!function_exists('getlichQuayThuongMT')) {
    function getlichQuayThuongMT()
    {
        $lichQuayThuong = [
            'Monday' => [
                ['name' => 'Phú Yên', 'code' => '10'],
                ['name' => 'Huế', 'code' => '15'],
            ],
            'Tuesday' => [
                ['name' => 'Đắk Lắk', 'code' => '4'],
                ['name' => 'Quảng Nam', 'code' => '13'],
            ],
            'Wednesday' => [
                ['name' => 'Đà Nẵng', 'code' => '3'],
                ['name' => 'Khánh Hòa', 'code' => '7'],
            ],
            'Thursday' => [
                ['name' => 'Bình Định', 'code' => '2'],
                ['name' => 'Quảng Trị', 'code' => '14'],
                ['name' => 'Quảng Bình', 'code' => '11'],
            ],
            'Friday' => [
                ['name' => '<PERSON><PERSON> Lai', 'code' => '6'],
                ['name' => 'Ninh Thuận', 'code' => '9'],
            ],
            'Saturday' => [
                ['name' => 'Đà Nẵng', 'code' => '3'],
                ['name' => 'Quảng Ngãi', 'code' => '12'],
                ['name' => 'Đắk Nông', 'code' => '5']
            ],
            'Sunday' => [
                ['name' => 'Kon Tum', 'code' => '8'],
                ['name' => 'Khánh Hòa', 'code' => '7'],
                ['name' => 'Huế', 'code' => '15'],
            ],
        ];

        $homNay = Carbon::now()->englishDayOfWeek;
        $tinhHomNay = $lichQuayThuong[$homNay] ?? [];

        return $tinhHomNay;
    }
}

if (!function_exists('getlichQuayThuongMN')) {
    function getlichQuayThuongMN()
    {
        $lichQuayThuong = [
            'Monday' => [
                ['name' => 'TP HCM', 'code' => '27'],
                ['name' => 'Đồng Tháp', 'code' => '25'],
                ['name' => 'Cà Mau', 'code' => '21'],
            ],
            'Tuesday' => [
                ['name' => 'Bến Tre', 'code' => '18'],
                ['name' => 'Vũng Tàu', 'code' => '35'],
                ['name' => 'Bạc Liêu', 'code' => '17'],
            ],
            'Wednesday' => [
                ['name' => 'Đồng Nai', 'code' => '24'],
                ['name' => 'Cần Thơ', 'code' => '22'],
                ['name' => 'Sóc Trăng', 'code' => '30'],
            ],
            'Thursday' => [
                ['name' => 'Tây Ninh', 'code' => '31'],
                ['name' => 'An Giang', 'code' => '16'],
                ['name' => 'Bình Thuận', 'code' => '20'],
            ],
            'Friday' => [
                ['name' => 'Vĩnh Long', 'code' => '34'],
                ['name' => 'Bình Dương', 'code' => '19'],
                ['name' => 'Trà Vinh', 'code' => '33'],

            ],
            'Saturday' => [
                ['name' => 'TP HCM', 'code' => '27'],
                ['name' => 'Long An', 'code' => '29'],
                ['name' => 'Bình Phước', 'code' => '40'],
                ['name' => 'Hậu Giang', 'code' => '26'],
            ],
            'Sunday' => [
                ['name' => 'Tiền Giang', 'code' => '32'],
                ['name' => 'Kiên Giang', 'code' => '28'],
                ['name' => 'Đà Lạt', 'code' => '23'],
            ],
        ];

        $homNay = Carbon::now()->englishDayOfWeek;
        $tinhHomNay = $lichQuayThuong[$homNay] ?? [];

        return $tinhHomNay;
    }
}
