<!DOCTYPE html>
<html lang="vi" xmlns="http://www.w3.org/1999/xhtml">

<head>
    <title><PERSON><PERSON> Số</title>
    <meta name="description" content="Xổ Số" />
    <meta name="keywords" content="Xổ Số" />
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <link href="{{ asset('assets/so-xo/css/bootstrap.min.css') }}" rel="stylesheet" type="text/css">
    <link href="{{ asset('assets/so-xo/css/style.css') }}" rel="stylesheet" type="text/css">
    <script src="{{ asset('assets/so-xo/js/bootstrap.bundle.min.js') }}"></script>
    <script src="{{ asset('assets/so-xo/js/jquery-1.9.1.min.js') }}"></script>

</head>

<body>
    <div class="wrapper">
        <section id="frame1" class="section section-frame1">
            <header id="header" class="header">
                <div class="frame-header">
                    <a class="logo" href="#">
                        <img src="{{ asset('assets/so-xo/images/logo.png') }}" class="img-fluid" alt="Logo" />
                    </a>
                    <h1 class="title desktop">Kqxs Miền Nam ngày {{ $date  }}</h1>
                    <div class="time" id="server-time">
                        16:15:00
                    </div>
                </div>
            </header>
            <div class="container1">
                <h1 class="title mobile">Kqxs Miền Nam ngày {{ $date }}</h1>
                <div class="line line1 d-flex justify-content-between align-items-center">
                    <div class="left">
                        G
                    </div>
                    <div class="right">
                        <ul class="nav nav-justified" id="list-dai-hom-nay">
                            <!-- Các tỉnh sẽ được thêm vào đây -->
                        </ul>
                    </div>
                </div>
                <div class="line line2 d-flex justify-content-between align-items-center">
                    <div class="left">
                        G8
                    </div>
                    <div class="right">
                        <ul class="list-group list-group-horizontal">
                            <li id="G8-01" class="list-group-item border-start-0 border-bottom-0 border-top-0"><span
                                    class="G8-Loader-01 loader"></span></li>
                            <li id="G8-02" class="list-group-item border-bottom-0 border-top-0"><span
                                    class="G8-Loader-02 loader"></span></li>
                            <li id="G8-03" class="list-group-item border-bottom-0 border-top-0"><span
                                    class="G8-Loader-03 loader"></span></li>
                            <li id="G8-04"
                                class="t4 list-group-item border-start-0 border-bottom-0 border-end-0 border-top-0">
                                <span class="G8-Loader-03 loader"></span>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="line line3 d-flex justify-content-between align-items-center">
                    <div class="left">
                        G7
                    </div>
                    <div class="right">
                        <ul class="list-group list-group-horizontal">
                            <li id="G7-01" class="list-group-item border-start-0 border-bottom-0 bg-gray-50"><span
                                    class="loader"></span></li>
                            <li id="G7-02" class="list-group-item border-bottom-0 bg-gray-50"><span
                                    class="loader"></span></li>
                            <li id="G7-03" class="list-group-item border-bottom-0 bg-gray-50"><span
                                    class="loader"></span></li>
                            <li id="G7-04"
                                class="t4 list-group-item border-start-0 border-bottom-0 border-end-0 bg-gray-50"><span
                                    class="loader"></span></li>
                        </ul>
                    </div>
                </div>
                <div class="line line4 d-flex justify-content-between align-items-center">
                    <div class="left">
                        G6
                    </div>
                    <div class="right">
                        <ul class="list-group list-group-horizontal">
                            <li class="list-group-item border-start-0 border-bottom-0">
                                <p class="row justify-content-center mb-0">
                                    <span id="G6-1-01" class="col-12 col-lg-6"><span class="loader"></span></span>
                                    <span id="G6-2-01" class="col-12 col-lg-6"><span class="loader"></span></span>
                                    <span id="G6-3-01" class="col-12 col-lg-6"><span class="loader"></span></span>
                                </p>
                            </li>
                            <li class="list-group-item border-bottom-0">
                                <p class="row justify-content-center mb-0">
                                    <span id="G6-1-02" class="col-12 col-lg-6"><span class="loader"></span></span>
                                    <span id="G6-2-02" class="col-12 col-lg-6"><span class="loader"></span></span>
                                    <span id="G6-3-02" class="col-12 col-lg-6"><span class="loader"></span></span>
                                </p>
                            </li>
                            <li class="list-group-item border-bottom-0">
                                <p class="row justify-content-center mb-0">
                                    <span id="G6-1-03" class="col-12 col-lg-6"><span class="loader"></span></span>
                                    <span id="G6-2-03" class="col-12 col-lg-6"><span class="loader"></span></span>
                                    <span id="G6-3-03" class="col-12 col-lg-6"><span class="loader"></span></span>
                                </p>
                            </li>
                            <li class="t4 list-group-item border-start-0 border-bottom-0 border-end-0">
                                <p class="row justify-content-center mb-0">
                                    <span id="G6-1-04" class="col-12 col-lg-6"><span class="loader"></span></span>
                                    <span id="G6-2-04" class="col-12 col-lg-6"><span class="loader"></span></span>
                                    <span id="G6-3-04" class="col-12 col-lg-6"><span class="loader"></span></span>
                                </p>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="line line5 d-flex justify-content-between align-items-center">
                    <div class="left">
                        G5
                    </div>
                    <div class="right">
                        <ul class="list-group list-group-horizontal">
                            <li id="G5-01" class="list-group-item border-start-0 border-bottom-0 bg-gray-50"><span
                                    class="loader"></span></li>
                            <li id="G5-02" class="list-group-item border-bottom-0 bg-gray-50"><span
                                    class="loader"></span></li>
                            <li id="G5-03"
                                class="list-group-item border-start-0 border-bottom-0 border-end-0 bg-gray-50"><span
                                    class="loader"></span></li>
                            <li id="G5-04"
                                class="t4 list-group-item border-start-0 border-bottom-0 border-end-0 bg-gray-50"><span
                                    class="loader"></span></li>
                        </ul>
                    </div>
                </div>
                <div class="line line6 d-flex justify-content-between align-items-center">
                    <div class="left">
                        G4
                    </div>
                    <div class="right">
                        <ul class="list-group list-group-horizontal">
                            <li class="list-group-item border-start-0 border-bottom-0">
                                <p class="row justify-content-center mb-0">
                                    <span id="G4-1-01" class="col-12 col-lg-6"><span class="loader"></span></span>
                                    <span id="G4-2-01" class="col-12 col-lg-6"><span class="loader"></span></span>
                                    <span id="G4-3-01" class="col-12 col-lg-6"><span class="loader"></span></span>
                                    <span id="G4-4-01" class="col-12 col-lg-6"><span class="loader"></span></span>
                                    <span id="G4-5-01" class="col-12 col-lg-6"><span class="loader"></span></span>
                                    <span id="G4-6-01" class="col-12 col-lg-6"><span class="loader"></span></span>
                                    <span id="G4-7-01" class="col-12 col-lg-6"><span class="loader"></span></span>
                                </p>
                            </li>
                            <li class="list-group-item border-bottom-0">
                                <p class="row justify-content-center mb-0">
                                    <span id="G4-1-02" class="col-12 col-lg-6"><span class="loader"></span></span>
                                    <span id="G4-2-02" class="col-12 col-lg-6"><span class="loader"></span></span>
                                    <span id="G4-3-02" class="col-12 col-lg-6"><span class="loader"></span></span>
                                    <span id="G4-4-02" class="col-12 col-lg-6"><span class="loader"></span></span>
                                    <span id="G4-5-02" class="col-12 col-lg-6"><span class="loader"></span></span>
                                    <span id="G4-6-02" class="col-12 col-lg-6"><span class="loader"></span></span>
                                    <span id="G4-7-02" class="col-12 col-lg-6"><span class="loader"></span></span>
                                </p>
                            </li>
                            <li class="list-group-item border-bottom-0">
                                <p class="row justify-content-center mb-0">
                                    <span id="G4-1-03" class="col-12 col-lg-6"><span class="loader"></span></span>
                                    <span id="G4-2-03" class="col-12 col-lg-6"><span class="loader"></span></span>
                                    <span id="G4-3-03" class="col-12 col-lg-6"><span class="loader"></span></span>
                                    <span id="G4-4-03" class="col-12 col-lg-6"><span class="loader"></span></span>
                                    <span id="G4-5-03" class="col-12 col-lg-6"><span class="loader"></span></span>
                                    <span id="G4-6-03" class="col-12 col-lg-6"><span class="loader"></span></span>
                                    <span id="G4-7-03" class="col-12 col-lg-6"><span class="loader"></span></span>
                                </p>
                            </li>
                            <li class="t4 list-group-item border-start-0 border-bottom-0 border-end-0">
                                <p class="row justify-content-center mb-0">
                                    <span id="G4-1-04" class="col-12 col-lg-6"><span class="loader"></span></span>
                                    <span id="G4-2-04" class="col-12 col-lg-6"><span class="loader"></span></span>
                                    <span id="G4-3-04" class="col-12 col-lg-6"><span class="loader"></span></span>
                                    <span id="G4-4-04" class="col-12 col-lg-6"><span class="loader"></span></span>
                                    <span id="G4-5-04" class="col-12 col-lg-6"><span class="loader"></span></span>
                                    <span id="G4-6-04" class="col-12 col-lg-6"><span class="loader"></span></span>
                                    <span id="G4-7-04" class="col-12 col-lg-6"><span class="loader"></span></span>
                                </p>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="line line7 d-flex justify-content-between align-items-center">
                    <div class="left">
                        G3
                    </div>
                    <div class="right">
                        <ul class="list-group list-group-horizontal">
                            <li class="list-group-item border-start-0 border-bottom-0 bg-gray-50">
                                <p class="row justify-content-center mb-0">
                                    <span id="G3-1-01" class="col-12 col-lg-6"><span class="loader"></span></span>
                                    <span id="G3-2-01" class="col-12 col-lg-6"><span class="loader"></span></span>
                                </p>
                            </li>
                            <li class="list-group-item border-bottom-0 bg-gray-50">
                                <p class="row justify-content-center mb-0">
                                    <span id="G3-1-02" class="col-12 col-lg-6"><span class="loader"></span></span>
                                    <span id="G3-2-02" class="col-12 col-lg-6"><span class="loader"></span></span>
                                </p>
                            </li>
                            <li class="list-group-item border-start-0 border-bottom-0 border-end-0 bg-gray-50">
                                <p class="row justify-content-center mb-0">
                                    <span id="G3-1-03" class="col-12 col-lg-6"><span class="loader"></span></span>
                                    <span id="G3-2-03" class="col-12 col-lg-6"><span class="loader"></span></span>
                                </p>
                            </li>
                            <li class="t4 list-group-item border-start-0 border-bottom-0 border-end-0 bg-gray-50">
                                <p class="row justify-content-center mb-0">
                                    <span id="G3-1-04" class="col-12 col-lg-6"><span class="loader"></span></span>
                                    <span id="G3-2-04" class="col-12 col-lg-6"><span class="loader"></span></span>
                                </p>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="line line8 d-flex justify-content-between align-items-center">
                    <div class="left">
                        G2
                    </div>
                    <div class="right">
                        <ul class="list-group list-group-horizontal">
                            <li id="G2-01" class="list-group-item border-start-0 border-bottom-0"><span
                                    class="loader"></span></li>
                            <li id="G2-02" class="list-group-item border-bottom-0"><span class="loader"></span></li>
                            <li id="G2-03" class="list-group-item border-bottom-0"><span class="loader"></span></li>
                            <li id="G2-04" class="t4 list-group-item border-start-0 border-bottom-0 border-end-0"><span
                                    class="loader"></span></li>
                        </ul>
                    </div>
                </div>
                <div class="line line8 d-flex justify-content-between align-items-center">
                    <div class="left">
                        G1
                    </div>
                    <div class="right">
                        <ul class="list-group list-group-horizontal">
                            <li id="G1-01" class="list-group-item border-start-0 border-bottom-0 bg-gray-50"><span
                                    class="loader"></span></li>
                            <li id="G1-02" class="list-group-item border-bottom-0 bg-gray-50"><span
                                    class="loader"></span></li>
                            <li id="G1-03" class="list-group-item border-bottom-0 bg-gray-50"><span
                                    class="loader"></span></li>
                            <li id="G1-04"
                                class="t4 list-group-item border-start-0 border-bottom-0 border-end-0 bg-gray-50"><span
                                    class="loader"></span></li>
                        </ul>
                    </div>
                </div>
                <div class="line line9 d-flex justify-content-between align-items-center bg-red-50">
                    <div class="left">
                        G.DB
                    </div>
                    <div class="right">
                        <ul class="list-group list-group-horizontal">
                            <li id="G0-01" class="list-group-item border-0 bg-red"><span class="loader"></span></li>
                            <li id="G0-02" class="list-group-item border-0 bg-red"><span class="loader"></span></li>
                            <li id="G0-03" class="list-group-item border-0 bg-red"><span class="loader"></span></li>
                            <li id="G0-04" class="t4 list-group-item border-0 bg-red"><span class="loader"></span></li>
                        </ul>
                    </div>
                </div>

            </div>
            <div class="ball1"><img src="{{ asset('assets/so-xo/images/ball1.png') }}" class="img-fluid" alt="Ball 1" />
            </div>
            <div class="ball2"><img src="{{ asset('assets/so-xo/images/ball2.png') }}" class="img-fluid" alt="Ball 2" />
            </div>
            <div class="ball3"><img src="{{ asset('assets/so-xo/images/ball3.png') }}" class="img-fluid" alt="Ball 3" />
            </div>
            <div class="ball4"><img src="{{ asset('assets/so-xo/images/ball4.png') }}" class="img-fluid" alt="Ball 4" />
            </div>
            <div class="ball5"><img src="{{ asset('assets/so-xo/images/ball5.png') }}" class="img-fluid" alt="Ball 5" />
            </div>
            <div class="ball6"><img src="{{ asset('assets/so-xo/images/ball6.png') }}" class="img-fluid" alt="Ball 6" />
            </div>
            <div class="ball7"><img src="{{ asset('assets/so-xo/images/ball7.png') }}" class="img-fluid" alt="Ball 7" />
            </div>

        </section>
        <div class="footer">
            <div class="container">
                <div class="row align-items-center">
                    <h2 class=" text-center col-12">
                        <img src="{{ asset('assets/so-xo/images/logo.png') }}" class="img-fluid" alt="Logo" />
                    </h2>
                    <div class="col-12 text-center text-footer">
                        <p class="mb-0">Công ty cổ phần VTC dịch vụ di động - Tầng 11 - Tòa nhà VTC Online, số 18 Tam
                            Trinh phường
                            Minh Khai, quận Hai Bà Trưng, Hà Nội</p>
                        <p class="mb-0">SĐT: (84-4).39877470 Email: <EMAIL></p>
                        <div>
                            <!-- <button id="start-button" onclick="playMusic()">Bắt đầu tự động đọc kết quả</button> -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <script src="{{ asset('assets/so-xo/js/res-1.js') }}"></script>

    <script>
    const audio = new Audio('audio/nhac-xo-so.mp3');

    // audio.play().then(() => {
    //     console.log("Nhạc đã phát");
    // }).catch(error => {
    //     console.warn("Không thể phát nhạc: ", error);
    // });
    // audio.loop = true;

    let currentServerTime = null;

    function pad(num) {
        return num.toString().padStart(2, '0');
    }

    function updateTimeDisplay() {
        if (currentServerTime) {
            currentServerTime.setSeconds(currentServerTime.getSeconds() + 1);
            const timeString = pad(currentServerTime.getHours()) + ':' +
                pad(currentServerTime.getMinutes()) + ':' +
                pad(currentServerTime.getSeconds());
            document.getElementById('server-time').textContent = timeString;
        }
    }

    async function initServerTime() {
        const res = await fetch('/server-time');
        const data = await res.json();

        // Tạo object Date từ giờ server
        const [hh, mm, ss] = data.time.split(':');
        const now = new Date(); // giờ client, chỉ dùng để lấy ngày/tháng/năm

        currentServerTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), hh, mm, ss);

        updateTimeDisplay();
        setInterval(updateTimeDisplay, 1000);
    }

    initServerTime();

    let audioQueue = [];
    let isPlaying = false;

    function playNext() {
        if (audioQueue.length === 0) {
            isPlaying = false;
            return;
        }

        isPlaying = true;
        const nextSrc = audioQueue.shift();
        const audio = new Audio(nextSrc);
        audio.play();
        audio.onended = playNext;
    }

    function fetchAndPlay() {
        fetch('/api/play-voice')
            .then(res => res.json())
            .then(data => {
                if (data.voices && data.voices.length > 0) {
                    audioQueue.push(...data.voices);
                    if (!isPlaying) playNext();
                }
            });
    }

    document.getElementById('start-button').addEventListener('click', () => {
        fetchAndPlay(); // gọi lần đầu
        setInterval(fetchAndPlay, 10000); // gọi lặp
    });
    </script>
    <script>
    $(document).ready(function() {
        let liveInterval = null;

        function isAfter4PM() {
            const now = new Date();
            return now.getHours() >= 16;
        }
        // Kiểm tra nếu đã sau 17h30
        function isAfter5_30PM() {
            const now = new Date();
            return now.getHours() > 17 || (now.getHours() === 17 && now.getMinutes() >= 30);
        }

        function simulateSpin(selector) {
            const elements = document.querySelectorAll(selector);
            elements.forEach((el, index) => {
                const finalDigit = el.textContent;
                let count = 0;

                const interval = setInterval(() => {
                    el.textContent = Math.floor(Math.random() * 10);
                    count++;

                    if (count >= 10 + index * 3) { // dừng dần từng số
                        el.textContent = finalDigit;
                        clearInterval(interval);
                    }
                }, 50);
            });
        }

        $.get('api/dai-hom-nay-mien-nam', function(res) {
            const colors = ['bg-blue', 'bg-yellow', 'bg-green', 'bg-gray'];
            const $ul = $('#list-dai-hom-nay');

            $ul.empty();

            res.forEach((province, index) => {
                const bgColor = colors[index % colors.length]; // Luân phiên màu

                const li = `
                    <li class="nav-item ${bgColor} bd-right">
                        <a class="nav-link" href="#" title="${province.name}">${province.name}</a>
                    </li>
                `;
                $ul.append(li);
            });

            if (res.length === 3) {
                $('.t4').hide();
            }

        });

        function fetchAndRenderData() {
            if (isAfter5_30PM()) {
                if (liveInterval) {
                    clearInterval(liveInterval);
                    liveInterval = null;
                    console.log('Đã dừng cập nhật lúc 17h30');
                }
                return;
            }

            $.get("api/kqxs", function(data) {
                if (!data || Object.keys(data).length === 0) {
                    console.warn('Dữ liệu rỗng hoặc không hợp lệ');
                    return;
                }

                const provinceCodes = ['11', '22', '33', '44'];
                const provinceIndexes = ['01', '02', '03', '04'];

                provinceCodes.forEach((code, idx) => {
                    const suffix = provinceIndexes[idx];
                    const provinceData = data.provinces[
                        code]; // 🔧 Thêm dòng này để dùng provinceData bên dưới

                    // G8
                    if (provinceData?.G8?. [0]) {
                        const el = $(`#G8-${suffix}`);
                        const prize = provinceData.G8[0];

                        if (prize.includes('*')) {
                            el.html(`<span class="loader"></span>`);

                            // Nếu chứa ký tự +, hiển thị số quay
                        } else if (prize.includes('+')) {
                            let spinning = '';

                            for (let i = 0; i < prize.length; i++) {
                                if (prize[i] === '+') {
                                    spinning += `<span class="spinning-digit">0</span>`;
                                } else {
                                    spinning += `<span>${prize[i]}</span>`;
                                }
                            }
                            el.html(spinning);
                            simulateSpin('.spinning-digit')
                        } else {
                            el.text(prize);
                        }

                    }

                    // G7
                    if (provinceData?.G7?. [0]) {
                        const el = $(`#G7-${suffix}`);
                        const prize = provinceData.G7[0];

                        if (prize.includes('*')) {
                            el.html(`<span class="loader"></span>`);

                            // Nếu chứa ký tự +, hiển thị số quay
                        } else if (prize.includes('+')) {
                            let spinning = '';

                            for (let i = 0; i < prize.length; i++) {
                                if (prize[i] === '+') {
                                    spinning += `<span class="spinning-digit">0</span>`;
                                } else {
                                    spinning += `<span>${prize[i]}</span>`;
                                }
                            }

                            el.html(spinning);
                            simulateSpin('.spinning-digit')
                        } else {
                            el.text(prize);
                        }
                    }

                    // G6 - 3 giải
                    if (provinceData?.G6) {
                        for (let i = 0; i < 3; i++) {
                            const el = $(`#G6-${i + 1}-${suffix}`);
                            const prize = provinceData.G6[i];
                            if (prize.includes('*')) {
                                el.html(`<span class="loader"></span>`);

                                // Nếu chứa ký tự +, hiển thị số quay
                            } else if (prize.includes('+')) {
                                let spinning = '';

                                for (let i = 0; i < prize.length; i++) {
                                    if (prize[i] === '+') {
                                        spinning += `<span class="spinning-digit">0</span>`;
                                    } else {
                                        spinning += `<span>${prize[i]}</span>`;
                                    }
                                }

                                el.html(spinning);
                                simulateSpin('.spinning-digit')
                                // Nếu là kết quả thật, hiển thị trực tiếp
                            } else {
                                el.text(prize);
                            }
                        }
                    }

                    // G5 - 1 giải
                    if (provinceData?.G5?. [0]) {
                        const el = $(`#G5-${suffix}`);
                        const prize = provinceData.G5[0];

                        if (prize.includes('*')) {
                            el.html(`<span class="loader"></span>`);

                            // Nếu chứa ký tự +, hiển thị số quay
                        } else if (prize.includes('+')) {
                            let spinning = '';

                            for (let i = 0; i < prize.length; i++) {
                                if (prize[i] === '+') {
                                    spinning += `<span class="spinning-digit">0</span>`;
                                } else {
                                    spinning += `<span>${prize[i]}</span>`;
                                }
                            }

                            el.html(spinning);
                            simulateSpin('.spinning-digit')
                        } else {
                            el.text(prize);
                        }
                    }

                    // G4 - 7 giải
                    if (provinceData?.G4) {
                        for (let i = 0; i < 7; i++) {
                            const el = $(`#G4-${i + 1}-${suffix}`);
                            const prize = provinceData.G4[i];
                            if (prize.includes('*')) {
                                el.html(`<span class="loader"></span>`);

                                // Nếu chứa ký tự +, hiển thị số quay
                            } else if (prize.includes('+')) {
                                let spinning = '';

                                for (let i = 0; i < prize.length; i++) {
                                    if (prize[i] === '+') {
                                        spinning += `<span class="spinning-digit">0</span>`;
                                    } else {
                                        spinning += `<span>${prize[i]}</span>`;
                                    }
                                }

                                el.html(spinning);
                                simulateSpin('.spinning-digit')
                                // Nếu là kết quả thật, hiển thị trực tiếp
                            } else {
                                el.text(prize);
                            }
                        }
                    }

                    // G3 - 2 giải
                    if (provinceData?.G3) {
                        for (let i = 0; i < 2; i++) {
                            const el = $(`#G3-${i + 1}-${suffix}`);
                            const prize = provinceData.G3[i];
                            if (prize.includes('*')) {
                                el.html(`<span class="loader"></span>`);

                                // Nếu chứa ký tự +, hiển thị số quay
                            } else if (prize.includes('+')) {
                                let spinning = '';

                                for (let i = 0; i < prize.length; i++) {
                                    if (prize[i] === '+') {
                                        spinning += `<span class="spinning-digit">0</span>`;
                                    } else {
                                        spinning += `<span>${prize[i]}</span>`;
                                    }
                                }

                                el.html(spinning);
                                simulateSpin('.spinning-digit')
                                // Nếu là kết quả thật, hiển thị trực tiếp
                            } else {
                                el.text(prize);
                            }
                        }
                    }

                    // G2
                    if (provinceData?.G2?. [0]) {
                        const el = $(`#G2-${suffix}`);
                        const prize = provinceData.G2[0];

                        if (prize.includes('*')) {
                            el.html(`<span class="loader"></span>`);

                            // Nếu chứa ký tự +, hiển thị số quay
                        } else if (prize.includes('+')) {
                            let spinning = '';

                            for (let i = 0; i < prize.length; i++) {
                                if (prize[i] === '+') {
                                    spinning += `<span class="spinning-digit">0</span>`;
                                } else {
                                    spinning += `<span>${prize[i]}</span>`;
                                }
                            }

                            el.html(spinning);
                            simulateSpin('.spinning-digit')
                        } else {
                            el.text(prize);
                        }
                    }

                    // G1
                    if (provinceData?.G1?. [0]) {
                        const el = $(`#G1-${suffix}`);
                        const prize = provinceData.G1[0];

                        if (prize.includes('*')) {
                            el.html(`<span class="loader"></span>`);

                            // Nếu chứa ký tự +, hiển thị số quay
                        } else if (prize.includes('+')) {
                            let spinning = '';

                            for (let i = 0; i < prize.length; i++) {
                                if (prize[i] === '+') {
                                    spinning += `<span class="spinning-digit">0</span>`;
                                } else {
                                    spinning += `<span>${prize[i]}</span>`;
                                }
                            }

                            el.html(spinning);
                            simulateSpin('.spinning-digit')
                        } else {
                            el.text(prize);
                        }
                    }

                    // G0 - giải đặc biệt
                    if (provinceData?.G0?. [0]) {
                        const el = $(`#G0-${suffix}`);
                        const prize = provinceData.G0[0];

                        if (prize.includes('*')) {
                            el.html(`<span class="loader"></span>`);

                            // Nếu chứa ký tự +, hiển thị số quay
                        } else if (prize.includes('+')) {
                            let spinning = '';

                            for (let i = 0; i < prize.length; i++) {
                                if (prize[i] === '+') {
                                    spinning += `<span class="spinning-digit">0</span>`;
                                } else {
                                    spinning += `<span>${prize[i]}</span>`;
                                }
                            }

                            el.html(spinning);
                        } else {
                            el.text(prize);
                        }
                    }

                });
            });
        }

        // function textToVoice(file) {
        //     const audio = new Audio('/api/play-voice?file=' + encodeURIComponent(file));
        //     audio.play().catch(function (error) {
        //         console.error("Không thể phát âm thanh:", error);
        //     });
        // }


        function isAfter5_30PM() {
            const now = new Date();
            return now.getHours() > 17 || (now.getHours() === 17 && now.getMinutes() >= 30);
        }

        function startLiveUpdate() {
            const now = new Date();

            const target = new Date();
            target.setHours(16, 0, 0, 0); // 16h10

            if (now >= target) {
                fetchAndRenderData();
                liveInterval = setInterval(fetchAndRenderData, 3000);
            } else {
                const msUntilTarget = target - now;

                setTimeout(() => {
                    fetchAndRenderData();
                    liveInterval = setInterval(fetchAndRenderData, 3000);
                }, msUntilTarget);
            }
        }

        // Gọi lần đầu
        fetchAndRenderData();

        // liveInterval = setInterval(fetchAndRenderData, 5000);
        // Bắt đầu theo lịch
        startLiveUpdate();
    });
    </script>
</body>

</html>
