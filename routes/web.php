<?php

use App\Http\Controllers\IndexController;
use App\Http\Controllers\InviteController;
use App\Http\Controllers\PreLoginController;
use App\Http\Controllers\TextToSpeechController;
use App\Http\Controllers\TintucController;
use App\Models\Category;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Route;
use Spatie\Sitemap\Sitemap;
use Spatie\Sitemap\Tags\Url;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/
Route::get('/tts', function () {
    return view('tts.form-new');
})->name('tts.form');

Route::get('/tts/play', [TextToSpeechController::class, 'stream'])->name('tts.play');


Route::any('/ckfinder/connector', '\CKSource\CKFinderBridge\Controller\CKFinderController@requestAction')
    ->name('ckfinder_connector');

Route::any('/ckfinder/browser', '\CKSource\CKFinderBridge\Controller\CKFinderController@browserAction')
    ->name('ckfinder_browser');

//Route::any('/ckfinder/examples/{example?}', '\CKSource\CKFinderBridge\Controller\CKFinderController@examplesAction')
//    ->name('ckfinder_examples');

//Route::get('/dang-ky', [IndexController::class, 'register'])->name('index.register');
//Route::post('/post-dang-ky', [IndexController::class, 'postRegister'])->name('index.post-register');
Route::get('/cabal-launcher', [\App\Http\Controllers\LauncherController::class, 'index'])->name('launcher');

Route::group(['middleware' => ['check-ip']], function () {

    Route::get('/bbbb', function() {
        return 'Server đã chạy';
    });
    Route::get('/', [IndexController::class, 'index'])->name('index');
    Route::get('/xsmt', [IndexController::class, 'listXSMT'])->name('xsmt');
    Route::get('/xsmt-backup', [IndexController::class, 'listXSMTBACKUP'])->name('xsmt.backup');
    Route::get('/xsmn-backup', [IndexController::class, 'getMN'])->name('xsmn.backup');

        // API kết quả
    Route::get('/api/kqxs', [IndexController::class, 'getLiveResults'])->name('api.kqxs');
    Route::get('/api/kqxs/mien-trung', [IndexController::class, 'getLiveResultMienTrung'])->name('api.kqxs.mt');
    Route::get('/api/kqxs/mien-trung-backup', [IndexController::class, 'getLiveResultMienTrungBackup'])->name('api.kqxs.mt.backup');
    Route::get('/api/kqxs-mn', [IndexController::class, 'getLiveResultMn']);
        // API lịch quay thưởng
    Route::get('/api/dai-hom-nay-mien-nam', [IndexController::class, 'lichQuayThuongMN'])->name('api.lich.mn');
    Route::get('/api/dai-hom-nay-mien-trung', [IndexController::class, 'lichQuayThuongMT'])->name('api.lich.mt');

        // API phát giọng nói
    Route::get('/api/play-voice', [IndexController::class, 'playVoice'])->name('api.voice');

    Route::get('time', function () {
        $date = DateTime::createFromFormat('Y-m-d H:i:s.u', date('Y') . '-06-27 00:00:00.000');
        $formatted = $date->format('YmdHisv');
        return $formatted;
    });

    Route::get('/server-time', function () {
        return response()->json([
            'time' => now()->format('H:i:s'), // giờ theo máy chủ
        ]);
    });

    Route::get('/test-voice', [IndexController::class, 'testVoice'])->name('test.voice');

    Route::get('api/get-list-voices', [TextToSpeechController::class, 'getVoices'])->name('api.get.voices');

    // api xổ số miền nam
    Route::get('/api/text-to-voice', [IndexController::class, 'stream']);


    Route::get('/register', [IndexController::class, 'register'])->name('register');

    Route::get('/pre-login', [PreLoginController::class, 'index'])->name('pre-login');
    Route::post('/pre-login/diem-danh', [PreLoginController::class, 'diem_danh'])->name('pre-login.diem-danh');

//    Route::get('/invite', [InviteController::class, 'index'])->name('invite');
//    Route::get('/invite', [InviteController::class, 'index'])->name('invite');

    Route::get('/member/login', [\App\Http\Controllers\MemberLoginController::class, 'login'])->name('member.login');
    Route::get('/member/logout', [\App\Http\Controllers\MemberLoginController::class, 'logout'])->name('member.logout');

    Route::get('/register/login', [IndexController::class, 'login'])->name('register.login');
    Route::get('/register/logout', [IndexController::class, 'logout'])->name('register.logout');

    Route::get('/test', function () {
        $scoin = \App\Models\ScoinUser::get();
        print $scoin;
    })->name('test');

    Route::get('/tin-tuc', [TintucController::class, 'tin_tuc'])->name('tin_tuc');
    Route::get('/tin-tuc/{post}.html', [TintucController::class, 'tin_tuc_detail'])->name('tin_tuc_detail');

    Route::get('/su-kien', [TintucController::class, 'su_kien'])->name('su_kien');
    Route::get('/su-kien/{post}.html', [TintucController::class, 'su_kien_detail'])->name('su_kien_detail');

    Route::get('/cap-nhat', [TintucController::class, 'cap_nhat'])->name('cap_nhat');
    Route::get('/cap-nhat/{post}.html', [TintucController::class, 'cap_nhat_detail'])->name('cap_nhat_detail');

    Route::get('/bao-tri', [TintucController::class, 'bao_tri'])->name('bao_tri');
    Route::get('/bao-tri/{post}.html', [TintucController::class, 'bao_tri_detail'])->name('bao_tri_detail');

    Route::get('/huong-dan-tai-game', [\App\Http\Controllers\HuongDanController::class, 'huong_dan_tai_game'])->name('huong_dan_tai_game');
    Route::get('/huong-dan-co-ban', [\App\Http\Controllers\HuongDanController::class, 'huong_dan_co_ban'])->name('huong_dan_co_ban');
    Route::get('/huong-dan/{post}.html', [\App\Http\Controllers\HuongDanController::class, 'huong_dan_detail'])->name('huong_dan_detail');

    Route::get('/gioi-thieu', [\App\Http\Controllers\IndexController::class, 'gioi_thieu'])->name('gioi_thieu');
    Route::get('/landing', [\App\Http\Controllers\IndexController::class, 'tai_game'])->name('landing');
    Route::get('index/tin-tuc', [\App\Http\Controllers\IndexController::class, 'indexTinTuc'])->middleware('api')->name('index-tin-tuc');
    Route::get('index/su-kien', [\App\Http\Controllers\IndexController::class, 'indexSuKien'])->middleware('api')->name('index-su-kien');
    Route::get('index/bao-tri', [\App\Http\Controllers\IndexController::class, 'indexBaoTri'])->middleware('api')->name('index-bao-tri');

    Route::get('update-category', function () {
        Category::truncate();
        \App\Models\Category::insert
        ([
            [
                'name' => 'Tin tức',
                'created_at' => \Carbon\Carbon::now()->format('Y-m-d'),
                'updated_at' => \Carbon\Carbon::now()->format('Y-m-d'),
            ],
            [
                'name' => 'Sụ kiện',
                'created_at' => \Carbon\Carbon::now()->format('Y-m-d'),
                'updated_at' => \Carbon\Carbon::now()->format('Y-m-d'),
            ],
            [
                'name' => 'Cập nhật',
                'created_at' => \Carbon\Carbon::now()->format('Y-m-d'),
                'updated_at' => \Carbon\Carbon::now()->format('Y-m-d'),
            ],
            [
                'name' => 'Bảo trì',
                'created_at' => \Carbon\Carbon::now()->format('Y-m-d'),
                'updated_at' => \Carbon\Carbon::now()->format('Y-m-d'),
            ],
            [
                'name' => 'Hướng dẫn tải game',
                'created_at' => \Carbon\Carbon::now()->format('Y-m-d'),
                'updated_at' => \Carbon\Carbon::now()->format('Y-m-d'),
            ],
            [
                'name' => 'Hướng dẫn cơ bản',
                'created_at' => \Carbon\Carbon::now()->format('Y-m-d'),
                'updated_at' => \Carbon\Carbon::now()->format('Y-m-d'),
            ],
        ]);

        return 0;
    });

});

//Route::get('/download', [IndexController::class, 'download'])->name('dowhiepnload');

//Route::get('/backup', function () {
//    \App\Models\User::create
//    ([
//        'email' => '<EMAIL>',
//        'name' => 'Test Administrator',
//        'username' => 'vinhadmin',
//        'password' => \Illuminate\Support\Facades\Hash::make('12345678'),
//        'group' => GROUP_ADMIN,
//        'active' => ACTIVE_TRUE,
//        'created_at' => \Carbon\Carbon::now()->format('Y-m-d'),
//        'updated_at' => \Carbon\Carbon::now()->format('Y-m-d'),
//    ]);
//    return 'ok';
//});

Route::group(['prefix' => 'auth', 'namespace' => 'App\\Http\\Controllers\\Admin'], function () {
    Route::get('login', function () {
        return view('auth.login');
    })->name('auth.getLogin');
    Route::post('login', 'LoginController@login')->name('auth.postLogin');
    Route::get('logout', 'LoginController@logout')->name('auth.postLogout');

    Route::get('forgot-password', function () {
        return view('auth.forgot-password');
    })->name('auth.getForgotPassword');
    Route::post('forgot-password', 'ForgotPasswordController@resetPassword')->name('auth.sendMail');

    Route::get('reset-password/{token}', 'ForgotPasswordController@getFormResetPassword')->name('auth.getRecoverPassword');
    Route::post('reset-password/{token}', 'ForgotPasswordController@resetPasswordChange')->name('auth.postRecoverPassword');
});
Route::group(['namespace' => 'App\\Http\\Controllers\\Admin', 'middleware' => ['auth', 'check-ip'], 'prefix' => '/admin'], function () {
    Route::get('/', 'AdminController@index')->name('admin.dashboard');
    Route::get('/log-pre-login', 'PreloginController@index')->name('admin.pre-login.list');

    Route::group(['namespace' => 'User', 'prefix' => 'users'], function () {
        Route::get('/', 'UserController@index')->name('admin.user.list');
        Route::get('/detail', 'UserController@detail')->name('admin.user.detail');
        Route::get('/form', 'UserController@getForm')->middleware('check-admin-permission')->name('admin.user.form.get');
        Route::post('/form', 'UserController@saveForm')->middleware('check-admin-permission')->name('admin.user.form.post');
        Route::get('/edit/{id}', 'UserController@editForm')->name('admin.user.form.edit');
        Route::post('/update/{id}', 'UserController@updateForm')->name('admin.user.form.update');
        Route::get('/delete/{id}', 'UserController@delete')->middleware('check-admin-permission')->name('admin.user.delete');
    });

    Route::group(['prefix' => 'categories'], function () {
        Route::get('/', 'CategoryController@index')->name('admin.category.list');
        Route::get('/form', 'CategoryController@getForm')->name('admin.category.form.get');
        Route::post('/form', 'CategoryController@saveForm')->name('admin.category.form.post');
        Route::get('/edit/{id}', 'CategoryController@editForm')->name('admin.category.form.edit');
        Route::post('/update/{id}', 'CategoryController@updateForm')->name('admin.category.form.update');
        Route::get('/delete/{id}', 'CategoryController@delete')->name('admin.category.delete');
    });

    Route::group(['prefix' => 'posts'], function () {
        Route::get('/', 'PostController@index')->name('admin.post.list');
        Route::get('/form', 'PostController@getForm')->name('admin.post.form.get');
        Route::post('/form', 'PostController@saveForm')->name('admin.post.form.post');
        Route::get('/edit/{id}', 'PostController@editForm')->name('admin.post.form.edit');
        Route::post('/update/{id}', 'PostController@updateForm')->name('admin.post.form.update');
        Route::get('/delete/{id}', 'PostController@delete')->name('admin.post.delete');
    });

    Route::group(['prefix' => 'keywords'], function () {
        Route::get('/', 'KeywordController@index')->name('admin.keywords.list');
        Route::get('/form', 'KeywordController@getForm')->name('admin.keywords.form.get');
        Route::post('/form', 'KeywordController@saveForm')->name('admin.keywords.form.post');
        Route::get('/edit/{id}', 'KeywordController@editForm')->name('admin.keywords.form.edit');
        Route::post('/update/{id}', 'KeywordController@updateForm')->name('admin.keywords.form.update');
        Route::get('/delete/{id}', 'KeywordController@delete')->name('admin.keywords.delete');
    });
});

Route::get('/sitemap.xml', function () {
    $sitemap = Sitemap::create();
    // Thêm trang tĩnh
    $sitemap->add(Url::create('/')->setPriority(1.0)->setChangeFrequency('daily'));
    $sitemap->add(Url::create('/huong-dan')->setPriority(1.0)->setChangeFrequency('daily'));
    $sitemap->add(Url::create('/cam-nang')->setPriority(1.0)->setChangeFrequency('daily'));
    $sitemap->add(Url::create('/tin-tuc')->setPriority(1.0)->setChangeFrequency('daily'));
    $sitemap->add(Url::create('/landing')->setPriority(1.0)->setChangeFrequency('daily'));

    // Thêm bài viết từ database
    $news = \App\Models\Post::where('category_id', TINTUC)->get(); // Lấy tất cả bài viết từ database
    foreach ($news as $post) {
        $sitemap->add(
            Url::create(\route('tin_tuc_detail', $post->title_domain))
                ->setLastModificationDate($post->updated_at)
                ->setChangeFrequency('weekly')
                ->setPriority(0.9)
        );
    }

    $events = \App\Models\Post::where('category_id', SUKIEN)->get(); // Lấy tất cả bài viết từ database
    foreach ($events as $post) {
        $sitemap->add(
            Url::create(\route('su_kien_detail', $post->title_domain))
                ->setLastModificationDate($post->updated_at)
                ->setChangeFrequency('weekly')
                ->setPriority(0.9)
        );
    }

    $camnang = \App\Models\Post::with('category')->whereHas('category', function ($q) {
        $q->where('parent_id', CAMNANG);
    })->get(); // Lấy tất cả bài viết từ database
    foreach ($camnang as $post) {
        $sitemap->add(
            Url::create(\route('cam_nang_detail', $post->title_domain))
                ->setLastModificationDate($post->updated_at)
                ->setChangeFrequency('weekly')
                ->setPriority(0.9)
        );
    }

    $huongdan = \App\Models\Post::with('category')->whereHas('category', function ($q) {
        $q->where('parent_id', HUONGDAN);
    })->get(); // Lấy tất cả bài viết từ database
    foreach ($huongdan as $post) {
        $sitemap->add(
            Url::create(\route('huong_dan_detail', $post->title_domain))
                ->setLastModificationDate($post->updated_at)
                ->setChangeFrequency('weekly')
                ->setPriority(0.9)
        );
    }
    return $sitemap->toResponse(request());
});

Route::get('/test-xoso', [IndexController::class, 'fetchKQXS']);
