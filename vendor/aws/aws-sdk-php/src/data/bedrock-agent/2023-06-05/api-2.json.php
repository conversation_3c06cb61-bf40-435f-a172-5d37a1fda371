<?php
// This file was auto-generated from sdk-root/src/data/bedrock-agent/2023-06-05/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2023-06-05', 'endpointPrefix' => 'bedrock-agent', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'Agents for Amazon Bedrock', 'serviceId' => 'Bedrock Agent', 'signatureVersion' => 'v4', 'signingName' => 'bedrock', 'uid' => 'bedrock-agent-2023-06-05', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'AssociateAgentCollaborator' => [ 'name' => 'AssociateAgentCollaborator', 'http' => [ 'method' => 'PUT', 'requestUri' => '/agents/{agentId}/agentversions/{agentVersion}/agentcollaborators/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssociateAgentCollaboratorRequest', ], 'output' => [ 'shape' => 'AssociateAgentCollaboratorResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'AssociateAgentKnowledgeBase' => [ 'name' => 'AssociateAgentKnowledgeBase', 'http' => [ 'method' => 'PUT', 'requestUri' => '/agents/{agentId}/agentversions/{agentVersion}/knowledgebases/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssociateAgentKnowledgeBaseRequest', ], 'output' => [ 'shape' => 'AssociateAgentKnowledgeBaseResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreateAgent' => [ 'name' => 'CreateAgent', 'http' => [ 'method' => 'PUT', 'requestUri' => '/agents/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'CreateAgentRequest', ], 'output' => [ 'shape' => 'CreateAgentResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreateAgentActionGroup' => [ 'name' => 'CreateAgentActionGroup', 'http' => [ 'method' => 'PUT', 'requestUri' => '/agents/{agentId}/agentversions/{agentVersion}/actiongroups/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateAgentActionGroupRequest', ], 'output' => [ 'shape' => 'CreateAgentActionGroupResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreateAgentAlias' => [ 'name' => 'CreateAgentAlias', 'http' => [ 'method' => 'PUT', 'requestUri' => '/agents/{agentId}/agentaliases/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'CreateAgentAliasRequest', ], 'output' => [ 'shape' => 'CreateAgentAliasResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreateDataSource' => [ 'name' => 'CreateDataSource', 'http' => [ 'method' => 'PUT', 'requestUri' => '/knowledgebases/{knowledgeBaseId}/datasources/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateDataSourceRequest', ], 'output' => [ 'shape' => 'CreateDataSourceResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreateFlow' => [ 'name' => 'CreateFlow', 'http' => [ 'method' => 'POST', 'requestUri' => '/flows/', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateFlowRequest', ], 'output' => [ 'shape' => 'CreateFlowResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreateFlowAlias' => [ 'name' => 'CreateFlowAlias', 'http' => [ 'method' => 'POST', 'requestUri' => '/flows/{flowIdentifier}/aliases', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateFlowAliasRequest', ], 'output' => [ 'shape' => 'CreateFlowAliasResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreateFlowVersion' => [ 'name' => 'CreateFlowVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/flows/{flowIdentifier}/versions', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateFlowVersionRequest', ], 'output' => [ 'shape' => 'CreateFlowVersionResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreateKnowledgeBase' => [ 'name' => 'CreateKnowledgeBase', 'http' => [ 'method' => 'PUT', 'requestUri' => '/knowledgebases/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'CreateKnowledgeBaseRequest', ], 'output' => [ 'shape' => 'CreateKnowledgeBaseResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreatePrompt' => [ 'name' => 'CreatePrompt', 'http' => [ 'method' => 'POST', 'requestUri' => '/prompts/', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreatePromptRequest', ], 'output' => [ 'shape' => 'CreatePromptResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreatePromptVersion' => [ 'name' => 'CreatePromptVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/prompts/{promptIdentifier}/versions', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreatePromptVersionRequest', ], 'output' => [ 'shape' => 'CreatePromptVersionResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'DeleteAgent' => [ 'name' => 'DeleteAgent', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/agents/{agentId}/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteAgentRequest', ], 'output' => [ 'shape' => 'DeleteAgentResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DeleteAgentActionGroup' => [ 'name' => 'DeleteAgentActionGroup', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/agents/{agentId}/agentversions/{agentVersion}/actiongroups/{actionGroupId}/', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteAgentActionGroupRequest', ], 'output' => [ 'shape' => 'DeleteAgentActionGroupResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DeleteAgentAlias' => [ 'name' => 'DeleteAgentAlias', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/agents/{agentId}/agentaliases/{agentAliasId}/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteAgentAliasRequest', ], 'output' => [ 'shape' => 'DeleteAgentAliasResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DeleteAgentVersion' => [ 'name' => 'DeleteAgentVersion', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/agents/{agentId}/agentversions/{agentVersion}/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteAgentVersionRequest', ], 'output' => [ 'shape' => 'DeleteAgentVersionResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DeleteDataSource' => [ 'name' => 'DeleteDataSource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/knowledgebases/{knowledgeBaseId}/datasources/{dataSourceId}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteDataSourceRequest', ], 'output' => [ 'shape' => 'DeleteDataSourceResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DeleteFlow' => [ 'name' => 'DeleteFlow', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/flows/{flowIdentifier}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteFlowRequest', ], 'output' => [ 'shape' => 'DeleteFlowResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DeleteFlowAlias' => [ 'name' => 'DeleteFlowAlias', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/flows/{flowIdentifier}/aliases/{aliasIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteFlowAliasRequest', ], 'output' => [ 'shape' => 'DeleteFlowAliasResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DeleteFlowVersion' => [ 'name' => 'DeleteFlowVersion', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/flows/{flowIdentifier}/versions/{flowVersion}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteFlowVersionRequest', ], 'output' => [ 'shape' => 'DeleteFlowVersionResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DeleteKnowledgeBase' => [ 'name' => 'DeleteKnowledgeBase', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/knowledgebases/{knowledgeBaseId}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteKnowledgeBaseRequest', ], 'output' => [ 'shape' => 'DeleteKnowledgeBaseResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DeleteKnowledgeBaseDocuments' => [ 'name' => 'DeleteKnowledgeBaseDocuments', 'http' => [ 'method' => 'POST', 'requestUri' => '/knowledgebases/{knowledgeBaseId}/datasources/{dataSourceId}/documents/deleteDocuments', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteKnowledgeBaseDocumentsRequest', ], 'output' => [ 'shape' => 'DeleteKnowledgeBaseDocumentsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'DeletePrompt' => [ 'name' => 'DeletePrompt', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/prompts/{promptIdentifier}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeletePromptRequest', ], 'output' => [ 'shape' => 'DeletePromptResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DisassociateAgentCollaborator' => [ 'name' => 'DisassociateAgentCollaborator', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/agents/{agentId}/agentversions/{agentVersion}/agentcollaborators/{collaboratorId}/', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DisassociateAgentCollaboratorRequest', ], 'output' => [ 'shape' => 'DisassociateAgentCollaboratorResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DisassociateAgentKnowledgeBase' => [ 'name' => 'DisassociateAgentKnowledgeBase', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/agents/{agentId}/agentversions/{agentVersion}/knowledgebases/{knowledgeBaseId}/', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DisassociateAgentKnowledgeBaseRequest', ], 'output' => [ 'shape' => 'DisassociateAgentKnowledgeBaseResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'GetAgent' => [ 'name' => 'GetAgent', 'http' => [ 'method' => 'GET', 'requestUri' => '/agents/{agentId}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAgentRequest', ], 'output' => [ 'shape' => 'GetAgentResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetAgentActionGroup' => [ 'name' => 'GetAgentActionGroup', 'http' => [ 'method' => 'GET', 'requestUri' => '/agents/{agentId}/agentversions/{agentVersion}/actiongroups/{actionGroupId}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAgentActionGroupRequest', ], 'output' => [ 'shape' => 'GetAgentActionGroupResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetAgentAlias' => [ 'name' => 'GetAgentAlias', 'http' => [ 'method' => 'GET', 'requestUri' => '/agents/{agentId}/agentaliases/{agentAliasId}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAgentAliasRequest', ], 'output' => [ 'shape' => 'GetAgentAliasResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetAgentCollaborator' => [ 'name' => 'GetAgentCollaborator', 'http' => [ 'method' => 'GET', 'requestUri' => '/agents/{agentId}/agentversions/{agentVersion}/agentcollaborators/{collaboratorId}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAgentCollaboratorRequest', ], 'output' => [ 'shape' => 'GetAgentCollaboratorResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetAgentKnowledgeBase' => [ 'name' => 'GetAgentKnowledgeBase', 'http' => [ 'method' => 'GET', 'requestUri' => '/agents/{agentId}/agentversions/{agentVersion}/knowledgebases/{knowledgeBaseId}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAgentKnowledgeBaseRequest', ], 'output' => [ 'shape' => 'GetAgentKnowledgeBaseResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetAgentVersion' => [ 'name' => 'GetAgentVersion', 'http' => [ 'method' => 'GET', 'requestUri' => '/agents/{agentId}/agentversions/{agentVersion}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAgentVersionRequest', ], 'output' => [ 'shape' => 'GetAgentVersionResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetDataSource' => [ 'name' => 'GetDataSource', 'http' => [ 'method' => 'GET', 'requestUri' => '/knowledgebases/{knowledgeBaseId}/datasources/{dataSourceId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetDataSourceRequest', ], 'output' => [ 'shape' => 'GetDataSourceResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetFlow' => [ 'name' => 'GetFlow', 'http' => [ 'method' => 'GET', 'requestUri' => '/flows/{flowIdentifier}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetFlowRequest', ], 'output' => [ 'shape' => 'GetFlowResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetFlowAlias' => [ 'name' => 'GetFlowAlias', 'http' => [ 'method' => 'GET', 'requestUri' => '/flows/{flowIdentifier}/aliases/{aliasIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetFlowAliasRequest', ], 'output' => [ 'shape' => 'GetFlowAliasResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetFlowVersion' => [ 'name' => 'GetFlowVersion', 'http' => [ 'method' => 'GET', 'requestUri' => '/flows/{flowIdentifier}/versions/{flowVersion}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetFlowVersionRequest', ], 'output' => [ 'shape' => 'GetFlowVersionResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetIngestionJob' => [ 'name' => 'GetIngestionJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/knowledgebases/{knowledgeBaseId}/datasources/{dataSourceId}/ingestionjobs/{ingestionJobId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetIngestionJobRequest', ], 'output' => [ 'shape' => 'GetIngestionJobResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetKnowledgeBase' => [ 'name' => 'GetKnowledgeBase', 'http' => [ 'method' => 'GET', 'requestUri' => '/knowledgebases/{knowledgeBaseId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetKnowledgeBaseRequest', ], 'output' => [ 'shape' => 'GetKnowledgeBaseResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetKnowledgeBaseDocuments' => [ 'name' => 'GetKnowledgeBaseDocuments', 'http' => [ 'method' => 'POST', 'requestUri' => '/knowledgebases/{knowledgeBaseId}/datasources/{dataSourceId}/documents/getDocuments', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetKnowledgeBaseDocumentsRequest', ], 'output' => [ 'shape' => 'GetKnowledgeBaseDocumentsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'GetPrompt' => [ 'name' => 'GetPrompt', 'http' => [ 'method' => 'GET', 'requestUri' => '/prompts/{promptIdentifier}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetPromptRequest', ], 'output' => [ 'shape' => 'GetPromptResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'IngestKnowledgeBaseDocuments' => [ 'name' => 'IngestKnowledgeBaseDocuments', 'http' => [ 'method' => 'PUT', 'requestUri' => '/knowledgebases/{knowledgeBaseId}/datasources/{dataSourceId}/documents', 'responseCode' => 202, ], 'input' => [ 'shape' => 'IngestKnowledgeBaseDocumentsRequest', ], 'output' => [ 'shape' => 'IngestKnowledgeBaseDocumentsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'ListAgentActionGroups' => [ 'name' => 'ListAgentActionGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/agents/{agentId}/agentversions/{agentVersion}/actiongroups/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAgentActionGroupsRequest', ], 'output' => [ 'shape' => 'ListAgentActionGroupsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListAgentAliases' => [ 'name' => 'ListAgentAliases', 'http' => [ 'method' => 'POST', 'requestUri' => '/agents/{agentId}/agentaliases/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAgentAliasesRequest', ], 'output' => [ 'shape' => 'ListAgentAliasesResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListAgentCollaborators' => [ 'name' => 'ListAgentCollaborators', 'http' => [ 'method' => 'POST', 'requestUri' => '/agents/{agentId}/agentversions/{agentVersion}/agentcollaborators/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAgentCollaboratorsRequest', ], 'output' => [ 'shape' => 'ListAgentCollaboratorsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListAgentKnowledgeBases' => [ 'name' => 'ListAgentKnowledgeBases', 'http' => [ 'method' => 'POST', 'requestUri' => '/agents/{agentId}/agentversions/{agentVersion}/knowledgebases/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAgentKnowledgeBasesRequest', ], 'output' => [ 'shape' => 'ListAgentKnowledgeBasesResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListAgentVersions' => [ 'name' => 'ListAgentVersions', 'http' => [ 'method' => 'POST', 'requestUri' => '/agents/{agentId}/agentversions/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAgentVersionsRequest', ], 'output' => [ 'shape' => 'ListAgentVersionsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListAgents' => [ 'name' => 'ListAgents', 'http' => [ 'method' => 'POST', 'requestUri' => '/agents/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAgentsRequest', ], 'output' => [ 'shape' => 'ListAgentsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListDataSources' => [ 'name' => 'ListDataSources', 'http' => [ 'method' => 'POST', 'requestUri' => '/knowledgebases/{knowledgeBaseId}/datasources/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDataSourcesRequest', ], 'output' => [ 'shape' => 'ListDataSourcesResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListFlowAliases' => [ 'name' => 'ListFlowAliases', 'http' => [ 'method' => 'GET', 'requestUri' => '/flows/{flowIdentifier}/aliases', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListFlowAliasesRequest', ], 'output' => [ 'shape' => 'ListFlowAliasesResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListFlowVersions' => [ 'name' => 'ListFlowVersions', 'http' => [ 'method' => 'GET', 'requestUri' => '/flows/{flowIdentifier}/versions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListFlowVersionsRequest', ], 'output' => [ 'shape' => 'ListFlowVersionsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListFlows' => [ 'name' => 'ListFlows', 'http' => [ 'method' => 'GET', 'requestUri' => '/flows/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListFlowsRequest', ], 'output' => [ 'shape' => 'ListFlowsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListIngestionJobs' => [ 'name' => 'ListIngestionJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/knowledgebases/{knowledgeBaseId}/datasources/{dataSourceId}/ingestionjobs/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListIngestionJobsRequest', ], 'output' => [ 'shape' => 'ListIngestionJobsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListKnowledgeBaseDocuments' => [ 'name' => 'ListKnowledgeBaseDocuments', 'http' => [ 'method' => 'POST', 'requestUri' => '/knowledgebases/{knowledgeBaseId}/datasources/{dataSourceId}/documents', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListKnowledgeBaseDocumentsRequest', ], 'output' => [ 'shape' => 'ListKnowledgeBaseDocumentsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'ListKnowledgeBases' => [ 'name' => 'ListKnowledgeBases', 'http' => [ 'method' => 'POST', 'requestUri' => '/knowledgebases/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListKnowledgeBasesRequest', ], 'output' => [ 'shape' => 'ListKnowledgeBasesResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListPrompts' => [ 'name' => 'ListPrompts', 'http' => [ 'method' => 'GET', 'requestUri' => '/prompts/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListPromptsRequest', ], 'output' => [ 'shape' => 'ListPromptsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'PrepareAgent' => [ 'name' => 'PrepareAgent', 'http' => [ 'method' => 'POST', 'requestUri' => '/agents/{agentId}/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'PrepareAgentRequest', ], 'output' => [ 'shape' => 'PrepareAgentResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'PrepareFlow' => [ 'name' => 'PrepareFlow', 'http' => [ 'method' => 'POST', 'requestUri' => '/flows/{flowIdentifier}/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'PrepareFlowRequest', ], 'output' => [ 'shape' => 'PrepareFlowResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'StartIngestionJob' => [ 'name' => 'StartIngestionJob', 'http' => [ 'method' => 'PUT', 'requestUri' => '/knowledgebases/{knowledgeBaseId}/datasources/{dataSourceId}/ingestionjobs/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'StartIngestionJobRequest', ], 'output' => [ 'shape' => 'StartIngestionJobResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'StopIngestionJob' => [ 'name' => 'StopIngestionJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/knowledgebases/{knowledgeBaseId}/datasources/{dataSourceId}/ingestionjobs/{ingestionJobId}/stop', 'responseCode' => 202, ], 'input' => [ 'shape' => 'StopIngestionJobRequest', ], 'output' => [ 'shape' => 'StopIngestionJobResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UpdateAgent' => [ 'name' => 'UpdateAgent', 'http' => [ 'method' => 'PUT', 'requestUri' => '/agents/{agentId}/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdateAgentRequest', ], 'output' => [ 'shape' => 'UpdateAgentResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'UpdateAgentActionGroup' => [ 'name' => 'UpdateAgentActionGroup', 'http' => [ 'method' => 'PUT', 'requestUri' => '/agents/{agentId}/agentversions/{agentVersion}/actiongroups/{actionGroupId}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateAgentActionGroupRequest', ], 'output' => [ 'shape' => 'UpdateAgentActionGroupResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'UpdateAgentAlias' => [ 'name' => 'UpdateAgentAlias', 'http' => [ 'method' => 'PUT', 'requestUri' => '/agents/{agentId}/agentaliases/{agentAliasId}/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdateAgentAliasRequest', ], 'output' => [ 'shape' => 'UpdateAgentAliasResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'UpdateAgentCollaborator' => [ 'name' => 'UpdateAgentCollaborator', 'http' => [ 'method' => 'PUT', 'requestUri' => '/agents/{agentId}/agentversions/{agentVersion}/agentcollaborators/{collaboratorId}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateAgentCollaboratorRequest', ], 'output' => [ 'shape' => 'UpdateAgentCollaboratorResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'UpdateAgentKnowledgeBase' => [ 'name' => 'UpdateAgentKnowledgeBase', 'http' => [ 'method' => 'PUT', 'requestUri' => '/agents/{agentId}/agentversions/{agentVersion}/knowledgebases/{knowledgeBaseId}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateAgentKnowledgeBaseRequest', ], 'output' => [ 'shape' => 'UpdateAgentKnowledgeBaseResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'UpdateDataSource' => [ 'name' => 'UpdateDataSource', 'http' => [ 'method' => 'PUT', 'requestUri' => '/knowledgebases/{knowledgeBaseId}/datasources/{dataSourceId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateDataSourceRequest', ], 'output' => [ 'shape' => 'UpdateDataSourceResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'UpdateFlow' => [ 'name' => 'UpdateFlow', 'http' => [ 'method' => 'PUT', 'requestUri' => '/flows/{flowIdentifier}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateFlowRequest', ], 'output' => [ 'shape' => 'UpdateFlowResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'UpdateFlowAlias' => [ 'name' => 'UpdateFlowAlias', 'http' => [ 'method' => 'PUT', 'requestUri' => '/flows/{flowIdentifier}/aliases/{aliasIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateFlowAliasRequest', ], 'output' => [ 'shape' => 'UpdateFlowAliasResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'UpdateKnowledgeBase' => [ 'name' => 'UpdateKnowledgeBase', 'http' => [ 'method' => 'PUT', 'requestUri' => '/knowledgebases/{knowledgeBaseId}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdateKnowledgeBaseRequest', ], 'output' => [ 'shape' => 'UpdateKnowledgeBaseResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'UpdatePrompt' => [ 'name' => 'UpdatePrompt', 'http' => [ 'method' => 'PUT', 'requestUri' => '/prompts/{promptIdentifier}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdatePromptRequest', ], 'output' => [ 'shape' => 'UpdatePromptResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'ValidateFlowDefinition' => [ 'name' => 'ValidateFlowDefinition', 'http' => [ 'method' => 'POST', 'requestUri' => '/flows/validate-definition', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ValidateFlowDefinitionRequest', ], 'output' => [ 'shape' => 'ValidateFlowDefinitionResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], ], 'shapes' => [ 'APISchema' => [ 'type' => 'structure', 'members' => [ 'payload' => [ 'shape' => 'Payload', ], 's3' => [ 'shape' => 'S3Identifier', ], ], 'union' => true, ], 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'ActionGroupExecutor' => [ 'type' => 'structure', 'members' => [ 'customControl' => [ 'shape' => 'CustomControlMethod', ], 'lambda' => [ 'shape' => 'LambdaArn', ], ], 'union' => true, ], 'ActionGroupSignature' => [ 'type' => 'string', 'enum' => [ 'AMAZON.UserInput', 'AMAZON.CodeInterpreter', 'ANTHROPIC.Computer', 'ANTHROPIC.Bash', 'ANTHROPIC.TextEditor', ], ], 'ActionGroupSignatureParams' => [ 'type' => 'map', 'key' => [ 'shape' => 'ActionGroupSignatureParamsKeyString', ], 'value' => [ 'shape' => 'ActionGroupSignatureParamsValueString', ], ], 'ActionGroupSignatureParamsKeyString' => [ 'type' => 'string', 'max' => 100, 'min' => 0, ], 'ActionGroupSignatureParamsValueString' => [ 'type' => 'string', 'max' => 100, 'min' => 0, ], 'ActionGroupState' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'ActionGroupSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ActionGroupSummary', ], 'max' => 10, 'min' => 0, ], 'ActionGroupSummary' => [ 'type' => 'structure', 'required' => [ 'actionGroupId', 'actionGroupName', 'actionGroupState', 'updatedAt', ], 'members' => [ 'actionGroupId' => [ 'shape' => 'Id', ], 'actionGroupName' => [ 'shape' => 'Name', ], 'actionGroupState' => [ 'shape' => 'ActionGroupState', ], 'description' => [ 'shape' => 'Description', ], 'updatedAt' => [ 'shape' => 'DateTimestamp', ], ], ], 'AdditionalModelRequestFields' => [ 'type' => 'map', 'key' => [ 'shape' => 'AdditionalModelRequestFieldsKey', ], 'value' => [ 'shape' => 'AdditionalModelRequestFieldsValue', ], ], 'AdditionalModelRequestFieldsKey' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'AdditionalModelRequestFieldsValue' => [ 'type' => 'structure', 'members' => [], 'document' => true, ], 'Agent' => [ 'type' => 'structure', 'required' => [ 'agentArn', 'agentId', 'agentName', 'agentResourceRoleArn', 'agentStatus', 'agentVersion', 'createdAt', 'idleSessionTTLInSeconds', 'updatedAt', ], 'members' => [ 'agentArn' => [ 'shape' => 'AgentArn', ], 'agentCollaboration' => [ 'shape' => 'AgentCollaboration', ], 'agentId' => [ 'shape' => 'Id', ], 'agentName' => [ 'shape' => 'Name', ], 'agentResourceRoleArn' => [ 'shape' => 'AgentRoleArn', ], 'agentStatus' => [ 'shape' => 'AgentStatus', ], 'agentVersion' => [ 'shape' => 'DraftVersion', ], 'clientToken' => [ 'shape' => 'ClientToken', ], 'createdAt' => [ 'shape' => 'DateTimestamp', ], 'customOrchestration' => [ 'shape' => 'CustomOrchestration', ], 'customerEncryptionKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'description' => [ 'shape' => 'Description', ], 'failureReasons' => [ 'shape' => 'FailureReasons', ], 'foundationModel' => [ 'shape' => 'ModelIdentifier', ], 'guardrailConfiguration' => [ 'shape' => 'GuardrailConfiguration', ], 'idleSessionTTLInSeconds' => [ 'shape' => 'SessionTTL', ], 'instruction' => [ 'shape' => 'Instruction', ], 'memoryConfiguration' => [ 'shape' => 'MemoryConfiguration', ], 'orchestrationType' => [ 'shape' => 'OrchestrationType', ], 'preparedAt' => [ 'shape' => 'DateTimestamp', ], 'promptOverrideConfiguration' => [ 'shape' => 'PromptOverrideConfiguration', ], 'recommendedActions' => [ 'shape' => 'RecommendedActions', ], 'updatedAt' => [ 'shape' => 'DateTimestamp', ], ], ], 'AgentActionGroup' => [ 'type' => 'structure', 'required' => [ 'actionGroupId', 'actionGroupName', 'actionGroupState', 'agentId', 'agentVersion', 'createdAt', 'updatedAt', ], 'members' => [ 'actionGroupExecutor' => [ 'shape' => 'ActionGroupExecutor', ], 'actionGroupId' => [ 'shape' => 'Id', ], 'actionGroupName' => [ 'shape' => 'Name', ], 'actionGroupState' => [ 'shape' => 'ActionGroupState', ], 'agentId' => [ 'shape' => 'Id', ], 'agentVersion' => [ 'shape' => 'Version', ], 'apiSchema' => [ 'shape' => 'APISchema', ], 'clientToken' => [ 'shape' => 'ClientToken', ], 'createdAt' => [ 'shape' => 'DateTimestamp', ], 'description' => [ 'shape' => 'Description', ], 'functionSchema' => [ 'shape' => 'FunctionSchema', ], 'parentActionGroupSignatureParams' => [ 'shape' => 'ActionGroupSignatureParams', ], 'parentActionSignature' => [ 'shape' => 'ActionGroupSignature', ], 'updatedAt' => [ 'shape' => 'DateTimestamp', ], ], ], 'AgentAlias' => [ 'type' => 'structure', 'required' => [ 'agentAliasArn', 'agentAliasId', 'agentAliasName', 'agentAliasStatus', 'agentId', 'createdAt', 'routingConfiguration', 'updatedAt', ], 'members' => [ 'agentAliasArn' => [ 'shape' => 'AgentAliasArn', ], 'agentAliasHistoryEvents' => [ 'shape' => 'AgentAliasHistoryEvents', ], 'agentAliasId' => [ 'shape' => 'AgentAliasId', ], 'agentAliasName' => [ 'shape' => 'Name', ], 'agentAliasStatus' => [ 'shape' => 'AgentAliasStatus', ], 'agentId' => [ 'shape' => 'Id', ], 'aliasInvocationState' => [ 'shape' => 'AliasInvocationState', ], 'clientToken' => [ 'shape' => 'ClientToken', ], 'createdAt' => [ 'shape' => 'DateTimestamp', ], 'description' => [ 'shape' => 'Description', ], 'failureReasons' => [ 'shape' => 'FailureReasons', ], 'routingConfiguration' => [ 'shape' => 'AgentAliasRoutingConfiguration', ], 'updatedAt' => [ 'shape' => 'DateTimestamp', ], ], ], 'AgentAliasArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '^arn:aws:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:agent-alias/[0-9a-zA-Z]{10}/[0-9a-zA-Z]{10}$', ], 'AgentAliasHistoryEvent' => [ 'type' => 'structure', 'members' => [ 'endDate' => [ 'shape' => 'DateTimestamp', ], 'routingConfiguration' => [ 'shape' => 'AgentAliasRoutingConfiguration', ], 'startDate' => [ 'shape' => 'DateTimestamp', ], ], ], 'AgentAliasHistoryEvents' => [ 'type' => 'list', 'member' => [ 'shape' => 'AgentAliasHistoryEvent', ], 'max' => 10, 'min' => 0, ], 'AgentAliasId' => [ 'type' => 'string', 'max' => 10, 'min' => 10, 'pattern' => '^(\\bTSTALIASID\\b|[0-9a-zA-Z]+)$', ], 'AgentAliasRoutingConfiguration' => [ 'type' => 'list', 'member' => [ 'shape' => 'AgentAliasRoutingConfigurationListItem', ], 'max' => 1, 'min' => 0, ], 'AgentAliasRoutingConfigurationListItem' => [ 'type' => 'structure', 'members' => [ 'agentVersion' => [ 'shape' => 'Version', ], 'provisionedThroughput' => [ 'shape' => 'ProvisionedModelIdentifier', ], ], ], 'AgentAliasStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'PREPARED', 'FAILED', 'UPDATING', 'DELETING', 'DISSOCIATED', ], ], 'AgentAliasSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'AgentAliasSummary', ], 'max' => 10, 'min' => 0, ], 'AgentAliasSummary' => [ 'type' => 'structure', 'required' => [ 'agentAliasId', 'agentAliasName', 'agentAliasStatus', 'createdAt', 'updatedAt', ], 'members' => [ 'agentAliasId' => [ 'shape' => 'AgentAliasId', ], 'agentAliasName' => [ 'shape' => 'Name', ], 'agentAliasStatus' => [ 'shape' => 'AgentAliasStatus', ], 'aliasInvocationState' => [ 'shape' => 'AliasInvocationState', ], 'createdAt' => [ 'shape' => 'DateTimestamp', ], 'description' => [ 'shape' => 'Description', ], 'routingConfiguration' => [ 'shape' => 'AgentAliasRoutingConfiguration', ], 'updatedAt' => [ 'shape' => 'DateTimestamp', ], ], ], 'AgentArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '^arn:aws:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:agent/[0-9a-zA-Z]{10}$', ], 'AgentCollaboration' => [ 'type' => 'string', 'enum' => [ 'SUPERVISOR', 'SUPERVISOR_ROUTER', 'DISABLED', ], ], 'AgentCollaborator' => [ 'type' => 'structure', 'required' => [ 'agentDescriptor', 'agentId', 'agentVersion', 'collaborationInstruction', 'collaboratorId', 'collaboratorName', 'createdAt', 'lastUpdatedAt', ], 'members' => [ 'agentDescriptor' => [ 'shape' => 'AgentDescriptor', ], 'agentId' => [ 'shape' => 'Id', ], 'agentVersion' => [ 'shape' => 'Version', ], 'clientToken' => [ 'shape' => 'ClientToken', ], 'collaborationInstruction' => [ 'shape' => 'CollaborationInstruction', ], 'collaboratorId' => [ 'shape' => 'Id', ], 'collaboratorName' => [ 'shape' => 'Name', ], 'createdAt' => [ 'shape' => 'DateTimestamp', ], 'lastUpdatedAt' => [ 'shape' => 'DateTimestamp', ], 'relayConversationHistory' => [ 'shape' => 'RelayConversationHistory', ], ], ], 'AgentCollaboratorSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'AgentCollaboratorSummary', ], 'max' => 10, 'min' => 0, ], 'AgentCollaboratorSummary' => [ 'type' => 'structure', 'required' => [ 'agentDescriptor', 'agentId', 'agentVersion', 'collaborationInstruction', 'collaboratorId', 'collaboratorName', 'createdAt', 'lastUpdatedAt', 'relayConversationHistory', ], 'members' => [ 'agentDescriptor' => [ 'shape' => 'AgentDescriptor', ], 'agentId' => [ 'shape' => 'Id', ], 'agentVersion' => [ 'shape' => 'Version', ], 'collaborationInstruction' => [ 'shape' => 'CollaborationInstruction', ], 'collaboratorId' => [ 'shape' => 'Id', ], 'collaboratorName' => [ 'shape' => 'Name', ], 'createdAt' => [ 'shape' => 'DateTimestamp', ], 'lastUpdatedAt' => [ 'shape' => 'DateTimestamp', ], 'relayConversationHistory' => [ 'shape' => 'RelayConversationHistory', ], ], ], 'AgentDescriptor' => [ 'type' => 'structure', 'members' => [ 'aliasArn' => [ 'shape' => 'AgentAliasArn', ], ], ], 'AgentFlowNodeConfiguration' => [ 'type' => 'structure', 'required' => [ 'agentAliasArn', ], 'members' => [ 'agentAliasArn' => [ 'shape' => 'AgentAliasArn', ], ], ], 'AgentKnowledgeBase' => [ 'type' => 'structure', 'required' => [ 'agentId', 'agentVersion', 'createdAt', 'description', 'knowledgeBaseId', 'knowledgeBaseState', 'updatedAt', ], 'members' => [ 'agentId' => [ 'shape' => 'Id', ], 'agentVersion' => [ 'shape' => 'Version', ], 'createdAt' => [ 'shape' => 'DateTimestamp', ], 'description' => [ 'shape' => 'Description', ], 'knowledgeBaseId' => [ 'shape' => 'Id', ], 'knowledgeBaseState' => [ 'shape' => 'KnowledgeBaseState', ], 'updatedAt' => [ 'shape' => 'DateTimestamp', ], ], ], 'AgentKnowledgeBaseSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'AgentKnowledgeBaseSummary', ], 'max' => 10, 'min' => 0, ], 'AgentKnowledgeBaseSummary' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'knowledgeBaseState', 'updatedAt', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'knowledgeBaseId' => [ 'shape' => 'Id', ], 'knowledgeBaseState' => [ 'shape' => 'KnowledgeBaseState', ], 'updatedAt' => [ 'shape' => 'DateTimestamp', ], ], ], 'AgentRoleArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '^arn:aws(-[^:]+)?:iam::([0-9]{12})?:role/.+$', ], 'AgentStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'PREPARING', 'PREPARED', 'NOT_PREPARED', 'DELETING', 'FAILED', 'VERSIONING', 'UPDATING', ], ], 'AgentSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'AgentSummary', ], 'max' => 10, 'min' => 0, ], 'AgentSummary' => [ 'type' => 'structure', 'required' => [ 'agentId', 'agentName', 'agentStatus', 'updatedAt', ], 'members' => [ 'agentId' => [ 'shape' => 'Id', ], 'agentName' => [ 'shape' => 'Name', ], 'agentStatus' => [ 'shape' => 'AgentStatus', ], 'description' => [ 'shape' => 'Description', ], 'guardrailConfiguration' => [ 'shape' => 'GuardrailConfiguration', ], 'latestAgentVersion' => [ 'shape' => 'Version', ], 'updatedAt' => [ 'shape' => 'DateTimestamp', ], ], ], 'AgentVersion' => [ 'type' => 'structure', 'required' => [ 'agentArn', 'agentId', 'agentName', 'agentResourceRoleArn', 'agentStatus', 'createdAt', 'idleSessionTTLInSeconds', 'updatedAt', 'version', ], 'members' => [ 'agentArn' => [ 'shape' => 'AgentArn', ], 'agentCollaboration' => [ 'shape' => 'AgentCollaboration', ], 'agentId' => [ 'shape' => 'Id', ], 'agentName' => [ 'shape' => 'Name', ], 'agentResourceRoleArn' => [ 'shape' => 'AgentRoleArn', ], 'agentStatus' => [ 'shape' => 'AgentStatus', ], 'createdAt' => [ 'shape' => 'DateTimestamp', ], 'customerEncryptionKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'description' => [ 'shape' => 'Description', ], 'failureReasons' => [ 'shape' => 'FailureReasons', ], 'foundationModel' => [ 'shape' => 'ModelIdentifier', ], 'guardrailConfiguration' => [ 'shape' => 'GuardrailConfiguration', ], 'idleSessionTTLInSeconds' => [ 'shape' => 'SessionTTL', ], 'instruction' => [ 'shape' => 'Instruction', ], 'memoryConfiguration' => [ 'shape' => 'MemoryConfiguration', ], 'promptOverrideConfiguration' => [ 'shape' => 'PromptOverrideConfiguration', ], 'recommendedActions' => [ 'shape' => 'RecommendedActions', ], 'updatedAt' => [ 'shape' => 'DateTimestamp', ], 'version' => [ 'shape' => 'NumericalVersion', ], ], ], 'AgentVersionSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'AgentVersionSummary', ], 'max' => 10, 'min' => 0, ], 'AgentVersionSummary' => [ 'type' => 'structure', 'required' => [ 'agentName', 'agentStatus', 'agentVersion', 'createdAt', 'updatedAt', ], 'members' => [ 'agentName' => [ 'shape' => 'Name', ], 'agentStatus' => [ 'shape' => 'AgentStatus', ], 'agentVersion' => [ 'shape' => 'Version', ], 'createdAt' => [ 'shape' => 'DateTimestamp', ], 'description' => [ 'shape' => 'Description', ], 'guardrailConfiguration' => [ 'shape' => 'GuardrailConfiguration', ], 'updatedAt' => [ 'shape' => 'DateTimestamp', ], ], ], 'AliasInvocationState' => [ 'type' => 'string', 'enum' => [ 'ACCEPT_INVOCATIONS', 'REJECT_INVOCATIONS', ], ], 'AnyToolChoice' => [ 'type' => 'structure', 'members' => [], ], 'AssociateAgentCollaboratorRequest' => [ 'type' => 'structure', 'required' => [ 'agentDescriptor', 'agentId', 'agentVersion', 'collaborationInstruction', 'collaboratorName', ], 'members' => [ 'agentDescriptor' => [ 'shape' => 'AgentDescriptor', ], 'agentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'agentId', ], 'agentVersion' => [ 'shape' => 'DraftVersion', 'location' => 'uri', 'locationName' => 'agentVersion', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'collaborationInstruction' => [ 'shape' => 'CollaborationInstruction', ], 'collaboratorName' => [ 'shape' => 'Name', ], 'relayConversationHistory' => [ 'shape' => 'RelayConversationHistory', ], ], ], 'AssociateAgentCollaboratorResponse' => [ 'type' => 'structure', 'required' => [ 'agentCollaborator', ], 'members' => [ 'agentCollaborator' => [ 'shape' => 'AgentCollaborator', ], ], ], 'AssociateAgentKnowledgeBaseRequest' => [ 'type' => 'structure', 'required' => [ 'agentId', 'agentVersion', 'description', 'knowledgeBaseId', ], 'members' => [ 'agentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'agentId', ], 'agentVersion' => [ 'shape' => 'DraftVersion', 'location' => 'uri', 'locationName' => 'agentVersion', ], 'description' => [ 'shape' => 'Description', ], 'knowledgeBaseId' => [ 'shape' => 'Id', ], 'knowledgeBaseState' => [ 'shape' => 'KnowledgeBaseState', ], ], ], 'AssociateAgentKnowledgeBaseResponse' => [ 'type' => 'structure', 'required' => [ 'agentKnowledgeBase', ], 'members' => [ 'agentKnowledgeBase' => [ 'shape' => 'AgentKnowledgeBase', ], ], ], 'AutoToolChoice' => [ 'type' => 'structure', 'members' => [], ], 'AwsDataCatalogTableName' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '^.*\\.*$', ], 'AwsDataCatalogTableNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsDataCatalogTableName', ], 'max' => 1000, 'min' => 1, ], 'BasePromptTemplate' => [ 'type' => 'string', 'max' => 100000, 'min' => 1, 'sensitive' => true, ], 'BedrockDataAutomationConfiguration' => [ 'type' => 'structure', 'members' => [ 'parsingModality' => [ 'shape' => 'ParsingModality', ], ], ], 'BedrockEmbeddingModelArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^(arn:aws(-[^:]{1,12})?:(bedrock|sagemaker):[a-z0-9-]{1,20}:([0-9]{12})?:([a-z-]+/)?)?([a-zA-Z0-9.-]{1,63}){0,2}(([:][a-z0-9-]{1,63}){0,2})?(/[a-z0-9]{1,12})?$', ], 'BedrockEmbeddingModelConfiguration' => [ 'type' => 'structure', 'members' => [ 'dimensions' => [ 'shape' => 'Dimensions', ], 'embeddingDataType' => [ 'shape' => 'EmbeddingDataType', ], ], ], 'BedrockFoundationModelConfiguration' => [ 'type' => 'structure', 'required' => [ 'modelArn', ], 'members' => [ 'modelArn' => [ 'shape' => 'BedrockModelArn', ], 'parsingModality' => [ 'shape' => 'ParsingModality', ], 'parsingPrompt' => [ 'shape' => 'ParsingPrompt', ], ], ], 'BedrockFoundationModelContextEnrichmentConfiguration' => [ 'type' => 'structure', 'required' => [ 'enrichmentStrategyConfiguration', 'modelArn', ], 'members' => [ 'enrichmentStrategyConfiguration' => [ 'shape' => 'EnrichmentStrategyConfiguration', ], 'modelArn' => [ 'shape' => 'BedrockModelArn', ], ], ], 'BedrockModelArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^(arn:aws(-[^:]{1,12})?:(bedrock):[a-z0-9-]{1,20}:([0-9]{12})?:([a-z-]+/)?)?([a-zA-Z0-9.-]{1,63}){0,2}(([:][a-z0-9-]{1,63}){0,2})?(/[a-z0-9]{1,12})?$', ], 'BedrockRerankingModelArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^(arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}::foundation-model/(.*))?$', ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'BucketOwnerAccountId' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => '^[0-9]{12}$', ], 'ByteContentBlob' => [ 'type' => 'blob', 'max' => 5242880, 'min' => 1, 'sensitive' => true, ], 'ByteContentDoc' => [ 'type' => 'structure', 'required' => [ 'data', 'mimeType', ], 'members' => [ 'data' => [ 'shape' => 'ByteContentBlob', ], 'mimeType' => [ 'shape' => 'ByteContentDocMimeTypeString', ], ], ], 'ByteContentDocMimeTypeString' => [ 'type' => 'string', 'pattern' => '[a-z]{1,20}/.{1,20}', ], 'CachePointBlock' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'type' => [ 'shape' => 'CachePointType', ], ], ], 'CachePointType' => [ 'type' => 'string', 'enum' => [ 'default', ], ], 'ChatPromptTemplateConfiguration' => [ 'type' => 'structure', 'required' => [ 'messages', ], 'members' => [ 'inputVariables' => [ 'shape' => 'PromptInputVariablesList', ], 'messages' => [ 'shape' => 'Messages', ], 'system' => [ 'shape' => 'SystemContentBlocks', ], 'toolConfiguration' => [ 'shape' => 'ToolConfiguration', ], ], 'sensitive' => true, ], 'ChunkingConfiguration' => [ 'type' => 'structure', 'required' => [ 'chunkingStrategy', ], 'members' => [ 'chunkingStrategy' => [ 'shape' => 'ChunkingStrategy', ], 'fixedSizeChunkingConfiguration' => [ 'shape' => 'FixedSizeChunkingConfiguration', ], 'hierarchicalChunkingConfiguration' => [ 'shape' => 'HierarchicalChunkingConfiguration', ], 'semanticChunkingConfiguration' => [ 'shape' => 'SemanticChunkingConfiguration', ], ], ], 'ChunkingStrategy' => [ 'type' => 'string', 'enum' => [ 'FIXED_SIZE', 'NONE', 'HIERARCHICAL', 'SEMANTIC', ], ], 'ClientToken' => [ 'type' => 'string', 'max' => 256, 'min' => 33, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9]){0,256}$', ], 'CollaborationInstruction' => [ 'type' => 'string', 'max' => 4000, 'min' => 1, 'sensitive' => true, ], 'CollectorFlowNodeConfiguration' => [ 'type' => 'structure', 'members' => [], ], 'ColumnName' => [ 'type' => 'string', 'max' => 63, 'min' => 0, 'pattern' => '^[a-zA-Z0-9_\\-]+$', ], 'ConcurrencyType' => [ 'type' => 'string', 'enum' => [ 'Automatic', 'Manual', ], ], 'ConditionFlowNodeConfiguration' => [ 'type' => 'structure', 'required' => [ 'conditions', ], 'members' => [ 'conditions' => [ 'shape' => 'FlowConditions', ], ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'ConfluenceAuthType' => [ 'type' => 'string', 'enum' => [ 'BASIC', 'OAUTH2_CLIENT_CREDENTIALS', ], ], 'ConfluenceCrawlerConfiguration' => [ 'type' => 'structure', 'members' => [ 'filterConfiguration' => [ 'shape' => 'CrawlFilterConfiguration', ], ], ], 'ConfluenceDataSourceConfiguration' => [ 'type' => 'structure', 'required' => [ 'sourceConfiguration', ], 'members' => [ 'crawlerConfiguration' => [ 'shape' => 'ConfluenceCrawlerConfiguration', ], 'sourceConfiguration' => [ 'shape' => 'ConfluenceSourceConfiguration', ], ], ], 'ConfluenceHostType' => [ 'type' => 'string', 'enum' => [ 'SAAS', ], ], 'ConfluenceSourceConfiguration' => [ 'type' => 'structure', 'required' => [ 'authType', 'credentialsSecretArn', 'hostType', 'hostUrl', ], 'members' => [ 'authType' => [ 'shape' => 'ConfluenceAuthType', ], 'credentialsSecretArn' => [ 'shape' => 'SecretArn', ], 'hostType' => [ 'shape' => 'ConfluenceHostType', ], 'hostUrl' => [ 'shape' => 'HttpsUrl', ], ], ], 'ContentBlock' => [ 'type' => 'structure', 'members' => [ 'cachePoint' => [ 'shape' => 'CachePointBlock', ], 'text' => [ 'shape' => 'String', ], ], 'sensitive' => true, 'union' => true, ], 'ContentBlocks' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContentBlock', ], ], 'ContentDataSourceType' => [ 'type' => 'string', 'enum' => [ 'CUSTOM', 'S3', ], ], 'ContextEnrichmentConfiguration' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'bedrockFoundationModelConfiguration' => [ 'shape' => 'BedrockFoundationModelContextEnrichmentConfiguration', ], 'type' => [ 'shape' => 'ContextEnrichmentType', ], ], ], 'ContextEnrichmentType' => [ 'type' => 'string', 'enum' => [ 'BEDROCK_FOUNDATION_MODEL', ], ], 'ConversationRole' => [ 'type' => 'string', 'enum' => [ 'user', 'assistant', ], ], 'CrawlFilterConfiguration' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'patternObjectFilter' => [ 'shape' => 'PatternObjectFilterConfiguration', ], 'type' => [ 'shape' => 'CrawlFilterConfigurationType', ], ], ], 'CrawlFilterConfigurationType' => [ 'type' => 'string', 'enum' => [ 'PATTERN', ], ], 'CreateAgentActionGroupRequest' => [ 'type' => 'structure', 'required' => [ 'actionGroupName', 'agentId', 'agentVersion', ], 'members' => [ 'actionGroupExecutor' => [ 'shape' => 'ActionGroupExecutor', ], 'actionGroupName' => [ 'shape' => 'Name', ], 'actionGroupState' => [ 'shape' => 'ActionGroupState', ], 'agentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'agentId', ], 'agentVersion' => [ 'shape' => 'DraftVersion', 'location' => 'uri', 'locationName' => 'agentVersion', ], 'apiSchema' => [ 'shape' => 'APISchema', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'description' => [ 'shape' => 'Description', ], 'functionSchema' => [ 'shape' => 'FunctionSchema', ], 'parentActionGroupSignature' => [ 'shape' => 'ActionGroupSignature', ], 'parentActionGroupSignatureParams' => [ 'shape' => 'ActionGroupSignatureParams', ], ], ], 'CreateAgentActionGroupResponse' => [ 'type' => 'structure', 'required' => [ 'agentActionGroup', ], 'members' => [ 'agentActionGroup' => [ 'shape' => 'AgentActionGroup', ], ], ], 'CreateAgentAliasRequest' => [ 'type' => 'structure', 'required' => [ 'agentAliasName', 'agentId', ], 'members' => [ 'agentAliasName' => [ 'shape' => 'Name', ], 'agentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'agentId', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'description' => [ 'shape' => 'Description', ], 'routingConfiguration' => [ 'shape' => 'AgentAliasRoutingConfiguration', ], 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'CreateAgentAliasResponse' => [ 'type' => 'structure', 'required' => [ 'agentAlias', ], 'members' => [ 'agentAlias' => [ 'shape' => 'AgentAlias', ], ], ], 'CreateAgentRequest' => [ 'type' => 'structure', 'required' => [ 'agentName', ], 'members' => [ 'agentCollaboration' => [ 'shape' => 'AgentCollaboration', ], 'agentName' => [ 'shape' => 'Name', ], 'agentResourceRoleArn' => [ 'shape' => 'AgentRoleArn', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'customOrchestration' => [ 'shape' => 'CustomOrchestration', ], 'customerEncryptionKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'description' => [ 'shape' => 'Description', ], 'foundationModel' => [ 'shape' => 'ModelIdentifier', ], 'guardrailConfiguration' => [ 'shape' => 'GuardrailConfiguration', ], 'idleSessionTTLInSeconds' => [ 'shape' => 'SessionTTL', ], 'instruction' => [ 'shape' => 'Instruction', ], 'memoryConfiguration' => [ 'shape' => 'MemoryConfiguration', ], 'orchestrationType' => [ 'shape' => 'OrchestrationType', ], 'promptOverrideConfiguration' => [ 'shape' => 'PromptOverrideConfiguration', ], 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'CreateAgentResponse' => [ 'type' => 'structure', 'required' => [ 'agent', ], 'members' => [ 'agent' => [ 'shape' => 'Agent', ], ], ], 'CreateDataSourceRequest' => [ 'type' => 'structure', 'required' => [ 'dataSourceConfiguration', 'knowledgeBaseId', 'name', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'dataDeletionPolicy' => [ 'shape' => 'DataDeletionPolicy', ], 'dataSourceConfiguration' => [ 'shape' => 'DataSourceConfiguration', ], 'description' => [ 'shape' => 'Description', ], 'knowledgeBaseId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'name' => [ 'shape' => 'Name', ], 'serverSideEncryptionConfiguration' => [ 'shape' => 'ServerSideEncryptionConfiguration', ], 'vectorIngestionConfiguration' => [ 'shape' => 'VectorIngestionConfiguration', ], ], ], 'CreateDataSourceResponse' => [ 'type' => 'structure', 'required' => [ 'dataSource', ], 'members' => [ 'dataSource' => [ 'shape' => 'DataSource', ], ], ], 'CreateFlowAliasRequest' => [ 'type' => 'structure', 'required' => [ 'flowIdentifier', 'name', 'routingConfiguration', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'concurrencyConfiguration' => [ 'shape' => 'FlowAliasConcurrencyConfiguration', ], 'description' => [ 'shape' => 'Description', ], 'flowIdentifier' => [ 'shape' => 'FlowIdentifier', 'location' => 'uri', 'locationName' => 'flowIdentifier', ], 'name' => [ 'shape' => 'Name', ], 'routingConfiguration' => [ 'shape' => 'FlowAliasRoutingConfiguration', ], 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'CreateFlowAliasResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdAt', 'flowId', 'id', 'name', 'routingConfiguration', 'updatedAt', ], 'members' => [ 'arn' => [ 'shape' => 'FlowAliasArn', ], 'concurrencyConfiguration' => [ 'shape' => 'FlowAliasConcurrencyConfiguration', ], 'createdAt' => [ 'shape' => 'DateTimestamp', ], 'description' => [ 'shape' => 'Description', ], 'flowId' => [ 'shape' => 'FlowId', ], 'id' => [ 'shape' => 'FlowAliasId', ], 'name' => [ 'shape' => 'Name', ], 'routingConfiguration' => [ 'shape' => 'FlowAliasRoutingConfiguration', ], 'updatedAt' => [ 'shape' => 'DateTimestamp', ], ], ], 'CreateFlowRequest' => [ 'type' => 'structure', 'required' => [ 'executionRoleArn', 'name', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'customerEncryptionKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'definition' => [ 'shape' => 'FlowDefinition', ], 'description' => [ 'shape' => 'FlowDescription', ], 'executionRoleArn' => [ 'shape' => 'FlowExecutionRoleArn', ], 'name' => [ 'shape' => 'FlowName', ], 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'CreateFlowResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdAt', 'executionRoleArn', 'id', 'name', 'status', 'updatedAt', 'version', ], 'members' => [ 'arn' => [ 'shape' => 'FlowArn', ], 'createdAt' => [ 'shape' => 'DateTimestamp', ], 'customerEncryptionKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'definition' => [ 'shape' => 'FlowDefinition', ], 'description' => [ 'shape' => 'FlowDescription', ], 'executionRoleArn' => [ 'shape' => 'FlowExecutionRoleArn', ], 'id' => [ 'shape' => 'FlowId', ], 'name' => [ 'shape' => 'FlowName', ], 'status' => [ 'shape' => 'FlowStatus', ], 'updatedAt' => [ 'shape' => 'DateTimestamp', ], 'version' => [ 'shape' => 'DraftVersion', ], ], ], 'CreateFlowVersionRequest' => [ 'type' => 'structure', 'required' => [ 'flowIdentifier', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'description' => [ 'shape' => 'FlowDescription', ], 'flowIdentifier' => [ 'shape' => 'FlowIdentifier', 'location' => 'uri', 'locationName' => 'flowIdentifier', ], ], ], 'CreateFlowVersionResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdAt', 'executionRoleArn', 'id', 'name', 'status', 'version', ], 'members' => [ 'arn' => [ 'shape' => 'FlowArn', ], 'createdAt' => [ 'shape' => 'DateTimestamp', ], 'customerEncryptionKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'definition' => [ 'shape' => 'FlowDefinition', ], 'description' => [ 'shape' => 'FlowDescription', ], 'executionRoleArn' => [ 'shape' => 'FlowExecutionRoleArn', ], 'id' => [ 'shape' => 'FlowId', ], 'name' => [ 'shape' => 'FlowName', ], 'status' => [ 'shape' => 'FlowStatus', ], 'version' => [ 'shape' => 'NumericalVersion', ], ], ], 'CreateKnowledgeBaseRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseConfiguration', 'name', 'roleArn', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'description' => [ 'shape' => 'Description', ], 'knowledgeBaseConfiguration' => [ 'shape' => 'KnowledgeBaseConfiguration', ], 'name' => [ 'shape' => 'Name', ], 'roleArn' => [ 'shape' => 'KnowledgeBaseRoleArn', ], 'storageConfiguration' => [ 'shape' => 'StorageConfiguration', ], 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'CreateKnowledgeBaseResponse' => [ 'type' => 'structure', 'required' => [ 'knowledgeBase', ], 'members' => [ 'knowledgeBase' => [ 'shape' => 'KnowledgeBase', ], ], ], 'CreatePromptRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'customerEncryptionKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'defaultVariant' => [ 'shape' => 'PromptVariantName', ], 'description' => [ 'shape' => 'PromptDescription', ], 'name' => [ 'shape' => 'PromptName', ], 'tags' => [ 'shape' => 'TagsMap', ], 'variants' => [ 'shape' => 'PromptVariantList', ], ], ], 'CreatePromptResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdAt', 'id', 'name', 'updatedAt', 'version', ], 'members' => [ 'arn' => [ 'shape' => 'PromptArn', ], 'createdAt' => [ 'shape' => 'DateTimestamp', ], 'customerEncryptionKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'defaultVariant' => [ 'shape' => 'PromptVariantName', ], 'description' => [ 'shape' => 'PromptDescription', ], 'id' => [ 'shape' => 'PromptId', ], 'name' => [ 'shape' => 'PromptName', ], 'updatedAt' => [ 'shape' => 'DateTimestamp', ], 'variants' => [ 'shape' => 'PromptVariantList', ], 'version' => [ 'shape' => 'Version', ], ], ], 'CreatePromptVersionRequest' => [ 'type' => 'structure', 'required' => [ 'promptIdentifier', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'description' => [ 'shape' => 'PromptDescription', ], 'promptIdentifier' => [ 'shape' => 'PromptIdentifier', 'location' => 'uri', 'locationName' => 'promptIdentifier', ], 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'CreatePromptVersionResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdAt', 'id', 'name', 'updatedAt', 'version', ], 'members' => [ 'arn' => [ 'shape' => 'PromptArn', ], 'createdAt' => [ 'shape' => 'DateTimestamp', ], 'customerEncryptionKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'defaultVariant' => [ 'shape' => 'PromptVariantName', ], 'description' => [ 'shape' => 'PromptDescription', ], 'id' => [ 'shape' => 'PromptId', ], 'name' => [ 'shape' => 'PromptName', ], 'updatedAt' => [ 'shape' => 'DateTimestamp', ], 'variants' => [ 'shape' => 'PromptVariantList', ], 'version' => [ 'shape' => 'Version', ], ], ], 'CreationMode' => [ 'type' => 'string', 'enum' => [ 'DEFAULT', 'OVERRIDDEN', ], ], 'CuratedQueries' => [ 'type' => 'list', 'member' => [ 'shape' => 'CuratedQuery', ], 'max' => 10, 'min' => 0, ], 'CuratedQuery' => [ 'type' => 'structure', 'required' => [ 'naturalLanguage', 'sql', ], 'members' => [ 'naturalLanguage' => [ 'shape' => 'NaturalLanguageString', ], 'sql' => [ 'shape' => 'SqlString', ], ], ], 'CustomContent' => [ 'type' => 'structure', 'required' => [ 'customDocumentIdentifier', 'sourceType', ], 'members' => [ 'customDocumentIdentifier' => [ 'shape' => 'CustomDocumentIdentifier', ], 'inlineContent' => [ 'shape' => 'InlineContent', ], 's3Location' => [ 'shape' => 'CustomS3Location', ], 'sourceType' => [ 'shape' => 'CustomSourceType', ], ], ], 'CustomControlMethod' => [ 'type' => 'string', 'enum' => [ 'RETURN_CONTROL', ], ], 'CustomDocumentIdentifier' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'CustomDocumentIdentifierIdString', ], ], ], 'CustomDocumentIdentifierIdString' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'CustomOrchestration' => [ 'type' => 'structure', 'members' => [ 'executor' => [ 'shape' => 'OrchestrationExecutor', ], ], ], 'CustomS3Location' => [ 'type' => 'structure', 'required' => [ 'uri', ], 'members' => [ 'bucketOwnerAccountId' => [ 'shape' => 'BucketOwnerAccountId', ], 'uri' => [ 'shape' => 'S3ObjectUri', ], ], ], 'CustomSourceType' => [ 'type' => 'string', 'enum' => [ 'IN_LINE', 'S3_LOCATION', ], ], 'CustomTransformationConfiguration' => [ 'type' => 'structure', 'required' => [ 'intermediateStorage', 'transformations', ], 'members' => [ 'intermediateStorage' => [ 'shape' => 'IntermediateStorage', ], 'transformations' => [ 'shape' => 'Transformations', ], ], ], 'CyclicConnectionFlowValidationDetails' => [ 'type' => 'structure', 'required' => [ 'connection', ], 'members' => [ 'connection' => [ 'shape' => 'FlowConnectionName', ], ], ], 'Data' => [ 'type' => 'string', 'max' => 5242880, 'min' => 1, 'sensitive' => true, ], 'DataDeletionPolicy' => [ 'type' => 'string', 'enum' => [ 'RETAIN', 'DELETE', ], ], 'DataSource' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'dataSourceConfiguration', 'dataSourceId', 'knowledgeBaseId', 'name', 'status', 'updatedAt', ], 'members' => [ 'createdAt' => [ 'shape' => 'DateTimestamp', ], 'dataDeletionPolicy' => [ 'shape' => 'DataDeletionPolicy', ], 'dataSourceConfiguration' => [ 'shape' => 'DataSourceConfiguration', ], 'dataSourceId' => [ 'shape' => 'Id', ], 'description' => [ 'shape' => 'Description', ], 'failureReasons' => [ 'shape' => 'FailureReasons', ], 'knowledgeBaseId' => [ 'shape' => 'Id', ], 'name' => [ 'shape' => 'Name', ], 'serverSideEncryptionConfiguration' => [ 'shape' => 'ServerSideEncryptionConfiguration', ], 'status' => [ 'shape' => 'DataSourceStatus', ], 'updatedAt' => [ 'shape' => 'DateTimestamp', ], 'vectorIngestionConfiguration' => [ 'shape' => 'VectorIngestionConfiguration', ], ], ], 'DataSourceConfiguration' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'confluenceConfiguration' => [ 'shape' => 'ConfluenceDataSourceConfiguration', ], 's3Configuration' => [ 'shape' => 'S3DataSourceConfiguration', ], 'salesforceConfiguration' => [ 'shape' => 'SalesforceDataSourceConfiguration', ], 'sharePointConfiguration' => [ 'shape' => 'SharePointDataSourceConfiguration', ], 'type' => [ 'shape' => 'DataSourceType', ], 'webConfiguration' => [ 'shape' => 'WebDataSourceConfiguration', ], ], ], 'DataSourceStatus' => [ 'type' => 'string', 'enum' => [ 'AVAILABLE', 'DELETING', 'DELETE_UNSUCCESSFUL', ], ], 'DataSourceSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataSourceSummary', ], ], 'DataSourceSummary' => [ 'type' => 'structure', 'required' => [ 'dataSourceId', 'knowledgeBaseId', 'name', 'status', 'updatedAt', ], 'members' => [ 'dataSourceId' => [ 'shape' => 'Id', ], 'description' => [ 'shape' => 'Description', ], 'knowledgeBaseId' => [ 'shape' => 'Id', ], 'name' => [ 'shape' => 'Name', ], 'status' => [ 'shape' => 'DataSourceStatus', ], 'updatedAt' => [ 'shape' => 'DateTimestamp', ], ], ], 'DataSourceType' => [ 'type' => 'string', 'enum' => [ 'S3', 'WEB', 'CONFLUENCE', 'SALESFORCE', 'SHAREPOINT', 'CUSTOM', 'REDSHIFT_METADATA', ], ], 'DateTimestamp' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'DeleteAgentActionGroupRequest' => [ 'type' => 'structure', 'required' => [ 'actionGroupId', 'agentId', 'agentVersion', ], 'members' => [ 'actionGroupId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'actionGroupId', ], 'agentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'agentId', ], 'agentVersion' => [ 'shape' => 'DraftVersion', 'location' => 'uri', 'locationName' => 'agentVersion', ], 'skipResourceInUseCheck' => [ 'shape' => 'Boolean', 'location' => 'querystring', 'locationName' => 'skipResourceInUseCheck', ], ], ], 'DeleteAgentActionGroupResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteAgentAliasRequest' => [ 'type' => 'structure', 'required' => [ 'agentAliasId', 'agentId', ], 'members' => [ 'agentAliasId' => [ 'shape' => 'AgentAliasId', 'location' => 'uri', 'locationName' => 'agentAliasId', ], 'agentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'agentId', ], ], ], 'DeleteAgentAliasResponse' => [ 'type' => 'structure', 'required' => [ 'agentAliasId', 'agentAliasStatus', 'agentId', ], 'members' => [ 'agentAliasId' => [ 'shape' => 'AgentAliasId', ], 'agentAliasStatus' => [ 'shape' => 'AgentAliasStatus', ], 'agentId' => [ 'shape' => 'Id', ], ], ], 'DeleteAgentRequest' => [ 'type' => 'structure', 'required' => [ 'agentId', ], 'members' => [ 'agentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'agentId', ], 'skipResourceInUseCheck' => [ 'shape' => 'Boolean', 'location' => 'querystring', 'locationName' => 'skipResourceInUseCheck', ], ], ], 'DeleteAgentResponse' => [ 'type' => 'structure', 'required' => [ 'agentId', 'agentStatus', ], 'members' => [ 'agentId' => [ 'shape' => 'Id', ], 'agentStatus' => [ 'shape' => 'AgentStatus', ], ], ], 'DeleteAgentVersionRequest' => [ 'type' => 'structure', 'required' => [ 'agentId', 'agentVersion', ], 'members' => [ 'agentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'agentId', ], 'agentVersion' => [ 'shape' => 'NumericalVersion', 'location' => 'uri', 'locationName' => 'agentVersion', ], 'skipResourceInUseCheck' => [ 'shape' => 'Boolean', 'location' => 'querystring', 'locationName' => 'skipResourceInUseCheck', ], ], ], 'DeleteAgentVersionResponse' => [ 'type' => 'structure', 'required' => [ 'agentId', 'agentStatus', 'agentVersion', ], 'members' => [ 'agentId' => [ 'shape' => 'Id', ], 'agentStatus' => [ 'shape' => 'AgentStatus', ], 'agentVersion' => [ 'shape' => 'NumericalVersion', ], ], ], 'DeleteDataSourceRequest' => [ 'type' => 'structure', 'required' => [ 'dataSourceId', 'knowledgeBaseId', ], 'members' => [ 'dataSourceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'dataSourceId', ], 'knowledgeBaseId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], ], ], 'DeleteDataSourceResponse' => [ 'type' => 'structure', 'required' => [ 'dataSourceId', 'knowledgeBaseId', 'status', ], 'members' => [ 'dataSourceId' => [ 'shape' => 'Id', ], 'knowledgeBaseId' => [ 'shape' => 'Id', ], 'status' => [ 'shape' => 'DataSourceStatus', ], ], ], 'DeleteFlowAliasRequest' => [ 'type' => 'structure', 'required' => [ 'aliasIdentifier', 'flowIdentifier', ], 'members' => [ 'aliasIdentifier' => [ 'shape' => 'FlowAliasIdentifier', 'location' => 'uri', 'locationName' => 'aliasIdentifier', ], 'flowIdentifier' => [ 'shape' => 'FlowIdentifier', 'location' => 'uri', 'locationName' => 'flowIdentifier', ], ], ], 'DeleteFlowAliasResponse' => [ 'type' => 'structure', 'required' => [ 'flowId', 'id', ], 'members' => [ 'flowId' => [ 'shape' => 'FlowId', ], 'id' => [ 'shape' => 'FlowAliasId', ], ], ], 'DeleteFlowRequest' => [ 'type' => 'structure', 'required' => [ 'flowIdentifier', ], 'members' => [ 'flowIdentifier' => [ 'shape' => 'FlowIdentifier', 'location' => 'uri', 'locationName' => 'flowIdentifier', ], 'skipResourceInUseCheck' => [ 'shape' => 'Boolean', 'location' => 'querystring', 'locationName' => 'skipResourceInUseCheck', ], ], ], 'DeleteFlowResponse' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'FlowId', ], ], ], 'DeleteFlowVersionRequest' => [ 'type' => 'structure', 'required' => [ 'flowIdentifier', 'flowVersion', ], 'members' => [ 'flowIdentifier' => [ 'shape' => 'FlowIdentifier', 'location' => 'uri', 'locationName' => 'flowIdentifier', ], 'flowVersion' => [ 'shape' => 'NumericalVersion', 'location' => 'uri', 'locationName' => 'flowVersion', ], 'skipResourceInUseCheck' => [ 'shape' => 'Boolean', 'location' => 'querystring', 'locationName' => 'skipResourceInUseCheck', ], ], ], 'DeleteFlowVersionResponse' => [ 'type' => 'structure', 'required' => [ 'id', 'version', ], 'members' => [ 'id' => [ 'shape' => 'Id', ], 'version' => [ 'shape' => 'NumericalVersion', ], ], ], 'DeleteKnowledgeBaseDocumentsRequest' => [ 'type' => 'structure', 'required' => [ 'dataSourceId', 'documentIdentifiers', 'knowledgeBaseId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'dataSourceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'dataSourceId', ], 'documentIdentifiers' => [ 'shape' => 'DocumentIdentifiers', ], 'knowledgeBaseId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], ], ], 'DeleteKnowledgeBaseDocumentsResponse' => [ 'type' => 'structure', 'members' => [ 'documentDetails' => [ 'shape' => 'KnowledgeBaseDocumentDetails', ], ], ], 'DeleteKnowledgeBaseRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], ], ], 'DeleteKnowledgeBaseResponse' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'status', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'Id', ], 'status' => [ 'shape' => 'KnowledgeBaseStatus', ], ], ], 'DeletePromptRequest' => [ 'type' => 'structure', 'required' => [ 'promptIdentifier', ], 'members' => [ 'promptIdentifier' => [ 'shape' => 'PromptIdentifier', 'location' => 'uri', 'locationName' => 'promptIdentifier', ], 'promptVersion' => [ 'shape' => 'NumericalVersion', 'location' => 'querystring', 'locationName' => 'promptVersion', ], ], ], 'DeletePromptResponse' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'PromptId', ], 'version' => [ 'shape' => 'NumericalVersion', ], ], ], 'Description' => [ 'type' => 'string', 'max' => 200, 'min' => 1, ], 'DescriptionString' => [ 'type' => 'string', 'max' => 200, 'min' => 1, ], 'Dimensions' => [ 'type' => 'integer', 'box' => true, 'max' => 4096, 'min' => 0, ], 'DisassociateAgentCollaboratorRequest' => [ 'type' => 'structure', 'required' => [ 'agentId', 'agentVersion', 'collaboratorId', ], 'members' => [ 'agentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'agentId', ], 'agentVersion' => [ 'shape' => 'DraftVersion', 'location' => 'uri', 'locationName' => 'agentVersion', ], 'collaboratorId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'collaboratorId', ], ], ], 'DisassociateAgentCollaboratorResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateAgentKnowledgeBaseRequest' => [ 'type' => 'structure', 'required' => [ 'agentId', 'agentVersion', 'knowledgeBaseId', ], 'members' => [ 'agentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'agentId', ], 'agentVersion' => [ 'shape' => 'DraftVersion', 'location' => 'uri', 'locationName' => 'agentVersion', ], 'knowledgeBaseId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], ], ], 'DisassociateAgentKnowledgeBaseResponse' => [ 'type' => 'structure', 'members' => [], ], 'Document' => [ 'type' => 'structure', 'members' => [], 'document' => true, ], 'DocumentContent' => [ 'type' => 'structure', 'required' => [ 'dataSourceType', ], 'members' => [ 'custom' => [ 'shape' => 'CustomContent', ], 'dataSourceType' => [ 'shape' => 'ContentDataSourceType', ], 's3' => [ 'shape' => 'S3Content', ], ], ], 'DocumentIdentifier' => [ 'type' => 'structure', 'required' => [ 'dataSourceType', ], 'members' => [ 'custom' => [ 'shape' => 'CustomDocumentIdentifier', ], 'dataSourceType' => [ 'shape' => 'ContentDataSourceType', ], 's3' => [ 'shape' => 'S3Location', ], ], ], 'DocumentIdentifiers' => [ 'type' => 'list', 'member' => [ 'shape' => 'DocumentIdentifier', ], 'max' => 10, 'min' => 1, ], 'DocumentMetadata' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'inlineAttributes' => [ 'shape' => 'DocumentMetadataInlineAttributesList', ], 's3Location' => [ 'shape' => 'CustomS3Location', ], 'type' => [ 'shape' => 'MetadataSourceType', ], ], ], 'DocumentMetadataInlineAttributesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetadataAttribute', ], 'max' => 50, 'min' => 1, ], 'DocumentStatus' => [ 'type' => 'string', 'enum' => [ 'INDEXED', 'PARTIALLY_INDEXED', 'PENDING', 'FAILED', 'METADATA_PARTIALLY_INDEXED', 'METADATA_UPDATE_FAILED', 'IGNORED', 'NOT_FOUND', 'STARTING', 'IN_PROGRESS', 'DELETING', 'DELETE_IN_PROGRESS', ], ], 'DraftVersion' => [ 'type' => 'string', 'max' => 5, 'min' => 5, 'pattern' => '^DRAFT$', ], 'DuplicateConditionExpressionFlowValidationDetails' => [ 'type' => 'structure', 'required' => [ 'expression', 'node', ], 'members' => [ 'expression' => [ 'shape' => 'FlowConditionExpression', ], 'node' => [ 'shape' => 'FlowNodeName', ], ], ], 'DuplicateConnectionsFlowValidationDetails' => [ 'type' => 'structure', 'required' => [ 'source', 'target', ], 'members' => [ 'source' => [ 'shape' => 'FlowNodeName', ], 'target' => [ 'shape' => 'FlowNodeName', ], ], ], 'EmbeddingDataType' => [ 'type' => 'string', 'enum' => [ 'FLOAT32', 'BINARY', ], ], 'EmbeddingModelConfiguration' => [ 'type' => 'structure', 'members' => [ 'bedrockEmbeddingModelConfiguration' => [ 'shape' => 'BedrockEmbeddingModelConfiguration', ], ], ], 'EnabledMemoryTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'MemoryType', ], 'max' => 1, 'min' => 1, ], 'EnrichmentStrategyConfiguration' => [ 'type' => 'structure', 'required' => [ 'method', ], 'members' => [ 'method' => [ 'shape' => 'EnrichmentStrategyMethod', ], ], ], 'EnrichmentStrategyMethod' => [ 'type' => 'string', 'enum' => [ 'CHUNK_ENTITY_EXTRACTION', ], ], 'ErrorMessage' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, ], 'FailureReason' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, ], 'FailureReasons' => [ 'type' => 'list', 'member' => [ 'shape' => 'FailureReason', ], 'max' => 2048, 'min' => 0, ], 'FieldForReranking' => [ 'type' => 'structure', 'required' => [ 'fieldName', ], 'members' => [ 'fieldName' => [ 'shape' => 'FieldForRerankingFieldNameString', ], ], ], 'FieldForRerankingFieldNameString' => [ 'type' => 'string', 'max' => 2000, 'min' => 1, ], 'FieldName' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '^.*$', ], 'FieldsForReranking' => [ 'type' => 'list', 'member' => [ 'shape' => 'FieldForReranking', ], 'max' => 100, 'min' => 1, 'sensitive' => true, ], 'FilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FilterPattern', ], 'max' => 25, 'min' => 1, 'sensitive' => true, ], 'FilterPattern' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'sensitive' => true, ], 'FilteredObjectType' => [ 'type' => 'string', 'max' => 50, 'min' => 1, 'sensitive' => true, ], 'FixedSizeChunkingConfiguration' => [ 'type' => 'structure', 'required' => [ 'maxTokens', 'overlapPercentage', ], 'members' => [ 'maxTokens' => [ 'shape' => 'FixedSizeChunkingConfigurationMaxTokensInteger', ], 'overlapPercentage' => [ 'shape' => 'FixedSizeChunkingConfigurationOverlapPercentageInteger', ], ], ], 'FixedSizeChunkingConfigurationMaxTokensInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'FixedSizeChunkingConfigurationOverlapPercentageInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 99, 'min' => 1, ], 'FlowAliasArn' => [ 'type' => 'string', 'pattern' => '^arn:aws:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:flow/[0-9a-zA-Z]{10}/alias/(TSTALIASID|[0-9a-zA-Z]{10})$', ], 'FlowAliasConcurrencyConfiguration' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'maxConcurrency' => [ 'shape' => 'FlowAliasConcurrencyConfigurationMaxConcurrencyInteger', ], 'type' => [ 'shape' => 'ConcurrencyType', ], ], ], 'FlowAliasConcurrencyConfigurationMaxConcurrencyInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'FlowAliasId' => [ 'type' => 'string', 'pattern' => '^(TSTALIASID|[0-9a-zA-Z]{10})$', ], 'FlowAliasIdentifier' => [ 'type' => 'string', 'pattern' => '^(arn:aws:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:flow/[0-9a-zA-Z]{10}/alias/[0-9a-zA-Z]{10})|(TSTALIASID|[0-9a-zA-Z]{10})$', ], 'FlowAliasRoutingConfiguration' => [ 'type' => 'list', 'member' => [ 'shape' => 'FlowAliasRoutingConfigurationListItem', ], 'max' => 1, 'min' => 1, ], 'FlowAliasRoutingConfigurationListItem' => [ 'type' => 'structure', 'members' => [ 'flowVersion' => [ 'shape' => 'Version', ], ], ], 'FlowAliasSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'FlowAliasSummary', ], 'max' => 10, 'min' => 0, ], 'FlowAliasSummary' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdAt', 'flowId', 'id', 'name', 'routingConfiguration', 'updatedAt', ], 'members' => [ 'arn' => [ 'shape' => 'FlowAliasArn', ], 'concurrencyConfiguration' => [ 'shape' => 'FlowAliasConcurrencyConfiguration', ], 'createdAt' => [ 'shape' => 'DateTimestamp', ], 'description' => [ 'shape' => 'Description', ], 'flowId' => [ 'shape' => 'FlowId', ], 'id' => [ 'shape' => 'FlowAliasId', ], 'name' => [ 'shape' => 'Name', ], 'routingConfiguration' => [ 'shape' => 'FlowAliasRoutingConfiguration', ], 'updatedAt' => [ 'shape' => 'DateTimestamp', ], ], ], 'FlowArn' => [ 'type' => 'string', 'pattern' => '^arn:aws:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:flow/[0-9a-zA-Z]{10}$', ], 'FlowCondition' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'expression' => [ 'shape' => 'FlowConditionExpression', ], 'name' => [ 'shape' => 'FlowConditionName', ], ], ], 'FlowConditionExpression' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'sensitive' => true, ], 'FlowConditionName' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z]([_]?[0-9a-zA-Z]){1,50}$', ], 'FlowConditionalConnectionConfiguration' => [ 'type' => 'structure', 'required' => [ 'condition', ], 'members' => [ 'condition' => [ 'shape' => 'FlowConditionName', ], ], ], 'FlowConditions' => [ 'type' => 'list', 'member' => [ 'shape' => 'FlowCondition', ], 'max' => 5, 'min' => 1, ], 'FlowConnection' => [ 'type' => 'structure', 'required' => [ 'name', 'source', 'target', 'type', ], 'members' => [ 'configuration' => [ 'shape' => 'FlowConnectionConfiguration', ], 'name' => [ 'shape' => 'FlowConnectionName', ], 'source' => [ 'shape' => 'FlowNodeName', ], 'target' => [ 'shape' => 'FlowNodeName', ], 'type' => [ 'shape' => 'FlowConnectionType', ], ], ], 'FlowConnectionConfiguration' => [ 'type' => 'structure', 'members' => [ 'conditional' => [ 'shape' => 'FlowConditionalConnectionConfiguration', ], 'data' => [ 'shape' => 'FlowDataConnectionConfiguration', ], ], 'union' => true, ], 'FlowConnectionName' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z]([_]?[0-9a-zA-Z]){1,100}$', ], 'FlowConnectionType' => [ 'type' => 'string', 'enum' => [ 'Data', 'Conditional', ], ], 'FlowConnections' => [ 'type' => 'list', 'member' => [ 'shape' => 'FlowConnection', ], 'max' => 20, 'min' => 0, ], 'FlowDataConnectionConfiguration' => [ 'type' => 'structure', 'required' => [ 'sourceOutput', 'targetInput', ], 'members' => [ 'sourceOutput' => [ 'shape' => 'FlowNodeOutputName', ], 'targetInput' => [ 'shape' => 'FlowNodeInputName', ], ], ], 'FlowDefinition' => [ 'type' => 'structure', 'members' => [ 'connections' => [ 'shape' => 'FlowConnections', ], 'nodes' => [ 'shape' => 'FlowNodes', ], ], 'sensitive' => true, ], 'FlowDescription' => [ 'type' => 'string', 'max' => 200, 'min' => 1, ], 'FlowExecutionRoleArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '^arn:aws(-[^:]+)?:iam::([0-9]{12})?:role/(service-role/)?.+$', ], 'FlowId' => [ 'type' => 'string', 'pattern' => '^[0-9a-zA-Z]{10}$', ], 'FlowIdentifier' => [ 'type' => 'string', 'pattern' => '^(arn:aws:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:flow/[0-9a-zA-Z]{10})|([0-9a-zA-Z]{10})$', ], 'FlowName' => [ 'type' => 'string', 'pattern' => '^([0-9a-zA-Z][_-]?){1,100}$', ], 'FlowNode' => [ 'type' => 'structure', 'required' => [ 'name', 'type', ], 'members' => [ 'configuration' => [ 'shape' => 'FlowNodeConfiguration', ], 'inputs' => [ 'shape' => 'FlowNodeInputs', ], 'name' => [ 'shape' => 'FlowNodeName', ], 'outputs' => [ 'shape' => 'FlowNodeOutputs', ], 'type' => [ 'shape' => 'FlowNodeType', ], ], ], 'FlowNodeConfiguration' => [ 'type' => 'structure', 'members' => [ 'agent' => [ 'shape' => 'AgentFlowNodeConfiguration', ], 'collector' => [ 'shape' => 'CollectorFlowNodeConfiguration', ], 'condition' => [ 'shape' => 'ConditionFlowNodeConfiguration', ], 'inlineCode' => [ 'shape' => 'InlineCodeFlowNodeConfiguration', ], 'input' => [ 'shape' => 'InputFlowNodeConfiguration', ], 'iterator' => [ 'shape' => 'IteratorFlowNodeConfiguration', ], 'knowledgeBase' => [ 'shape' => 'KnowledgeBaseFlowNodeConfiguration', ], 'lambdaFunction' => [ 'shape' => 'LambdaFunctionFlowNodeConfiguration', ], 'lex' => [ 'shape' => 'LexFlowNodeConfiguration', ], 'loop' => [ 'shape' => 'LoopFlowNodeConfiguration', ], 'loopController' => [ 'shape' => 'LoopControllerFlowNodeConfiguration', ], 'loopInput' => [ 'shape' => 'LoopInputFlowNodeConfiguration', ], 'output' => [ 'shape' => 'OutputFlowNodeConfiguration', ], 'prompt' => [ 'shape' => 'PromptFlowNodeConfiguration', ], 'retrieval' => [ 'shape' => 'RetrievalFlowNodeConfiguration', ], 'storage' => [ 'shape' => 'StorageFlowNodeConfiguration', ], ], 'union' => true, ], 'FlowNodeIODataType' => [ 'type' => 'string', 'enum' => [ 'String', 'Number', 'Boolean', 'Object', 'Array', ], ], 'FlowNodeInput' => [ 'type' => 'structure', 'required' => [ 'expression', 'name', 'type', ], 'members' => [ 'category' => [ 'shape' => 'FlowNodeInputCategory', ], 'expression' => [ 'shape' => 'FlowNodeInputExpression', ], 'name' => [ 'shape' => 'FlowNodeInputName', ], 'type' => [ 'shape' => 'FlowNodeIODataType', ], ], ], 'FlowNodeInputCategory' => [ 'type' => 'string', 'enum' => [ 'LoopCondition', 'ReturnValueToLoopStart', 'ExitLoop', ], ], 'FlowNodeInputExpression' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'sensitive' => true, ], 'FlowNodeInputName' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z]([_]?[0-9a-zA-Z]){1,50}$', ], 'FlowNodeInputs' => [ 'type' => 'list', 'member' => [ 'shape' => 'FlowNodeInput', ], 'max' => 20, 'min' => 0, ], 'FlowNodeName' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z]([_]?[0-9a-zA-Z]){1,50}$', ], 'FlowNodeOutput' => [ 'type' => 'structure', 'required' => [ 'name', 'type', ], 'members' => [ 'name' => [ 'shape' => 'FlowNodeOutputName', ], 'type' => [ 'shape' => 'FlowNodeIODataType', ], ], ], 'FlowNodeOutputName' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z]([_]?[0-9a-zA-Z]){1,50}$', ], 'FlowNodeOutputs' => [ 'type' => 'list', 'member' => [ 'shape' => 'FlowNodeOutput', ], 'max' => 5, 'min' => 0, ], 'FlowNodeType' => [ 'type' => 'string', 'enum' => [ 'Input', 'Output', 'KnowledgeBase', 'Condition', 'Lex', 'Prompt', 'LambdaFunction', 'Storage', 'Agent', 'Retrieval', 'Iterator', 'Collector', 'InlineCode', 'Loop', 'LoopInput', 'LoopController', ], ], 'FlowNodes' => [ 'type' => 'list', 'member' => [ 'shape' => 'FlowNode', ], 'max' => 40, 'min' => 0, ], 'FlowStatus' => [ 'type' => 'string', 'enum' => [ 'Failed', 'Prepared', 'Preparing', 'NotPrepared', ], ], 'FlowSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'FlowSummary', ], 'max' => 10, 'min' => 0, ], 'FlowSummary' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdAt', 'id', 'name', 'status', 'updatedAt', 'version', ], 'members' => [ 'arn' => [ 'shape' => 'FlowArn', ], 'createdAt' => [ 'shape' => 'DateTimestamp', ], 'description' => [ 'shape' => 'FlowDescription', ], 'id' => [ 'shape' => 'FlowId', ], 'name' => [ 'shape' => 'FlowName', ], 'status' => [ 'shape' => 'FlowStatus', ], 'updatedAt' => [ 'shape' => 'DateTimestamp', ], 'version' => [ 'shape' => 'DraftVersion', ], ], ], 'FlowValidation' => [ 'type' => 'structure', 'required' => [ 'message', 'severity', ], 'members' => [ 'details' => [ 'shape' => 'FlowValidationDetails', ], 'message' => [ 'shape' => 'NonBlankString', ], 'severity' => [ 'shape' => 'FlowValidationSeverity', ], 'type' => [ 'shape' => 'FlowValidationType', ], ], ], 'FlowValidationDetails' => [ 'type' => 'structure', 'members' => [ 'cyclicConnection' => [ 'shape' => 'CyclicConnectionFlowValidationDetails', ], 'duplicateConditionExpression' => [ 'shape' => 'DuplicateConditionExpressionFlowValidationDetails', ], 'duplicateConnections' => [ 'shape' => 'DuplicateConnectionsFlowValidationDetails', ], 'incompatibleConnectionDataType' => [ 'shape' => 'IncompatibleConnectionDataTypeFlowValidationDetails', ], 'invalidLoopBoundary' => [ 'shape' => 'InvalidLoopBoundaryFlowValidationDetails', ], 'loopIncompatibleNodeType' => [ 'shape' => 'LoopIncompatibleNodeTypeFlowValidationDetails', ], 'malformedConditionExpression' => [ 'shape' => 'MalformedConditionExpressionFlowValidationDetails', ], 'malformedNodeInputExpression' => [ 'shape' => 'MalformedNodeInputExpressionFlowValidationDetails', ], 'mismatchedNodeInputType' => [ 'shape' => 'MismatchedNodeInputTypeFlowValidationDetails', ], 'mismatchedNodeOutputType' => [ 'shape' => 'MismatchedNodeOutputTypeFlowValidationDetails', ], 'missingConnectionConfiguration' => [ 'shape' => 'MissingConnectionConfigurationFlowValidationDetails', ], 'missingDefaultCondition' => [ 'shape' => 'MissingDefaultConditionFlowValidationDetails', ], 'missingEndingNodes' => [ 'shape' => 'MissingEndingNodesFlowValidationDetails', ], 'missingLoopControllerNode' => [ 'shape' => 'MissingLoopControllerNodeFlowValidationDetails', ], 'missingLoopInputNode' => [ 'shape' => 'MissingLoopInputNodeFlowValidationDetails', ], 'missingNodeConfiguration' => [ 'shape' => 'MissingNodeConfigurationFlowValidationDetails', ], 'missingNodeInput' => [ 'shape' => 'MissingNodeInputFlowValidationDetails', ], 'missingNodeOutput' => [ 'shape' => 'MissingNodeOutputFlowValidationDetails', ], 'missingStartingNodes' => [ 'shape' => 'MissingStartingNodesFlowValidationDetails', ], 'multipleLoopControllerNodes' => [ 'shape' => 'MultipleLoopControllerNodesFlowValidationDetails', ], 'multipleLoopInputNodes' => [ 'shape' => 'MultipleLoopInputNodesFlowValidationDetails', ], 'multipleNodeInputConnections' => [ 'shape' => 'MultipleNodeInputConnectionsFlowValidationDetails', ], 'unfulfilledNodeInput' => [ 'shape' => 'UnfulfilledNodeInputFlowValidationDetails', ], 'unknownConnectionCondition' => [ 'shape' => 'UnknownConnectionConditionFlowValidationDetails', ], 'unknownConnectionSource' => [ 'shape' => 'UnknownConnectionSourceFlowValidationDetails', ], 'unknownConnectionSourceOutput' => [ 'shape' => 'UnknownConnectionSourceOutputFlowValidationDetails', ], 'unknownConnectionTarget' => [ 'shape' => 'UnknownConnectionTargetFlowValidationDetails', ], 'unknownConnectionTargetInput' => [ 'shape' => 'UnknownConnectionTargetInputFlowValidationDetails', ], 'unknownNodeInput' => [ 'shape' => 'UnknownNodeInputFlowValidationDetails', ], 'unknownNodeOutput' => [ 'shape' => 'UnknownNodeOutputFlowValidationDetails', ], 'unreachableNode' => [ 'shape' => 'UnreachableNodeFlowValidationDetails', ], 'unsatisfiedConnectionConditions' => [ 'shape' => 'UnsatisfiedConnectionConditionsFlowValidationDetails', ], 'unspecified' => [ 'shape' => 'UnspecifiedFlowValidationDetails', ], ], 'union' => true, ], 'FlowValidationSeverity' => [ 'type' => 'string', 'enum' => [ 'Warning', 'Error', ], ], 'FlowValidationType' => [ 'type' => 'string', 'enum' => [ 'CyclicConnection', 'DuplicateConnections', 'DuplicateConditionExpression', 'UnreachableNode', 'UnknownConnectionSource', 'UnknownConnectionSourceOutput', 'UnknownConnectionTarget', 'UnknownConnectionTargetInput', 'UnknownConnectionCondition', 'MalformedConditionExpression', 'MalformedNodeInputExpression', 'MismatchedNodeInputType', 'MismatchedNodeOutputType', 'IncompatibleConnectionDataType', 'MissingConnectionConfiguration', 'MissingDefaultCondition', 'MissingEndingNodes', 'MissingNodeConfiguration', 'MissingNodeInput', 'MissingNodeOutput', 'MissingStartingNodes', 'MultipleNodeInputConnections', 'UnfulfilledNodeInput', 'UnsatisfiedConnectionConditions', 'Unspecified', 'UnknownNodeInput', 'UnknownNodeOutput', 'MissingLoopInputNode', 'MissingLoopControllerNode', 'MultipleLoopInputNodes', 'MultipleLoopControllerNodes', 'LoopIncompatibleNodeType', 'InvalidLoopBoundary', ], ], 'FlowValidations' => [ 'type' => 'list', 'member' => [ 'shape' => 'FlowValidation', ], 'max' => 100, 'min' => 0, ], 'FlowVersionSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'FlowVersionSummary', ], 'max' => 10, 'min' => 0, ], 'FlowVersionSummary' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdAt', 'id', 'status', 'version', ], 'members' => [ 'arn' => [ 'shape' => 'FlowArn', ], 'createdAt' => [ 'shape' => 'DateTimestamp', ], 'id' => [ 'shape' => 'FlowId', ], 'status' => [ 'shape' => 'FlowStatus', ], 'version' => [ 'shape' => 'NumericalVersion', ], ], ], 'Function' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'description' => [ 'shape' => 'FunctionDescription', ], 'name' => [ 'shape' => 'Name', ], 'parameters' => [ 'shape' => 'ParameterMap', ], 'requireConfirmation' => [ 'shape' => 'RequireConfirmation', ], ], ], 'FunctionDescription' => [ 'type' => 'string', 'max' => 1200, 'min' => 1, ], 'FunctionSchema' => [ 'type' => 'structure', 'members' => [ 'functions' => [ 'shape' => 'Functions', ], ], 'union' => true, ], 'Functions' => [ 'type' => 'list', 'member' => [ 'shape' => 'Function', ], ], 'GetAgentActionGroupRequest' => [ 'type' => 'structure', 'required' => [ 'actionGroupId', 'agentId', 'agentVersion', ], 'members' => [ 'actionGroupId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'actionGroupId', ], 'agentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'agentId', ], 'agentVersion' => [ 'shape' => 'Version', 'location' => 'uri', 'locationName' => 'agentVersion', ], ], ], 'GetAgentActionGroupResponse' => [ 'type' => 'structure', 'required' => [ 'agentActionGroup', ], 'members' => [ 'agentActionGroup' => [ 'shape' => 'AgentActionGroup', ], ], ], 'GetAgentAliasRequest' => [ 'type' => 'structure', 'required' => [ 'agentAliasId', 'agentId', ], 'members' => [ 'agentAliasId' => [ 'shape' => 'AgentAliasId', 'location' => 'uri', 'locationName' => 'agentAliasId', ], 'agentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'agentId', ], ], ], 'GetAgentAliasResponse' => [ 'type' => 'structure', 'required' => [ 'agentAlias', ], 'members' => [ 'agentAlias' => [ 'shape' => 'AgentAlias', ], ], ], 'GetAgentCollaboratorRequest' => [ 'type' => 'structure', 'required' => [ 'agentId', 'agentVersion', 'collaboratorId', ], 'members' => [ 'agentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'agentId', ], 'agentVersion' => [ 'shape' => 'Version', 'location' => 'uri', 'locationName' => 'agentVersion', ], 'collaboratorId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'collaboratorId', ], ], ], 'GetAgentCollaboratorResponse' => [ 'type' => 'structure', 'required' => [ 'agentCollaborator', ], 'members' => [ 'agentCollaborator' => [ 'shape' => 'AgentCollaborator', ], ], ], 'GetAgentKnowledgeBaseRequest' => [ 'type' => 'structure', 'required' => [ 'agentId', 'agentVersion', 'knowledgeBaseId', ], 'members' => [ 'agentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'agentId', ], 'agentVersion' => [ 'shape' => 'Version', 'location' => 'uri', 'locationName' => 'agentVersion', ], 'knowledgeBaseId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], ], ], 'GetAgentKnowledgeBaseResponse' => [ 'type' => 'structure', 'required' => [ 'agentKnowledgeBase', ], 'members' => [ 'agentKnowledgeBase' => [ 'shape' => 'AgentKnowledgeBase', ], ], ], 'GetAgentRequest' => [ 'type' => 'structure', 'required' => [ 'agentId', ], 'members' => [ 'agentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'agentId', ], ], ], 'GetAgentResponse' => [ 'type' => 'structure', 'required' => [ 'agent', ], 'members' => [ 'agent' => [ 'shape' => 'Agent', ], ], ], 'GetAgentVersionRequest' => [ 'type' => 'structure', 'required' => [ 'agentId', 'agentVersion', ], 'members' => [ 'agentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'agentId', ], 'agentVersion' => [ 'shape' => 'NumericalVersion', 'location' => 'uri', 'locationName' => 'agentVersion', ], ], ], 'GetAgentVersionResponse' => [ 'type' => 'structure', 'required' => [ 'agentVersion', ], 'members' => [ 'agentVersion' => [ 'shape' => 'AgentVersion', ], ], ], 'GetDataSourceRequest' => [ 'type' => 'structure', 'required' => [ 'dataSourceId', 'knowledgeBaseId', ], 'members' => [ 'dataSourceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'dataSourceId', ], 'knowledgeBaseId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], ], ], 'GetDataSourceResponse' => [ 'type' => 'structure', 'required' => [ 'dataSource', ], 'members' => [ 'dataSource' => [ 'shape' => 'DataSource', ], ], ], 'GetFlowAliasRequest' => [ 'type' => 'structure', 'required' => [ 'aliasIdentifier', 'flowIdentifier', ], 'members' => [ 'aliasIdentifier' => [ 'shape' => 'FlowAliasIdentifier', 'location' => 'uri', 'locationName' => 'aliasIdentifier', ], 'flowIdentifier' => [ 'shape' => 'FlowIdentifier', 'location' => 'uri', 'locationName' => 'flowIdentifier', ], ], ], 'GetFlowAliasResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdAt', 'flowId', 'id', 'name', 'routingConfiguration', 'updatedAt', ], 'members' => [ 'arn' => [ 'shape' => 'FlowAliasArn', ], 'concurrencyConfiguration' => [ 'shape' => 'FlowAliasConcurrencyConfiguration', ], 'createdAt' => [ 'shape' => 'DateTimestamp', ], 'description' => [ 'shape' => 'Description', ], 'flowId' => [ 'shape' => 'FlowId', ], 'id' => [ 'shape' => 'FlowAliasId', ], 'name' => [ 'shape' => 'Name', ], 'routingConfiguration' => [ 'shape' => 'FlowAliasRoutingConfiguration', ], 'updatedAt' => [ 'shape' => 'DateTimestamp', ], ], ], 'GetFlowRequest' => [ 'type' => 'structure', 'required' => [ 'flowIdentifier', ], 'members' => [ 'flowIdentifier' => [ 'shape' => 'FlowIdentifier', 'location' => 'uri', 'locationName' => 'flowIdentifier', ], ], ], 'GetFlowResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdAt', 'executionRoleArn', 'id', 'name', 'status', 'updatedAt', 'version', ], 'members' => [ 'arn' => [ 'shape' => 'FlowArn', ], 'createdAt' => [ 'shape' => 'DateTimestamp', ], 'customerEncryptionKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'definition' => [ 'shape' => 'FlowDefinition', ], 'description' => [ 'shape' => 'FlowDescription', ], 'executionRoleArn' => [ 'shape' => 'FlowExecutionRoleArn', ], 'id' => [ 'shape' => 'FlowId', ], 'name' => [ 'shape' => 'FlowName', ], 'status' => [ 'shape' => 'FlowStatus', ], 'updatedAt' => [ 'shape' => 'DateTimestamp', ], 'validations' => [ 'shape' => 'FlowValidations', ], 'version' => [ 'shape' => 'DraftVersion', ], ], ], 'GetFlowVersionRequest' => [ 'type' => 'structure', 'required' => [ 'flowIdentifier', 'flowVersion', ], 'members' => [ 'flowIdentifier' => [ 'shape' => 'FlowIdentifier', 'location' => 'uri', 'locationName' => 'flowIdentifier', ], 'flowVersion' => [ 'shape' => 'NumericalVersion', 'location' => 'uri', 'locationName' => 'flowVersion', ], ], ], 'GetFlowVersionResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdAt', 'executionRoleArn', 'id', 'name', 'status', 'version', ], 'members' => [ 'arn' => [ 'shape' => 'FlowArn', ], 'createdAt' => [ 'shape' => 'DateTimestamp', ], 'customerEncryptionKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'definition' => [ 'shape' => 'FlowDefinition', ], 'description' => [ 'shape' => 'FlowDescription', ], 'executionRoleArn' => [ 'shape' => 'FlowExecutionRoleArn', ], 'id' => [ 'shape' => 'FlowId', ], 'name' => [ 'shape' => 'FlowName', ], 'status' => [ 'shape' => 'FlowStatus', ], 'version' => [ 'shape' => 'NumericalVersion', ], ], ], 'GetIngestionJobRequest' => [ 'type' => 'structure', 'required' => [ 'dataSourceId', 'ingestionJobId', 'knowledgeBaseId', ], 'members' => [ 'dataSourceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'dataSourceId', ], 'ingestionJobId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'ingestionJobId', ], 'knowledgeBaseId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], ], ], 'GetIngestionJobResponse' => [ 'type' => 'structure', 'required' => [ 'ingestionJob', ], 'members' => [ 'ingestionJob' => [ 'shape' => 'IngestionJob', ], ], ], 'GetKnowledgeBaseDocumentsRequest' => [ 'type' => 'structure', 'required' => [ 'dataSourceId', 'documentIdentifiers', 'knowledgeBaseId', ], 'members' => [ 'dataSourceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'dataSourceId', ], 'documentIdentifiers' => [ 'shape' => 'DocumentIdentifiers', ], 'knowledgeBaseId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], ], ], 'GetKnowledgeBaseDocumentsResponse' => [ 'type' => 'structure', 'members' => [ 'documentDetails' => [ 'shape' => 'KnowledgeBaseDocumentDetails', ], ], ], 'GetKnowledgeBaseRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], ], ], 'GetKnowledgeBaseResponse' => [ 'type' => 'structure', 'required' => [ 'knowledgeBase', ], 'members' => [ 'knowledgeBase' => [ 'shape' => 'KnowledgeBase', ], ], ], 'GetPromptRequest' => [ 'type' => 'structure', 'required' => [ 'promptIdentifier', ], 'members' => [ 'promptIdentifier' => [ 'shape' => 'PromptIdentifier', 'location' => 'uri', 'locationName' => 'promptIdentifier', ], 'promptVersion' => [ 'shape' => 'Version', 'location' => 'querystring', 'locationName' => 'promptVersion', ], ], ], 'GetPromptResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdAt', 'id', 'name', 'updatedAt', 'version', ], 'members' => [ 'arn' => [ 'shape' => 'PromptArn', ], 'createdAt' => [ 'shape' => 'DateTimestamp', ], 'customerEncryptionKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'defaultVariant' => [ 'shape' => 'PromptVariantName', ], 'description' => [ 'shape' => 'PromptDescription', ], 'id' => [ 'shape' => 'PromptId', ], 'name' => [ 'shape' => 'PromptName', ], 'updatedAt' => [ 'shape' => 'DateTimestamp', ], 'variants' => [ 'shape' => 'PromptVariantList', ], 'version' => [ 'shape' => 'Version', ], ], ], 'GraphArn' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^arn:aws(|-cn|-us-gov):neptune-graph:[a-zA-Z0-9-]*:[0-9]{12}:graph/g-[a-zA-Z0-9]{10}$', 'sensitive' => true, ], 'GuardrailConfiguration' => [ 'type' => 'structure', 'members' => [ 'guardrailIdentifier' => [ 'shape' => 'GuardrailIdentifier', ], 'guardrailVersion' => [ 'shape' => 'GuardrailVersion', ], ], ], 'GuardrailIdentifier' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '^(([a-z0-9]+)|(arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:guardrail/[a-z0-9]+))$', ], 'GuardrailVersion' => [ 'type' => 'string', 'pattern' => '^(([0-9]{1,8})|(DRAFT))$', ], 'HierarchicalChunkingConfiguration' => [ 'type' => 'structure', 'required' => [ 'levelConfigurations', 'overlapTokens', ], 'members' => [ 'levelConfigurations' => [ 'shape' => 'HierarchicalChunkingLevelConfigurations', ], 'overlapTokens' => [ 'shape' => 'HierarchicalChunkingConfigurationOverlapTokensInteger', ], ], ], 'HierarchicalChunkingConfigurationOverlapTokensInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'HierarchicalChunkingLevelConfiguration' => [ 'type' => 'structure', 'required' => [ 'maxTokens', ], 'members' => [ 'maxTokens' => [ 'shape' => 'HierarchicalChunkingLevelConfigurationMaxTokensInteger', ], ], ], 'HierarchicalChunkingLevelConfigurationMaxTokensInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 8192, 'min' => 1, ], 'HierarchicalChunkingLevelConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'HierarchicalChunkingLevelConfiguration', ], 'max' => 2, 'min' => 2, ], 'HttpsUrl' => [ 'type' => 'string', 'pattern' => '^https://[A-Za-z0-9][^\\s]*$', ], 'Id' => [ 'type' => 'string', 'pattern' => '^[0-9a-zA-Z]{10}$', ], 'IncludeExclude' => [ 'type' => 'string', 'enum' => [ 'INCLUDE', 'EXCLUDE', ], ], 'IncompatibleConnectionDataTypeFlowValidationDetails' => [ 'type' => 'structure', 'required' => [ 'connection', ], 'members' => [ 'connection' => [ 'shape' => 'FlowConnectionName', ], ], ], 'IncompatibleLoopNodeType' => [ 'type' => 'string', 'enum' => [ 'Input', 'Condition', 'Iterator', 'Collector', ], ], 'InferenceConfiguration' => [ 'type' => 'structure', 'members' => [ 'maximumLength' => [ 'shape' => 'MaximumLength', ], 'stopSequences' => [ 'shape' => 'StopSequences', ], 'temperature' => [ 'shape' => 'Temperature', ], 'topK' => [ 'shape' => 'TopK', ], 'topP' => [ 'shape' => 'TopP', ], ], ], 'IngestKnowledgeBaseDocumentsRequest' => [ 'type' => 'structure', 'required' => [ 'dataSourceId', 'documents', 'knowledgeBaseId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'dataSourceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'dataSourceId', ], 'documents' => [ 'shape' => 'KnowledgeBaseDocuments', ], 'knowledgeBaseId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], ], ], 'IngestKnowledgeBaseDocumentsResponse' => [ 'type' => 'structure', 'members' => [ 'documentDetails' => [ 'shape' => 'KnowledgeBaseDocumentDetails', ], ], ], 'IngestionJob' => [ 'type' => 'structure', 'required' => [ 'dataSourceId', 'ingestionJobId', 'knowledgeBaseId', 'startedAt', 'status', 'updatedAt', ], 'members' => [ 'dataSourceId' => [ 'shape' => 'Id', ], 'description' => [ 'shape' => 'Description', ], 'failureReasons' => [ 'shape' => 'FailureReasons', ], 'ingestionJobId' => [ 'shape' => 'Id', ], 'knowledgeBaseId' => [ 'shape' => 'Id', ], 'startedAt' => [ 'shape' => 'DateTimestamp', ], 'statistics' => [ 'shape' => 'IngestionJobStatistics', ], 'status' => [ 'shape' => 'IngestionJobStatus', ], 'updatedAt' => [ 'shape' => 'DateTimestamp', ], ], ], 'IngestionJobFilter' => [ 'type' => 'structure', 'required' => [ 'attribute', 'operator', 'values', ], 'members' => [ 'attribute' => [ 'shape' => 'IngestionJobFilterAttribute', ], 'operator' => [ 'shape' => 'IngestionJobFilterOperator', ], 'values' => [ 'shape' => 'IngestionJobFilterValues', ], ], ], 'IngestionJobFilterAttribute' => [ 'type' => 'string', 'enum' => [ 'STATUS', ], ], 'IngestionJobFilterOperator' => [ 'type' => 'string', 'enum' => [ 'EQ', ], ], 'IngestionJobFilterValue' => [ 'type' => 'string', 'max' => 100, 'min' => 0, 'pattern' => '^.*$', ], 'IngestionJobFilterValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'IngestionJobFilterValue', ], 'max' => 10, 'min' => 0, ], 'IngestionJobFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'IngestionJobFilter', ], 'max' => 1, 'min' => 1, ], 'IngestionJobSortBy' => [ 'type' => 'structure', 'required' => [ 'attribute', 'order', ], 'members' => [ 'attribute' => [ 'shape' => 'IngestionJobSortByAttribute', ], 'order' => [ 'shape' => 'SortOrder', ], ], ], 'IngestionJobSortByAttribute' => [ 'type' => 'string', 'enum' => [ 'STATUS', 'STARTED_AT', ], ], 'IngestionJobStatistics' => [ 'type' => 'structure', 'members' => [ 'numberOfDocumentsDeleted' => [ 'shape' => 'PrimitiveLong', ], 'numberOfDocumentsFailed' => [ 'shape' => 'PrimitiveLong', ], 'numberOfDocumentsScanned' => [ 'shape' => 'PrimitiveLong', ], 'numberOfMetadataDocumentsModified' => [ 'shape' => 'PrimitiveLong', ], 'numberOfMetadataDocumentsScanned' => [ 'shape' => 'PrimitiveLong', ], 'numberOfModifiedDocumentsIndexed' => [ 'shape' => 'PrimitiveLong', ], 'numberOfNewDocumentsIndexed' => [ 'shape' => 'PrimitiveLong', ], ], ], 'IngestionJobStatus' => [ 'type' => 'string', 'enum' => [ 'STARTING', 'IN_PROGRESS', 'COMPLETE', 'FAILED', 'STOPPING', 'STOPPED', ], ], 'IngestionJobSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'IngestionJobSummary', ], ], 'IngestionJobSummary' => [ 'type' => 'structure', 'required' => [ 'dataSourceId', 'ingestionJobId', 'knowledgeBaseId', 'startedAt', 'status', 'updatedAt', ], 'members' => [ 'dataSourceId' => [ 'shape' => 'Id', ], 'description' => [ 'shape' => 'Description', ], 'ingestionJobId' => [ 'shape' => 'Id', ], 'knowledgeBaseId' => [ 'shape' => 'Id', ], 'startedAt' => [ 'shape' => 'DateTimestamp', ], 'statistics' => [ 'shape' => 'IngestionJobStatistics', ], 'status' => [ 'shape' => 'IngestionJobStatus', ], 'updatedAt' => [ 'shape' => 'DateTimestamp', ], ], ], 'InlineCode' => [ 'type' => 'string', 'max' => 5000000, 'min' => 1, 'sensitive' => true, ], 'InlineCodeFlowNodeConfiguration' => [ 'type' => 'structure', 'required' => [ 'code', 'language', ], 'members' => [ 'code' => [ 'shape' => 'InlineCode', ], 'language' => [ 'shape' => 'SupportedLanguages', ], ], ], 'InlineContent' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'byteContent' => [ 'shape' => 'ByteContentDoc', ], 'textContent' => [ 'shape' => 'TextContentDoc', ], 'type' => [ 'shape' => 'InlineContentType', ], ], ], 'InlineContentType' => [ 'type' => 'string', 'enum' => [ 'BYTE', 'TEXT', ], ], 'InputFlowNodeConfiguration' => [ 'type' => 'structure', 'members' => [], ], 'Instruction' => [ 'type' => 'string', 'max' => 4000, 'min' => 40, 'sensitive' => true, ], 'IntermediateStorage' => [ 'type' => 'structure', 'required' => [ 's3Location', ], 'members' => [ 's3Location' => [ 'shape' => 'S3Location', ], ], ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'InvalidLoopBoundaryFlowValidationDetails' => [ 'type' => 'structure', 'required' => [ 'connection', 'source', 'target', ], 'members' => [ 'connection' => [ 'shape' => 'FlowConnectionName', ], 'source' => [ 'shape' => 'FlowNodeName', ], 'target' => [ 'shape' => 'FlowNodeName', ], ], ], 'IteratorFlowNodeConfiguration' => [ 'type' => 'structure', 'members' => [], ], 'KendraIndexArn' => [ 'type' => 'string', 'pattern' => '^arn:aws(|-cn|-us-gov):kendra:[a-z0-9-]{1,20}:([0-9]{12}|):index/([a-zA-Z0-9][a-zA-Z0-9-]{35}|[a-zA-Z0-9][a-zA-Z0-9-]{35}-[a-zA-Z0-9][a-zA-Z0-9-]{35})$', ], 'KendraKnowledgeBaseConfiguration' => [ 'type' => 'structure', 'required' => [ 'kendraIndexArn', ], 'members' => [ 'kendraIndexArn' => [ 'shape' => 'KendraIndexArn', ], ], ], 'Key' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'sensitive' => true, ], 'KmsKeyArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^arn:aws(|-cn|-us-gov):kms:[a-zA-Z0-9-]*:[0-9]{12}:key/[a-zA-Z0-9-]{36}$', ], 'KnowledgeBase' => [ 'type' => 'structure', 'required' => [ 'createdAt', 'knowledgeBaseArn', 'knowledgeBaseConfiguration', 'knowledgeBaseId', 'name', 'roleArn', 'status', 'updatedAt', ], 'members' => [ 'createdAt' => [ 'shape' => 'DateTimestamp', ], 'description' => [ 'shape' => 'Description', ], 'failureReasons' => [ 'shape' => 'FailureReasons', ], 'knowledgeBaseArn' => [ 'shape' => 'KnowledgeBaseArn', ], 'knowledgeBaseConfiguration' => [ 'shape' => 'KnowledgeBaseConfiguration', ], 'knowledgeBaseId' => [ 'shape' => 'Id', ], 'name' => [ 'shape' => 'Name', ], 'roleArn' => [ 'shape' => 'KnowledgeBaseRoleArn', ], 'status' => [ 'shape' => 'KnowledgeBaseStatus', ], 'storageConfiguration' => [ 'shape' => 'StorageConfiguration', ], 'updatedAt' => [ 'shape' => 'DateTimestamp', ], ], ], 'KnowledgeBaseArn' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => '^arn:aws(|-cn|-us-gov):bedrock:[a-zA-Z0-9-]*:[0-9]{12}:knowledge-base/[0-9a-zA-Z]+$', ], 'KnowledgeBaseConfiguration' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'kendraKnowledgeBaseConfiguration' => [ 'shape' => 'KendraKnowledgeBaseConfiguration', ], 'sqlKnowledgeBaseConfiguration' => [ 'shape' => 'SqlKnowledgeBaseConfiguration', ], 'type' => [ 'shape' => 'KnowledgeBaseType', ], 'vectorKnowledgeBaseConfiguration' => [ 'shape' => 'VectorKnowledgeBaseConfiguration', ], ], ], 'KnowledgeBaseDocument' => [ 'type' => 'structure', 'required' => [ 'content', ], 'members' => [ 'content' => [ 'shape' => 'DocumentContent', ], 'metadata' => [ 'shape' => 'DocumentMetadata', ], ], ], 'KnowledgeBaseDocumentDetail' => [ 'type' => 'structure', 'required' => [ 'dataSourceId', 'identifier', 'knowledgeBaseId', 'status', ], 'members' => [ 'dataSourceId' => [ 'shape' => 'Id', ], 'identifier' => [ 'shape' => 'DocumentIdentifier', ], 'knowledgeBaseId' => [ 'shape' => 'Id', ], 'status' => [ 'shape' => 'DocumentStatus', ], 'statusReason' => [ 'shape' => 'String', ], 'updatedAt' => [ 'shape' => 'DateTimestamp', ], ], ], 'KnowledgeBaseDocumentDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'KnowledgeBaseDocumentDetail', ], ], 'KnowledgeBaseDocuments' => [ 'type' => 'list', 'member' => [ 'shape' => 'KnowledgeBaseDocument', ], 'max' => 10, 'min' => 1, ], 'KnowledgeBaseFlowNodeConfiguration' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', ], 'members' => [ 'guardrailConfiguration' => [ 'shape' => 'GuardrailConfiguration', ], 'inferenceConfiguration' => [ 'shape' => 'PromptInferenceConfiguration', ], 'knowledgeBaseId' => [ 'shape' => 'KnowledgeBaseId', ], 'modelId' => [ 'shape' => 'KnowledgeBaseModelIdentifier', ], 'numberOfResults' => [ 'shape' => 'KnowledgeBaseFlowNodeConfigurationNumberOfResultsInteger', ], 'orchestrationConfiguration' => [ 'shape' => 'KnowledgeBaseOrchestrationConfiguration', ], 'promptTemplate' => [ 'shape' => 'KnowledgeBasePromptTemplate', ], 'rerankingConfiguration' => [ 'shape' => 'VectorSearchRerankingConfiguration', ], ], ], 'KnowledgeBaseFlowNodeConfigurationNumberOfResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'KnowledgeBaseId' => [ 'type' => 'string', 'max' => 10, 'min' => 0, 'pattern' => '^[0-9a-zA-Z]+$', ], 'KnowledgeBaseModelIdentifier' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^(arn:aws(-[^:]{1,12})?:(bedrock|sagemaker):[a-z0-9-]{1,20}:([0-9]{12})?:([a-z-]+/)?)?([a-zA-Z0-9.-]{1,63}){0,2}(([:][a-z0-9-]{1,63}){0,2})?(/[a-z0-9]{1,12})?$', ], 'KnowledgeBaseOrchestrationConfiguration' => [ 'type' => 'structure', 'members' => [ 'additionalModelRequestFields' => [ 'shape' => 'AdditionalModelRequestFields', ], 'inferenceConfig' => [ 'shape' => 'PromptInferenceConfiguration', ], 'performanceConfig' => [ 'shape' => 'PerformanceConfiguration', ], 'promptTemplate' => [ 'shape' => 'KnowledgeBasePromptTemplate', ], ], ], 'KnowledgeBasePromptTemplate' => [ 'type' => 'structure', 'members' => [ 'textPromptTemplate' => [ 'shape' => 'KnowledgeBaseTextPrompt', ], ], ], 'KnowledgeBaseRoleArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '^arn:aws(-[^:]+)?:iam::([0-9]{12})?:role/.+$', ], 'KnowledgeBaseState' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'KnowledgeBaseStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'DELETING', 'UPDATING', 'FAILED', 'DELETE_UNSUCCESSFUL', ], ], 'KnowledgeBaseStorageType' => [ 'type' => 'string', 'enum' => [ 'OPENSEARCH_SERVERLESS', 'PINECONE', 'REDIS_ENTERPRISE_CLOUD', 'RDS', 'MONGO_DB_ATLAS', 'NEPTUNE_ANALYTICS', 'OPENSEARCH_MANAGED_CLUSTER', ], ], 'KnowledgeBaseSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'KnowledgeBaseSummary', ], ], 'KnowledgeBaseSummary' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'name', 'status', 'updatedAt', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'knowledgeBaseId' => [ 'shape' => 'Id', ], 'name' => [ 'shape' => 'Name', ], 'status' => [ 'shape' => 'KnowledgeBaseStatus', ], 'updatedAt' => [ 'shape' => 'DateTimestamp', ], ], ], 'KnowledgeBaseTextPrompt' => [ 'type' => 'string', 'max' => 100000, 'min' => 1, 'sensitive' => true, ], 'KnowledgeBaseType' => [ 'type' => 'string', 'enum' => [ 'VECTOR', 'KENDRA', 'SQL', ], ], 'LambdaArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '^arn:(aws[a-zA-Z-]*)?:lambda:[a-z]{2}(-gov)?-[a-z]+-\\d{1}:\\d{12}:function:[a-zA-Z0-9-_\\.]+(:(\\$LATEST|[a-zA-Z0-9-_]+))?$', ], 'LambdaFunctionFlowNodeConfiguration' => [ 'type' => 'structure', 'required' => [ 'lambdaArn', ], 'members' => [ 'lambdaArn' => [ 'shape' => 'LambdaArn', ], ], ], 'LexBotAliasArn' => [ 'type' => 'string', 'max' => 78, 'min' => 0, 'pattern' => '^arn:aws(|-us-gov):lex:[a-z]{2}(-gov)?-[a-z]+-\\d{1}:\\d{12}:bot-alias/[0-9a-zA-Z]+/[0-9a-zA-Z]+$', ], 'LexBotLocaleId' => [ 'type' => 'string', 'max' => 10, 'min' => 1, ], 'LexFlowNodeConfiguration' => [ 'type' => 'structure', 'required' => [ 'botAliasArn', 'localeId', ], 'members' => [ 'botAliasArn' => [ 'shape' => 'LexBotAliasArn', ], 'localeId' => [ 'shape' => 'LexBotLocaleId', ], ], ], 'ListAgentActionGroupsRequest' => [ 'type' => 'structure', 'required' => [ 'agentId', 'agentVersion', ], 'members' => [ 'agentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'agentId', ], 'agentVersion' => [ 'shape' => 'Version', 'location' => 'uri', 'locationName' => 'agentVersion', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAgentActionGroupsResponse' => [ 'type' => 'structure', 'required' => [ 'actionGroupSummaries', ], 'members' => [ 'actionGroupSummaries' => [ 'shape' => 'ActionGroupSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAgentAliasesRequest' => [ 'type' => 'structure', 'required' => [ 'agentId', ], 'members' => [ 'agentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'agentId', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAgentAliasesResponse' => [ 'type' => 'structure', 'required' => [ 'agentAliasSummaries', ], 'members' => [ 'agentAliasSummaries' => [ 'shape' => 'AgentAliasSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAgentCollaboratorsRequest' => [ 'type' => 'structure', 'required' => [ 'agentId', 'agentVersion', ], 'members' => [ 'agentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'agentId', ], 'agentVersion' => [ 'shape' => 'Version', 'location' => 'uri', 'locationName' => 'agentVersion', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAgentCollaboratorsResponse' => [ 'type' => 'structure', 'required' => [ 'agentCollaboratorSummaries', ], 'members' => [ 'agentCollaboratorSummaries' => [ 'shape' => 'AgentCollaboratorSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAgentKnowledgeBasesRequest' => [ 'type' => 'structure', 'required' => [ 'agentId', 'agentVersion', ], 'members' => [ 'agentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'agentId', ], 'agentVersion' => [ 'shape' => 'Version', 'location' => 'uri', 'locationName' => 'agentVersion', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAgentKnowledgeBasesResponse' => [ 'type' => 'structure', 'required' => [ 'agentKnowledgeBaseSummaries', ], 'members' => [ 'agentKnowledgeBaseSummaries' => [ 'shape' => 'AgentKnowledgeBaseSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAgentVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'agentId', ], 'members' => [ 'agentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'agentId', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAgentVersionsResponse' => [ 'type' => 'structure', 'required' => [ 'agentVersionSummaries', ], 'members' => [ 'agentVersionSummaries' => [ 'shape' => 'AgentVersionSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAgentsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAgentsResponse' => [ 'type' => 'structure', 'required' => [ 'agentSummaries', ], 'members' => [ 'agentSummaries' => [ 'shape' => 'AgentSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDataSourcesRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDataSourcesResponse' => [ 'type' => 'structure', 'required' => [ 'dataSourceSummaries', ], 'members' => [ 'dataSourceSummaries' => [ 'shape' => 'DataSourceSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListFlowAliasesRequest' => [ 'type' => 'structure', 'required' => [ 'flowIdentifier', ], 'members' => [ 'flowIdentifier' => [ 'shape' => 'FlowIdentifier', 'location' => 'uri', 'locationName' => 'flowIdentifier', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListFlowAliasesResponse' => [ 'type' => 'structure', 'required' => [ 'flowAliasSummaries', ], 'members' => [ 'flowAliasSummaries' => [ 'shape' => 'FlowAliasSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListFlowVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'flowIdentifier', ], 'members' => [ 'flowIdentifier' => [ 'shape' => 'FlowIdentifier', 'location' => 'uri', 'locationName' => 'flowIdentifier', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListFlowVersionsResponse' => [ 'type' => 'structure', 'required' => [ 'flowVersionSummaries', ], 'members' => [ 'flowVersionSummaries' => [ 'shape' => 'FlowVersionSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListFlowsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListFlowsResponse' => [ 'type' => 'structure', 'required' => [ 'flowSummaries', ], 'members' => [ 'flowSummaries' => [ 'shape' => 'FlowSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListIngestionJobsRequest' => [ 'type' => 'structure', 'required' => [ 'dataSourceId', 'knowledgeBaseId', ], 'members' => [ 'dataSourceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'dataSourceId', ], 'filters' => [ 'shape' => 'IngestionJobFilters', ], 'knowledgeBaseId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'sortBy' => [ 'shape' => 'IngestionJobSortBy', ], ], ], 'ListIngestionJobsResponse' => [ 'type' => 'structure', 'required' => [ 'ingestionJobSummaries', ], 'members' => [ 'ingestionJobSummaries' => [ 'shape' => 'IngestionJobSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListKnowledgeBaseDocumentsRequest' => [ 'type' => 'structure', 'required' => [ 'dataSourceId', 'knowledgeBaseId', ], 'members' => [ 'dataSourceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'dataSourceId', ], 'knowledgeBaseId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListKnowledgeBaseDocumentsResponse' => [ 'type' => 'structure', 'required' => [ 'documentDetails', ], 'members' => [ 'documentDetails' => [ 'shape' => 'KnowledgeBaseDocumentDetails', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListKnowledgeBasesRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListKnowledgeBasesResponse' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseSummaries', ], 'members' => [ 'knowledgeBaseSummaries' => [ 'shape' => 'KnowledgeBaseSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListPromptsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'promptIdentifier' => [ 'shape' => 'PromptIdentifier', 'location' => 'querystring', 'locationName' => 'promptIdentifier', ], ], ], 'ListPromptsResponse' => [ 'type' => 'structure', 'required' => [ 'promptSummaries', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'promptSummaries' => [ 'shape' => 'PromptSummaries', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'TaggableResourcesArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'LoopControllerFlowNodeConfiguration' => [ 'type' => 'structure', 'required' => [ 'continueCondition', ], 'members' => [ 'continueCondition' => [ 'shape' => 'FlowCondition', ], 'maxIterations' => [ 'shape' => 'LoopControllerFlowNodeConfigurationMaxIterationsInteger', ], ], ], 'LoopControllerFlowNodeConfigurationMaxIterationsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 1, ], 'LoopFlowNodeConfiguration' => [ 'type' => 'structure', 'required' => [ 'definition', ], 'members' => [ 'definition' => [ 'shape' => 'FlowDefinition', ], ], ], 'LoopIncompatibleNodeTypeFlowValidationDetails' => [ 'type' => 'structure', 'required' => [ 'incompatibleNodeName', 'incompatibleNodeType', 'node', ], 'members' => [ 'incompatibleNodeName' => [ 'shape' => 'FlowNodeName', ], 'incompatibleNodeType' => [ 'shape' => 'IncompatibleLoopNodeType', ], 'node' => [ 'shape' => 'FlowNodeName', ], ], ], 'LoopInputFlowNodeConfiguration' => [ 'type' => 'structure', 'members' => [], ], 'MalformedConditionExpressionFlowValidationDetails' => [ 'type' => 'structure', 'required' => [ 'cause', 'condition', 'node', ], 'members' => [ 'cause' => [ 'shape' => 'ErrorMessage', ], 'condition' => [ 'shape' => 'FlowConditionName', ], 'node' => [ 'shape' => 'FlowNodeName', ], ], ], 'MalformedNodeInputExpressionFlowValidationDetails' => [ 'type' => 'structure', 'required' => [ 'cause', 'input', 'node', ], 'members' => [ 'cause' => [ 'shape' => 'ErrorMessage', ], 'input' => [ 'shape' => 'FlowNodeInputName', ], 'node' => [ 'shape' => 'FlowNodeName', ], ], ], 'MaxRecentSessions' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 1, ], 'MaximumLength' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'MemoryConfiguration' => [ 'type' => 'structure', 'required' => [ 'enabledMemoryTypes', ], 'members' => [ 'enabledMemoryTypes' => [ 'shape' => 'EnabledMemoryTypes', ], 'sessionSummaryConfiguration' => [ 'shape' => 'SessionSummaryConfiguration', ], 'storageDays' => [ 'shape' => 'StorageDays', ], ], ], 'MemoryType' => [ 'type' => 'string', 'enum' => [ 'SESSION_SUMMARY', ], ], 'Message' => [ 'type' => 'structure', 'required' => [ 'content', 'role', ], 'members' => [ 'content' => [ 'shape' => 'ContentBlocks', ], 'role' => [ 'shape' => 'ConversationRole', ], ], ], 'Messages' => [ 'type' => 'list', 'member' => [ 'shape' => 'Message', ], ], 'MetadataAttribute' => [ 'type' => 'structure', 'required' => [ 'key', 'value', ], 'members' => [ 'key' => [ 'shape' => 'Key', ], 'value' => [ 'shape' => 'MetadataAttributeValue', ], ], ], 'MetadataAttributeValue' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'booleanValue' => [ 'shape' => 'Boolean', ], 'numberValue' => [ 'shape' => 'NumberValue', ], 'stringListValue' => [ 'shape' => 'MetadataAttributeValueStringListValueList', ], 'stringValue' => [ 'shape' => 'StringValue', ], 'type' => [ 'shape' => 'MetadataValueType', ], ], ], 'MetadataAttributeValueStringListValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StringValue', ], 'max' => 10, 'min' => 1, ], 'MetadataConfigurationForReranking' => [ 'type' => 'structure', 'required' => [ 'selectionMode', ], 'members' => [ 'selectionMode' => [ 'shape' => 'RerankingMetadataSelectionMode', ], 'selectiveModeConfiguration' => [ 'shape' => 'RerankingMetadataSelectiveModeConfiguration', ], ], ], 'MetadataSourceType' => [ 'type' => 'string', 'enum' => [ 'IN_LINE_ATTRIBUTE', 'S3_LOCATION', ], ], 'MetadataValueType' => [ 'type' => 'string', 'enum' => [ 'BOOLEAN', 'NUMBER', 'STRING', 'STRING_LIST', ], ], 'Microsoft365TenantId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$', ], 'MismatchedNodeInputTypeFlowValidationDetails' => [ 'type' => 'structure', 'required' => [ 'expectedType', 'input', 'node', ], 'members' => [ 'expectedType' => [ 'shape' => 'FlowNodeIODataType', ], 'input' => [ 'shape' => 'FlowNodeInputName', ], 'node' => [ 'shape' => 'FlowNodeName', ], ], ], 'MismatchedNodeOutputTypeFlowValidationDetails' => [ 'type' => 'structure', 'required' => [ 'expectedType', 'node', 'output', ], 'members' => [ 'expectedType' => [ 'shape' => 'FlowNodeIODataType', ], 'node' => [ 'shape' => 'FlowNodeName', ], 'output' => [ 'shape' => 'FlowNodeOutputName', ], ], ], 'MissingConnectionConfigurationFlowValidationDetails' => [ 'type' => 'structure', 'required' => [ 'connection', ], 'members' => [ 'connection' => [ 'shape' => 'FlowConnectionName', ], ], ], 'MissingDefaultConditionFlowValidationDetails' => [ 'type' => 'structure', 'required' => [ 'node', ], 'members' => [ 'node' => [ 'shape' => 'FlowNodeName', ], ], ], 'MissingEndingNodesFlowValidationDetails' => [ 'type' => 'structure', 'members' => [], ], 'MissingLoopControllerNodeFlowValidationDetails' => [ 'type' => 'structure', 'required' => [ 'loopNode', ], 'members' => [ 'loopNode' => [ 'shape' => 'FlowNodeName', ], ], ], 'MissingLoopInputNodeFlowValidationDetails' => [ 'type' => 'structure', 'required' => [ 'loopNode', ], 'members' => [ 'loopNode' => [ 'shape' => 'FlowNodeName', ], ], ], 'MissingNodeConfigurationFlowValidationDetails' => [ 'type' => 'structure', 'required' => [ 'node', ], 'members' => [ 'node' => [ 'shape' => 'FlowNodeName', ], ], ], 'MissingNodeInputFlowValidationDetails' => [ 'type' => 'structure', 'required' => [ 'input', 'node', ], 'members' => [ 'input' => [ 'shape' => 'FlowNodeInputName', ], 'node' => [ 'shape' => 'FlowNodeName', ], ], ], 'MissingNodeOutputFlowValidationDetails' => [ 'type' => 'structure', 'required' => [ 'node', 'output', ], 'members' => [ 'node' => [ 'shape' => 'FlowNodeName', ], 'output' => [ 'shape' => 'FlowNodeOutputName', ], ], ], 'MissingStartingNodesFlowValidationDetails' => [ 'type' => 'structure', 'members' => [], ], 'ModelIdentifier' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^(arn:aws(-[^:]{1,12})?:(bedrock|sagemaker):[a-z0-9-]{1,20}:([0-9]{12})?:([a-z-]+/)?)?([a-zA-Z0-9.-]{1,63}){0,2}(([:][a-z0-9-]{1,63}){0,2})?(/[a-z0-9]{1,12})?$', ], 'MongoDbAtlasCollectionName' => [ 'type' => 'string', 'max' => 63, 'min' => 0, 'pattern' => '^.*$', ], 'MongoDbAtlasConfiguration' => [ 'type' => 'structure', 'required' => [ 'collectionName', 'credentialsSecretArn', 'databaseName', 'endpoint', 'fieldMapping', 'vectorIndexName', ], 'members' => [ 'collectionName' => [ 'shape' => 'MongoDbAtlasCollectionName', ], 'credentialsSecretArn' => [ 'shape' => 'SecretArn', ], 'databaseName' => [ 'shape' => 'MongoDbAtlasDatabaseName', ], 'endpoint' => [ 'shape' => 'MongoDbAtlasEndpoint', ], 'endpointServiceName' => [ 'shape' => 'MongoDbAtlasEndpointServiceName', ], 'fieldMapping' => [ 'shape' => 'MongoDbAtlasFieldMapping', ], 'textIndexName' => [ 'shape' => 'MongoDbAtlasIndexName', ], 'vectorIndexName' => [ 'shape' => 'MongoDbAtlasIndexName', ], ], ], 'MongoDbAtlasDatabaseName' => [ 'type' => 'string', 'max' => 63, 'min' => 0, 'pattern' => '^.*$', ], 'MongoDbAtlasEndpoint' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '^.*$', ], 'MongoDbAtlasEndpointServiceName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^(?:arn:aws(?:-us-gov|-cn|-iso|-iso-[a-z])*:.+:.*:\\d+:.+/.+$|[a-zA-Z0-9*]+[a-zA-Z0-9._-]*)$', ], 'MongoDbAtlasFieldMapping' => [ 'type' => 'structure', 'required' => [ 'metadataField', 'textField', 'vectorField', ], 'members' => [ 'metadataField' => [ 'shape' => 'FieldName', ], 'textField' => [ 'shape' => 'FieldName', ], 'vectorField' => [ 'shape' => 'FieldName', ], ], ], 'MongoDbAtlasIndexName' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '^.*$', ], 'MultipleLoopControllerNodesFlowValidationDetails' => [ 'type' => 'structure', 'required' => [ 'loopNode', ], 'members' => [ 'loopNode' => [ 'shape' => 'FlowNodeName', ], ], ], 'MultipleLoopInputNodesFlowValidationDetails' => [ 'type' => 'structure', 'required' => [ 'loopNode', ], 'members' => [ 'loopNode' => [ 'shape' => 'FlowNodeName', ], ], ], 'MultipleNodeInputConnectionsFlowValidationDetails' => [ 'type' => 'structure', 'required' => [ 'input', 'node', ], 'members' => [ 'input' => [ 'shape' => 'FlowNodeInputName', ], 'node' => [ 'shape' => 'FlowNodeName', ], ], ], 'Name' => [ 'type' => 'string', 'pattern' => '^([0-9a-zA-Z][_-]?){1,100}$', ], 'NaturalLanguageString' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, ], 'NeptuneAnalyticsConfiguration' => [ 'type' => 'structure', 'required' => [ 'fieldMapping', 'graphArn', ], 'members' => [ 'fieldMapping' => [ 'shape' => 'NeptuneAnalyticsFieldMapping', ], 'graphArn' => [ 'shape' => 'GraphArn', ], ], ], 'NeptuneAnalyticsFieldMapping' => [ 'type' => 'structure', 'required' => [ 'metadataField', 'textField', ], 'members' => [ 'metadataField' => [ 'shape' => 'FieldName', ], 'textField' => [ 'shape' => 'FieldName', ], ], ], 'NextToken' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^\\S*$', ], 'NonBlankString' => [ 'type' => 'string', 'pattern' => '^[\\s\\S]+$', ], 'NonEmptyString' => [ 'type' => 'string', 'min' => 1, ], 'NumberValue' => [ 'type' => 'double', 'box' => true, 'sensitive' => true, ], 'NumericalVersion' => [ 'type' => 'string', 'pattern' => '^[0-9]{1,5}$', ], 'OpenSearchManagedClusterConfiguration' => [ 'type' => 'structure', 'required' => [ 'domainArn', 'domainEndpoint', 'fieldMapping', 'vectorIndexName', ], 'members' => [ 'domainArn' => [ 'shape' => 'OpenSearchManagedClusterDomainArn', ], 'domainEndpoint' => [ 'shape' => 'OpenSearchManagedClusterDomainEndpoint', ], 'fieldMapping' => [ 'shape' => 'OpenSearchManagedClusterFieldMapping', ], 'vectorIndexName' => [ 'shape' => 'OpenSearchManagedClusterIndexName', ], ], ], 'OpenSearchManagedClusterDomainArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '^arn:aws(|-cn|-us-gov|-iso):es:[a-z]{2}(-gov)?-[a-z]+-\\d{1}:\\d{12}:domain/[a-z][a-z0-9-]{3,28}$', ], 'OpenSearchManagedClusterDomainEndpoint' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '^https://.*$', ], 'OpenSearchManagedClusterFieldMapping' => [ 'type' => 'structure', 'required' => [ 'metadataField', 'textField', 'vectorField', ], 'members' => [ 'metadataField' => [ 'shape' => 'FieldName', ], 'textField' => [ 'shape' => 'FieldName', ], 'vectorField' => [ 'shape' => 'FieldName', ], ], ], 'OpenSearchManagedClusterIndexName' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^(?![\\-_+.])[a-z0-9][a-z0-9\\-_\\.]*$', 'sensitive' => true, ], 'OpenSearchServerlessCollectionArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '^arn:aws:aoss:[a-z]{2}(-gov)?-[a-z]+-\\d{1}:\\d{12}:collection/[a-z0-9-]{3,32}$', ], 'OpenSearchServerlessConfiguration' => [ 'type' => 'structure', 'required' => [ 'collectionArn', 'fieldMapping', 'vectorIndexName', ], 'members' => [ 'collectionArn' => [ 'shape' => 'OpenSearchServerlessCollectionArn', ], 'fieldMapping' => [ 'shape' => 'OpenSearchServerlessFieldMapping', ], 'vectorIndexName' => [ 'shape' => 'OpenSearchServerlessIndexName', ], ], ], 'OpenSearchServerlessFieldMapping' => [ 'type' => 'structure', 'required' => [ 'metadataField', 'textField', 'vectorField', ], 'members' => [ 'metadataField' => [ 'shape' => 'FieldName', ], 'textField' => [ 'shape' => 'FieldName', ], 'vectorField' => [ 'shape' => 'FieldName', ], ], ], 'OpenSearchServerlessIndexName' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '^.*$', ], 'OrchestrationExecutor' => [ 'type' => 'structure', 'members' => [ 'lambda' => [ 'shape' => 'LambdaArn', ], ], 'union' => true, ], 'OrchestrationType' => [ 'type' => 'string', 'enum' => [ 'DEFAULT', 'CUSTOM_ORCHESTRATION', ], ], 'OutputFlowNodeConfiguration' => [ 'type' => 'structure', 'members' => [], ], 'ParameterDescription' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'ParameterDetail' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'description' => [ 'shape' => 'ParameterDescription', ], 'required' => [ 'shape' => 'Boolean', ], 'type' => [ 'shape' => 'Type', ], ], ], 'ParameterMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'Name', ], 'value' => [ 'shape' => 'ParameterDetail', ], ], 'ParsingConfiguration' => [ 'type' => 'structure', 'required' => [ 'parsingStrategy', ], 'members' => [ 'bedrockDataAutomationConfiguration' => [ 'shape' => 'BedrockDataAutomationConfiguration', ], 'bedrockFoundationModelConfiguration' => [ 'shape' => 'BedrockFoundationModelConfiguration', ], 'parsingStrategy' => [ 'shape' => 'ParsingStrategy', ], ], ], 'ParsingModality' => [ 'type' => 'string', 'enum' => [ 'MULTIMODAL', ], ], 'ParsingPrompt' => [ 'type' => 'structure', 'required' => [ 'parsingPromptText', ], 'members' => [ 'parsingPromptText' => [ 'shape' => 'ParsingPromptText', ], ], ], 'ParsingPromptText' => [ 'type' => 'string', 'max' => 10000, 'min' => 1, ], 'ParsingStrategy' => [ 'type' => 'string', 'enum' => [ 'BEDROCK_FOUNDATION_MODEL', 'BEDROCK_DATA_AUTOMATION', ], ], 'PatternObjectFilter' => [ 'type' => 'structure', 'required' => [ 'objectType', ], 'members' => [ 'exclusionFilters' => [ 'shape' => 'FilterList', ], 'inclusionFilters' => [ 'shape' => 'FilterList', ], 'objectType' => [ 'shape' => 'FilteredObjectType', ], ], ], 'PatternObjectFilterConfiguration' => [ 'type' => 'structure', 'required' => [ 'filters', ], 'members' => [ 'filters' => [ 'shape' => 'PatternObjectFilterList', ], ], ], 'PatternObjectFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PatternObjectFilter', ], 'max' => 25, 'min' => 1, 'sensitive' => true, ], 'Payload' => [ 'type' => 'string', 'sensitive' => true, ], 'PerformanceConfigLatency' => [ 'type' => 'string', 'enum' => [ 'standard', 'optimized', ], ], 'PerformanceConfiguration' => [ 'type' => 'structure', 'members' => [ 'latency' => [ 'shape' => 'PerformanceConfigLatency', ], ], ], 'PineconeConfiguration' => [ 'type' => 'structure', 'required' => [ 'connectionString', 'credentialsSecretArn', 'fieldMapping', ], 'members' => [ 'connectionString' => [ 'shape' => 'PineconeConnectionString', ], 'credentialsSecretArn' => [ 'shape' => 'SecretArn', ], 'fieldMapping' => [ 'shape' => 'PineconeFieldMapping', ], 'namespace' => [ 'shape' => 'PineconeNamespace', ], ], ], 'PineconeConnectionString' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '^.*$', ], 'PineconeFieldMapping' => [ 'type' => 'structure', 'required' => [ 'metadataField', 'textField', ], 'members' => [ 'metadataField' => [ 'shape' => 'FieldName', ], 'textField' => [ 'shape' => 'FieldName', ], ], ], 'PineconeNamespace' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '^.*$', ], 'PrepareAgentRequest' => [ 'type' => 'structure', 'required' => [ 'agentId', ], 'members' => [ 'agentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'agentId', ], ], ], 'PrepareAgentResponse' => [ 'type' => 'structure', 'required' => [ 'agentId', 'agentStatus', 'agentVersion', 'preparedAt', ], 'members' => [ 'agentId' => [ 'shape' => 'Id', ], 'agentStatus' => [ 'shape' => 'AgentStatus', ], 'agentVersion' => [ 'shape' => 'Version', ], 'preparedAt' => [ 'shape' => 'DateTimestamp', ], ], ], 'PrepareFlowRequest' => [ 'type' => 'structure', 'required' => [ 'flowIdentifier', ], 'members' => [ 'flowIdentifier' => [ 'shape' => 'FlowIdentifier', 'location' => 'uri', 'locationName' => 'flowIdentifier', ], ], ], 'PrepareFlowResponse' => [ 'type' => 'structure', 'required' => [ 'id', 'status', ], 'members' => [ 'id' => [ 'shape' => 'FlowId', ], 'status' => [ 'shape' => 'FlowStatus', ], ], ], 'PrimitiveLong' => [ 'type' => 'long', ], 'PromptAgentResource' => [ 'type' => 'structure', 'required' => [ 'agentIdentifier', ], 'members' => [ 'agentIdentifier' => [ 'shape' => 'AgentAliasArn', ], ], 'sensitive' => true, ], 'PromptArn' => [ 'type' => 'string', 'pattern' => '^(arn:aws:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:prompt/[0-9a-zA-Z]{10}(?::[0-9]{1,5})?)$', ], 'PromptConfiguration' => [ 'type' => 'structure', 'members' => [ 'additionalModelRequestFields' => [ 'shape' => 'Document', ], 'basePromptTemplate' => [ 'shape' => 'BasePromptTemplate', ], 'foundationModel' => [ 'shape' => 'ModelIdentifier', ], 'inferenceConfiguration' => [ 'shape' => 'InferenceConfiguration', ], 'parserMode' => [ 'shape' => 'CreationMode', ], 'promptCreationMode' => [ 'shape' => 'CreationMode', ], 'promptState' => [ 'shape' => 'PromptState', ], 'promptType' => [ 'shape' => 'PromptType', ], ], ], 'PromptConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'PromptConfiguration', ], 'max' => 10, 'min' => 0, ], 'PromptDescription' => [ 'type' => 'string', 'max' => 200, 'min' => 1, ], 'PromptFlowNodeConfiguration' => [ 'type' => 'structure', 'required' => [ 'sourceConfiguration', ], 'members' => [ 'guardrailConfiguration' => [ 'shape' => 'GuardrailConfiguration', ], 'sourceConfiguration' => [ 'shape' => 'PromptFlowNodeSourceConfiguration', ], ], ], 'PromptFlowNodeInlineConfiguration' => [ 'type' => 'structure', 'required' => [ 'modelId', 'templateConfiguration', 'templateType', ], 'members' => [ 'additionalModelRequestFields' => [ 'shape' => 'Document', ], 'inferenceConfiguration' => [ 'shape' => 'PromptInferenceConfiguration', ], 'modelId' => [ 'shape' => 'PromptModelIdentifier', ], 'templateConfiguration' => [ 'shape' => 'PromptTemplateConfiguration', ], 'templateType' => [ 'shape' => 'PromptTemplateType', ], ], ], 'PromptFlowNodeResourceConfiguration' => [ 'type' => 'structure', 'required' => [ 'promptArn', ], 'members' => [ 'promptArn' => [ 'shape' => 'PromptArn', ], ], ], 'PromptFlowNodeSourceConfiguration' => [ 'type' => 'structure', 'members' => [ 'inline' => [ 'shape' => 'PromptFlowNodeInlineConfiguration', ], 'resource' => [ 'shape' => 'PromptFlowNodeResourceConfiguration', ], ], 'union' => true, ], 'PromptGenAiResource' => [ 'type' => 'structure', 'members' => [ 'agent' => [ 'shape' => 'PromptAgentResource', ], ], 'sensitive' => true, 'union' => true, ], 'PromptId' => [ 'type' => 'string', 'pattern' => '^[0-9a-zA-Z]{10}$', ], 'PromptIdentifier' => [ 'type' => 'string', 'pattern' => '^([0-9a-zA-Z]{10})|(arn:aws:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:prompt/[0-9a-zA-Z]{10})(?::[0-9]{1,5})?$', ], 'PromptInferenceConfiguration' => [ 'type' => 'structure', 'members' => [ 'text' => [ 'shape' => 'PromptModelInferenceConfiguration', ], ], 'union' => true, ], 'PromptInputVariable' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'PromptInputVariableName', ], ], ], 'PromptInputVariableName' => [ 'type' => 'string', 'pattern' => '^([0-9a-zA-Z][_-]?){1,100}$', ], 'PromptInputVariablesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PromptInputVariable', ], 'max' => 20, 'min' => 0, 'sensitive' => true, ], 'PromptMetadataEntry' => [ 'type' => 'structure', 'required' => [ 'key', 'value', ], 'members' => [ 'key' => [ 'shape' => 'PromptMetadataKey', ], 'value' => [ 'shape' => 'PromptMetadataValue', ], ], 'sensitive' => true, ], 'PromptMetadataKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\s._:/=+@-]*$', 'sensitive' => true, ], 'PromptMetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PromptMetadataEntry', ], 'max' => 50, 'min' => 0, 'sensitive' => true, ], 'PromptMetadataValue' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '^[a-zA-Z0-9\\s._:/=+@-]*$', 'sensitive' => true, ], 'PromptModelIdentifier' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^(arn:aws(-[^:]{1,12})?:(bedrock|sagemaker):[a-z0-9-]{1,20}:([0-9]{12})?:([a-z-]+/)?)?([a-zA-Z0-9.-]{1,63}){0,2}(([:][a-z0-9-]{1,63}){0,2})?(/[a-z0-9]{1,12})?$', ], 'PromptModelInferenceConfiguration' => [ 'type' => 'structure', 'members' => [ 'maxTokens' => [ 'shape' => 'MaximumLength', ], 'stopSequences' => [ 'shape' => 'StopSequences', ], 'temperature' => [ 'shape' => 'Temperature', ], 'topP' => [ 'shape' => 'TopP', ], ], ], 'PromptName' => [ 'type' => 'string', 'pattern' => '^([0-9a-zA-Z][_-]?){1,100}$', ], 'PromptOverrideConfiguration' => [ 'type' => 'structure', 'required' => [ 'promptConfigurations', ], 'members' => [ 'overrideLambda' => [ 'shape' => 'LambdaArn', ], 'promptConfigurations' => [ 'shape' => 'PromptConfigurations', ], ], 'sensitive' => true, ], 'PromptState' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'PromptSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'PromptSummary', ], 'max' => 10, 'min' => 0, ], 'PromptSummary' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdAt', 'id', 'name', 'updatedAt', 'version', ], 'members' => [ 'arn' => [ 'shape' => 'PromptArn', ], 'createdAt' => [ 'shape' => 'DateTimestamp', ], 'description' => [ 'shape' => 'PromptDescription', ], 'id' => [ 'shape' => 'PromptId', ], 'name' => [ 'shape' => 'PromptName', ], 'updatedAt' => [ 'shape' => 'DateTimestamp', ], 'version' => [ 'shape' => 'Version', ], ], ], 'PromptTemplateConfiguration' => [ 'type' => 'structure', 'members' => [ 'chat' => [ 'shape' => 'ChatPromptTemplateConfiguration', ], 'text' => [ 'shape' => 'TextPromptTemplateConfiguration', ], ], 'union' => true, ], 'PromptTemplateType' => [ 'type' => 'string', 'enum' => [ 'TEXT', 'CHAT', ], ], 'PromptType' => [ 'type' => 'string', 'enum' => [ 'PRE_PROCESSING', 'ORCHESTRATION', 'POST_PROCESSING', 'KNOWLEDGE_BASE_RESPONSE_GENERATION', 'MEMORY_SUMMARIZATION', ], ], 'PromptVariant' => [ 'type' => 'structure', 'required' => [ 'name', 'templateConfiguration', 'templateType', ], 'members' => [ 'additionalModelRequestFields' => [ 'shape' => 'Document', ], 'genAiResource' => [ 'shape' => 'PromptGenAiResource', ], 'inferenceConfiguration' => [ 'shape' => 'PromptInferenceConfiguration', ], 'metadata' => [ 'shape' => 'PromptMetadataList', ], 'modelId' => [ 'shape' => 'PromptModelIdentifier', ], 'name' => [ 'shape' => 'PromptVariantName', ], 'templateConfiguration' => [ 'shape' => 'PromptTemplateConfiguration', ], 'templateType' => [ 'shape' => 'PromptTemplateType', ], ], 'sensitive' => true, ], 'PromptVariantList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PromptVariant', ], 'max' => 1, 'min' => 0, 'sensitive' => true, ], 'PromptVariantName' => [ 'type' => 'string', 'pattern' => '^([0-9a-zA-Z][_-]?){1,100}$', ], 'ProvisionedModelIdentifier' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^((([0-9a-zA-Z][_-]?){1,63})|(arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:provisioned-model/[a-z0-9]{12}))$', ], 'QueryEngineType' => [ 'type' => 'string', 'enum' => [ 'REDSHIFT', ], ], 'QueryExecutionTimeoutSeconds' => [ 'type' => 'integer', 'box' => true, 'max' => 200, 'min' => 1, ], 'QueryGenerationColumn' => [ 'type' => 'structure', 'members' => [ 'description' => [ 'shape' => 'DescriptionString', ], 'inclusion' => [ 'shape' => 'IncludeExclude', ], 'name' => [ 'shape' => 'QueryGenerationColumnName', ], ], ], 'QueryGenerationColumnName' => [ 'type' => 'string', 'max' => 127, 'min' => 1, ], 'QueryGenerationColumns' => [ 'type' => 'list', 'member' => [ 'shape' => 'QueryGenerationColumn', ], ], 'QueryGenerationConfiguration' => [ 'type' => 'structure', 'members' => [ 'executionTimeoutSeconds' => [ 'shape' => 'QueryExecutionTimeoutSeconds', ], 'generationContext' => [ 'shape' => 'QueryGenerationContext', ], ], ], 'QueryGenerationContext' => [ 'type' => 'structure', 'members' => [ 'curatedQueries' => [ 'shape' => 'CuratedQueries', ], 'tables' => [ 'shape' => 'QueryGenerationTables', ], ], 'sensitive' => true, ], 'QueryGenerationTable' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'columns' => [ 'shape' => 'QueryGenerationColumns', ], 'description' => [ 'shape' => 'DescriptionString', ], 'inclusion' => [ 'shape' => 'IncludeExclude', ], 'name' => [ 'shape' => 'QueryGenerationTableName', ], ], ], 'QueryGenerationTableName' => [ 'type' => 'string', 'pattern' => '^.*\\..*\\..*$', ], 'QueryGenerationTables' => [ 'type' => 'list', 'member' => [ 'shape' => 'QueryGenerationTable', ], 'max' => 50, 'min' => 0, ], 'RdsArn' => [ 'type' => 'string', 'pattern' => '^arn:aws(|-cn|-us-gov):rds:[a-zA-Z0-9-]*:[0-9]{12}:cluster:[a-zA-Z0-9-]{1,63}$', ], 'RdsConfiguration' => [ 'type' => 'structure', 'required' => [ 'credentialsSecretArn', 'databaseName', 'fieldMapping', 'resourceArn', 'tableName', ], 'members' => [ 'credentialsSecretArn' => [ 'shape' => 'SecretArn', ], 'databaseName' => [ 'shape' => 'RdsDatabaseName', ], 'fieldMapping' => [ 'shape' => 'RdsFieldMapping', ], 'resourceArn' => [ 'shape' => 'RdsArn', ], 'tableName' => [ 'shape' => 'RdsTableName', ], ], ], 'RdsDatabaseName' => [ 'type' => 'string', 'max' => 63, 'min' => 0, 'pattern' => '^[a-zA-Z0-9_\\-]+$', ], 'RdsFieldMapping' => [ 'type' => 'structure', 'required' => [ 'metadataField', 'primaryKeyField', 'textField', 'vectorField', ], 'members' => [ 'customMetadataField' => [ 'shape' => 'ColumnName', ], 'metadataField' => [ 'shape' => 'ColumnName', ], 'primaryKeyField' => [ 'shape' => 'ColumnName', ], 'textField' => [ 'shape' => 'ColumnName', ], 'vectorField' => [ 'shape' => 'ColumnName', ], ], ], 'RdsTableName' => [ 'type' => 'string', 'max' => 63, 'min' => 0, 'pattern' => '^[a-zA-Z0-9_\\.\\-]+$', ], 'RecommendedAction' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, ], 'RecommendedActions' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecommendedAction', ], 'max' => 2048, 'min' => 0, ], 'RedisEnterpriseCloudConfiguration' => [ 'type' => 'structure', 'required' => [ 'credentialsSecretArn', 'endpoint', 'fieldMapping', 'vectorIndexName', ], 'members' => [ 'credentialsSecretArn' => [ 'shape' => 'SecretArn', ], 'endpoint' => [ 'shape' => 'RedisEnterpriseCloudEndpoint', ], 'fieldMapping' => [ 'shape' => 'RedisEnterpriseCloudFieldMapping', ], 'vectorIndexName' => [ 'shape' => 'RedisEnterpriseCloudIndexName', ], ], ], 'RedisEnterpriseCloudEndpoint' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '^.*$', ], 'RedisEnterpriseCloudFieldMapping' => [ 'type' => 'structure', 'required' => [ 'metadataField', 'textField', 'vectorField', ], 'members' => [ 'metadataField' => [ 'shape' => 'FieldName', ], 'textField' => [ 'shape' => 'FieldName', ], 'vectorField' => [ 'shape' => 'FieldName', ], ], ], 'RedisEnterpriseCloudIndexName' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '^.*$', ], 'RedshiftClusterIdentifier' => [ 'type' => 'string', 'max' => 63, 'min' => 1, ], 'RedshiftConfiguration' => [ 'type' => 'structure', 'required' => [ 'queryEngineConfiguration', 'storageConfigurations', ], 'members' => [ 'queryEngineConfiguration' => [ 'shape' => 'RedshiftQueryEngineConfiguration', ], 'queryGenerationConfiguration' => [ 'shape' => 'QueryGenerationConfiguration', ], 'storageConfigurations' => [ 'shape' => 'RedshiftQueryEngineStorageConfigurations', ], ], ], 'RedshiftDatabase' => [ 'type' => 'string', 'max' => 200, 'min' => 1, ], 'RedshiftProvisionedAuthConfiguration' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'databaseUser' => [ 'shape' => 'String', ], 'type' => [ 'shape' => 'RedshiftProvisionedAuthType', ], 'usernamePasswordSecretArn' => [ 'shape' => 'SecretArn', ], ], ], 'RedshiftProvisionedAuthType' => [ 'type' => 'string', 'enum' => [ 'IAM', 'USERNAME_PASSWORD', 'USERNAME', ], ], 'RedshiftProvisionedConfiguration' => [ 'type' => 'structure', 'required' => [ 'authConfiguration', 'clusterIdentifier', ], 'members' => [ 'authConfiguration' => [ 'shape' => 'RedshiftProvisionedAuthConfiguration', ], 'clusterIdentifier' => [ 'shape' => 'RedshiftClusterIdentifier', ], ], ], 'RedshiftQueryEngineAwsDataCatalogStorageConfiguration' => [ 'type' => 'structure', 'required' => [ 'tableNames', ], 'members' => [ 'tableNames' => [ 'shape' => 'AwsDataCatalogTableNames', ], ], ], 'RedshiftQueryEngineConfiguration' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'provisionedConfiguration' => [ 'shape' => 'RedshiftProvisionedConfiguration', ], 'serverlessConfiguration' => [ 'shape' => 'RedshiftServerlessConfiguration', ], 'type' => [ 'shape' => 'RedshiftQueryEngineType', ], ], ], 'RedshiftQueryEngineRedshiftStorageConfiguration' => [ 'type' => 'structure', 'required' => [ 'databaseName', ], 'members' => [ 'databaseName' => [ 'shape' => 'RedshiftDatabase', ], ], ], 'RedshiftQueryEngineStorageConfiguration' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'awsDataCatalogConfiguration' => [ 'shape' => 'RedshiftQueryEngineAwsDataCatalogStorageConfiguration', ], 'redshiftConfiguration' => [ 'shape' => 'RedshiftQueryEngineRedshiftStorageConfiguration', ], 'type' => [ 'shape' => 'RedshiftQueryEngineStorageType', ], ], ], 'RedshiftQueryEngineStorageConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'RedshiftQueryEngineStorageConfiguration', ], 'max' => 1, 'min' => 1, ], 'RedshiftQueryEngineStorageType' => [ 'type' => 'string', 'enum' => [ 'REDSHIFT', 'AWS_DATA_CATALOG', ], ], 'RedshiftQueryEngineType' => [ 'type' => 'string', 'enum' => [ 'SERVERLESS', 'PROVISIONED', ], ], 'RedshiftServerlessAuthConfiguration' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'type' => [ 'shape' => 'RedshiftServerlessAuthType', ], 'usernamePasswordSecretArn' => [ 'shape' => 'SecretArn', ], ], ], 'RedshiftServerlessAuthType' => [ 'type' => 'string', 'enum' => [ 'IAM', 'USERNAME_PASSWORD', ], ], 'RedshiftServerlessConfiguration' => [ 'type' => 'structure', 'required' => [ 'authConfiguration', 'workgroupArn', ], 'members' => [ 'authConfiguration' => [ 'shape' => 'RedshiftServerlessAuthConfiguration', ], 'workgroupArn' => [ 'shape' => 'WorkgroupArn', ], ], ], 'RelayConversationHistory' => [ 'type' => 'string', 'enum' => [ 'TO_COLLABORATOR', 'DISABLED', ], ], 'RequireConfirmation' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'RerankingMetadataSelectionMode' => [ 'type' => 'string', 'enum' => [ 'SELECTIVE', 'ALL', ], ], 'RerankingMetadataSelectiveModeConfiguration' => [ 'type' => 'structure', 'members' => [ 'fieldsToExclude' => [ 'shape' => 'FieldsForReranking', ], 'fieldsToInclude' => [ 'shape' => 'FieldsForReranking', ], ], 'union' => true, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'RetrievalFlowNodeConfiguration' => [ 'type' => 'structure', 'required' => [ 'serviceConfiguration', ], 'members' => [ 'serviceConfiguration' => [ 'shape' => 'RetrievalFlowNodeServiceConfiguration', ], ], ], 'RetrievalFlowNodeS3Configuration' => [ 'type' => 'structure', 'required' => [ 'bucketName', ], 'members' => [ 'bucketName' => [ 'shape' => 'S3BucketName', ], ], ], 'RetrievalFlowNodeServiceConfiguration' => [ 'type' => 'structure', 'members' => [ 's3' => [ 'shape' => 'RetrievalFlowNodeS3Configuration', ], ], 'union' => true, ], 'S3BucketArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^arn:aws(|-cn|-us-gov):s3:::[a-z0-9][a-z0-9.-]{1,61}[a-z0-9]$', ], 'S3BucketName' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '^[a-z0-9][\\.\\-a-z0-9]{1,61}[a-z0-9]$', ], 'S3BucketUri' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^s3://.{1,128}$', ], 'S3Content' => [ 'type' => 'structure', 'required' => [ 's3Location', ], 'members' => [ 's3Location' => [ 'shape' => 'S3Location', ], ], ], 'S3DataSourceConfiguration' => [ 'type' => 'structure', 'required' => [ 'bucketArn', ], 'members' => [ 'bucketArn' => [ 'shape' => 'S3BucketArn', ], 'bucketOwnerAccountId' => [ 'shape' => 'BucketOwnerAccountId', ], 'inclusionPrefixes' => [ 'shape' => 'S3Prefixes', ], ], ], 'S3Identifier' => [ 'type' => 'structure', 'members' => [ 's3BucketName' => [ 'shape' => 'S3BucketName', ], 's3ObjectKey' => [ 'shape' => 'S3ObjectKey', ], ], ], 'S3Location' => [ 'type' => 'structure', 'required' => [ 'uri', ], 'members' => [ 'uri' => [ 'shape' => 'S3BucketUri', ], ], ], 'S3ObjectKey' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^[\\.\\-\\!\\*\\_\\\'\\(\\)a-zA-Z0-9][\\.\\-\\!\\*\\_\\\'\\(\\)\\/a-zA-Z0-9]*$', ], 'S3ObjectUri' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^s3://[a-z0-9][a-z0-9.-]{1,61}[a-z0-9]/.{1,1024}$', ], 'S3Prefix' => [ 'type' => 'string', 'max' => 300, 'min' => 1, 'sensitive' => true, ], 'S3Prefixes' => [ 'type' => 'list', 'member' => [ 'shape' => 'S3Prefix', ], 'max' => 1, 'min' => 1, ], 'SalesforceAuthType' => [ 'type' => 'string', 'enum' => [ 'OAUTH2_CLIENT_CREDENTIALS', ], ], 'SalesforceCrawlerConfiguration' => [ 'type' => 'structure', 'members' => [ 'filterConfiguration' => [ 'shape' => 'CrawlFilterConfiguration', ], ], ], 'SalesforceDataSourceConfiguration' => [ 'type' => 'structure', 'required' => [ 'sourceConfiguration', ], 'members' => [ 'crawlerConfiguration' => [ 'shape' => 'SalesforceCrawlerConfiguration', ], 'sourceConfiguration' => [ 'shape' => 'SalesforceSourceConfiguration', ], ], ], 'SalesforceSourceConfiguration' => [ 'type' => 'structure', 'required' => [ 'authType', 'credentialsSecretArn', 'hostUrl', ], 'members' => [ 'authType' => [ 'shape' => 'SalesforceAuthType', ], 'credentialsSecretArn' => [ 'shape' => 'SecretArn', ], 'hostUrl' => [ 'shape' => 'HttpsUrl', ], ], ], 'SecretArn' => [ 'type' => 'string', 'pattern' => '^arn:aws(|-cn|-us-gov):secretsmanager:[a-z0-9-]{1,20}:([0-9]{12}|):secret:[a-zA-Z0-9!/_+=.@-]{1,512}$', ], 'SeedUrl' => [ 'type' => 'structure', 'members' => [ 'url' => [ 'shape' => 'Url', ], ], ], 'SeedUrls' => [ 'type' => 'list', 'member' => [ 'shape' => 'SeedUrl', ], 'max' => 100, 'min' => 1, ], 'SemanticChunkingConfiguration' => [ 'type' => 'structure', 'required' => [ 'breakpointPercentileThreshold', 'bufferSize', 'maxTokens', ], 'members' => [ 'breakpointPercentileThreshold' => [ 'shape' => 'SemanticChunkingConfigurationBreakpointPercentileThresholdInteger', ], 'bufferSize' => [ 'shape' => 'SemanticChunkingConfigurationBufferSizeInteger', ], 'maxTokens' => [ 'shape' => 'SemanticChunkingConfigurationMaxTokensInteger', ], ], ], 'SemanticChunkingConfigurationBreakpointPercentileThresholdInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 99, 'min' => 50, ], 'SemanticChunkingConfigurationBufferSizeInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 1, 'min' => 0, ], 'SemanticChunkingConfigurationMaxTokensInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'ServerSideEncryptionConfiguration' => [ 'type' => 'structure', 'members' => [ 'kmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'SessionSummaryConfiguration' => [ 'type' => 'structure', 'members' => [ 'maxRecentSessions' => [ 'shape' => 'MaxRecentSessions', ], ], ], 'SessionTTL' => [ 'type' => 'integer', 'box' => true, 'max' => 5400, 'min' => 60, ], 'SharePointAuthType' => [ 'type' => 'string', 'enum' => [ 'OAUTH2_CLIENT_CREDENTIALS', 'OAUTH2_SHAREPOINT_APP_ONLY_CLIENT_CREDENTIALS', ], ], 'SharePointCrawlerConfiguration' => [ 'type' => 'structure', 'members' => [ 'filterConfiguration' => [ 'shape' => 'CrawlFilterConfiguration', ], ], ], 'SharePointDataSourceConfiguration' => [ 'type' => 'structure', 'required' => [ 'sourceConfiguration', ], 'members' => [ 'crawlerConfiguration' => [ 'shape' => 'SharePointCrawlerConfiguration', ], 'sourceConfiguration' => [ 'shape' => 'SharePointSourceConfiguration', ], ], ], 'SharePointDomain' => [ 'type' => 'string', 'max' => 50, 'min' => 1, ], 'SharePointHostType' => [ 'type' => 'string', 'enum' => [ 'ONLINE', ], ], 'SharePointSiteUrls' => [ 'type' => 'list', 'member' => [ 'shape' => 'HttpsUrl', ], 'max' => 100, 'min' => 1, ], 'SharePointSourceConfiguration' => [ 'type' => 'structure', 'required' => [ 'authType', 'credentialsSecretArn', 'domain', 'hostType', 'siteUrls', ], 'members' => [ 'authType' => [ 'shape' => 'SharePointAuthType', ], 'credentialsSecretArn' => [ 'shape' => 'SecretArn', ], 'domain' => [ 'shape' => 'SharePointDomain', ], 'hostType' => [ 'shape' => 'SharePointHostType', ], 'siteUrls' => [ 'shape' => 'SharePointSiteUrls', ], 'tenantId' => [ 'shape' => 'Microsoft365TenantId', ], ], ], 'SortOrder' => [ 'type' => 'string', 'enum' => [ 'ASCENDING', 'DESCENDING', ], ], 'SpecificToolChoice' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'ToolName', ], ], ], 'SqlKnowledgeBaseConfiguration' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'redshiftConfiguration' => [ 'shape' => 'RedshiftConfiguration', ], 'type' => [ 'shape' => 'QueryEngineType', ], ], ], 'SqlString' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, ], 'StartIngestionJobRequest' => [ 'type' => 'structure', 'required' => [ 'dataSourceId', 'knowledgeBaseId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'dataSourceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'dataSourceId', ], 'description' => [ 'shape' => 'Description', ], 'knowledgeBaseId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], ], ], 'StartIngestionJobResponse' => [ 'type' => 'structure', 'required' => [ 'ingestionJob', ], 'members' => [ 'ingestionJob' => [ 'shape' => 'IngestionJob', ], ], ], 'StepType' => [ 'type' => 'string', 'enum' => [ 'POST_CHUNKING', ], ], 'StopIngestionJobRequest' => [ 'type' => 'structure', 'required' => [ 'dataSourceId', 'ingestionJobId', 'knowledgeBaseId', ], 'members' => [ 'dataSourceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'dataSourceId', ], 'ingestionJobId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'ingestionJobId', ], 'knowledgeBaseId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], ], ], 'StopIngestionJobResponse' => [ 'type' => 'structure', 'required' => [ 'ingestionJob', ], 'members' => [ 'ingestionJob' => [ 'shape' => 'IngestionJob', ], ], ], 'StopSequences' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'max' => 4, 'min' => 0, ], 'StorageConfiguration' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'mongoDbAtlasConfiguration' => [ 'shape' => 'MongoDbAtlasConfiguration', ], 'neptuneAnalyticsConfiguration' => [ 'shape' => 'NeptuneAnalyticsConfiguration', ], 'opensearchManagedClusterConfiguration' => [ 'shape' => 'OpenSearchManagedClusterConfiguration', ], 'opensearchServerlessConfiguration' => [ 'shape' => 'OpenSearchServerlessConfiguration', ], 'pineconeConfiguration' => [ 'shape' => 'PineconeConfiguration', ], 'rdsConfiguration' => [ 'shape' => 'RdsConfiguration', ], 'redisEnterpriseCloudConfiguration' => [ 'shape' => 'RedisEnterpriseCloudConfiguration', ], 'type' => [ 'shape' => 'KnowledgeBaseStorageType', ], ], ], 'StorageDays' => [ 'type' => 'integer', 'box' => true, 'max' => 365, 'min' => 0, ], 'StorageFlowNodeConfiguration' => [ 'type' => 'structure', 'required' => [ 'serviceConfiguration', ], 'members' => [ 'serviceConfiguration' => [ 'shape' => 'StorageFlowNodeServiceConfiguration', ], ], ], 'StorageFlowNodeS3Configuration' => [ 'type' => 'structure', 'required' => [ 'bucketName', ], 'members' => [ 'bucketName' => [ 'shape' => 'S3BucketName', ], ], ], 'StorageFlowNodeServiceConfiguration' => [ 'type' => 'structure', 'members' => [ 's3' => [ 'shape' => 'StorageFlowNodeS3Configuration', ], ], 'union' => true, ], 'String' => [ 'type' => 'string', ], 'StringValue' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'sensitive' => true, ], 'SupplementalDataStorageConfiguration' => [ 'type' => 'structure', 'required' => [ 'storageLocations', ], 'members' => [ 'storageLocations' => [ 'shape' => 'SupplementalDataStorageLocations', ], ], ], 'SupplementalDataStorageLocation' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 's3Location' => [ 'shape' => 'S3Location', ], 'type' => [ 'shape' => 'SupplementalDataStorageLocationType', ], ], ], 'SupplementalDataStorageLocationType' => [ 'type' => 'string', 'enum' => [ 'S3', ], ], 'SupplementalDataStorageLocations' => [ 'type' => 'list', 'member' => [ 'shape' => 'SupplementalDataStorageLocation', ], 'max' => 1, 'min' => 1, ], 'SupportedLanguages' => [ 'type' => 'string', 'enum' => [ 'Python_3', ], ], 'SystemContentBlock' => [ 'type' => 'structure', 'members' => [ 'cachePoint' => [ 'shape' => 'CachePointBlock', ], 'text' => [ 'shape' => 'NonEmptyString', ], ], 'sensitive' => true, 'union' => true, ], 'SystemContentBlocks' => [ 'type' => 'list', 'member' => [ 'shape' => 'SystemContentBlock', ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\s._:/=+@-]*$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'TaggableResourcesArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^[a-zA-Z0-9\\s._:/=+@-]*$', ], 'TaggableResourcesArn' => [ 'type' => 'string', 'max' => 1011, 'min' => 20, 'pattern' => '(^arn:aws:bedrock:[a-zA-Z0-9-]+:/d{12}:(agent|agent-alias|knowledge-base|flow|prompt)/[A-Z0-9]{10}(?:/[A-Z0-9]{10})?$|^arn:aws:bedrock:[a-zA-Z0-9-]+:/d{12}:flow/([A-Z0-9]{10})/alias/([A-Z0-9]{10})$|^arn:aws:bedrock:[a-zA-Z0-9-]+:/d{12}:prompt/([A-Z0-9]{10})?(?::/d+)?$)', ], 'TagsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], ], 'Temperature' => [ 'type' => 'float', 'box' => true, 'max' => 1, 'min' => 0, ], 'TextContentDoc' => [ 'type' => 'structure', 'required' => [ 'data', ], 'members' => [ 'data' => [ 'shape' => 'Data', ], ], ], 'TextPrompt' => [ 'type' => 'string', 'min' => 1, 'sensitive' => true, ], 'TextPromptTemplateConfiguration' => [ 'type' => 'structure', 'required' => [ 'text', ], 'members' => [ 'cachePoint' => [ 'shape' => 'CachePointBlock', ], 'inputVariables' => [ 'shape' => 'PromptInputVariablesList', ], 'text' => [ 'shape' => 'TextPrompt', ], ], 'sensitive' => true, ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, ], 'Tool' => [ 'type' => 'structure', 'members' => [ 'cachePoint' => [ 'shape' => 'CachePointBlock', ], 'toolSpec' => [ 'shape' => 'ToolSpecification', ], ], 'union' => true, ], 'ToolChoice' => [ 'type' => 'structure', 'members' => [ 'any' => [ 'shape' => 'AnyToolChoice', ], 'auto' => [ 'shape' => 'AutoToolChoice', ], 'tool' => [ 'shape' => 'SpecificToolChoice', ], ], 'sensitive' => true, 'union' => true, ], 'ToolConfiguration' => [ 'type' => 'structure', 'required' => [ 'tools', ], 'members' => [ 'toolChoice' => [ 'shape' => 'ToolChoice', ], 'tools' => [ 'shape' => 'ToolConfigurationToolsList', ], ], ], 'ToolConfigurationToolsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tool', ], 'min' => 1, 'sensitive' => true, ], 'ToolInputSchema' => [ 'type' => 'structure', 'members' => [ 'json' => [ 'shape' => 'Document', ], ], 'union' => true, ], 'ToolName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[a-zA-Z][a-zA-Z0-9_]*$', ], 'ToolSpecification' => [ 'type' => 'structure', 'required' => [ 'inputSchema', 'name', ], 'members' => [ 'description' => [ 'shape' => 'NonEmptyString', ], 'inputSchema' => [ 'shape' => 'ToolInputSchema', ], 'name' => [ 'shape' => 'ToolName', ], ], ], 'TopK' => [ 'type' => 'integer', 'box' => true, 'max' => 500, 'min' => 0, ], 'TopP' => [ 'type' => 'float', 'box' => true, 'max' => 1, 'min' => 0, ], 'Transformation' => [ 'type' => 'structure', 'required' => [ 'stepToApply', 'transformationFunction', ], 'members' => [ 'stepToApply' => [ 'shape' => 'StepType', ], 'transformationFunction' => [ 'shape' => 'TransformationFunction', ], ], ], 'TransformationFunction' => [ 'type' => 'structure', 'required' => [ 'transformationLambdaConfiguration', ], 'members' => [ 'transformationLambdaConfiguration' => [ 'shape' => 'TransformationLambdaConfiguration', ], ], ], 'TransformationLambdaConfiguration' => [ 'type' => 'structure', 'required' => [ 'lambdaArn', ], 'members' => [ 'lambdaArn' => [ 'shape' => 'LambdaArn', ], ], ], 'Transformations' => [ 'type' => 'list', 'member' => [ 'shape' => 'Transformation', ], 'max' => 1, 'min' => 1, ], 'Type' => [ 'type' => 'string', 'enum' => [ 'string', 'number', 'integer', 'boolean', 'array', ], ], 'UnfulfilledNodeInputFlowValidationDetails' => [ 'type' => 'structure', 'required' => [ 'input', 'node', ], 'members' => [ 'input' => [ 'shape' => 'FlowNodeInputName', ], 'node' => [ 'shape' => 'FlowNodeName', ], ], ], 'UnknownConnectionConditionFlowValidationDetails' => [ 'type' => 'structure', 'required' => [ 'connection', ], 'members' => [ 'connection' => [ 'shape' => 'FlowConnectionName', ], ], ], 'UnknownConnectionSourceFlowValidationDetails' => [ 'type' => 'structure', 'required' => [ 'connection', ], 'members' => [ 'connection' => [ 'shape' => 'FlowConnectionName', ], ], ], 'UnknownConnectionSourceOutputFlowValidationDetails' => [ 'type' => 'structure', 'required' => [ 'connection', ], 'members' => [ 'connection' => [ 'shape' => 'FlowConnectionName', ], ], ], 'UnknownConnectionTargetFlowValidationDetails' => [ 'type' => 'structure', 'required' => [ 'connection', ], 'members' => [ 'connection' => [ 'shape' => 'FlowConnectionName', ], ], ], 'UnknownConnectionTargetInputFlowValidationDetails' => [ 'type' => 'structure', 'required' => [ 'connection', ], 'members' => [ 'connection' => [ 'shape' => 'FlowConnectionName', ], ], ], 'UnknownNodeInputFlowValidationDetails' => [ 'type' => 'structure', 'required' => [ 'input', 'node', ], 'members' => [ 'input' => [ 'shape' => 'FlowNodeInputName', ], 'node' => [ 'shape' => 'FlowNodeName', ], ], ], 'UnknownNodeOutputFlowValidationDetails' => [ 'type' => 'structure', 'required' => [ 'node', 'output', ], 'members' => [ 'node' => [ 'shape' => 'FlowNodeName', ], 'output' => [ 'shape' => 'FlowNodeOutputName', ], ], ], 'UnreachableNodeFlowValidationDetails' => [ 'type' => 'structure', 'required' => [ 'node', ], 'members' => [ 'node' => [ 'shape' => 'FlowNodeName', ], ], ], 'UnsatisfiedConnectionConditionsFlowValidationDetails' => [ 'type' => 'structure', 'required' => [ 'connection', ], 'members' => [ 'connection' => [ 'shape' => 'FlowConnectionName', ], ], ], 'UnspecifiedFlowValidationDetails' => [ 'type' => 'structure', 'members' => [], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'TaggableResourcesArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateAgentActionGroupRequest' => [ 'type' => 'structure', 'required' => [ 'actionGroupId', 'actionGroupName', 'agentId', 'agentVersion', ], 'members' => [ 'actionGroupExecutor' => [ 'shape' => 'ActionGroupExecutor', ], 'actionGroupId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'actionGroupId', ], 'actionGroupName' => [ 'shape' => 'Name', ], 'actionGroupState' => [ 'shape' => 'ActionGroupState', ], 'agentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'agentId', ], 'agentVersion' => [ 'shape' => 'DraftVersion', 'location' => 'uri', 'locationName' => 'agentVersion', ], 'apiSchema' => [ 'shape' => 'APISchema', ], 'description' => [ 'shape' => 'Description', ], 'functionSchema' => [ 'shape' => 'FunctionSchema', ], 'parentActionGroupSignature' => [ 'shape' => 'ActionGroupSignature', ], 'parentActionGroupSignatureParams' => [ 'shape' => 'ActionGroupSignatureParams', ], ], ], 'UpdateAgentActionGroupResponse' => [ 'type' => 'structure', 'required' => [ 'agentActionGroup', ], 'members' => [ 'agentActionGroup' => [ 'shape' => 'AgentActionGroup', ], ], ], 'UpdateAgentAliasRequest' => [ 'type' => 'structure', 'required' => [ 'agentAliasId', 'agentAliasName', 'agentId', ], 'members' => [ 'agentAliasId' => [ 'shape' => 'AgentAliasId', 'location' => 'uri', 'locationName' => 'agentAliasId', ], 'agentAliasName' => [ 'shape' => 'Name', ], 'agentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'agentId', ], 'aliasInvocationState' => [ 'shape' => 'AliasInvocationState', ], 'description' => [ 'shape' => 'Description', ], 'routingConfiguration' => [ 'shape' => 'AgentAliasRoutingConfiguration', ], ], ], 'UpdateAgentAliasResponse' => [ 'type' => 'structure', 'required' => [ 'agentAlias', ], 'members' => [ 'agentAlias' => [ 'shape' => 'AgentAlias', ], ], ], 'UpdateAgentCollaboratorRequest' => [ 'type' => 'structure', 'required' => [ 'agentDescriptor', 'agentId', 'agentVersion', 'collaborationInstruction', 'collaboratorId', 'collaboratorName', ], 'members' => [ 'agentDescriptor' => [ 'shape' => 'AgentDescriptor', ], 'agentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'agentId', ], 'agentVersion' => [ 'shape' => 'DraftVersion', 'location' => 'uri', 'locationName' => 'agentVersion', ], 'collaborationInstruction' => [ 'shape' => 'CollaborationInstruction', ], 'collaboratorId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'collaboratorId', ], 'collaboratorName' => [ 'shape' => 'Name', ], 'relayConversationHistory' => [ 'shape' => 'RelayConversationHistory', ], ], ], 'UpdateAgentCollaboratorResponse' => [ 'type' => 'structure', 'required' => [ 'agentCollaborator', ], 'members' => [ 'agentCollaborator' => [ 'shape' => 'AgentCollaborator', ], ], ], 'UpdateAgentKnowledgeBaseRequest' => [ 'type' => 'structure', 'required' => [ 'agentId', 'agentVersion', 'knowledgeBaseId', ], 'members' => [ 'agentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'agentId', ], 'agentVersion' => [ 'shape' => 'DraftVersion', 'location' => 'uri', 'locationName' => 'agentVersion', ], 'description' => [ 'shape' => 'Description', ], 'knowledgeBaseId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'knowledgeBaseState' => [ 'shape' => 'KnowledgeBaseState', ], ], ], 'UpdateAgentKnowledgeBaseResponse' => [ 'type' => 'structure', 'required' => [ 'agentKnowledgeBase', ], 'members' => [ 'agentKnowledgeBase' => [ 'shape' => 'AgentKnowledgeBase', ], ], ], 'UpdateAgentRequest' => [ 'type' => 'structure', 'required' => [ 'agentId', 'agentName', 'agentResourceRoleArn', 'foundationModel', ], 'members' => [ 'agentCollaboration' => [ 'shape' => 'AgentCollaboration', ], 'agentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'agentId', ], 'agentName' => [ 'shape' => 'Name', ], 'agentResourceRoleArn' => [ 'shape' => 'AgentRoleArn', ], 'customOrchestration' => [ 'shape' => 'CustomOrchestration', ], 'customerEncryptionKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'description' => [ 'shape' => 'Description', ], 'foundationModel' => [ 'shape' => 'ModelIdentifier', ], 'guardrailConfiguration' => [ 'shape' => 'GuardrailConfiguration', ], 'idleSessionTTLInSeconds' => [ 'shape' => 'SessionTTL', ], 'instruction' => [ 'shape' => 'Instruction', ], 'memoryConfiguration' => [ 'shape' => 'MemoryConfiguration', ], 'orchestrationType' => [ 'shape' => 'OrchestrationType', ], 'promptOverrideConfiguration' => [ 'shape' => 'PromptOverrideConfiguration', ], ], ], 'UpdateAgentResponse' => [ 'type' => 'structure', 'required' => [ 'agent', ], 'members' => [ 'agent' => [ 'shape' => 'Agent', ], ], ], 'UpdateDataSourceRequest' => [ 'type' => 'structure', 'required' => [ 'dataSourceConfiguration', 'dataSourceId', 'knowledgeBaseId', 'name', ], 'members' => [ 'dataDeletionPolicy' => [ 'shape' => 'DataDeletionPolicy', ], 'dataSourceConfiguration' => [ 'shape' => 'DataSourceConfiguration', ], 'dataSourceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'dataSourceId', ], 'description' => [ 'shape' => 'Description', ], 'knowledgeBaseId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'name' => [ 'shape' => 'Name', ], 'serverSideEncryptionConfiguration' => [ 'shape' => 'ServerSideEncryptionConfiguration', ], 'vectorIngestionConfiguration' => [ 'shape' => 'VectorIngestionConfiguration', ], ], ], 'UpdateDataSourceResponse' => [ 'type' => 'structure', 'required' => [ 'dataSource', ], 'members' => [ 'dataSource' => [ 'shape' => 'DataSource', ], ], ], 'UpdateFlowAliasRequest' => [ 'type' => 'structure', 'required' => [ 'aliasIdentifier', 'flowIdentifier', 'name', 'routingConfiguration', ], 'members' => [ 'aliasIdentifier' => [ 'shape' => 'FlowAliasIdentifier', 'location' => 'uri', 'locationName' => 'aliasIdentifier', ], 'concurrencyConfiguration' => [ 'shape' => 'FlowAliasConcurrencyConfiguration', ], 'description' => [ 'shape' => 'Description', ], 'flowIdentifier' => [ 'shape' => 'FlowIdentifier', 'location' => 'uri', 'locationName' => 'flowIdentifier', ], 'name' => [ 'shape' => 'Name', ], 'routingConfiguration' => [ 'shape' => 'FlowAliasRoutingConfiguration', ], ], ], 'UpdateFlowAliasResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdAt', 'flowId', 'id', 'name', 'routingConfiguration', 'updatedAt', ], 'members' => [ 'arn' => [ 'shape' => 'FlowAliasArn', ], 'concurrencyConfiguration' => [ 'shape' => 'FlowAliasConcurrencyConfiguration', ], 'createdAt' => [ 'shape' => 'DateTimestamp', ], 'description' => [ 'shape' => 'Description', ], 'flowId' => [ 'shape' => 'FlowId', ], 'id' => [ 'shape' => 'FlowAliasId', ], 'name' => [ 'shape' => 'Name', ], 'routingConfiguration' => [ 'shape' => 'FlowAliasRoutingConfiguration', ], 'updatedAt' => [ 'shape' => 'DateTimestamp', ], ], ], 'UpdateFlowRequest' => [ 'type' => 'structure', 'required' => [ 'executionRoleArn', 'flowIdentifier', 'name', ], 'members' => [ 'customerEncryptionKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'definition' => [ 'shape' => 'FlowDefinition', ], 'description' => [ 'shape' => 'FlowDescription', ], 'executionRoleArn' => [ 'shape' => 'FlowExecutionRoleArn', ], 'flowIdentifier' => [ 'shape' => 'FlowIdentifier', 'location' => 'uri', 'locationName' => 'flowIdentifier', ], 'name' => [ 'shape' => 'FlowName', ], ], ], 'UpdateFlowResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdAt', 'executionRoleArn', 'id', 'name', 'status', 'updatedAt', 'version', ], 'members' => [ 'arn' => [ 'shape' => 'FlowArn', ], 'createdAt' => [ 'shape' => 'DateTimestamp', ], 'customerEncryptionKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'definition' => [ 'shape' => 'FlowDefinition', ], 'description' => [ 'shape' => 'FlowDescription', ], 'executionRoleArn' => [ 'shape' => 'FlowExecutionRoleArn', ], 'id' => [ 'shape' => 'FlowId', ], 'name' => [ 'shape' => 'FlowName', ], 'status' => [ 'shape' => 'FlowStatus', ], 'updatedAt' => [ 'shape' => 'DateTimestamp', ], 'version' => [ 'shape' => 'DraftVersion', ], ], ], 'UpdateKnowledgeBaseRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseConfiguration', 'knowledgeBaseId', 'name', 'roleArn', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'knowledgeBaseConfiguration' => [ 'shape' => 'KnowledgeBaseConfiguration', ], 'knowledgeBaseId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'name' => [ 'shape' => 'Name', ], 'roleArn' => [ 'shape' => 'KnowledgeBaseRoleArn', ], 'storageConfiguration' => [ 'shape' => 'StorageConfiguration', ], ], ], 'UpdateKnowledgeBaseResponse' => [ 'type' => 'structure', 'required' => [ 'knowledgeBase', ], 'members' => [ 'knowledgeBase' => [ 'shape' => 'KnowledgeBase', ], ], ], 'UpdatePromptRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'promptIdentifier', ], 'members' => [ 'customerEncryptionKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'defaultVariant' => [ 'shape' => 'PromptVariantName', ], 'description' => [ 'shape' => 'PromptDescription', ], 'name' => [ 'shape' => 'PromptName', ], 'promptIdentifier' => [ 'shape' => 'PromptIdentifier', 'location' => 'uri', 'locationName' => 'promptIdentifier', ], 'variants' => [ 'shape' => 'PromptVariantList', ], ], ], 'UpdatePromptResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'createdAt', 'id', 'name', 'updatedAt', 'version', ], 'members' => [ 'arn' => [ 'shape' => 'PromptArn', ], 'createdAt' => [ 'shape' => 'DateTimestamp', ], 'customerEncryptionKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'defaultVariant' => [ 'shape' => 'PromptVariantName', ], 'description' => [ 'shape' => 'PromptDescription', ], 'id' => [ 'shape' => 'PromptId', ], 'name' => [ 'shape' => 'PromptName', ], 'updatedAt' => [ 'shape' => 'DateTimestamp', ], 'variants' => [ 'shape' => 'PromptVariantList', ], 'version' => [ 'shape' => 'Version', ], ], ], 'Url' => [ 'type' => 'string', 'pattern' => '^https?://[A-Za-z0-9][^\\s]*$', ], 'UrlConfiguration' => [ 'type' => 'structure', 'members' => [ 'seedUrls' => [ 'shape' => 'SeedUrls', ], ], ], 'UserAgent' => [ 'type' => 'string', 'max' => 40, 'min' => 15, 'sensitive' => true, ], 'UserAgentHeader' => [ 'type' => 'string', 'max' => 86, 'min' => 61, 'sensitive' => true, ], 'ValidateFlowDefinitionRequest' => [ 'type' => 'structure', 'required' => [ 'definition', ], 'members' => [ 'definition' => [ 'shape' => 'FlowDefinition', ], ], ], 'ValidateFlowDefinitionResponse' => [ 'type' => 'structure', 'required' => [ 'validations', ], 'members' => [ 'validations' => [ 'shape' => 'FlowValidations', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'fieldList' => [ 'shape' => 'ValidationExceptionFieldList', ], 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'message', 'name', ], 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], 'name' => [ 'shape' => 'NonBlankString', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'VectorIngestionConfiguration' => [ 'type' => 'structure', 'members' => [ 'chunkingConfiguration' => [ 'shape' => 'ChunkingConfiguration', ], 'contextEnrichmentConfiguration' => [ 'shape' => 'ContextEnrichmentConfiguration', ], 'customTransformationConfiguration' => [ 'shape' => 'CustomTransformationConfiguration', ], 'parsingConfiguration' => [ 'shape' => 'ParsingConfiguration', ], ], ], 'VectorKnowledgeBaseConfiguration' => [ 'type' => 'structure', 'required' => [ 'embeddingModelArn', ], 'members' => [ 'embeddingModelArn' => [ 'shape' => 'BedrockEmbeddingModelArn', ], 'embeddingModelConfiguration' => [ 'shape' => 'EmbeddingModelConfiguration', ], 'supplementalDataStorageConfiguration' => [ 'shape' => 'SupplementalDataStorageConfiguration', ], ], ], 'VectorSearchBedrockRerankingConfiguration' => [ 'type' => 'structure', 'required' => [ 'modelConfiguration', ], 'members' => [ 'metadataConfiguration' => [ 'shape' => 'MetadataConfigurationForReranking', ], 'modelConfiguration' => [ 'shape' => 'VectorSearchBedrockRerankingModelConfiguration', ], 'numberOfRerankedResults' => [ 'shape' => 'VectorSearchBedrockRerankingConfigurationNumberOfRerankedResultsInteger', ], ], ], 'VectorSearchBedrockRerankingConfigurationNumberOfRerankedResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'VectorSearchBedrockRerankingModelConfiguration' => [ 'type' => 'structure', 'required' => [ 'modelArn', ], 'members' => [ 'additionalModelRequestFields' => [ 'shape' => 'AdditionalModelRequestFields', ], 'modelArn' => [ 'shape' => 'BedrockRerankingModelArn', ], ], ], 'VectorSearchRerankingConfiguration' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'bedrockRerankingConfiguration' => [ 'shape' => 'VectorSearchBedrockRerankingConfiguration', ], 'type' => [ 'shape' => 'VectorSearchRerankingConfigurationType', ], ], ], 'VectorSearchRerankingConfigurationType' => [ 'type' => 'string', 'enum' => [ 'BEDROCK_RERANKING_MODEL', ], ], 'Version' => [ 'type' => 'string', 'max' => 5, 'min' => 1, 'pattern' => '^(DRAFT|[0-9]{0,4}[1-9][0-9]{0,4})$', ], 'WebCrawlerConfiguration' => [ 'type' => 'structure', 'members' => [ 'crawlerLimits' => [ 'shape' => 'WebCrawlerLimits', ], 'exclusionFilters' => [ 'shape' => 'FilterList', ], 'inclusionFilters' => [ 'shape' => 'FilterList', ], 'scope' => [ 'shape' => 'WebScopeType', ], 'userAgent' => [ 'shape' => 'UserAgent', ], 'userAgentHeader' => [ 'shape' => 'UserAgentHeader', ], ], ], 'WebCrawlerLimits' => [ 'type' => 'structure', 'members' => [ 'maxPages' => [ 'shape' => 'WebCrawlerLimitsMaxPagesInteger', ], 'rateLimit' => [ 'shape' => 'WebCrawlerLimitsRateLimitInteger', ], ], ], 'WebCrawlerLimitsMaxPagesInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'WebCrawlerLimitsRateLimitInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 300, 'min' => 1, ], 'WebDataSourceConfiguration' => [ 'type' => 'structure', 'required' => [ 'sourceConfiguration', ], 'members' => [ 'crawlerConfiguration' => [ 'shape' => 'WebCrawlerConfiguration', ], 'sourceConfiguration' => [ 'shape' => 'WebSourceConfiguration', ], ], ], 'WebScopeType' => [ 'type' => 'string', 'enum' => [ 'HOST_ONLY', 'SUBDOMAINS', ], ], 'WebSourceConfiguration' => [ 'type' => 'structure', 'required' => [ 'urlConfiguration', ], 'members' => [ 'urlConfiguration' => [ 'shape' => 'UrlConfiguration', ], ], ], 'WorkgroupArn' => [ 'type' => 'string', 'pattern' => '^(arn:(aws(-[a-z]+)*):redshift-serverless:[a-z]{2}(-gov)?-[a-z]+-\\d{1}:\\d{12}:workgroup/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})$', ], ],];
