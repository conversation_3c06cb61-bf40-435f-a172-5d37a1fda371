<?php
// This file was auto-generated from sdk-root/src/data/connect/2017-08-08/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2017-08-08', 'endpointPrefix' => 'connect', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceAbbreviation' => 'Amazon Connect', 'serviceFullName' => 'Amazon Connect Service', 'serviceId' => 'Connect', 'signatureVersion' => 'v4', 'signingName' => 'connect', 'uid' => 'connect-2017-08-08', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'ActivateEvaluationForm' => [ 'name' => 'ActivateEvaluationForm', 'http' => [ 'method' => 'POST', 'requestUri' => '/evaluation-forms/{InstanceId}/{EvaluationFormId}/activate', ], 'input' => [ 'shape' => 'ActivateEvaluationFormRequest', ], 'output' => [ 'shape' => 'ActivateEvaluationFormResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ResourceConflictException', ], ], ], 'AssociateAnalyticsDataSet' => [ 'name' => 'AssociateAnalyticsDataSet', 'http' => [ 'method' => 'PUT', 'requestUri' => '/analytics-data/instance/{InstanceId}/association', ], 'input' => [ 'shape' => 'AssociateAnalyticsDataSetRequest', ], 'output' => [ 'shape' => 'AssociateAnalyticsDataSetResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'AssociateApprovedOrigin' => [ 'name' => 'AssociateApprovedOrigin', 'http' => [ 'method' => 'PUT', 'requestUri' => '/instance/{InstanceId}/approved-origin', ], 'input' => [ 'shape' => 'AssociateApprovedOriginRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'AssociateBot' => [ 'name' => 'AssociateBot', 'http' => [ 'method' => 'PUT', 'requestUri' => '/instance/{InstanceId}/bot', ], 'input' => [ 'shape' => 'AssociateBotRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'AssociateDefaultVocabulary' => [ 'name' => 'AssociateDefaultVocabulary', 'http' => [ 'method' => 'PUT', 'requestUri' => '/default-vocabulary/{InstanceId}/{LanguageCode}', ], 'input' => [ 'shape' => 'AssociateDefaultVocabularyRequest', ], 'output' => [ 'shape' => 'AssociateDefaultVocabularyResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'AssociateFlow' => [ 'name' => 'AssociateFlow', 'http' => [ 'method' => 'PUT', 'requestUri' => '/flow-associations/{InstanceId}', ], 'input' => [ 'shape' => 'AssociateFlowRequest', ], 'output' => [ 'shape' => 'AssociateFlowResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'AssociateInstanceStorageConfig' => [ 'name' => 'AssociateInstanceStorageConfig', 'http' => [ 'method' => 'PUT', 'requestUri' => '/instance/{InstanceId}/storage-config', ], 'input' => [ 'shape' => 'AssociateInstanceStorageConfigRequest', ], 'output' => [ 'shape' => 'AssociateInstanceStorageConfigResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'AssociateLambdaFunction' => [ 'name' => 'AssociateLambdaFunction', 'http' => [ 'method' => 'PUT', 'requestUri' => '/instance/{InstanceId}/lambda-function', ], 'input' => [ 'shape' => 'AssociateLambdaFunctionRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'AssociateLexBot' => [ 'name' => 'AssociateLexBot', 'http' => [ 'method' => 'PUT', 'requestUri' => '/instance/{InstanceId}/lex-bot', ], 'input' => [ 'shape' => 'AssociateLexBotRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'AssociatePhoneNumberContactFlow' => [ 'name' => 'AssociatePhoneNumberContactFlow', 'http' => [ 'method' => 'PUT', 'requestUri' => '/phone-number/{PhoneNumberId}/contact-flow', ], 'input' => [ 'shape' => 'AssociatePhoneNumberContactFlowRequest', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'AssociateQueueQuickConnects' => [ 'name' => 'AssociateQueueQuickConnects', 'http' => [ 'method' => 'POST', 'requestUri' => '/queues/{InstanceId}/{QueueId}/associate-quick-connects', ], 'input' => [ 'shape' => 'AssociateQueueQuickConnectsRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'AssociateRoutingProfileQueues' => [ 'name' => 'AssociateRoutingProfileQueues', 'http' => [ 'method' => 'POST', 'requestUri' => '/routing-profiles/{InstanceId}/{RoutingProfileId}/associate-queues', ], 'input' => [ 'shape' => 'AssociateRoutingProfileQueuesRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'AssociateSecurityKey' => [ 'name' => 'AssociateSecurityKey', 'http' => [ 'method' => 'PUT', 'requestUri' => '/instance/{InstanceId}/security-key', ], 'input' => [ 'shape' => 'AssociateSecurityKeyRequest', ], 'output' => [ 'shape' => 'AssociateSecurityKeyResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'AssociateTrafficDistributionGroupUser' => [ 'name' => 'AssociateTrafficDistributionGroupUser', 'http' => [ 'method' => 'PUT', 'requestUri' => '/traffic-distribution-group/{TrafficDistributionGroupId}/user', ], 'input' => [ 'shape' => 'AssociateTrafficDistributionGroupUserRequest', ], 'output' => [ 'shape' => 'AssociateTrafficDistributionGroupUserResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'InternalServiceException', ], ], 'idempotent' => true, ], 'AssociateUserProficiencies' => [ 'name' => 'AssociateUserProficiencies', 'http' => [ 'method' => 'POST', 'requestUri' => '/users/{InstanceId}/{UserId}/associate-proficiencies', ], 'input' => [ 'shape' => 'AssociateUserProficienciesRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'BatchAssociateAnalyticsDataSet' => [ 'name' => 'BatchAssociateAnalyticsDataSet', 'http' => [ 'method' => 'PUT', 'requestUri' => '/analytics-data/instance/{InstanceId}/associations', ], 'input' => [ 'shape' => 'BatchAssociateAnalyticsDataSetRequest', ], 'output' => [ 'shape' => 'BatchAssociateAnalyticsDataSetResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'BatchDisassociateAnalyticsDataSet' => [ 'name' => 'BatchDisassociateAnalyticsDataSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/analytics-data/instance/{InstanceId}/associations', ], 'input' => [ 'shape' => 'BatchDisassociateAnalyticsDataSetRequest', ], 'output' => [ 'shape' => 'BatchDisassociateAnalyticsDataSetResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'BatchGetAttachedFileMetadata' => [ 'name' => 'BatchGetAttachedFileMetadata', 'http' => [ 'method' => 'POST', 'requestUri' => '/attached-files/{InstanceId}', ], 'input' => [ 'shape' => 'BatchGetAttachedFileMetadataRequest', ], 'output' => [ 'shape' => 'BatchGetAttachedFileMetadataResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'BatchGetFlowAssociation' => [ 'name' => 'BatchGetFlowAssociation', 'http' => [ 'method' => 'POST', 'requestUri' => '/flow-associations-batch/{InstanceId}', ], 'input' => [ 'shape' => 'BatchGetFlowAssociationRequest', ], 'output' => [ 'shape' => 'BatchGetFlowAssociationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'BatchPutContact' => [ 'name' => 'BatchPutContact', 'http' => [ 'method' => 'PUT', 'requestUri' => '/contact/batch/{InstanceId}', ], 'input' => [ 'shape' => 'BatchPutContactRequest', ], 'output' => [ 'shape' => 'BatchPutContactResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'IdempotencyException', ], ], 'idempotent' => true, ], 'ClaimPhoneNumber' => [ 'name' => 'ClaimPhoneNumber', 'http' => [ 'method' => 'POST', 'requestUri' => '/phone-number/claim', ], 'input' => [ 'shape' => 'ClaimPhoneNumberRequest', ], 'output' => [ 'shape' => 'ClaimPhoneNumberResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'IdempotencyException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CompleteAttachedFileUpload' => [ 'name' => 'CompleteAttachedFileUpload', 'http' => [ 'method' => 'POST', 'requestUri' => '/attached-files/{InstanceId}/{FileId}', ], 'input' => [ 'shape' => 'CompleteAttachedFileUploadRequest', ], 'output' => [ 'shape' => 'CompleteAttachedFileUploadResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateAgentStatus' => [ 'name' => 'CreateAgentStatus', 'http' => [ 'method' => 'PUT', 'requestUri' => '/agent-status/{InstanceId}', ], 'input' => [ 'shape' => 'CreateAgentStatusRequest', ], 'output' => [ 'shape' => 'CreateAgentStatusResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'CreateContact' => [ 'name' => 'CreateContact', 'http' => [ 'method' => 'PUT', 'requestUri' => '/contact/create-contact', ], 'input' => [ 'shape' => 'CreateContactRequest', ], 'output' => [ 'shape' => 'CreateContactResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'IdempotencyException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'CreateContactFlow' => [ 'name' => 'CreateContactFlow', 'http' => [ 'method' => 'PUT', 'requestUri' => '/contact-flows/{InstanceId}', ], 'input' => [ 'shape' => 'CreateContactFlowRequest', ], 'output' => [ 'shape' => 'CreateContactFlowResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidContactFlowException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'CreateContactFlowModule' => [ 'name' => 'CreateContactFlowModule', 'http' => [ 'method' => 'PUT', 'requestUri' => '/contact-flow-modules/{InstanceId}', ], 'input' => [ 'shape' => 'CreateContactFlowModuleRequest', ], 'output' => [ 'shape' => 'CreateContactFlowModuleResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidContactFlowModuleException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'IdempotencyException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'CreateContactFlowVersion' => [ 'name' => 'CreateContactFlowVersion', 'http' => [ 'method' => 'PUT', 'requestUri' => '/contact-flows/{InstanceId}/{ContactFlowId}/version', ], 'input' => [ 'shape' => 'CreateContactFlowVersionRequest', ], 'output' => [ 'shape' => 'CreateContactFlowVersionResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateEmailAddress' => [ 'name' => 'CreateEmailAddress', 'http' => [ 'method' => 'PUT', 'requestUri' => '/email-addresses/{InstanceId}', ], 'input' => [ 'shape' => 'CreateEmailAddressRequest', ], 'output' => [ 'shape' => 'CreateEmailAddressResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'IdempotencyException', ], [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'ResourceConflictException', ], ], ], 'CreateEvaluationForm' => [ 'name' => 'CreateEvaluationForm', 'http' => [ 'method' => 'PUT', 'requestUri' => '/evaluation-forms/{InstanceId}', ], 'input' => [ 'shape' => 'CreateEvaluationFormRequest', ], 'output' => [ 'shape' => 'CreateEvaluationFormResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceConflictException', ], ], 'idempotent' => true, ], 'CreateHoursOfOperation' => [ 'name' => 'CreateHoursOfOperation', 'http' => [ 'method' => 'PUT', 'requestUri' => '/hours-of-operations/{InstanceId}', ], 'input' => [ 'shape' => 'CreateHoursOfOperationRequest', ], 'output' => [ 'shape' => 'CreateHoursOfOperationResponse', ], 'errors' => [ [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'CreateHoursOfOperationOverride' => [ 'name' => 'CreateHoursOfOperationOverride', 'http' => [ 'method' => 'PUT', 'requestUri' => '/hours-of-operations/{InstanceId}/{HoursOfOperationId}/overrides', ], 'input' => [ 'shape' => 'CreateHoursOfOperationOverrideRequest', ], 'output' => [ 'shape' => 'CreateHoursOfOperationOverrideResponse', ], 'errors' => [ [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'CreateInstance' => [ 'name' => 'CreateInstance', 'http' => [ 'method' => 'PUT', 'requestUri' => '/instance', ], 'input' => [ 'shape' => 'CreateInstanceRequest', ], 'output' => [ 'shape' => 'CreateInstanceResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'CreateIntegrationAssociation' => [ 'name' => 'CreateIntegrationAssociation', 'http' => [ 'method' => 'PUT', 'requestUri' => '/instance/{InstanceId}/integration-associations', ], 'input' => [ 'shape' => 'CreateIntegrationAssociationRequest', ], 'output' => [ 'shape' => 'CreateIntegrationAssociationResponse', ], 'errors' => [ [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateParticipant' => [ 'name' => 'CreateParticipant', 'http' => [ 'method' => 'POST', 'requestUri' => '/contact/create-participant', ], 'input' => [ 'shape' => 'CreateParticipantRequest', ], 'output' => [ 'shape' => 'CreateParticipantResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreatePersistentContactAssociation' => [ 'name' => 'CreatePersistentContactAssociation', 'http' => [ 'method' => 'POST', 'requestUri' => '/contact/persistent-contact-association/{InstanceId}/{InitialContactId}', ], 'input' => [ 'shape' => 'CreatePersistentContactAssociationRequest', ], 'output' => [ 'shape' => 'CreatePersistentContactAssociationResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'CreatePredefinedAttribute' => [ 'name' => 'CreatePredefinedAttribute', 'http' => [ 'method' => 'PUT', 'requestUri' => '/predefined-attributes/{InstanceId}', ], 'input' => [ 'shape' => 'CreatePredefinedAttributeRequest', ], 'errors' => [ [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'CreatePrompt' => [ 'name' => 'CreatePrompt', 'http' => [ 'method' => 'PUT', 'requestUri' => '/prompts/{InstanceId}', ], 'input' => [ 'shape' => 'CreatePromptRequest', ], 'output' => [ 'shape' => 'CreatePromptResponse', ], 'errors' => [ [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'CreatePushNotificationRegistration' => [ 'name' => 'CreatePushNotificationRegistration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/push-notification/{InstanceId}/registrations', ], 'input' => [ 'shape' => 'CreatePushNotificationRegistrationRequest', ], 'output' => [ 'shape' => 'CreatePushNotificationRegistrationResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'CreateQueue' => [ 'name' => 'CreateQueue', 'http' => [ 'method' => 'PUT', 'requestUri' => '/queues/{InstanceId}', ], 'input' => [ 'shape' => 'CreateQueueRequest', ], 'output' => [ 'shape' => 'CreateQueueResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'CreateQuickConnect' => [ 'name' => 'CreateQuickConnect', 'http' => [ 'method' => 'PUT', 'requestUri' => '/quick-connects/{InstanceId}', ], 'input' => [ 'shape' => 'CreateQuickConnectRequest', ], 'output' => [ 'shape' => 'CreateQuickConnectResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'CreateRoutingProfile' => [ 'name' => 'CreateRoutingProfile', 'http' => [ 'method' => 'PUT', 'requestUri' => '/routing-profiles/{InstanceId}', ], 'input' => [ 'shape' => 'CreateRoutingProfileRequest', ], 'output' => [ 'shape' => 'CreateRoutingProfileResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'CreateRule' => [ 'name' => 'CreateRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/rules/{InstanceId}', ], 'input' => [ 'shape' => 'CreateRuleRequest', ], 'output' => [ 'shape' => 'CreateRuleResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'CreateSecurityProfile' => [ 'name' => 'CreateSecurityProfile', 'http' => [ 'method' => 'PUT', 'requestUri' => '/security-profiles/{InstanceId}', ], 'input' => [ 'shape' => 'CreateSecurityProfileRequest', ], 'output' => [ 'shape' => 'CreateSecurityProfileResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'CreateTaskTemplate' => [ 'name' => 'CreateTaskTemplate', 'http' => [ 'method' => 'PUT', 'requestUri' => '/instance/{InstanceId}/task/template', ], 'input' => [ 'shape' => 'CreateTaskTemplateRequest', ], 'output' => [ 'shape' => 'CreateTaskTemplateResponse', ], 'errors' => [ [ 'shape' => 'PropertyValidationException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'CreateTrafficDistributionGroup' => [ 'name' => 'CreateTrafficDistributionGroup', 'http' => [ 'method' => 'PUT', 'requestUri' => '/traffic-distribution-group', ], 'input' => [ 'shape' => 'CreateTrafficDistributionGroupRequest', ], 'output' => [ 'shape' => 'CreateTrafficDistributionGroupResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'ResourceNotReadyException', ], ], ], 'CreateUseCase' => [ 'name' => 'CreateUseCase', 'http' => [ 'method' => 'PUT', 'requestUri' => '/instance/{InstanceId}/integration-associations/{IntegrationAssociationId}/use-cases', ], 'input' => [ 'shape' => 'CreateUseCaseRequest', ], 'output' => [ 'shape' => 'CreateUseCaseResponse', ], 'errors' => [ [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateUser' => [ 'name' => 'CreateUser', 'http' => [ 'method' => 'PUT', 'requestUri' => '/users/{InstanceId}', ], 'input' => [ 'shape' => 'CreateUserRequest', ], 'output' => [ 'shape' => 'CreateUserResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'CreateUserHierarchyGroup' => [ 'name' => 'CreateUserHierarchyGroup', 'http' => [ 'method' => 'PUT', 'requestUri' => '/user-hierarchy-groups/{InstanceId}', ], 'input' => [ 'shape' => 'CreateUserHierarchyGroupRequest', ], 'output' => [ 'shape' => 'CreateUserHierarchyGroupResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'CreateView' => [ 'name' => 'CreateView', 'http' => [ 'method' => 'PUT', 'requestUri' => '/views/{InstanceId}', ], 'input' => [ 'shape' => 'CreateViewRequest', ], 'output' => [ 'shape' => 'CreateViewResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ResourceInUseException', ], ], 'idempotent' => true, ], 'CreateViewVersion' => [ 'name' => 'CreateViewVersion', 'http' => [ 'method' => 'PUT', 'requestUri' => '/views/{InstanceId}/{ViewId}/versions', ], 'input' => [ 'shape' => 'CreateViewVersionRequest', ], 'output' => [ 'shape' => 'CreateViewVersionResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceInUseException', ], ], 'idempotent' => true, ], 'CreateVocabulary' => [ 'name' => 'CreateVocabulary', 'http' => [ 'method' => 'POST', 'requestUri' => '/vocabulary/{InstanceId}', ], 'input' => [ 'shape' => 'CreateVocabularyRequest', ], 'output' => [ 'shape' => 'CreateVocabularyResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'DeactivateEvaluationForm' => [ 'name' => 'DeactivateEvaluationForm', 'http' => [ 'method' => 'POST', 'requestUri' => '/evaluation-forms/{InstanceId}/{EvaluationFormId}/deactivate', ], 'input' => [ 'shape' => 'DeactivateEvaluationFormRequest', ], 'output' => [ 'shape' => 'DeactivateEvaluationFormResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ResourceConflictException', ], ], ], 'DeleteAttachedFile' => [ 'name' => 'DeleteAttachedFile', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/attached-files/{InstanceId}/{FileId}', ], 'input' => [ 'shape' => 'DeleteAttachedFileRequest', ], 'output' => [ 'shape' => 'DeleteAttachedFileResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeleteContactEvaluation' => [ 'name' => 'DeleteContactEvaluation', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/contact-evaluations/{InstanceId}/{EvaluationId}', ], 'input' => [ 'shape' => 'DeleteContactEvaluationRequest', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ResourceConflictException', ], ], 'idempotent' => true, ], 'DeleteContactFlow' => [ 'name' => 'DeleteContactFlow', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/contact-flows/{InstanceId}/{ContactFlowId}', ], 'input' => [ 'shape' => 'DeleteContactFlowRequest', ], 'output' => [ 'shape' => 'DeleteContactFlowResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeleteContactFlowModule' => [ 'name' => 'DeleteContactFlowModule', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/contact-flow-modules/{InstanceId}/{ContactFlowModuleId}', ], 'input' => [ 'shape' => 'DeleteContactFlowModuleRequest', ], 'output' => [ 'shape' => 'DeleteContactFlowModuleResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DeleteContactFlowVersion' => [ 'name' => 'DeleteContactFlowVersion', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/contact-flows/{InstanceId}/{ContactFlowId}/version/{ContactFlowVersion}', ], 'input' => [ 'shape' => 'DeleteContactFlowVersionRequest', ], 'output' => [ 'shape' => 'DeleteContactFlowVersionResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeleteEmailAddress' => [ 'name' => 'DeleteEmailAddress', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/email-addresses/{InstanceId}/{EmailAddressId}', ], 'input' => [ 'shape' => 'DeleteEmailAddressRequest', ], 'output' => [ 'shape' => 'DeleteEmailAddressResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ResourceConflictException', ], ], ], 'DeleteEvaluationForm' => [ 'name' => 'DeleteEvaluationForm', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/evaluation-forms/{InstanceId}/{EvaluationFormId}', ], 'input' => [ 'shape' => 'DeleteEvaluationFormRequest', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ResourceConflictException', ], ], 'idempotent' => true, ], 'DeleteHoursOfOperation' => [ 'name' => 'DeleteHoursOfOperation', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/hours-of-operations/{InstanceId}/{HoursOfOperationId}', ], 'input' => [ 'shape' => 'DeleteHoursOfOperationRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DeleteHoursOfOperationOverride' => [ 'name' => 'DeleteHoursOfOperationOverride', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/hours-of-operations/{InstanceId}/{HoursOfOperationId}/overrides/{HoursOfOperationOverrideId}', ], 'input' => [ 'shape' => 'DeleteHoursOfOperationOverrideRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DeleteInstance' => [ 'name' => 'DeleteInstance', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/instance/{InstanceId}', ], 'input' => [ 'shape' => 'DeleteInstanceRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'DeleteIntegrationAssociation' => [ 'name' => 'DeleteIntegrationAssociation', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/instance/{InstanceId}/integration-associations/{IntegrationAssociationId}', ], 'input' => [ 'shape' => 'DeleteIntegrationAssociationRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeletePredefinedAttribute' => [ 'name' => 'DeletePredefinedAttribute', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/predefined-attributes/{InstanceId}/{Name}', ], 'input' => [ 'shape' => 'DeletePredefinedAttributeRequest', ], 'errors' => [ [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], 'idempotent' => true, ], 'DeletePrompt' => [ 'name' => 'DeletePrompt', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/prompts/{InstanceId}/{PromptId}', ], 'input' => [ 'shape' => 'DeletePromptRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DeletePushNotificationRegistration' => [ 'name' => 'DeletePushNotificationRegistration', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/push-notification/{InstanceId}/registrations/{RegistrationId}', ], 'input' => [ 'shape' => 'DeletePushNotificationRegistrationRequest', ], 'output' => [ 'shape' => 'DeletePushNotificationRegistrationResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteQueue' => [ 'name' => 'DeleteQueue', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/queues/{InstanceId}/{QueueId}', ], 'input' => [ 'shape' => 'DeleteQueueRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DeleteQuickConnect' => [ 'name' => 'DeleteQuickConnect', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/quick-connects/{InstanceId}/{QuickConnectId}', ], 'input' => [ 'shape' => 'DeleteQuickConnectRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DeleteRoutingProfile' => [ 'name' => 'DeleteRoutingProfile', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/routing-profiles/{InstanceId}/{RoutingProfileId}', ], 'input' => [ 'shape' => 'DeleteRoutingProfileRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DeleteRule' => [ 'name' => 'DeleteRule', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/rules/{InstanceId}/{RuleId}', ], 'input' => [ 'shape' => 'DeleteRuleRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteSecurityProfile' => [ 'name' => 'DeleteSecurityProfile', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/security-profiles/{InstanceId}/{SecurityProfileId}', ], 'input' => [ 'shape' => 'DeleteSecurityProfileRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceInUseException', ], ], ], 'DeleteTaskTemplate' => [ 'name' => 'DeleteTaskTemplate', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/instance/{InstanceId}/task/template/{TaskTemplateId}', ], 'input' => [ 'shape' => 'DeleteTaskTemplateRequest', ], 'output' => [ 'shape' => 'DeleteTaskTemplateResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DeleteTrafficDistributionGroup' => [ 'name' => 'DeleteTrafficDistributionGroup', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/traffic-distribution-group/{TrafficDistributionGroupId}', ], 'input' => [ 'shape' => 'DeleteTrafficDistributionGroupRequest', ], 'output' => [ 'shape' => 'DeleteTrafficDistributionGroupResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DeleteUseCase' => [ 'name' => 'DeleteUseCase', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/instance/{InstanceId}/integration-associations/{IntegrationAssociationId}/use-cases/{UseCaseId}', ], 'input' => [ 'shape' => 'DeleteUseCaseRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeleteUser' => [ 'name' => 'DeleteUser', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/users/{InstanceId}/{UserId}', ], 'input' => [ 'shape' => 'DeleteUserRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DeleteUserHierarchyGroup' => [ 'name' => 'DeleteUserHierarchyGroup', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/user-hierarchy-groups/{InstanceId}/{HierarchyGroupId}', ], 'input' => [ 'shape' => 'DeleteUserHierarchyGroupRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DeleteView' => [ 'name' => 'DeleteView', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/views/{InstanceId}/{ViewId}', ], 'input' => [ 'shape' => 'DeleteViewRequest', ], 'output' => [ 'shape' => 'DeleteViewResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ResourceInUseException', ], ], ], 'DeleteViewVersion' => [ 'name' => 'DeleteViewVersion', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/views/{InstanceId}/{ViewId}/versions/{ViewVersion}', ], 'input' => [ 'shape' => 'DeleteViewVersionRequest', ], 'output' => [ 'shape' => 'DeleteViewVersionResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ResourceInUseException', ], ], ], 'DeleteVocabulary' => [ 'name' => 'DeleteVocabulary', 'http' => [ 'method' => 'POST', 'requestUri' => '/vocabulary-remove/{InstanceId}/{VocabularyId}', ], 'input' => [ 'shape' => 'DeleteVocabularyRequest', ], 'output' => [ 'shape' => 'DeleteVocabularyResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceInUseException', ], ], ], 'DescribeAgentStatus' => [ 'name' => 'DescribeAgentStatus', 'http' => [ 'method' => 'GET', 'requestUri' => '/agent-status/{InstanceId}/{AgentStatusId}', ], 'input' => [ 'shape' => 'DescribeAgentStatusRequest', ], 'output' => [ 'shape' => 'DescribeAgentStatusResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DescribeAuthenticationProfile' => [ 'name' => 'DescribeAuthenticationProfile', 'http' => [ 'method' => 'GET', 'requestUri' => '/authentication-profiles/{InstanceId}/{AuthenticationProfileId}', ], 'input' => [ 'shape' => 'DescribeAuthenticationProfileRequest', ], 'output' => [ 'shape' => 'DescribeAuthenticationProfileResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DescribeContact' => [ 'name' => 'DescribeContact', 'http' => [ 'method' => 'GET', 'requestUri' => '/contacts/{InstanceId}/{ContactId}', ], 'input' => [ 'shape' => 'DescribeContactRequest', ], 'output' => [ 'shape' => 'DescribeContactResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DescribeContactEvaluation' => [ 'name' => 'DescribeContactEvaluation', 'http' => [ 'method' => 'GET', 'requestUri' => '/contact-evaluations/{InstanceId}/{EvaluationId}', ], 'input' => [ 'shape' => 'DescribeContactEvaluationRequest', ], 'output' => [ 'shape' => 'DescribeContactEvaluationResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DescribeContactFlow' => [ 'name' => 'DescribeContactFlow', 'http' => [ 'method' => 'GET', 'requestUri' => '/contact-flows/{InstanceId}/{ContactFlowId}', ], 'input' => [ 'shape' => 'DescribeContactFlowRequest', ], 'output' => [ 'shape' => 'DescribeContactFlowResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ContactFlowNotPublishedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DescribeContactFlowModule' => [ 'name' => 'DescribeContactFlowModule', 'http' => [ 'method' => 'GET', 'requestUri' => '/contact-flow-modules/{InstanceId}/{ContactFlowModuleId}', ], 'input' => [ 'shape' => 'DescribeContactFlowModuleRequest', ], 'output' => [ 'shape' => 'DescribeContactFlowModuleResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DescribeEmailAddress' => [ 'name' => 'DescribeEmailAddress', 'http' => [ 'method' => 'GET', 'requestUri' => '/email-addresses/{InstanceId}/{EmailAddressId}', ], 'input' => [ 'shape' => 'DescribeEmailAddressRequest', ], 'output' => [ 'shape' => 'DescribeEmailAddressResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DescribeEvaluationForm' => [ 'name' => 'DescribeEvaluationForm', 'http' => [ 'method' => 'GET', 'requestUri' => '/evaluation-forms/{InstanceId}/{EvaluationFormId}', ], 'input' => [ 'shape' => 'DescribeEvaluationFormRequest', ], 'output' => [ 'shape' => 'DescribeEvaluationFormResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DescribeHoursOfOperation' => [ 'name' => 'DescribeHoursOfOperation', 'http' => [ 'method' => 'GET', 'requestUri' => '/hours-of-operations/{InstanceId}/{HoursOfOperationId}', ], 'input' => [ 'shape' => 'DescribeHoursOfOperationRequest', ], 'output' => [ 'shape' => 'DescribeHoursOfOperationResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DescribeHoursOfOperationOverride' => [ 'name' => 'DescribeHoursOfOperationOverride', 'http' => [ 'method' => 'GET', 'requestUri' => '/hours-of-operations/{InstanceId}/{HoursOfOperationId}/overrides/{HoursOfOperationOverrideId}', ], 'input' => [ 'shape' => 'DescribeHoursOfOperationOverrideRequest', ], 'output' => [ 'shape' => 'DescribeHoursOfOperationOverrideResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DescribeInstance' => [ 'name' => 'DescribeInstance', 'http' => [ 'method' => 'GET', 'requestUri' => '/instance/{InstanceId}', ], 'input' => [ 'shape' => 'DescribeInstanceRequest', ], 'output' => [ 'shape' => 'DescribeInstanceResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DescribeInstanceAttribute' => [ 'name' => 'DescribeInstanceAttribute', 'http' => [ 'method' => 'GET', 'requestUri' => '/instance/{InstanceId}/attribute/{AttributeType}', ], 'input' => [ 'shape' => 'DescribeInstanceAttributeRequest', ], 'output' => [ 'shape' => 'DescribeInstanceAttributeResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DescribeInstanceStorageConfig' => [ 'name' => 'DescribeInstanceStorageConfig', 'http' => [ 'method' => 'GET', 'requestUri' => '/instance/{InstanceId}/storage-config/{AssociationId}', ], 'input' => [ 'shape' => 'DescribeInstanceStorageConfigRequest', ], 'output' => [ 'shape' => 'DescribeInstanceStorageConfigResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DescribePhoneNumber' => [ 'name' => 'DescribePhoneNumber', 'http' => [ 'method' => 'GET', 'requestUri' => '/phone-number/{PhoneNumberId}', ], 'input' => [ 'shape' => 'DescribePhoneNumberRequest', ], 'output' => [ 'shape' => 'DescribePhoneNumberResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DescribePredefinedAttribute' => [ 'name' => 'DescribePredefinedAttribute', 'http' => [ 'method' => 'GET', 'requestUri' => '/predefined-attributes/{InstanceId}/{Name}', ], 'input' => [ 'shape' => 'DescribePredefinedAttributeRequest', ], 'output' => [ 'shape' => 'DescribePredefinedAttributeResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DescribePrompt' => [ 'name' => 'DescribePrompt', 'http' => [ 'method' => 'GET', 'requestUri' => '/prompts/{InstanceId}/{PromptId}', ], 'input' => [ 'shape' => 'DescribePromptRequest', ], 'output' => [ 'shape' => 'DescribePromptResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DescribeQueue' => [ 'name' => 'DescribeQueue', 'http' => [ 'method' => 'GET', 'requestUri' => '/queues/{InstanceId}/{QueueId}', ], 'input' => [ 'shape' => 'DescribeQueueRequest', ], 'output' => [ 'shape' => 'DescribeQueueResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DescribeQuickConnect' => [ 'name' => 'DescribeQuickConnect', 'http' => [ 'method' => 'GET', 'requestUri' => '/quick-connects/{InstanceId}/{QuickConnectId}', ], 'input' => [ 'shape' => 'DescribeQuickConnectRequest', ], 'output' => [ 'shape' => 'DescribeQuickConnectResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DescribeRoutingProfile' => [ 'name' => 'DescribeRoutingProfile', 'http' => [ 'method' => 'GET', 'requestUri' => '/routing-profiles/{InstanceId}/{RoutingProfileId}', ], 'input' => [ 'shape' => 'DescribeRoutingProfileRequest', ], 'output' => [ 'shape' => 'DescribeRoutingProfileResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DescribeRule' => [ 'name' => 'DescribeRule', 'http' => [ 'method' => 'GET', 'requestUri' => '/rules/{InstanceId}/{RuleId}', ], 'input' => [ 'shape' => 'DescribeRuleRequest', ], 'output' => [ 'shape' => 'DescribeRuleResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DescribeSecurityProfile' => [ 'name' => 'DescribeSecurityProfile', 'http' => [ 'method' => 'GET', 'requestUri' => '/security-profiles/{InstanceId}/{SecurityProfileId}', ], 'input' => [ 'shape' => 'DescribeSecurityProfileRequest', ], 'output' => [ 'shape' => 'DescribeSecurityProfileResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DescribeTrafficDistributionGroup' => [ 'name' => 'DescribeTrafficDistributionGroup', 'http' => [ 'method' => 'GET', 'requestUri' => '/traffic-distribution-group/{TrafficDistributionGroupId}', ], 'input' => [ 'shape' => 'DescribeTrafficDistributionGroupRequest', ], 'output' => [ 'shape' => 'DescribeTrafficDistributionGroupResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DescribeUser' => [ 'name' => 'DescribeUser', 'http' => [ 'method' => 'GET', 'requestUri' => '/users/{InstanceId}/{UserId}', ], 'input' => [ 'shape' => 'DescribeUserRequest', ], 'output' => [ 'shape' => 'DescribeUserResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DescribeUserHierarchyGroup' => [ 'name' => 'DescribeUserHierarchyGroup', 'http' => [ 'method' => 'GET', 'requestUri' => '/user-hierarchy-groups/{InstanceId}/{HierarchyGroupId}', ], 'input' => [ 'shape' => 'DescribeUserHierarchyGroupRequest', ], 'output' => [ 'shape' => 'DescribeUserHierarchyGroupResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DescribeUserHierarchyStructure' => [ 'name' => 'DescribeUserHierarchyStructure', 'http' => [ 'method' => 'GET', 'requestUri' => '/user-hierarchy-structure/{InstanceId}', ], 'input' => [ 'shape' => 'DescribeUserHierarchyStructureRequest', ], 'output' => [ 'shape' => 'DescribeUserHierarchyStructureResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DescribeView' => [ 'name' => 'DescribeView', 'http' => [ 'method' => 'GET', 'requestUri' => '/views/{InstanceId}/{ViewId}', ], 'input' => [ 'shape' => 'DescribeViewRequest', ], 'output' => [ 'shape' => 'DescribeViewResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'DescribeVocabulary' => [ 'name' => 'DescribeVocabulary', 'http' => [ 'method' => 'GET', 'requestUri' => '/vocabulary/{InstanceId}/{VocabularyId}', ], 'input' => [ 'shape' => 'DescribeVocabularyRequest', ], 'output' => [ 'shape' => 'DescribeVocabularyResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DisassociateAnalyticsDataSet' => [ 'name' => 'DisassociateAnalyticsDataSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/analytics-data/instance/{InstanceId}/association', ], 'input' => [ 'shape' => 'DisassociateAnalyticsDataSetRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DisassociateApprovedOrigin' => [ 'name' => 'DisassociateApprovedOrigin', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/instance/{InstanceId}/approved-origin', ], 'input' => [ 'shape' => 'DisassociateApprovedOriginRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DisassociateBot' => [ 'name' => 'DisassociateBot', 'http' => [ 'method' => 'POST', 'requestUri' => '/instance/{InstanceId}/bot', ], 'input' => [ 'shape' => 'DisassociateBotRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DisassociateFlow' => [ 'name' => 'DisassociateFlow', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/flow-associations/{InstanceId}/{ResourceId}/{ResourceType}', ], 'input' => [ 'shape' => 'DisassociateFlowRequest', ], 'output' => [ 'shape' => 'DisassociateFlowResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DisassociateInstanceStorageConfig' => [ 'name' => 'DisassociateInstanceStorageConfig', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/instance/{InstanceId}/storage-config/{AssociationId}', ], 'input' => [ 'shape' => 'DisassociateInstanceStorageConfigRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DisassociateLambdaFunction' => [ 'name' => 'DisassociateLambdaFunction', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/instance/{InstanceId}/lambda-function', ], 'input' => [ 'shape' => 'DisassociateLambdaFunctionRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DisassociateLexBot' => [ 'name' => 'DisassociateLexBot', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/instance/{InstanceId}/lex-bot', ], 'input' => [ 'shape' => 'DisassociateLexBotRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DisassociatePhoneNumberContactFlow' => [ 'name' => 'DisassociatePhoneNumberContactFlow', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/phone-number/{PhoneNumberId}/contact-flow', ], 'input' => [ 'shape' => 'DisassociatePhoneNumberContactFlowRequest', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DisassociateQueueQuickConnects' => [ 'name' => 'DisassociateQueueQuickConnects', 'http' => [ 'method' => 'POST', 'requestUri' => '/queues/{InstanceId}/{QueueId}/disassociate-quick-connects', ], 'input' => [ 'shape' => 'DisassociateQueueQuickConnectsRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DisassociateRoutingProfileQueues' => [ 'name' => 'DisassociateRoutingProfileQueues', 'http' => [ 'method' => 'POST', 'requestUri' => '/routing-profiles/{InstanceId}/{RoutingProfileId}/disassociate-queues', ], 'input' => [ 'shape' => 'DisassociateRoutingProfileQueuesRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DisassociateSecurityKey' => [ 'name' => 'DisassociateSecurityKey', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/instance/{InstanceId}/security-key/{AssociationId}', ], 'input' => [ 'shape' => 'DisassociateSecurityKeyRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DisassociateTrafficDistributionGroupUser' => [ 'name' => 'DisassociateTrafficDistributionGroupUser', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/traffic-distribution-group/{TrafficDistributionGroupId}/user', ], 'input' => [ 'shape' => 'DisassociateTrafficDistributionGroupUserRequest', ], 'output' => [ 'shape' => 'DisassociateTrafficDistributionGroupUserResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'InternalServiceException', ], ], 'idempotent' => true, ], 'DisassociateUserProficiencies' => [ 'name' => 'DisassociateUserProficiencies', 'http' => [ 'method' => 'POST', 'requestUri' => '/users/{InstanceId}/{UserId}/disassociate-proficiencies', ], 'input' => [ 'shape' => 'DisassociateUserProficienciesRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DismissUserContact' => [ 'name' => 'DismissUserContact', 'http' => [ 'method' => 'POST', 'requestUri' => '/users/{InstanceId}/{UserId}/contact', ], 'input' => [ 'shape' => 'DismissUserContactRequest', ], 'output' => [ 'shape' => 'DismissUserContactResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'GetAttachedFile' => [ 'name' => 'GetAttachedFile', 'http' => [ 'method' => 'GET', 'requestUri' => '/attached-files/{InstanceId}/{FileId}', ], 'input' => [ 'shape' => 'GetAttachedFileRequest', ], 'output' => [ 'shape' => 'GetAttachedFileResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetContactAttributes' => [ 'name' => 'GetContactAttributes', 'http' => [ 'method' => 'GET', 'requestUri' => '/contact/attributes/{InstanceId}/{InitialContactId}', ], 'input' => [ 'shape' => 'GetContactAttributesRequest', ], 'output' => [ 'shape' => 'GetContactAttributesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'GetCurrentMetricData' => [ 'name' => 'GetCurrentMetricData', 'http' => [ 'method' => 'POST', 'requestUri' => '/metrics/current/{InstanceId}', ], 'input' => [ 'shape' => 'GetCurrentMetricDataRequest', ], 'output' => [ 'shape' => 'GetCurrentMetricDataResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetCurrentUserData' => [ 'name' => 'GetCurrentUserData', 'http' => [ 'method' => 'POST', 'requestUri' => '/metrics/userdata/{InstanceId}', ], 'input' => [ 'shape' => 'GetCurrentUserDataRequest', ], 'output' => [ 'shape' => 'GetCurrentUserDataResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetEffectiveHoursOfOperations' => [ 'name' => 'GetEffectiveHoursOfOperations', 'http' => [ 'method' => 'GET', 'requestUri' => '/effective-hours-of-operations/{InstanceId}/{HoursOfOperationId}', ], 'input' => [ 'shape' => 'GetEffectiveHoursOfOperationsRequest', ], 'output' => [ 'shape' => 'GetEffectiveHoursOfOperationsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'GetFederationToken' => [ 'name' => 'GetFederationToken', 'http' => [ 'method' => 'GET', 'requestUri' => '/user/federate/{InstanceId}', ], 'input' => [ 'shape' => 'GetFederationTokenRequest', ], 'output' => [ 'shape' => 'GetFederationTokenResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'DuplicateResourceException', ], ], ], 'GetFlowAssociation' => [ 'name' => 'GetFlowAssociation', 'http' => [ 'method' => 'GET', 'requestUri' => '/flow-associations/{InstanceId}/{ResourceId}/{ResourceType}', ], 'input' => [ 'shape' => 'GetFlowAssociationRequest', ], 'output' => [ 'shape' => 'GetFlowAssociationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetMetricData' => [ 'name' => 'GetMetricData', 'http' => [ 'method' => 'POST', 'requestUri' => '/metrics/historical/{InstanceId}', ], 'input' => [ 'shape' => 'GetMetricDataRequest', ], 'output' => [ 'shape' => 'GetMetricDataResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetMetricDataV2' => [ 'name' => 'GetMetricDataV2', 'http' => [ 'method' => 'POST', 'requestUri' => '/metrics/data', ], 'input' => [ 'shape' => 'GetMetricDataV2Request', ], 'output' => [ 'shape' => 'GetMetricDataV2Response', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetPromptFile' => [ 'name' => 'GetPromptFile', 'http' => [ 'method' => 'GET', 'requestUri' => '/prompts/{InstanceId}/{PromptId}/file', ], 'input' => [ 'shape' => 'GetPromptFileRequest', ], 'output' => [ 'shape' => 'GetPromptFileResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'GetTaskTemplate' => [ 'name' => 'GetTaskTemplate', 'http' => [ 'method' => 'GET', 'requestUri' => '/instance/{InstanceId}/task/template/{TaskTemplateId}', ], 'input' => [ 'shape' => 'GetTaskTemplateRequest', ], 'output' => [ 'shape' => 'GetTaskTemplateResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'GetTrafficDistribution' => [ 'name' => 'GetTrafficDistribution', 'http' => [ 'method' => 'GET', 'requestUri' => '/traffic-distribution/{Id}', ], 'input' => [ 'shape' => 'GetTrafficDistributionRequest', ], 'output' => [ 'shape' => 'GetTrafficDistributionResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ImportPhoneNumber' => [ 'name' => 'ImportPhoneNumber', 'http' => [ 'method' => 'POST', 'requestUri' => '/phone-number/import', ], 'input' => [ 'shape' => 'ImportPhoneNumberRequest', ], 'output' => [ 'shape' => 'ImportPhoneNumberResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'IdempotencyException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListAgentStatuses' => [ 'name' => 'ListAgentStatuses', 'http' => [ 'method' => 'GET', 'requestUri' => '/agent-status/{InstanceId}', ], 'input' => [ 'shape' => 'ListAgentStatusRequest', ], 'output' => [ 'shape' => 'ListAgentStatusResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListAnalyticsDataAssociations' => [ 'name' => 'ListAnalyticsDataAssociations', 'http' => [ 'method' => 'GET', 'requestUri' => '/analytics-data/instance/{InstanceId}/association', ], 'input' => [ 'shape' => 'ListAnalyticsDataAssociationsRequest', ], 'output' => [ 'shape' => 'ListAnalyticsDataAssociationsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListAnalyticsDataLakeDataSets' => [ 'name' => 'ListAnalyticsDataLakeDataSets', 'http' => [ 'method' => 'GET', 'requestUri' => '/analytics-data/instance/{InstanceId}/datasets', ], 'input' => [ 'shape' => 'ListAnalyticsDataLakeDataSetsRequest', ], 'output' => [ 'shape' => 'ListAnalyticsDataLakeDataSetsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListApprovedOrigins' => [ 'name' => 'ListApprovedOrigins', 'http' => [ 'method' => 'GET', 'requestUri' => '/instance/{InstanceId}/approved-origins', ], 'input' => [ 'shape' => 'ListApprovedOriginsRequest', ], 'output' => [ 'shape' => 'ListApprovedOriginsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListAssociatedContacts' => [ 'name' => 'ListAssociatedContacts', 'http' => [ 'method' => 'GET', 'requestUri' => '/contact/associated/{InstanceId}', ], 'input' => [ 'shape' => 'ListAssociatedContactsRequest', ], 'output' => [ 'shape' => 'ListAssociatedContactsResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListAuthenticationProfiles' => [ 'name' => 'ListAuthenticationProfiles', 'http' => [ 'method' => 'GET', 'requestUri' => '/authentication-profiles-summary/{InstanceId}', ], 'input' => [ 'shape' => 'ListAuthenticationProfilesRequest', ], 'output' => [ 'shape' => 'ListAuthenticationProfilesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListBots' => [ 'name' => 'ListBots', 'http' => [ 'method' => 'GET', 'requestUri' => '/instance/{InstanceId}/bots', ], 'input' => [ 'shape' => 'ListBotsRequest', ], 'output' => [ 'shape' => 'ListBotsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListContactEvaluations' => [ 'name' => 'ListContactEvaluations', 'http' => [ 'method' => 'GET', 'requestUri' => '/contact-evaluations/{InstanceId}', ], 'input' => [ 'shape' => 'ListContactEvaluationsRequest', ], 'output' => [ 'shape' => 'ListContactEvaluationsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListContactFlowModules' => [ 'name' => 'ListContactFlowModules', 'http' => [ 'method' => 'GET', 'requestUri' => '/contact-flow-modules-summary/{InstanceId}', ], 'input' => [ 'shape' => 'ListContactFlowModulesRequest', ], 'output' => [ 'shape' => 'ListContactFlowModulesResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListContactFlowVersions' => [ 'name' => 'ListContactFlowVersions', 'http' => [ 'method' => 'GET', 'requestUri' => '/contact-flows/{InstanceId}/{ContactFlowId}/versions', ], 'input' => [ 'shape' => 'ListContactFlowVersionsRequest', ], 'output' => [ 'shape' => 'ListContactFlowVersionsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListContactFlows' => [ 'name' => 'ListContactFlows', 'http' => [ 'method' => 'GET', 'requestUri' => '/contact-flows-summary/{InstanceId}', ], 'input' => [ 'shape' => 'ListContactFlowsRequest', ], 'output' => [ 'shape' => 'ListContactFlowsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListContactReferences' => [ 'name' => 'ListContactReferences', 'http' => [ 'method' => 'GET', 'requestUri' => '/contact/references/{InstanceId}/{ContactId}', ], 'input' => [ 'shape' => 'ListContactReferencesRequest', ], 'output' => [ 'shape' => 'ListContactReferencesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListDefaultVocabularies' => [ 'name' => 'ListDefaultVocabularies', 'http' => [ 'method' => 'POST', 'requestUri' => '/default-vocabulary-summary/{InstanceId}', ], 'input' => [ 'shape' => 'ListDefaultVocabulariesRequest', ], 'output' => [ 'shape' => 'ListDefaultVocabulariesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListEvaluationFormVersions' => [ 'name' => 'ListEvaluationFormVersions', 'http' => [ 'method' => 'GET', 'requestUri' => '/evaluation-forms/{InstanceId}/{EvaluationFormId}/versions', ], 'input' => [ 'shape' => 'ListEvaluationFormVersionsRequest', ], 'output' => [ 'shape' => 'ListEvaluationFormVersionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListEvaluationForms' => [ 'name' => 'ListEvaluationForms', 'http' => [ 'method' => 'GET', 'requestUri' => '/evaluation-forms/{InstanceId}', ], 'input' => [ 'shape' => 'ListEvaluationFormsRequest', ], 'output' => [ 'shape' => 'ListEvaluationFormsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListFlowAssociations' => [ 'name' => 'ListFlowAssociations', 'http' => [ 'method' => 'GET', 'requestUri' => '/flow-associations-summary/{InstanceId}', ], 'input' => [ 'shape' => 'ListFlowAssociationsRequest', ], 'output' => [ 'shape' => 'ListFlowAssociationsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListHoursOfOperationOverrides' => [ 'name' => 'ListHoursOfOperationOverrides', 'http' => [ 'method' => 'GET', 'requestUri' => '/hours-of-operations/{InstanceId}/{HoursOfOperationId}/overrides', ], 'input' => [ 'shape' => 'ListHoursOfOperationOverridesRequest', ], 'output' => [ 'shape' => 'ListHoursOfOperationOverridesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListHoursOfOperations' => [ 'name' => 'ListHoursOfOperations', 'http' => [ 'method' => 'GET', 'requestUri' => '/hours-of-operations-summary/{InstanceId}', ], 'input' => [ 'shape' => 'ListHoursOfOperationsRequest', ], 'output' => [ 'shape' => 'ListHoursOfOperationsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListInstanceAttributes' => [ 'name' => 'ListInstanceAttributes', 'http' => [ 'method' => 'GET', 'requestUri' => '/instance/{InstanceId}/attributes', ], 'input' => [ 'shape' => 'ListInstanceAttributesRequest', ], 'output' => [ 'shape' => 'ListInstanceAttributesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListInstanceStorageConfigs' => [ 'name' => 'ListInstanceStorageConfigs', 'http' => [ 'method' => 'GET', 'requestUri' => '/instance/{InstanceId}/storage-configs', ], 'input' => [ 'shape' => 'ListInstanceStorageConfigsRequest', ], 'output' => [ 'shape' => 'ListInstanceStorageConfigsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListInstances' => [ 'name' => 'ListInstances', 'http' => [ 'method' => 'GET', 'requestUri' => '/instance', ], 'input' => [ 'shape' => 'ListInstancesRequest', ], 'output' => [ 'shape' => 'ListInstancesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListIntegrationAssociations' => [ 'name' => 'ListIntegrationAssociations', 'http' => [ 'method' => 'GET', 'requestUri' => '/instance/{InstanceId}/integration-associations', ], 'input' => [ 'shape' => 'ListIntegrationAssociationsRequest', ], 'output' => [ 'shape' => 'ListIntegrationAssociationsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListLambdaFunctions' => [ 'name' => 'ListLambdaFunctions', 'http' => [ 'method' => 'GET', 'requestUri' => '/instance/{InstanceId}/lambda-functions', ], 'input' => [ 'shape' => 'ListLambdaFunctionsRequest', ], 'output' => [ 'shape' => 'ListLambdaFunctionsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListLexBots' => [ 'name' => 'ListLexBots', 'http' => [ 'method' => 'GET', 'requestUri' => '/instance/{InstanceId}/lex-bots', ], 'input' => [ 'shape' => 'ListLexBotsRequest', ], 'output' => [ 'shape' => 'ListLexBotsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListPhoneNumbers' => [ 'name' => 'ListPhoneNumbers', 'http' => [ 'method' => 'GET', 'requestUri' => '/phone-numbers-summary/{InstanceId}', ], 'input' => [ 'shape' => 'ListPhoneNumbersRequest', ], 'output' => [ 'shape' => 'ListPhoneNumbersResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListPhoneNumbersV2' => [ 'name' => 'ListPhoneNumbersV2', 'http' => [ 'method' => 'POST', 'requestUri' => '/phone-number/list', ], 'input' => [ 'shape' => 'ListPhoneNumbersV2Request', ], 'output' => [ 'shape' => 'ListPhoneNumbersV2Response', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListPredefinedAttributes' => [ 'name' => 'ListPredefinedAttributes', 'http' => [ 'method' => 'GET', 'requestUri' => '/predefined-attributes/{InstanceId}', ], 'input' => [ 'shape' => 'ListPredefinedAttributesRequest', ], 'output' => [ 'shape' => 'ListPredefinedAttributesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListPrompts' => [ 'name' => 'ListPrompts', 'http' => [ 'method' => 'GET', 'requestUri' => '/prompts-summary/{InstanceId}', ], 'input' => [ 'shape' => 'ListPromptsRequest', ], 'output' => [ 'shape' => 'ListPromptsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListQueueQuickConnects' => [ 'name' => 'ListQueueQuickConnects', 'http' => [ 'method' => 'GET', 'requestUri' => '/queues/{InstanceId}/{QueueId}/quick-connects', ], 'input' => [ 'shape' => 'ListQueueQuickConnectsRequest', ], 'output' => [ 'shape' => 'ListQueueQuickConnectsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListQueues' => [ 'name' => 'ListQueues', 'http' => [ 'method' => 'GET', 'requestUri' => '/queues-summary/{InstanceId}', ], 'input' => [ 'shape' => 'ListQueuesRequest', ], 'output' => [ 'shape' => 'ListQueuesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListQuickConnects' => [ 'name' => 'ListQuickConnects', 'http' => [ 'method' => 'GET', 'requestUri' => '/quick-connects/{InstanceId}', ], 'input' => [ 'shape' => 'ListQuickConnectsRequest', ], 'output' => [ 'shape' => 'ListQuickConnectsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListRealtimeContactAnalysisSegmentsV2' => [ 'name' => 'ListRealtimeContactAnalysisSegmentsV2', 'http' => [ 'method' => 'POST', 'requestUri' => '/contact/list-real-time-analysis-segments-v2/{InstanceId}/{ContactId}', ], 'input' => [ 'shape' => 'ListRealtimeContactAnalysisSegmentsV2Request', ], 'output' => [ 'shape' => 'ListRealtimeContactAnalysisSegmentsV2Response', ], 'errors' => [ [ 'shape' => 'OutputTypeNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListRoutingProfileQueues' => [ 'name' => 'ListRoutingProfileQueues', 'http' => [ 'method' => 'GET', 'requestUri' => '/routing-profiles/{InstanceId}/{RoutingProfileId}/queues', ], 'input' => [ 'shape' => 'ListRoutingProfileQueuesRequest', ], 'output' => [ 'shape' => 'ListRoutingProfileQueuesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListRoutingProfiles' => [ 'name' => 'ListRoutingProfiles', 'http' => [ 'method' => 'GET', 'requestUri' => '/routing-profiles-summary/{InstanceId}', ], 'input' => [ 'shape' => 'ListRoutingProfilesRequest', ], 'output' => [ 'shape' => 'ListRoutingProfilesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListRules' => [ 'name' => 'ListRules', 'http' => [ 'method' => 'GET', 'requestUri' => '/rules/{InstanceId}', ], 'input' => [ 'shape' => 'ListRulesRequest', ], 'output' => [ 'shape' => 'ListRulesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListSecurityKeys' => [ 'name' => 'ListSecurityKeys', 'http' => [ 'method' => 'GET', 'requestUri' => '/instance/{InstanceId}/security-keys', ], 'input' => [ 'shape' => 'ListSecurityKeysRequest', ], 'output' => [ 'shape' => 'ListSecurityKeysResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListSecurityProfileApplications' => [ 'name' => 'ListSecurityProfileApplications', 'http' => [ 'method' => 'GET', 'requestUri' => '/security-profiles-applications/{InstanceId}/{SecurityProfileId}', ], 'input' => [ 'shape' => 'ListSecurityProfileApplicationsRequest', ], 'output' => [ 'shape' => 'ListSecurityProfileApplicationsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListSecurityProfilePermissions' => [ 'name' => 'ListSecurityProfilePermissions', 'http' => [ 'method' => 'GET', 'requestUri' => '/security-profiles-permissions/{InstanceId}/{SecurityProfileId}', ], 'input' => [ 'shape' => 'ListSecurityProfilePermissionsRequest', ], 'output' => [ 'shape' => 'ListSecurityProfilePermissionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListSecurityProfiles' => [ 'name' => 'ListSecurityProfiles', 'http' => [ 'method' => 'GET', 'requestUri' => '/security-profiles-summary/{InstanceId}', ], 'input' => [ 'shape' => 'ListSecurityProfilesRequest', ], 'output' => [ 'shape' => 'ListSecurityProfilesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListTaskTemplates' => [ 'name' => 'ListTaskTemplates', 'http' => [ 'method' => 'GET', 'requestUri' => '/instance/{InstanceId}/task/template', ], 'input' => [ 'shape' => 'ListTaskTemplatesRequest', ], 'output' => [ 'shape' => 'ListTaskTemplatesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListTrafficDistributionGroupUsers' => [ 'name' => 'ListTrafficDistributionGroupUsers', 'http' => [ 'method' => 'GET', 'requestUri' => '/traffic-distribution-group/{TrafficDistributionGroupId}/user', ], 'input' => [ 'shape' => 'ListTrafficDistributionGroupUsersRequest', ], 'output' => [ 'shape' => 'ListTrafficDistributionGroupUsersResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListTrafficDistributionGroups' => [ 'name' => 'ListTrafficDistributionGroups', 'http' => [ 'method' => 'GET', 'requestUri' => '/traffic-distribution-groups', ], 'input' => [ 'shape' => 'ListTrafficDistributionGroupsRequest', ], 'output' => [ 'shape' => 'ListTrafficDistributionGroupsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListUseCases' => [ 'name' => 'ListUseCases', 'http' => [ 'method' => 'GET', 'requestUri' => '/instance/{InstanceId}/integration-associations/{IntegrationAssociationId}/use-cases', ], 'input' => [ 'shape' => 'ListUseCasesRequest', ], 'output' => [ 'shape' => 'ListUseCasesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListUserHierarchyGroups' => [ 'name' => 'ListUserHierarchyGroups', 'http' => [ 'method' => 'GET', 'requestUri' => '/user-hierarchy-groups-summary/{InstanceId}', ], 'input' => [ 'shape' => 'ListUserHierarchyGroupsRequest', ], 'output' => [ 'shape' => 'ListUserHierarchyGroupsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListUserProficiencies' => [ 'name' => 'ListUserProficiencies', 'http' => [ 'method' => 'GET', 'requestUri' => '/users/{InstanceId}/{UserId}/proficiencies', ], 'input' => [ 'shape' => 'ListUserProficienciesRequest', ], 'output' => [ 'shape' => 'ListUserProficienciesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListUsers' => [ 'name' => 'ListUsers', 'http' => [ 'method' => 'GET', 'requestUri' => '/users-summary/{InstanceId}', ], 'input' => [ 'shape' => 'ListUsersRequest', ], 'output' => [ 'shape' => 'ListUsersResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListViewVersions' => [ 'name' => 'ListViewVersions', 'http' => [ 'method' => 'GET', 'requestUri' => '/views/{InstanceId}/{ViewId}/versions', ], 'input' => [ 'shape' => 'ListViewVersionsRequest', ], 'output' => [ 'shape' => 'ListViewVersionsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'ListViews' => [ 'name' => 'ListViews', 'http' => [ 'method' => 'GET', 'requestUri' => '/views/{InstanceId}', ], 'input' => [ 'shape' => 'ListViewsRequest', ], 'output' => [ 'shape' => 'ListViewsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'TooManyRequestsException', ], ], ], 'MonitorContact' => [ 'name' => 'MonitorContact', 'http' => [ 'method' => 'POST', 'requestUri' => '/contact/monitor', ], 'input' => [ 'shape' => 'MonitorContactRequest', ], 'output' => [ 'shape' => 'MonitorContactResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'IdempotencyException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'PauseContact' => [ 'name' => 'PauseContact', 'http' => [ 'method' => 'POST', 'requestUri' => '/contact/pause', ], 'input' => [ 'shape' => 'PauseContactRequest', ], 'output' => [ 'shape' => 'PauseContactResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConflictException', ], ], ], 'PutUserStatus' => [ 'name' => 'PutUserStatus', 'http' => [ 'method' => 'PUT', 'requestUri' => '/users/{InstanceId}/{UserId}/status', ], 'input' => [ 'shape' => 'PutUserStatusRequest', ], 'output' => [ 'shape' => 'PutUserStatusResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ReleasePhoneNumber' => [ 'name' => 'ReleasePhoneNumber', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/phone-number/{PhoneNumberId}', ], 'input' => [ 'shape' => 'ReleasePhoneNumberRequest', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'IdempotencyException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ReplicateInstance' => [ 'name' => 'ReplicateInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/instance/{InstanceId}/replicate', ], 'input' => [ 'shape' => 'ReplicateInstanceRequest', ], 'output' => [ 'shape' => 'ReplicateInstanceResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ResourceNotReadyException', ], [ 'shape' => 'ResourceConflictException', ], ], ], 'ResumeContact' => [ 'name' => 'ResumeContact', 'http' => [ 'method' => 'POST', 'requestUri' => '/contact/resume', ], 'input' => [ 'shape' => 'ResumeContactRequest', ], 'output' => [ 'shape' => 'ResumeContactResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'ResumeContactRecording' => [ 'name' => 'ResumeContactRecording', 'http' => [ 'method' => 'POST', 'requestUri' => '/contact/resume-recording', ], 'input' => [ 'shape' => 'ResumeContactRecordingRequest', ], 'output' => [ 'shape' => 'ResumeContactRecordingResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'SearchAgentStatuses' => [ 'name' => 'SearchAgentStatuses', 'http' => [ 'method' => 'POST', 'requestUri' => '/search-agent-statuses', ], 'input' => [ 'shape' => 'SearchAgentStatusesRequest', ], 'output' => [ 'shape' => 'SearchAgentStatusesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'SearchAvailablePhoneNumbers' => [ 'name' => 'SearchAvailablePhoneNumbers', 'http' => [ 'method' => 'POST', 'requestUri' => '/phone-number/search-available', ], 'input' => [ 'shape' => 'SearchAvailablePhoneNumbersRequest', ], 'output' => [ 'shape' => 'SearchAvailablePhoneNumbersResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'SearchContactFlowModules' => [ 'name' => 'SearchContactFlowModules', 'http' => [ 'method' => 'POST', 'requestUri' => '/search-contact-flow-modules', ], 'input' => [ 'shape' => 'SearchContactFlowModulesRequest', ], 'output' => [ 'shape' => 'SearchContactFlowModulesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'SearchContactFlows' => [ 'name' => 'SearchContactFlows', 'http' => [ 'method' => 'POST', 'requestUri' => '/search-contact-flows', ], 'input' => [ 'shape' => 'SearchContactFlowsRequest', ], 'output' => [ 'shape' => 'SearchContactFlowsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'SearchContacts' => [ 'name' => 'SearchContacts', 'http' => [ 'method' => 'POST', 'requestUri' => '/search-contacts', ], 'input' => [ 'shape' => 'SearchContactsRequest', ], 'output' => [ 'shape' => 'SearchContactsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'SearchEmailAddresses' => [ 'name' => 'SearchEmailAddresses', 'http' => [ 'method' => 'POST', 'requestUri' => '/search-email-addresses', ], 'input' => [ 'shape' => 'SearchEmailAddressesRequest', ], 'output' => [ 'shape' => 'SearchEmailAddressesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'SearchHoursOfOperationOverrides' => [ 'name' => 'SearchHoursOfOperationOverrides', 'http' => [ 'method' => 'POST', 'requestUri' => '/search-hours-of-operation-overrides', ], 'input' => [ 'shape' => 'SearchHoursOfOperationOverridesRequest', ], 'output' => [ 'shape' => 'SearchHoursOfOperationOverridesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'SearchHoursOfOperations' => [ 'name' => 'SearchHoursOfOperations', 'http' => [ 'method' => 'POST', 'requestUri' => '/search-hours-of-operations', ], 'input' => [ 'shape' => 'SearchHoursOfOperationsRequest', ], 'output' => [ 'shape' => 'SearchHoursOfOperationsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'SearchPredefinedAttributes' => [ 'name' => 'SearchPredefinedAttributes', 'http' => [ 'method' => 'POST', 'requestUri' => '/search-predefined-attributes', ], 'input' => [ 'shape' => 'SearchPredefinedAttributesRequest', ], 'output' => [ 'shape' => 'SearchPredefinedAttributesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'SearchPrompts' => [ 'name' => 'SearchPrompts', 'http' => [ 'method' => 'POST', 'requestUri' => '/search-prompts', ], 'input' => [ 'shape' => 'SearchPromptsRequest', ], 'output' => [ 'shape' => 'SearchPromptsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'SearchQueues' => [ 'name' => 'SearchQueues', 'http' => [ 'method' => 'POST', 'requestUri' => '/search-queues', ], 'input' => [ 'shape' => 'SearchQueuesRequest', ], 'output' => [ 'shape' => 'SearchQueuesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'SearchQuickConnects' => [ 'name' => 'SearchQuickConnects', 'http' => [ 'method' => 'POST', 'requestUri' => '/search-quick-connects', ], 'input' => [ 'shape' => 'SearchQuickConnectsRequest', ], 'output' => [ 'shape' => 'SearchQuickConnectsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'SearchResourceTags' => [ 'name' => 'SearchResourceTags', 'http' => [ 'method' => 'POST', 'requestUri' => '/search-resource-tags', ], 'input' => [ 'shape' => 'SearchResourceTagsRequest', ], 'output' => [ 'shape' => 'SearchResourceTagsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'MaximumResultReturnedException', ], ], ], 'SearchRoutingProfiles' => [ 'name' => 'SearchRoutingProfiles', 'http' => [ 'method' => 'POST', 'requestUri' => '/search-routing-profiles', ], 'input' => [ 'shape' => 'SearchRoutingProfilesRequest', ], 'output' => [ 'shape' => 'SearchRoutingProfilesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'SearchSecurityProfiles' => [ 'name' => 'SearchSecurityProfiles', 'http' => [ 'method' => 'POST', 'requestUri' => '/search-security-profiles', ], 'input' => [ 'shape' => 'SearchSecurityProfilesRequest', ], 'output' => [ 'shape' => 'SearchSecurityProfilesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'SearchUserHierarchyGroups' => [ 'name' => 'SearchUserHierarchyGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/search-user-hierarchy-groups', ], 'input' => [ 'shape' => 'SearchUserHierarchyGroupsRequest', ], 'output' => [ 'shape' => 'SearchUserHierarchyGroupsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'SearchUsers' => [ 'name' => 'SearchUsers', 'http' => [ 'method' => 'POST', 'requestUri' => '/search-users', ], 'input' => [ 'shape' => 'SearchUsersRequest', ], 'output' => [ 'shape' => 'SearchUsersResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'SearchVocabularies' => [ 'name' => 'SearchVocabularies', 'http' => [ 'method' => 'POST', 'requestUri' => '/vocabulary-summary/{InstanceId}', ], 'input' => [ 'shape' => 'SearchVocabulariesRequest', ], 'output' => [ 'shape' => 'SearchVocabulariesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'SendChatIntegrationEvent' => [ 'name' => 'SendChatIntegrationEvent', 'http' => [ 'method' => 'POST', 'requestUri' => '/chat-integration-event', ], 'input' => [ 'shape' => 'SendChatIntegrationEventRequest', ], 'output' => [ 'shape' => 'SendChatIntegrationEventResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'SendOutboundEmail' => [ 'name' => 'SendOutboundEmail', 'http' => [ 'method' => 'PUT', 'requestUri' => '/instance/{InstanceId}/outbound-email', ], 'input' => [ 'shape' => 'SendOutboundEmailRequest', ], 'output' => [ 'shape' => 'SendOutboundEmailResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'IdempotencyException', ], ], ], 'StartAttachedFileUpload' => [ 'name' => 'StartAttachedFileUpload', 'http' => [ 'method' => 'PUT', 'requestUri' => '/attached-files/{InstanceId}', ], 'input' => [ 'shape' => 'StartAttachedFileUploadRequest', ], 'output' => [ 'shape' => 'StartAttachedFileUploadResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'StartChatContact' => [ 'name' => 'StartChatContact', 'http' => [ 'method' => 'PUT', 'requestUri' => '/contact/chat', ], 'input' => [ 'shape' => 'StartChatContactRequest', ], 'output' => [ 'shape' => 'StartChatContactResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'StartContactEvaluation' => [ 'name' => 'StartContactEvaluation', 'http' => [ 'method' => 'PUT', 'requestUri' => '/contact-evaluations/{InstanceId}', ], 'input' => [ 'shape' => 'StartContactEvaluationRequest', ], 'output' => [ 'shape' => 'StartContactEvaluationResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceConflictException', ], ], 'idempotent' => true, ], 'StartContactRecording' => [ 'name' => 'StartContactRecording', 'http' => [ 'method' => 'POST', 'requestUri' => '/contact/start-recording', ], 'input' => [ 'shape' => 'StartContactRecordingRequest', ], 'output' => [ 'shape' => 'StartContactRecordingResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'StartContactStreaming' => [ 'name' => 'StartContactStreaming', 'http' => [ 'method' => 'POST', 'requestUri' => '/contact/start-streaming', ], 'input' => [ 'shape' => 'StartContactStreamingRequest', ], 'output' => [ 'shape' => 'StartContactStreamingResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'StartEmailContact' => [ 'name' => 'StartEmailContact', 'http' => [ 'method' => 'PUT', 'requestUri' => '/contact/email', ], 'input' => [ 'shape' => 'StartEmailContactRequest', ], 'output' => [ 'shape' => 'StartEmailContactResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'IdempotencyException', ], ], ], 'StartOutboundChatContact' => [ 'name' => 'StartOutboundChatContact', 'http' => [ 'method' => 'PUT', 'requestUri' => '/contact/outbound-chat', ], 'input' => [ 'shape' => 'StartOutboundChatContactRequest', ], 'output' => [ 'shape' => 'StartOutboundChatContactResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'StartOutboundEmailContact' => [ 'name' => 'StartOutboundEmailContact', 'http' => [ 'method' => 'PUT', 'requestUri' => '/contact/outbound-email', ], 'input' => [ 'shape' => 'StartOutboundEmailContactRequest', ], 'output' => [ 'shape' => 'StartOutboundEmailContactResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'IdempotencyException', ], ], ], 'StartOutboundVoiceContact' => [ 'name' => 'StartOutboundVoiceContact', 'http' => [ 'method' => 'PUT', 'requestUri' => '/contact/outbound-voice', ], 'input' => [ 'shape' => 'StartOutboundVoiceContactRequest', ], 'output' => [ 'shape' => 'StartOutboundVoiceContactResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'DestinationNotAllowedException', ], [ 'shape' => 'OutboundContactNotPermittedException', ], ], ], 'StartScreenSharing' => [ 'name' => 'StartScreenSharing', 'http' => [ 'method' => 'PUT', 'requestUri' => '/contact/screen-sharing', ], 'input' => [ 'shape' => 'StartScreenSharingRequest', ], 'output' => [ 'shape' => 'StartScreenSharingResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'StartTaskContact' => [ 'name' => 'StartTaskContact', 'http' => [ 'method' => 'PUT', 'requestUri' => '/contact/task', ], 'input' => [ 'shape' => 'StartTaskContactRequest', ], 'output' => [ 'shape' => 'StartTaskContactResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'StartWebRTCContact' => [ 'name' => 'StartWebRTCContact', 'http' => [ 'method' => 'PUT', 'requestUri' => '/contact/webrtc', ], 'input' => [ 'shape' => 'StartWebRTCContactRequest', ], 'output' => [ 'shape' => 'StartWebRTCContactResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'StopContact' => [ 'name' => 'StopContact', 'http' => [ 'method' => 'POST', 'requestUri' => '/contact/stop', ], 'input' => [ 'shape' => 'StopContactRequest', ], 'output' => [ 'shape' => 'StopContactResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ContactNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'StopContactRecording' => [ 'name' => 'StopContactRecording', 'http' => [ 'method' => 'POST', 'requestUri' => '/contact/stop-recording', ], 'input' => [ 'shape' => 'StopContactRecordingRequest', ], 'output' => [ 'shape' => 'StopContactRecordingResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'StopContactStreaming' => [ 'name' => 'StopContactStreaming', 'http' => [ 'method' => 'POST', 'requestUri' => '/contact/stop-streaming', ], 'input' => [ 'shape' => 'StopContactStreamingRequest', ], 'output' => [ 'shape' => 'StopContactStreamingResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'SubmitContactEvaluation' => [ 'name' => 'SubmitContactEvaluation', 'http' => [ 'method' => 'POST', 'requestUri' => '/contact-evaluations/{InstanceId}/{EvaluationId}/submit', ], 'input' => [ 'shape' => 'SubmitContactEvaluationRequest', ], 'output' => [ 'shape' => 'SubmitContactEvaluationResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ResourceConflictException', ], ], ], 'SuspendContactRecording' => [ 'name' => 'SuspendContactRecording', 'http' => [ 'method' => 'POST', 'requestUri' => '/contact/suspend-recording', ], 'input' => [ 'shape' => 'SuspendContactRecordingRequest', ], 'output' => [ 'shape' => 'SuspendContactRecordingResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'TagContact' => [ 'name' => 'TagContact', 'http' => [ 'method' => 'POST', 'requestUri' => '/contact/tags', ], 'input' => [ 'shape' => 'TagContactRequest', ], 'output' => [ 'shape' => 'TagContactResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'TransferContact' => [ 'name' => 'TransferContact', 'http' => [ 'method' => 'POST', 'requestUri' => '/contact/transfer', ], 'input' => [ 'shape' => 'TransferContactRequest', ], 'output' => [ 'shape' => 'TransferContactResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'IdempotencyException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UntagContact' => [ 'name' => 'UntagContact', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/contact/tags/{InstanceId}/{ContactId}', ], 'input' => [ 'shape' => 'UntagContactRequest', ], 'output' => [ 'shape' => 'UntagContactResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateAgentStatus' => [ 'name' => 'UpdateAgentStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/agent-status/{InstanceId}/{AgentStatusId}', ], 'input' => [ 'shape' => 'UpdateAgentStatusRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateAuthenticationProfile' => [ 'name' => 'UpdateAuthenticationProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/authentication-profiles/{InstanceId}/{AuthenticationProfileId}', ], 'input' => [ 'shape' => 'UpdateAuthenticationProfileRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateContact' => [ 'name' => 'UpdateContact', 'http' => [ 'method' => 'POST', 'requestUri' => '/contacts/{InstanceId}/{ContactId}', ], 'input' => [ 'shape' => 'UpdateContactRequest', ], 'output' => [ 'shape' => 'UpdateContactResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateContactAttributes' => [ 'name' => 'UpdateContactAttributes', 'http' => [ 'method' => 'POST', 'requestUri' => '/contact/attributes', ], 'input' => [ 'shape' => 'UpdateContactAttributesRequest', ], 'output' => [ 'shape' => 'UpdateContactAttributesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateContactEvaluation' => [ 'name' => 'UpdateContactEvaluation', 'http' => [ 'method' => 'POST', 'requestUri' => '/contact-evaluations/{InstanceId}/{EvaluationId}', ], 'input' => [ 'shape' => 'UpdateContactEvaluationRequest', ], 'output' => [ 'shape' => 'UpdateContactEvaluationResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ResourceConflictException', ], ], ], 'UpdateContactFlowContent' => [ 'name' => 'UpdateContactFlowContent', 'http' => [ 'method' => 'POST', 'requestUri' => '/contact-flows/{InstanceId}/{ContactFlowId}/content', ], 'input' => [ 'shape' => 'UpdateContactFlowContentRequest', ], 'output' => [ 'shape' => 'UpdateContactFlowContentResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidContactFlowException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateContactFlowMetadata' => [ 'name' => 'UpdateContactFlowMetadata', 'http' => [ 'method' => 'POST', 'requestUri' => '/contact-flows/{InstanceId}/{ContactFlowId}/metadata', ], 'input' => [ 'shape' => 'UpdateContactFlowMetadataRequest', ], 'output' => [ 'shape' => 'UpdateContactFlowMetadataResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateContactFlowModuleContent' => [ 'name' => 'UpdateContactFlowModuleContent', 'http' => [ 'method' => 'POST', 'requestUri' => '/contact-flow-modules/{InstanceId}/{ContactFlowModuleId}/content', ], 'input' => [ 'shape' => 'UpdateContactFlowModuleContentRequest', ], 'output' => [ 'shape' => 'UpdateContactFlowModuleContentResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidContactFlowModuleException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateContactFlowModuleMetadata' => [ 'name' => 'UpdateContactFlowModuleMetadata', 'http' => [ 'method' => 'POST', 'requestUri' => '/contact-flow-modules/{InstanceId}/{ContactFlowModuleId}/metadata', ], 'input' => [ 'shape' => 'UpdateContactFlowModuleMetadataRequest', ], 'output' => [ 'shape' => 'UpdateContactFlowModuleMetadataResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateContactFlowName' => [ 'name' => 'UpdateContactFlowName', 'http' => [ 'method' => 'POST', 'requestUri' => '/contact-flows/{InstanceId}/{ContactFlowId}/name', ], 'input' => [ 'shape' => 'UpdateContactFlowNameRequest', ], 'output' => [ 'shape' => 'UpdateContactFlowNameResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateContactRoutingData' => [ 'name' => 'UpdateContactRoutingData', 'http' => [ 'method' => 'POST', 'requestUri' => '/contacts/{InstanceId}/{ContactId}/routing-data', ], 'input' => [ 'shape' => 'UpdateContactRoutingDataRequest', ], 'output' => [ 'shape' => 'UpdateContactRoutingDataResponse', ], 'errors' => [ [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateContactSchedule' => [ 'name' => 'UpdateContactSchedule', 'http' => [ 'method' => 'POST', 'requestUri' => '/contact/schedule', ], 'input' => [ 'shape' => 'UpdateContactScheduleRequest', ], 'output' => [ 'shape' => 'UpdateContactScheduleResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateEmailAddressMetadata' => [ 'name' => 'UpdateEmailAddressMetadata', 'http' => [ 'method' => 'POST', 'requestUri' => '/email-addresses/{InstanceId}/{EmailAddressId}', ], 'input' => [ 'shape' => 'UpdateEmailAddressMetadataRequest', ], 'output' => [ 'shape' => 'UpdateEmailAddressMetadataResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'IdempotencyException', ], ], ], 'UpdateEvaluationForm' => [ 'name' => 'UpdateEvaluationForm', 'http' => [ 'method' => 'PUT', 'requestUri' => '/evaluation-forms/{InstanceId}/{EvaluationFormId}', ], 'input' => [ 'shape' => 'UpdateEvaluationFormRequest', ], 'output' => [ 'shape' => 'UpdateEvaluationFormResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceConflictException', ], ], 'idempotent' => true, ], 'UpdateHoursOfOperation' => [ 'name' => 'UpdateHoursOfOperation', 'http' => [ 'method' => 'POST', 'requestUri' => '/hours-of-operations/{InstanceId}/{HoursOfOperationId}', ], 'input' => [ 'shape' => 'UpdateHoursOfOperationRequest', ], 'errors' => [ [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateHoursOfOperationOverride' => [ 'name' => 'UpdateHoursOfOperationOverride', 'http' => [ 'method' => 'POST', 'requestUri' => '/hours-of-operations/{InstanceId}/{HoursOfOperationId}/overrides/{HoursOfOperationOverrideId}', ], 'input' => [ 'shape' => 'UpdateHoursOfOperationOverrideRequest', ], 'errors' => [ [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ConditionalOperationFailedException', ], ], ], 'UpdateInstanceAttribute' => [ 'name' => 'UpdateInstanceAttribute', 'http' => [ 'method' => 'POST', 'requestUri' => '/instance/{InstanceId}/attribute/{AttributeType}', ], 'input' => [ 'shape' => 'UpdateInstanceAttributeRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateInstanceStorageConfig' => [ 'name' => 'UpdateInstanceStorageConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/instance/{InstanceId}/storage-config/{AssociationId}', ], 'input' => [ 'shape' => 'UpdateInstanceStorageConfigRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateParticipantAuthentication' => [ 'name' => 'UpdateParticipantAuthentication', 'http' => [ 'method' => 'POST', 'requestUri' => '/contact/update-participant-authentication', ], 'input' => [ 'shape' => 'UpdateParticipantAuthenticationRequest', ], 'output' => [ 'shape' => 'UpdateParticipantAuthenticationResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateParticipantRoleConfig' => [ 'name' => 'UpdateParticipantRoleConfig', 'http' => [ 'method' => 'PUT', 'requestUri' => '/contact/participant-role-config/{InstanceId}/{ContactId}', ], 'input' => [ 'shape' => 'UpdateParticipantRoleConfigRequest', ], 'output' => [ 'shape' => 'UpdateParticipantRoleConfigResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdatePhoneNumber' => [ 'name' => 'UpdatePhoneNumber', 'http' => [ 'method' => 'PUT', 'requestUri' => '/phone-number/{PhoneNumberId}', ], 'input' => [ 'shape' => 'UpdatePhoneNumberRequest', ], 'output' => [ 'shape' => 'UpdatePhoneNumberResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'IdempotencyException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdatePhoneNumberMetadata' => [ 'name' => 'UpdatePhoneNumberMetadata', 'http' => [ 'method' => 'PUT', 'requestUri' => '/phone-number/{PhoneNumberId}/metadata', ], 'input' => [ 'shape' => 'UpdatePhoneNumberMetadataRequest', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'IdempotencyException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdatePredefinedAttribute' => [ 'name' => 'UpdatePredefinedAttribute', 'http' => [ 'method' => 'POST', 'requestUri' => '/predefined-attributes/{InstanceId}/{Name}', ], 'input' => [ 'shape' => 'UpdatePredefinedAttributeRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdatePrompt' => [ 'name' => 'UpdatePrompt', 'http' => [ 'method' => 'POST', 'requestUri' => '/prompts/{InstanceId}/{PromptId}', ], 'input' => [ 'shape' => 'UpdatePromptRequest', ], 'output' => [ 'shape' => 'UpdatePromptResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateQueueHoursOfOperation' => [ 'name' => 'UpdateQueueHoursOfOperation', 'http' => [ 'method' => 'POST', 'requestUri' => '/queues/{InstanceId}/{QueueId}/hours-of-operation', ], 'input' => [ 'shape' => 'UpdateQueueHoursOfOperationRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateQueueMaxContacts' => [ 'name' => 'UpdateQueueMaxContacts', 'http' => [ 'method' => 'POST', 'requestUri' => '/queues/{InstanceId}/{QueueId}/max-contacts', ], 'input' => [ 'shape' => 'UpdateQueueMaxContactsRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateQueueName' => [ 'name' => 'UpdateQueueName', 'http' => [ 'method' => 'POST', 'requestUri' => '/queues/{InstanceId}/{QueueId}/name', ], 'input' => [ 'shape' => 'UpdateQueueNameRequest', ], 'errors' => [ [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateQueueOutboundCallerConfig' => [ 'name' => 'UpdateQueueOutboundCallerConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/queues/{InstanceId}/{QueueId}/outbound-caller-config', ], 'input' => [ 'shape' => 'UpdateQueueOutboundCallerConfigRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateQueueOutboundEmailConfig' => [ 'name' => 'UpdateQueueOutboundEmailConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/queues/{InstanceId}/{QueueId}/outbound-email-config', ], 'input' => [ 'shape' => 'UpdateQueueOutboundEmailConfigRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConditionalOperationFailedException', ], ], ], 'UpdateQueueStatus' => [ 'name' => 'UpdateQueueStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/queues/{InstanceId}/{QueueId}/status', ], 'input' => [ 'shape' => 'UpdateQueueStatusRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateQuickConnectConfig' => [ 'name' => 'UpdateQuickConnectConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/quick-connects/{InstanceId}/{QuickConnectId}/config', ], 'input' => [ 'shape' => 'UpdateQuickConnectConfigRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateQuickConnectName' => [ 'name' => 'UpdateQuickConnectName', 'http' => [ 'method' => 'POST', 'requestUri' => '/quick-connects/{InstanceId}/{QuickConnectId}/name', ], 'input' => [ 'shape' => 'UpdateQuickConnectNameRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateRoutingProfileAgentAvailabilityTimer' => [ 'name' => 'UpdateRoutingProfileAgentAvailabilityTimer', 'http' => [ 'method' => 'POST', 'requestUri' => '/routing-profiles/{InstanceId}/{RoutingProfileId}/agent-availability-timer', ], 'input' => [ 'shape' => 'UpdateRoutingProfileAgentAvailabilityTimerRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateRoutingProfileConcurrency' => [ 'name' => 'UpdateRoutingProfileConcurrency', 'http' => [ 'method' => 'POST', 'requestUri' => '/routing-profiles/{InstanceId}/{RoutingProfileId}/concurrency', ], 'input' => [ 'shape' => 'UpdateRoutingProfileConcurrencyRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateRoutingProfileDefaultOutboundQueue' => [ 'name' => 'UpdateRoutingProfileDefaultOutboundQueue', 'http' => [ 'method' => 'POST', 'requestUri' => '/routing-profiles/{InstanceId}/{RoutingProfileId}/default-outbound-queue', ], 'input' => [ 'shape' => 'UpdateRoutingProfileDefaultOutboundQueueRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateRoutingProfileName' => [ 'name' => 'UpdateRoutingProfileName', 'http' => [ 'method' => 'POST', 'requestUri' => '/routing-profiles/{InstanceId}/{RoutingProfileId}/name', ], 'input' => [ 'shape' => 'UpdateRoutingProfileNameRequest', ], 'errors' => [ [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateRoutingProfileQueues' => [ 'name' => 'UpdateRoutingProfileQueues', 'http' => [ 'method' => 'POST', 'requestUri' => '/routing-profiles/{InstanceId}/{RoutingProfileId}/queues', ], 'input' => [ 'shape' => 'UpdateRoutingProfileQueuesRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateRule' => [ 'name' => 'UpdateRule', 'http' => [ 'method' => 'PUT', 'requestUri' => '/rules/{InstanceId}/{RuleId}', ], 'input' => [ 'shape' => 'UpdateRuleRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceConflictException', ], ], ], 'UpdateSecurityProfile' => [ 'name' => 'UpdateSecurityProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/security-profiles/{InstanceId}/{SecurityProfileId}', ], 'input' => [ 'shape' => 'UpdateSecurityProfileRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateTaskTemplate' => [ 'name' => 'UpdateTaskTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/instance/{InstanceId}/task/template/{TaskTemplateId}', ], 'input' => [ 'shape' => 'UpdateTaskTemplateRequest', ], 'output' => [ 'shape' => 'UpdateTaskTemplateResponse', ], 'errors' => [ [ 'shape' => 'PropertyValidationException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateTrafficDistribution' => [ 'name' => 'UpdateTrafficDistribution', 'http' => [ 'method' => 'PUT', 'requestUri' => '/traffic-distribution/{Id}', ], 'input' => [ 'shape' => 'UpdateTrafficDistributionRequest', ], 'output' => [ 'shape' => 'UpdateTrafficDistributionResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateUserHierarchy' => [ 'name' => 'UpdateUserHierarchy', 'http' => [ 'method' => 'POST', 'requestUri' => '/users/{InstanceId}/{UserId}/hierarchy', ], 'input' => [ 'shape' => 'UpdateUserHierarchyRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateUserHierarchyGroupName' => [ 'name' => 'UpdateUserHierarchyGroupName', 'http' => [ 'method' => 'POST', 'requestUri' => '/user-hierarchy-groups/{InstanceId}/{HierarchyGroupId}/name', ], 'input' => [ 'shape' => 'UpdateUserHierarchyGroupNameRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateUserHierarchyStructure' => [ 'name' => 'UpdateUserHierarchyStructure', 'http' => [ 'method' => 'POST', 'requestUri' => '/user-hierarchy-structure/{InstanceId}', ], 'input' => [ 'shape' => 'UpdateUserHierarchyStructureRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateUserIdentityInfo' => [ 'name' => 'UpdateUserIdentityInfo', 'http' => [ 'method' => 'POST', 'requestUri' => '/users/{InstanceId}/{UserId}/identity-info', ], 'input' => [ 'shape' => 'UpdateUserIdentityInfoRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateUserPhoneConfig' => [ 'name' => 'UpdateUserPhoneConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/users/{InstanceId}/{UserId}/phone-config', ], 'input' => [ 'shape' => 'UpdateUserPhoneConfigRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateUserProficiencies' => [ 'name' => 'UpdateUserProficiencies', 'http' => [ 'method' => 'POST', 'requestUri' => '/users/{InstanceId}/{UserId}/proficiencies', ], 'input' => [ 'shape' => 'UpdateUserProficienciesRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateUserRoutingProfile' => [ 'name' => 'UpdateUserRoutingProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/users/{InstanceId}/{UserId}/routing-profile', ], 'input' => [ 'shape' => 'UpdateUserRoutingProfileRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateUserSecurityProfiles' => [ 'name' => 'UpdateUserSecurityProfiles', 'http' => [ 'method' => 'POST', 'requestUri' => '/users/{InstanceId}/{UserId}/security-profiles', ], 'input' => [ 'shape' => 'UpdateUserSecurityProfilesRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateViewContent' => [ 'name' => 'UpdateViewContent', 'http' => [ 'method' => 'POST', 'requestUri' => '/views/{InstanceId}/{ViewId}', ], 'input' => [ 'shape' => 'UpdateViewContentRequest', ], 'output' => [ 'shape' => 'UpdateViewContentResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ResourceInUseException', ], ], ], 'UpdateViewMetadata' => [ 'name' => 'UpdateViewMetadata', 'http' => [ 'method' => 'POST', 'requestUri' => '/views/{InstanceId}/{ViewId}/metadata', ], 'input' => [ 'shape' => 'UpdateViewMetadataRequest', ], 'output' => [ 'shape' => 'UpdateViewMetadataResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'ResourceInUseException', ], ], ], ], 'shapes' => [ 'ARN' => [ 'type' => 'string', ], 'AWSAccountId' => [ 'type' => 'string', ], 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'AccessTokenDuration' => [ 'type' => 'integer', 'box' => true, 'max' => 60, 'min' => 10, ], 'ActionSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ActionSummary', ], ], 'ActionSummary' => [ 'type' => 'structure', 'required' => [ 'ActionType', ], 'members' => [ 'ActionType' => [ 'shape' => 'ActionType', ], ], ], 'ActionType' => [ 'type' => 'string', 'enum' => [ 'CREATE_TASK', 'ASSIGN_CONTACT_CATEGORY', 'GENERATE_EVENTBRIDGE_EVENT', 'SEND_NOTIFICATION', 'CREATE_CASE', 'UPDATE_CASE', 'ASSIGN_SLA', 'END_ASSOCIATED_TASKS', 'SUBMIT_AUTO_EVALUATION', ], ], 'ActivateEvaluationFormRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'EvaluationFormId', 'EvaluationFormVersion', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'EvaluationFormId' => [ 'shape' => 'ResourceId', 'location' => 'uri', 'locationName' => 'EvaluationFormId', ], 'EvaluationFormVersion' => [ 'shape' => 'VersionNumber', ], ], ], 'ActivateEvaluationFormResponse' => [ 'type' => 'structure', 'required' => [ 'EvaluationFormId', 'EvaluationFormArn', 'EvaluationFormVersion', ], 'members' => [ 'EvaluationFormId' => [ 'shape' => 'ResourceId', ], 'EvaluationFormArn' => [ 'shape' => 'ARN', ], 'EvaluationFormVersion' => [ 'shape' => 'VersionNumber', ], ], ], 'AdditionalEmailRecipients' => [ 'type' => 'structure', 'members' => [ 'ToList' => [ 'shape' => 'EmailRecipientsList', ], 'CcList' => [ 'shape' => 'EmailRecipientsList', ], ], ], 'AfterContactWorkTimeLimit' => [ 'type' => 'integer', 'min' => 0, ], 'AgentAvailabilityTimer' => [ 'type' => 'string', 'enum' => [ 'TIME_SINCE_LAST_ACTIVITY', 'TIME_SINCE_LAST_INBOUND', ], ], 'AgentConfig' => [ 'type' => 'structure', 'required' => [ 'Distributions', ], 'members' => [ 'Distributions' => [ 'shape' => 'DistributionList', ], ], ], 'AgentContactReference' => [ 'type' => 'structure', 'members' => [ 'ContactId' => [ 'shape' => 'ContactId', ], 'Channel' => [ 'shape' => 'Channel', ], 'InitiationMethod' => [ 'shape' => 'ContactInitiationMethod', ], 'AgentContactState' => [ 'shape' => 'ContactState', ], 'StateStartTimestamp' => [ 'shape' => 'Timestamp', ], 'ConnectedToAgentTimestamp' => [ 'shape' => 'Timestamp', ], 'Queue' => [ 'shape' => 'QueueReference', ], ], ], 'AgentContactReferenceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AgentContactReference', ], ], 'AgentFirstName' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'sensitive' => true, ], 'AgentHierarchyGroup' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'ARN', ], ], ], 'AgentHierarchyGroups' => [ 'type' => 'structure', 'members' => [ 'L1Ids' => [ 'shape' => 'HierarchyGroupIdList', ], 'L2Ids' => [ 'shape' => 'HierarchyGroupIdList', ], 'L3Ids' => [ 'shape' => 'HierarchyGroupIdList', ], 'L4Ids' => [ 'shape' => 'HierarchyGroupIdList', ], 'L5Ids' => [ 'shape' => 'HierarchyGroupIdList', ], ], ], 'AgentId' => [ 'type' => 'string', 'max' => 256, ], 'AgentIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'AgentId', ], ], 'AgentInfo' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'AgentResourceId', ], 'ConnectedToAgentTimestamp' => [ 'shape' => 'timestamp', ], 'AgentPauseDurationInSeconds' => [ 'shape' => 'AgentPauseDurationInSeconds', ], 'HierarchyGroups' => [ 'shape' => 'HierarchyGroups', ], 'DeviceInfo' => [ 'shape' => 'DeviceInfo', ], 'Capabilities' => [ 'shape' => 'ParticipantCapabilities', ], 'AfterContactWorkDuration' => [ 'shape' => 'Duration', ], 'AfterContactWorkStartTimestamp' => [ 'shape' => 'timestamp', ], 'AfterContactWorkEndTimestamp' => [ 'shape' => 'timestamp', ], 'AgentInitiatedHoldDuration' => [ 'shape' => 'Duration', ], 'StateTransitions' => [ 'shape' => 'StateTransitions', ], ], ], 'AgentLastName' => [ 'type' => 'string', 'max' => 300, 'min' => 0, 'sensitive' => true, ], 'AgentPauseDurationInSeconds' => [ 'type' => 'integer', 'min' => 0, ], 'AgentQualityMetrics' => [ 'type' => 'structure', 'members' => [ 'Audio' => [ 'shape' => 'AudioQualityMetricsInfo', ], ], ], 'AgentResourceId' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'AgentResourceIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AgentResourceId', ], 'max' => 100, 'min' => 0, ], 'AgentStatus' => [ 'type' => 'structure', 'members' => [ 'AgentStatusARN' => [ 'shape' => 'ARN', ], 'AgentStatusId' => [ 'shape' => 'AgentStatusId', ], 'Name' => [ 'shape' => 'AgentStatusName', ], 'Description' => [ 'shape' => 'AgentStatusDescription', ], 'Type' => [ 'shape' => 'AgentStatusType', ], 'DisplayOrder' => [ 'shape' => 'AgentStatusOrderNumber', ], 'State' => [ 'shape' => 'AgentStatusState', ], 'Tags' => [ 'shape' => 'TagMap', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedRegion' => [ 'shape' => 'RegionName', ], ], ], 'AgentStatusDescription' => [ 'type' => 'string', 'max' => 250, 'min' => 1, ], 'AgentStatusId' => [ 'type' => 'string', ], 'AgentStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AgentStatus', ], ], 'AgentStatusName' => [ 'type' => 'string', 'max' => 127, 'min' => 1, ], 'AgentStatusOrderNumber' => [ 'type' => 'integer', 'max' => 50, 'min' => 1, ], 'AgentStatusReference' => [ 'type' => 'structure', 'members' => [ 'StatusStartTimestamp' => [ 'shape' => 'Timestamp', ], 'StatusArn' => [ 'shape' => 'ARN', ], 'StatusName' => [ 'shape' => 'AgentStatusName', ], ], ], 'AgentStatusSearchConditionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AgentStatusSearchCriteria', ], ], 'AgentStatusSearchCriteria' => [ 'type' => 'structure', 'members' => [ 'OrConditions' => [ 'shape' => 'AgentStatusSearchConditionList', ], 'AndConditions' => [ 'shape' => 'AgentStatusSearchConditionList', ], 'StringCondition' => [ 'shape' => 'StringCondition', ], ], ], 'AgentStatusSearchFilter' => [ 'type' => 'structure', 'members' => [ 'AttributeFilter' => [ 'shape' => 'ControlPlaneAttributeFilter', ], ], ], 'AgentStatusState' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'AgentStatusSummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'AgentStatusId', ], 'Arn' => [ 'shape' => 'ARN', ], 'Name' => [ 'shape' => 'AgentStatusName', ], 'Type' => [ 'shape' => 'AgentStatusType', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedRegion' => [ 'shape' => 'RegionName', ], ], ], 'AgentStatusSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AgentStatusSummary', ], ], 'AgentStatusType' => [ 'type' => 'string', 'enum' => [ 'ROUTABLE', 'CUSTOM', 'OFFLINE', ], ], 'AgentStatusTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'AgentStatusType', ], 'max' => 3, ], 'AgentUsername' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'AgentsCriteria' => [ 'type' => 'structure', 'members' => [ 'AgentIds' => [ 'shape' => 'AgentIds', ], ], ], 'AgentsMinOneMaxHundred' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserId', ], 'max' => 100, 'min' => 1, ], 'AliasArn' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'AllowedAccessControlTags' => [ 'type' => 'map', 'key' => [ 'shape' => 'SecurityProfilePolicyKey', ], 'value' => [ 'shape' => 'SecurityProfilePolicyValue', ], 'max' => 4, ], 'AllowedCapabilities' => [ 'type' => 'structure', 'members' => [ 'Customer' => [ 'shape' => 'ParticipantCapabilities', ], 'Agent' => [ 'shape' => 'ParticipantCapabilities', ], ], ], 'AllowedMonitorCapabilities' => [ 'type' => 'list', 'member' => [ 'shape' => 'MonitorCapability', ], 'max' => 2, ], 'AnalyticsDataAssociationResult' => [ 'type' => 'structure', 'members' => [ 'DataSetId' => [ 'shape' => 'DataSetId', ], 'TargetAccountId' => [ 'shape' => 'AWSAccountId', ], 'ResourceShareId' => [ 'shape' => 'String', ], 'ResourceShareArn' => [ 'shape' => 'ARN', ], 'ResourceShareStatus' => [ 'shape' => 'String', ], ], ], 'AnalyticsDataAssociationResults' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalyticsDataAssociationResult', ], ], 'AnalyticsDataSetsResult' => [ 'type' => 'structure', 'members' => [ 'DataSetId' => [ 'shape' => 'DataSetId', ], 'DataSetName' => [ 'shape' => 'String', ], ], ], 'AnalyticsDataSetsResults' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalyticsDataSetsResult', ], ], 'AnswerMachineDetectionConfig' => [ 'type' => 'structure', 'members' => [ 'EnableAnswerMachineDetection' => [ 'shape' => 'Boolean', ], 'AwaitAnswerMachinePrompt' => [ 'shape' => 'Boolean', ], ], ], 'AnsweringMachineDetectionStatus' => [ 'type' => 'string', 'enum' => [ 'ANSWERED', 'UNDETECTED', 'ERROR', 'HUMAN_ANSWERED', 'SIT_TONE_DETECTED', 'SIT_TONE_BUSY', 'SIT_TONE_INVALID_NUMBER', 'FAX_MACHINE_DETECTED', 'VOICEMAIL_BEEP', 'VOICEMAIL_NO_BEEP', 'AMD_UNRESOLVED', 'AMD_UNANSWERED', 'AMD_ERROR', 'AMD_NOT_APPLICABLE', ], ], 'Application' => [ 'type' => 'structure', 'members' => [ 'Namespace' => [ 'shape' => 'Namespace', ], 'ApplicationPermissions' => [ 'shape' => 'ApplicationPermissions', ], ], ], 'ApplicationPermissions' => [ 'type' => 'list', 'member' => [ 'shape' => 'Permission', ], 'max' => 10, 'min' => 1, ], 'Applications' => [ 'type' => 'list', 'member' => [ 'shape' => 'Application', ], 'max' => 10, ], 'ApproximateTotalCount' => [ 'type' => 'long', ], 'ArtifactId' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'ArtifactStatus' => [ 'type' => 'string', 'enum' => [ 'APPROVED', 'REJECTED', 'IN_PROGRESS', ], ], 'AssignContactCategoryActionDefinition' => [ 'type' => 'structure', 'members' => [], ], 'AssignSlaActionDefinition' => [ 'type' => 'structure', 'required' => [ 'SlaAssignmentType', ], 'members' => [ 'SlaAssignmentType' => [ 'shape' => 'SlaAssignmentType', ], 'CaseSlaConfiguration' => [ 'shape' => 'CaseSlaConfiguration', ], ], ], 'AssociateAnalyticsDataSetRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'DataSetId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'DataSetId' => [ 'shape' => 'DataSetId', ], 'TargetAccountId' => [ 'shape' => 'AWSAccountId', ], ], ], 'AssociateAnalyticsDataSetResponse' => [ 'type' => 'structure', 'members' => [ 'DataSetId' => [ 'shape' => 'DataSetId', ], 'TargetAccountId' => [ 'shape' => 'AWSAccountId', ], 'ResourceShareId' => [ 'shape' => 'String', ], 'ResourceShareArn' => [ 'shape' => 'ARN', ], ], ], 'AssociateApprovedOriginRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'Origin', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'Origin' => [ 'shape' => 'Origin', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'AssociateBotRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'LexBot' => [ 'shape' => 'LexBot', ], 'LexV2Bot' => [ 'shape' => 'LexV2Bot', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'AssociateDefaultVocabularyRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'LanguageCode', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'LanguageCode' => [ 'shape' => 'VocabularyLanguageCode', 'location' => 'uri', 'locationName' => 'LanguageCode', ], 'VocabularyId' => [ 'shape' => 'VocabularyId', ], ], ], 'AssociateDefaultVocabularyResponse' => [ 'type' => 'structure', 'members' => [], ], 'AssociateFlowRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ResourceId', 'FlowId', 'ResourceType', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ResourceId' => [ 'shape' => 'ARN', ], 'FlowId' => [ 'shape' => 'ARN', ], 'ResourceType' => [ 'shape' => 'FlowAssociationResourceType', ], ], ], 'AssociateFlowResponse' => [ 'type' => 'structure', 'members' => [], ], 'AssociateInstanceStorageConfigRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ResourceType', 'StorageConfig', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ResourceType' => [ 'shape' => 'InstanceStorageResourceType', ], 'StorageConfig' => [ 'shape' => 'InstanceStorageConfig', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'AssociateInstanceStorageConfigResponse' => [ 'type' => 'structure', 'members' => [ 'AssociationId' => [ 'shape' => 'AssociationId', ], ], ], 'AssociateLambdaFunctionRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'FunctionArn', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'FunctionArn' => [ 'shape' => 'FunctionArn', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'AssociateLexBotRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'LexBot', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'LexBot' => [ 'shape' => 'LexBot', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'AssociatePhoneNumberContactFlowRequest' => [ 'type' => 'structure', 'required' => [ 'PhoneNumberId', 'InstanceId', 'ContactFlowId', ], 'members' => [ 'PhoneNumberId' => [ 'shape' => 'PhoneNumberId', 'location' => 'uri', 'locationName' => 'PhoneNumberId', ], 'InstanceId' => [ 'shape' => 'InstanceId', ], 'ContactFlowId' => [ 'shape' => 'ContactFlowId', ], ], ], 'AssociateQueueQuickConnectsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'QueueId', 'QuickConnectIds', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'QueueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'QueueId', ], 'QuickConnectIds' => [ 'shape' => 'QuickConnectsList', ], ], ], 'AssociateRoutingProfileQueuesRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'RoutingProfileId', 'QueueConfigs', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'RoutingProfileId' => [ 'shape' => 'RoutingProfileId', 'location' => 'uri', 'locationName' => 'RoutingProfileId', ], 'QueueConfigs' => [ 'shape' => 'RoutingProfileQueueConfigList', ], ], ], 'AssociateSecurityKeyRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'Key', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'Key' => [ 'shape' => 'PEM', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'AssociateSecurityKeyResponse' => [ 'type' => 'structure', 'members' => [ 'AssociationId' => [ 'shape' => 'AssociationId', ], ], ], 'AssociateTrafficDistributionGroupUserRequest' => [ 'type' => 'structure', 'required' => [ 'TrafficDistributionGroupId', 'UserId', 'InstanceId', ], 'members' => [ 'TrafficDistributionGroupId' => [ 'shape' => 'TrafficDistributionGroupIdOrArn', 'location' => 'uri', 'locationName' => 'TrafficDistributionGroupId', ], 'UserId' => [ 'shape' => 'UserId', ], 'InstanceId' => [ 'shape' => 'InstanceId', ], ], ], 'AssociateTrafficDistributionGroupUserResponse' => [ 'type' => 'structure', 'members' => [], ], 'AssociateUserProficienciesRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'UserId', 'UserProficiencies', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'UserId' => [ 'shape' => 'UserId', 'location' => 'uri', 'locationName' => 'UserId', ], 'UserProficiencies' => [ 'shape' => 'UserProficiencyList', ], ], ], 'AssociatedContactSummary' => [ 'type' => 'structure', 'members' => [ 'ContactId' => [ 'shape' => 'ContactId', ], 'ContactArn' => [ 'shape' => 'ARN', ], 'InitiationTimestamp' => [ 'shape' => 'Timestamp', ], 'DisconnectTimestamp' => [ 'shape' => 'Timestamp', ], 'InitialContactId' => [ 'shape' => 'ContactId', ], 'PreviousContactId' => [ 'shape' => 'ContactId', ], 'RelatedContactId' => [ 'shape' => 'ContactId', ], 'InitiationMethod' => [ 'shape' => 'ContactInitiationMethod', ], 'Channel' => [ 'shape' => 'Channel', ], ], ], 'AssociatedContactSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssociatedContactSummary', ], ], 'AssociatedQueueIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'QueueId', ], ], 'AssociationId' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'AttachedFile' => [ 'type' => 'structure', 'required' => [ 'CreationTime', 'FileArn', 'FileId', 'FileName', 'FileSizeInBytes', 'FileStatus', ], 'members' => [ 'CreationTime' => [ 'shape' => 'ISO8601Datetime', ], 'FileArn' => [ 'shape' => 'ARN', ], 'FileId' => [ 'shape' => 'FileId', ], 'FileName' => [ 'shape' => 'FileName', ], 'FileSizeInBytes' => [ 'shape' => 'FileSizeInBytes', 'box' => true, ], 'FileStatus' => [ 'shape' => 'FileStatusType', ], 'CreatedBy' => [ 'shape' => 'CreatedByInfo', ], 'FileUseCaseType' => [ 'shape' => 'FileUseCaseType', ], 'AssociatedResourceArn' => [ 'shape' => 'ARN', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'AttachedFileError' => [ 'type' => 'structure', 'members' => [ 'ErrorCode' => [ 'shape' => 'ErrorCode', ], 'ErrorMessage' => [ 'shape' => 'ErrorMessage', ], 'FileId' => [ 'shape' => 'FileId', ], ], ], 'AttachedFileErrorsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AttachedFileError', ], ], 'AttachedFileInvalidRequestExceptionReason' => [ 'type' => 'string', 'enum' => [ 'INVALID_FILE_SIZE', 'INVALID_FILE_TYPE', 'INVALID_FILE_NAME', ], ], 'AttachedFileServiceQuotaExceededExceptionReason' => [ 'type' => 'string', 'enum' => [ 'TOTAL_FILE_SIZE_EXCEEDED', 'TOTAL_FILE_COUNT_EXCEEDED', ], ], 'AttachedFilesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AttachedFile', ], ], 'AttachmentName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'AttachmentReference' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'ReferenceKey', ], 'Value' => [ 'shape' => 'ReferenceValue', ], 'Status' => [ 'shape' => 'ReferenceStatus', ], 'Arn' => [ 'shape' => 'ReferenceArn', ], ], ], 'Attendee' => [ 'type' => 'structure', 'members' => [ 'AttendeeId' => [ 'shape' => 'AttendeeId', ], 'JoinToken' => [ 'shape' => 'JoinToken', ], ], ], 'AttendeeId' => [ 'type' => 'string', ], 'Attribute' => [ 'type' => 'structure', 'members' => [ 'AttributeType' => [ 'shape' => 'InstanceAttributeType', ], 'Value' => [ 'shape' => 'InstanceAttributeValue', ], ], ], 'AttributeAndCondition' => [ 'type' => 'structure', 'members' => [ 'TagConditions' => [ 'shape' => 'TagAndConditionList', ], 'HierarchyGroupCondition' => [ 'shape' => 'HierarchyGroupCondition', ], ], ], 'AttributeCondition' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'PredefinedAttributeName', ], 'Value' => [ 'shape' => 'ProficiencyValue', ], 'ProficiencyLevel' => [ 'shape' => 'NullableProficiencyLevel', ], 'Range' => [ 'shape' => 'Range', ], 'MatchCriteria' => [ 'shape' => 'MatchCriteria', ], 'ComparisonOperator' => [ 'shape' => 'ComparisonOperator', ], ], ], 'AttributeName' => [ 'type' => 'string', 'max' => 32767, 'min' => 1, ], 'AttributeOrConditionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AttributeAndCondition', ], ], 'AttributeValue' => [ 'type' => 'string', 'max' => 32767, 'min' => 0, ], 'Attributes' => [ 'type' => 'map', 'key' => [ 'shape' => 'AttributeName', ], 'value' => [ 'shape' => 'AttributeValue', ], ], 'AttributesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Attribute', ], ], 'AudioFeatures' => [ 'type' => 'structure', 'members' => [ 'EchoReduction' => [ 'shape' => 'MeetingFeatureStatus', ], ], ], 'AudioQualityMetricsInfo' => [ 'type' => 'structure', 'members' => [ 'QualityScore' => [ 'shape' => 'AudioQualityScore', ], 'PotentialQualityIssues' => [ 'shape' => 'PotentialAudioQualityIssues', ], ], ], 'AudioQualityScore' => [ 'type' => 'float', ], 'AuthenticationError' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^[\\x20-\\x21\\x23-\\x5B\\x5D-\\x7E]*$', 'sensitive' => true, ], 'AuthenticationErrorDescription' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^[\\x20-\\x21\\x23-\\x5B\\x5D-\\x7E]*$', 'sensitive' => true, ], 'AuthenticationProfile' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'AuthenticationProfileId', ], 'Arn' => [ 'shape' => 'ARN', ], 'Name' => [ 'shape' => 'AuthenticationProfileName', ], 'Description' => [ 'shape' => 'AuthenticationProfileDescription', ], 'AllowedIps' => [ 'shape' => 'IpCidrList', ], 'BlockedIps' => [ 'shape' => 'IpCidrList', ], 'IsDefault' => [ 'shape' => 'Boolean', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedRegion' => [ 'shape' => 'RegionName', ], 'PeriodicSessionDuration' => [ 'shape' => 'AccessTokenDuration', ], 'MaxSessionDuration' => [ 'shape' => 'RefreshTokenDuration', ], ], ], 'AuthenticationProfileDescription' => [ 'type' => 'string', 'max' => 250, 'min' => 1, ], 'AuthenticationProfileId' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'AuthenticationProfileName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'AuthenticationProfileSummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'AuthenticationProfileId', ], 'Arn' => [ 'shape' => 'ARN', ], 'Name' => [ 'shape' => 'AuthenticationProfileName', ], 'IsDefault' => [ 'shape' => 'Boolean', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedRegion' => [ 'shape' => 'RegionName', ], ], ], 'AuthenticationProfileSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AuthenticationProfileSummary', ], ], 'AuthorizationCode' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'sensitive' => true, ], 'AutoAccept' => [ 'type' => 'boolean', ], 'AvailableNumberSummary' => [ 'type' => 'structure', 'members' => [ 'PhoneNumber' => [ 'shape' => 'PhoneNumber', ], 'PhoneNumberCountryCode' => [ 'shape' => 'PhoneNumberCountryCode', ], 'PhoneNumberType' => [ 'shape' => 'PhoneNumberType', ], ], ], 'AvailableNumbersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AvailableNumberSummary', ], ], 'AwsRegion' => [ 'type' => 'string', 'max' => 31, 'min' => 8, 'pattern' => '[a-z]{2}(-[a-z]+){1,2}(-[0-9])?', ], 'BatchAssociateAnalyticsDataSetRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'DataSetIds', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'DataSetIds' => [ 'shape' => 'DataSetIds', ], 'TargetAccountId' => [ 'shape' => 'AWSAccountId', ], ], ], 'BatchAssociateAnalyticsDataSetResponse' => [ 'type' => 'structure', 'members' => [ 'Created' => [ 'shape' => 'AnalyticsDataAssociationResults', ], 'Errors' => [ 'shape' => 'ErrorResults', ], ], ], 'BatchDisassociateAnalyticsDataSetRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'DataSetIds', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'DataSetIds' => [ 'shape' => 'DataSetIds', ], 'TargetAccountId' => [ 'shape' => 'AWSAccountId', ], ], ], 'BatchDisassociateAnalyticsDataSetResponse' => [ 'type' => 'structure', 'members' => [ 'Deleted' => [ 'shape' => 'DataSetIds', ], 'Errors' => [ 'shape' => 'ErrorResults', ], ], ], 'BatchGetAttachedFileMetadataRequest' => [ 'type' => 'structure', 'required' => [ 'FileIds', 'InstanceId', 'AssociatedResourceArn', ], 'members' => [ 'FileIds' => [ 'shape' => 'FileIdList', ], 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'AssociatedResourceArn' => [ 'shape' => 'ARN', 'location' => 'querystring', 'locationName' => 'associatedResourceArn', ], ], ], 'BatchGetAttachedFileMetadataResponse' => [ 'type' => 'structure', 'members' => [ 'Files' => [ 'shape' => 'AttachedFilesList', ], 'Errors' => [ 'shape' => 'AttachedFileErrorsList', ], ], ], 'BatchGetFlowAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ResourceIds', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ResourceIds' => [ 'shape' => 'resourceArnListMaxLimit100', ], 'ResourceType' => [ 'shape' => 'ListFlowAssociationResourceType', ], ], ], 'BatchGetFlowAssociationResponse' => [ 'type' => 'structure', 'members' => [ 'FlowAssociationSummaryList' => [ 'shape' => 'FlowAssociationSummaryList', ], ], ], 'BatchPutContactRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactDataRequestList', ], 'members' => [ 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ContactDataRequestList' => [ 'shape' => 'ContactDataRequestList', ], ], ], 'BatchPutContactResponse' => [ 'type' => 'structure', 'members' => [ 'SuccessfulRequestList' => [ 'shape' => 'SuccessfulRequestList', ], 'FailedRequestList' => [ 'shape' => 'FailedRequestList', ], ], ], 'BehaviorType' => [ 'type' => 'string', 'enum' => [ 'ROUTE_CURRENT_CHANNEL_ONLY', 'ROUTE_ANY_CHANNEL', ], ], 'Body' => [ 'type' => 'string', 'max' => 5242880, 'min' => 1, 'sensitive' => true, ], 'Boolean' => [ 'type' => 'boolean', ], 'BotName' => [ 'type' => 'string', 'max' => 50, ], 'BoxedBoolean' => [ 'type' => 'boolean', ], 'BucketName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'Campaign' => [ 'type' => 'structure', 'members' => [ 'CampaignId' => [ 'shape' => 'CampaignId', ], ], ], 'CampaignId' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'CaseSlaConfiguration' => [ 'type' => 'structure', 'required' => [ 'Name', 'Type', 'TargetSlaMinutes', ], 'members' => [ 'Name' => [ 'shape' => 'SlaName', ], 'Type' => [ 'shape' => 'SlaType', ], 'FieldId' => [ 'shape' => 'FieldValueId', ], 'TargetFieldValues' => [ 'shape' => 'SlaFieldValueUnionList', ], 'TargetSlaMinutes' => [ 'shape' => 'TargetSlaMinutes', ], ], ], 'Channel' => [ 'type' => 'string', 'enum' => [ 'VOICE', 'CHAT', 'TASK', 'EMAIL', ], ], 'ChannelList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Channel', ], ], 'ChannelToCountMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'Channel', ], 'value' => [ 'shape' => 'IntegerCount', ], ], 'Channels' => [ 'type' => 'list', 'member' => [ 'shape' => 'Channel', ], 'max' => 4, ], 'ChatContent' => [ 'type' => 'string', 'max' => 16384, 'min' => 1, ], 'ChatContentType' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'ChatDurationInMinutes' => [ 'type' => 'integer', 'max' => 10080, 'min' => 60, ], 'ChatEvent' => [ 'type' => 'structure', 'required' => [ 'Type', ], 'members' => [ 'Type' => [ 'shape' => 'ChatEventType', ], 'ContentType' => [ 'shape' => 'ChatContentType', ], 'Content' => [ 'shape' => 'ChatContent', ], ], ], 'ChatEventType' => [ 'type' => 'string', 'enum' => [ 'DISCONNECT', 'MESSAGE', 'EVENT', ], ], 'ChatMessage' => [ 'type' => 'structure', 'required' => [ 'ContentType', 'Content', ], 'members' => [ 'ContentType' => [ 'shape' => 'ChatContentType', ], 'Content' => [ 'shape' => 'ChatContent', ], ], ], 'ChatParticipantRoleConfig' => [ 'type' => 'structure', 'required' => [ 'ParticipantTimerConfigList', ], 'members' => [ 'ParticipantTimerConfigList' => [ 'shape' => 'ParticipantTimerConfigList', ], ], ], 'ChatStreamingConfiguration' => [ 'type' => 'structure', 'required' => [ 'StreamingEndpointArn', ], 'members' => [ 'StreamingEndpointArn' => [ 'shape' => 'ChatStreamingEndpointARN', ], ], ], 'ChatStreamingEndpointARN' => [ 'type' => 'string', 'max' => 350, 'min' => 1, ], 'ClaimPhoneNumberRequest' => [ 'type' => 'structure', 'required' => [ 'PhoneNumber', ], 'members' => [ 'TargetArn' => [ 'shape' => 'ARN', ], 'InstanceId' => [ 'shape' => 'InstanceId', ], 'PhoneNumber' => [ 'shape' => 'PhoneNumber', ], 'PhoneNumberDescription' => [ 'shape' => 'PhoneNumberDescription', ], 'Tags' => [ 'shape' => 'TagMap', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'ClaimPhoneNumberResponse' => [ 'type' => 'structure', 'members' => [ 'PhoneNumberId' => [ 'shape' => 'PhoneNumberId', ], 'PhoneNumberArn' => [ 'shape' => 'ARN', ], ], ], 'ClaimedPhoneNumberSummary' => [ 'type' => 'structure', 'members' => [ 'PhoneNumberId' => [ 'shape' => 'PhoneNumberId', ], 'PhoneNumberArn' => [ 'shape' => 'ARN', ], 'PhoneNumber' => [ 'shape' => 'PhoneNumber', ], 'PhoneNumberCountryCode' => [ 'shape' => 'PhoneNumberCountryCode', ], 'PhoneNumberType' => [ 'shape' => 'PhoneNumberType', ], 'PhoneNumberDescription' => [ 'shape' => 'PhoneNumberDescription', ], 'TargetArn' => [ 'shape' => 'ARN', ], 'InstanceId' => [ 'shape' => 'InstanceId', ], 'Tags' => [ 'shape' => 'TagMap', ], 'PhoneNumberStatus' => [ 'shape' => 'PhoneNumberStatus', ], 'SourcePhoneNumberArn' => [ 'shape' => 'ARN', ], ], ], 'ClientToken' => [ 'type' => 'string', 'max' => 500, ], 'CommonAttributeAndCondition' => [ 'type' => 'structure', 'members' => [ 'TagConditions' => [ 'shape' => 'TagAndConditionList', ], ], ], 'CommonAttributeOrConditionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CommonAttributeAndCondition', ], ], 'CommonHumanReadableDescription' => [ 'type' => 'string', 'pattern' => '^[\\P{C}\\r\\n\\t]{1,250}$', ], 'CommonHumanReadableName' => [ 'type' => 'string', 'pattern' => '^[\\P{C}\\r\\n\\t]{1,127}$', ], 'CommonNameLength127' => [ 'type' => 'string', 'max' => 127, 'min' => 1, ], 'Comparison' => [ 'type' => 'string', 'enum' => [ 'LT', ], ], 'ComparisonOperator' => [ 'type' => 'string', 'max' => 127, 'min' => 1, ], 'CompleteAttachedFileUploadRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'FileId', 'AssociatedResourceArn', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'FileId' => [ 'shape' => 'FileId', 'location' => 'uri', 'locationName' => 'FileId', ], 'AssociatedResourceArn' => [ 'shape' => 'ARN', 'location' => 'querystring', 'locationName' => 'associatedResourceArn', ], ], ], 'CompleteAttachedFileUploadResponse' => [ 'type' => 'structure', 'members' => [], ], 'Concurrency' => [ 'type' => 'integer', 'max' => 10, 'min' => 1, ], 'Condition' => [ 'type' => 'structure', 'members' => [ 'StringCondition' => [ 'shape' => 'StringCondition', ], 'NumberCondition' => [ 'shape' => 'NumberCondition', ], ], ], 'ConditionalOperationFailedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'Conditions' => [ 'type' => 'list', 'member' => [ 'shape' => 'Condition', ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ConnectionData' => [ 'type' => 'structure', 'members' => [ 'Attendee' => [ 'shape' => 'Attendee', ], 'Meeting' => [ 'shape' => 'Meeting', ], ], ], 'Contact' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'ARN', ], 'Id' => [ 'shape' => 'ContactId', ], 'InitialContactId' => [ 'shape' => 'ContactId', ], 'PreviousContactId' => [ 'shape' => 'ContactId', ], 'ContactAssociationId' => [ 'shape' => 'ContactId', ], 'InitiationMethod' => [ 'shape' => 'ContactInitiationMethod', ], 'Name' => [ 'shape' => 'Name', ], 'Description' => [ 'shape' => 'Description', ], 'Channel' => [ 'shape' => 'Channel', ], 'QueueInfo' => [ 'shape' => 'QueueInfo', ], 'AgentInfo' => [ 'shape' => 'AgentInfo', ], 'InitiationTimestamp' => [ 'shape' => 'timestamp', ], 'DisconnectTimestamp' => [ 'shape' => 'timestamp', ], 'LastUpdateTimestamp' => [ 'shape' => 'timestamp', ], 'LastPausedTimestamp' => [ 'shape' => 'timestamp', ], 'LastResumedTimestamp' => [ 'shape' => 'timestamp', ], 'TotalPauseCount' => [ 'shape' => 'TotalPauseCount', ], 'TotalPauseDurationInSeconds' => [ 'shape' => 'TotalPauseDurationInSeconds', ], 'ScheduledTimestamp' => [ 'shape' => 'timestamp', ], 'RelatedContactId' => [ 'shape' => 'ContactId', ], 'WisdomInfo' => [ 'shape' => 'WisdomInfo', ], 'CustomerId' => [ 'shape' => 'CustomerId', ], 'CustomerEndpoint' => [ 'shape' => 'EndpointInfo', ], 'SystemEndpoint' => [ 'shape' => 'EndpointInfo', ], 'QueueTimeAdjustmentSeconds' => [ 'shape' => 'QueueTimeAdjustmentSeconds', ], 'QueuePriority' => [ 'shape' => 'QueuePriority', ], 'Tags' => [ 'shape' => 'ContactTagMap', ], 'ConnectedToSystemTimestamp' => [ 'shape' => 'timestamp', ], 'RoutingCriteria' => [ 'shape' => 'RoutingCriteria', ], 'Customer' => [ 'shape' => 'Customer', ], 'Campaign' => [ 'shape' => 'Campaign', ], 'AnsweringMachineDetectionStatus' => [ 'shape' => 'AnsweringMachineDetectionStatus', ], 'CustomerVoiceActivity' => [ 'shape' => 'CustomerVoiceActivity', ], 'QualityMetrics' => [ 'shape' => 'QualityMetrics', ], 'DisconnectDetails' => [ 'shape' => 'DisconnectDetails', ], 'AdditionalEmailRecipients' => [ 'shape' => 'AdditionalEmailRecipients', ], 'SegmentAttributes' => [ 'shape' => 'SegmentAttributes', ], 'Recordings' => [ 'shape' => 'Recordings', ], 'DisconnectReason' => [ 'shape' => 'String', ], 'ContactEvaluations' => [ 'shape' => 'ContactEvaluations', ], 'ContactDetails' => [ 'shape' => 'ContactDetails', ], 'Attributes' => [ 'shape' => 'Attributes', ], ], ], 'ContactAnalysis' => [ 'type' => 'structure', 'members' => [ 'Transcript' => [ 'shape' => 'Transcript', ], ], ], 'ContactConfiguration' => [ 'type' => 'structure', 'required' => [ 'ContactId', ], 'members' => [ 'ContactId' => [ 'shape' => 'ContactId', ], 'ParticipantRole' => [ 'shape' => 'ParticipantRole', ], 'IncludeRawMessage' => [ 'shape' => 'IncludeRawMessage', ], ], ], 'ContactDataRequest' => [ 'type' => 'structure', 'members' => [ 'SystemEndpoint' => [ 'shape' => 'Endpoint', ], 'CustomerEndpoint' => [ 'shape' => 'Endpoint', ], 'RequestIdentifier' => [ 'shape' => 'RequestIdentifier', ], 'QueueId' => [ 'shape' => 'QueueId', ], 'Attributes' => [ 'shape' => 'Attributes', ], 'Campaign' => [ 'shape' => 'Campaign', ], ], ], 'ContactDataRequestList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContactDataRequest', ], 'max' => 25, 'min' => 1, ], 'ContactDetailDescription' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'ContactDetailName' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'ContactDetails' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'ContactDetailName', ], 'Description' => [ 'shape' => 'ContactDetailDescription', ], ], ], 'ContactEvaluation' => [ 'type' => 'structure', 'members' => [ 'FormId' => [ 'shape' => 'FormId', ], 'EvaluationArn' => [ 'shape' => 'EvaluationArn', ], 'Status' => [ 'shape' => 'Status', ], 'StartTimestamp' => [ 'shape' => 'timestamp', ], 'EndTimestamp' => [ 'shape' => 'timestamp', ], 'DeleteTimestamp' => [ 'shape' => 'timestamp', ], 'ExportLocation' => [ 'shape' => 'ExportLocation', ], ], ], 'ContactEvaluations' => [ 'type' => 'map', 'key' => [ 'shape' => 'EvaluationId', ], 'value' => [ 'shape' => 'ContactEvaluation', ], ], 'ContactFilter' => [ 'type' => 'structure', 'members' => [ 'ContactStates' => [ 'shape' => 'ContactStates', ], ], ], 'ContactFlow' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'ARN', ], 'Id' => [ 'shape' => 'ContactFlowId', ], 'Name' => [ 'shape' => 'ContactFlowName', ], 'Type' => [ 'shape' => 'ContactFlowType', ], 'State' => [ 'shape' => 'ContactFlowState', ], 'Status' => [ 'shape' => 'ContactFlowStatus', ], 'Description' => [ 'shape' => 'ContactFlowDescription', ], 'Content' => [ 'shape' => 'ContactFlowContent', ], 'Tags' => [ 'shape' => 'TagMap', ], 'FlowContentSha256' => [ 'shape' => 'FlowContentSha256', ], 'Version' => [ 'shape' => 'ResourceVersion', ], 'VersionDescription' => [ 'shape' => 'ContactFlowDescription', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedRegion' => [ 'shape' => 'RegionName', ], ], ], 'ContactFlowContent' => [ 'type' => 'string', ], 'ContactFlowDescription' => [ 'type' => 'string', ], 'ContactFlowId' => [ 'type' => 'string', 'max' => 500, ], 'ContactFlowModule' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'ARN', ], 'Id' => [ 'shape' => 'ContactFlowModuleId', ], 'Name' => [ 'shape' => 'ContactFlowModuleName', ], 'Content' => [ 'shape' => 'ContactFlowModuleContent', ], 'Description' => [ 'shape' => 'ContactFlowModuleDescription', ], 'State' => [ 'shape' => 'ContactFlowModuleState', ], 'Status' => [ 'shape' => 'ContactFlowModuleStatus', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'ContactFlowModuleContent' => [ 'type' => 'string', 'max' => 256000, 'min' => 1, ], 'ContactFlowModuleDescription' => [ 'type' => 'string', 'max' => 500, 'min' => 0, 'pattern' => '.*\\S.*', ], 'ContactFlowModuleId' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'ContactFlowModuleName' => [ 'type' => 'string', 'max' => 127, 'min' => 1, 'pattern' => '.*\\S.*', ], 'ContactFlowModuleSearchConditionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContactFlowModuleSearchCriteria', ], ], 'ContactFlowModuleSearchCriteria' => [ 'type' => 'structure', 'members' => [ 'OrConditions' => [ 'shape' => 'ContactFlowModuleSearchConditionList', ], 'AndConditions' => [ 'shape' => 'ContactFlowModuleSearchConditionList', ], 'StringCondition' => [ 'shape' => 'StringCondition', ], 'StateCondition' => [ 'shape' => 'ContactFlowModuleState', ], 'StatusCondition' => [ 'shape' => 'ContactFlowModuleStatus', ], ], ], 'ContactFlowModuleSearchFilter' => [ 'type' => 'structure', 'members' => [ 'TagFilter' => [ 'shape' => 'ControlPlaneTagFilter', ], ], ], 'ContactFlowModuleSearchSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContactFlowModule', ], ], 'ContactFlowModuleState' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'ARCHIVED', ], ], 'ContactFlowModuleStatus' => [ 'type' => 'string', 'enum' => [ 'PUBLISHED', 'SAVED', ], ], 'ContactFlowModuleSummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ContactFlowModuleId', ], 'Arn' => [ 'shape' => 'ARN', ], 'Name' => [ 'shape' => 'ContactFlowModuleName', ], 'State' => [ 'shape' => 'ContactFlowModuleState', ], ], ], 'ContactFlowModulesSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContactFlowModuleSummary', ], ], 'ContactFlowName' => [ 'type' => 'string', 'min' => 1, ], 'ContactFlowNotPublishedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'ContactFlowSearchConditionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContactFlowSearchCriteria', ], ], 'ContactFlowSearchCriteria' => [ 'type' => 'structure', 'members' => [ 'OrConditions' => [ 'shape' => 'ContactFlowSearchConditionList', ], 'AndConditions' => [ 'shape' => 'ContactFlowSearchConditionList', ], 'StringCondition' => [ 'shape' => 'StringCondition', ], 'TypeCondition' => [ 'shape' => 'ContactFlowType', ], 'StateCondition' => [ 'shape' => 'ContactFlowState', ], 'StatusCondition' => [ 'shape' => 'ContactFlowStatus', ], ], ], 'ContactFlowSearchFilter' => [ 'type' => 'structure', 'members' => [ 'TagFilter' => [ 'shape' => 'ControlPlaneTagFilter', ], ], ], 'ContactFlowSearchSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContactFlow', ], ], 'ContactFlowState' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'ARCHIVED', ], ], 'ContactFlowStatus' => [ 'type' => 'string', 'enum' => [ 'PUBLISHED', 'SAVED', ], ], 'ContactFlowSummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ContactFlowId', ], 'Arn' => [ 'shape' => 'ARN', ], 'Name' => [ 'shape' => 'ContactFlowName', ], 'ContactFlowType' => [ 'shape' => 'ContactFlowType', ], 'ContactFlowState' => [ 'shape' => 'ContactFlowState', ], 'ContactFlowStatus' => [ 'shape' => 'ContactFlowStatus', ], ], ], 'ContactFlowSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContactFlowSummary', ], ], 'ContactFlowType' => [ 'type' => 'string', 'enum' => [ 'CONTACT_FLOW', 'CUSTOMER_QUEUE', 'CUSTOMER_HOLD', 'CUSTOMER_WHISPER', 'AGENT_HOLD', 'AGENT_WHISPER', 'OUTBOUND_WHISPER', 'AGENT_TRANSFER', 'QUEUE_TRANSFER', 'CAMPAIGN', ], ], 'ContactFlowTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContactFlowType', ], 'max' => 10, ], 'ContactFlowVersionSummary' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'ARN', ], 'VersionDescription' => [ 'shape' => 'ContactFlowDescription', ], 'Version' => [ 'shape' => 'ResourceVersion', ], ], ], 'ContactFlowVersionSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContactFlowVersionSummary', ], ], 'ContactId' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'ContactInitiationMethod' => [ 'type' => 'string', 'enum' => [ 'INBOUND', 'OUTBOUND', 'TRANSFER', 'QUEUE_TRANSFER', 'CALLBACK', 'API', 'DISCONNECT', 'MONITOR', 'EXTERNAL_OUTBOUND', 'WEBRTC_API', 'AGENT_REPLY', 'FLOW', ], ], 'ContactNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 410, ], 'exception' => true, ], 'ContactRecordingType' => [ 'type' => 'string', 'enum' => [ 'AGENT', 'IVR', 'SCREEN', ], ], 'ContactReferences' => [ 'type' => 'map', 'key' => [ 'shape' => 'ReferenceKey', ], 'value' => [ 'shape' => 'Reference', ], ], 'ContactSearchSummary' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'ARN', ], 'Id' => [ 'shape' => 'ContactId', ], 'InitialContactId' => [ 'shape' => 'ContactId', ], 'PreviousContactId' => [ 'shape' => 'ContactId', ], 'InitiationMethod' => [ 'shape' => 'ContactInitiationMethod', ], 'Channel' => [ 'shape' => 'Channel', ], 'QueueInfo' => [ 'shape' => 'ContactSearchSummaryQueueInfo', ], 'AgentInfo' => [ 'shape' => 'ContactSearchSummaryAgentInfo', ], 'InitiationTimestamp' => [ 'shape' => 'timestamp', ], 'DisconnectTimestamp' => [ 'shape' => 'timestamp', ], 'ScheduledTimestamp' => [ 'shape' => 'timestamp', ], 'SegmentAttributes' => [ 'shape' => 'ContactSearchSummarySegmentAttributes', ], ], ], 'ContactSearchSummaryAgentInfo' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'AgentResourceId', ], 'ConnectedToAgentTimestamp' => [ 'shape' => 'timestamp', ], ], ], 'ContactSearchSummaryQueueInfo' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'QueueId', ], 'EnqueueTimestamp' => [ 'shape' => 'timestamp', ], ], ], 'ContactSearchSummarySegmentAttributeValue' => [ 'type' => 'structure', 'members' => [ 'ValueString' => [ 'shape' => 'SegmentAttributeValueString', ], ], ], 'ContactSearchSummarySegmentAttributes' => [ 'type' => 'map', 'key' => [ 'shape' => 'SegmentAttributeName', ], 'value' => [ 'shape' => 'ContactSearchSummarySegmentAttributeValue', ], ], 'ContactState' => [ 'type' => 'string', 'enum' => [ 'INCOMING', 'PENDING', 'CONNECTING', 'CONNECTED', 'CONNECTED_ONHOLD', 'MISSED', 'ERROR', 'ENDED', 'REJECTED', ], ], 'ContactStates' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContactState', ], 'max' => 9, ], 'ContactTagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^(?!aws:)[a-zA-Z+-=._:/]+$', ], 'ContactTagKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContactTagKey', ], 'max' => 6, 'min' => 1, ], 'ContactTagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ContactTagKey', ], 'value' => [ 'shape' => 'ContactTagValue', ], 'max' => 6, 'min' => 1, ], 'ContactTagValue' => [ 'type' => 'string', 'max' => 256, ], 'Contacts' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContactSearchSummary', ], ], 'Content' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'ContentType' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'ControlPlaneAttributeFilter' => [ 'type' => 'structure', 'members' => [ 'OrConditions' => [ 'shape' => 'CommonAttributeOrConditionList', ], 'AndCondition' => [ 'shape' => 'CommonAttributeAndCondition', ], 'TagCondition' => [ 'shape' => 'TagCondition', ], ], ], 'ControlPlaneTagFilter' => [ 'type' => 'structure', 'members' => [ 'OrConditions' => [ 'shape' => 'TagOrConditionList', ], 'AndConditions' => [ 'shape' => 'TagAndConditionList', ], 'TagCondition' => [ 'shape' => 'TagCondition', ], ], ], 'ControlPlaneUserAttributeFilter' => [ 'type' => 'structure', 'members' => [ 'OrConditions' => [ 'shape' => 'AttributeOrConditionList', ], 'AndCondition' => [ 'shape' => 'AttributeAndCondition', ], 'TagCondition' => [ 'shape' => 'TagCondition', ], 'HierarchyGroupCondition' => [ 'shape' => 'HierarchyGroupCondition', ], ], ], 'CreateAgentStatusRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'Name', 'State', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'Name' => [ 'shape' => 'AgentStatusName', ], 'Description' => [ 'shape' => 'AgentStatusDescription', ], 'State' => [ 'shape' => 'AgentStatusState', ], 'DisplayOrder' => [ 'shape' => 'AgentStatusOrderNumber', 'box' => true, ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateAgentStatusResponse' => [ 'type' => 'structure', 'members' => [ 'AgentStatusARN' => [ 'shape' => 'ARN', ], 'AgentStatusId' => [ 'shape' => 'AgentStatusId', ], ], ], 'CreateCaseActionDefinition' => [ 'type' => 'structure', 'required' => [ 'Fields', 'TemplateId', ], 'members' => [ 'Fields' => [ 'shape' => 'FieldValues', ], 'TemplateId' => [ 'shape' => 'TemplateId', ], ], ], 'CreateContactFlowModuleRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'Name', 'Content', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'Name' => [ 'shape' => 'ContactFlowModuleName', ], 'Description' => [ 'shape' => 'ContactFlowModuleDescription', ], 'Content' => [ 'shape' => 'ContactFlowModuleContent', ], 'Tags' => [ 'shape' => 'TagMap', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CreateContactFlowModuleResponse' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ContactFlowModuleId', ], 'Arn' => [ 'shape' => 'ARN', ], ], ], 'CreateContactFlowRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'Name', 'Type', 'Content', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'Name' => [ 'shape' => 'ContactFlowName', ], 'Type' => [ 'shape' => 'ContactFlowType', ], 'Description' => [ 'shape' => 'ContactFlowDescription', ], 'Content' => [ 'shape' => 'ContactFlowContent', ], 'Status' => [ 'shape' => 'ContactFlowStatus', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateContactFlowResponse' => [ 'type' => 'structure', 'members' => [ 'ContactFlowId' => [ 'shape' => 'ContactFlowId', ], 'ContactFlowArn' => [ 'shape' => 'ARN', ], 'FlowContentSha256' => [ 'shape' => 'FlowContentSha256', ], ], ], 'CreateContactFlowVersionRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactFlowId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'Description' => [ 'shape' => 'ContactFlowDescription', ], 'ContactFlowId' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'ContactFlowId', ], 'FlowContentSha256' => [ 'shape' => 'FlowContentSha256', ], 'ContactFlowVersion' => [ 'shape' => 'ResourceVersion', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedRegion' => [ 'shape' => 'RegionName', ], ], ], 'CreateContactFlowVersionResponse' => [ 'type' => 'structure', 'members' => [ 'ContactFlowArn' => [ 'shape' => 'ARN', ], 'Version' => [ 'shape' => 'ResourceVersion', ], ], ], 'CreateContactRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'Channel', 'InitiationMethod', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'RelatedContactId' => [ 'shape' => 'ContactId', ], 'Attributes' => [ 'shape' => 'Attributes', ], 'References' => [ 'shape' => 'ContactReferences', ], 'Channel' => [ 'shape' => 'Channel', ], 'InitiationMethod' => [ 'shape' => 'ContactInitiationMethod', ], 'ExpiryDurationInMinutes' => [ 'shape' => 'ExpiryDurationInMinutes', ], 'UserInfo' => [ 'shape' => 'UserInfo', ], 'InitiateAs' => [ 'shape' => 'InitiateAs', ], 'Name' => [ 'shape' => 'Name', ], 'Description' => [ 'shape' => 'Description', ], 'SegmentAttributes' => [ 'shape' => 'SegmentAttributes', ], 'PreviousContactId' => [ 'shape' => 'ContactId', ], ], ], 'CreateContactResponse' => [ 'type' => 'structure', 'members' => [ 'ContactId' => [ 'shape' => 'ContactId', ], 'ContactArn' => [ 'shape' => 'ARN', ], ], ], 'CreateEmailAddressRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'EmailAddress', ], 'members' => [ 'Description' => [ 'shape' => 'Description', ], 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'EmailAddress' => [ 'shape' => 'EmailAddress', ], 'DisplayName' => [ 'shape' => 'EmailAddressDisplayName', ], 'Tags' => [ 'shape' => 'TagMap', ], 'ClientToken' => [ 'shape' => 'ClientToken', ], ], ], 'CreateEmailAddressResponse' => [ 'type' => 'structure', 'members' => [ 'EmailAddressId' => [ 'shape' => 'EmailAddressId', ], 'EmailAddressArn' => [ 'shape' => 'EmailAddressArn', ], ], ], 'CreateEvaluationFormRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'Title', 'Items', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'Title' => [ 'shape' => 'EvaluationFormTitle', ], 'Description' => [ 'shape' => 'EvaluationFormDescription', ], 'Items' => [ 'shape' => 'EvaluationFormItemsList', ], 'ScoringStrategy' => [ 'shape' => 'EvaluationFormScoringStrategy', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CreateEvaluationFormResponse' => [ 'type' => 'structure', 'required' => [ 'EvaluationFormId', 'EvaluationFormArn', ], 'members' => [ 'EvaluationFormId' => [ 'shape' => 'ResourceId', ], 'EvaluationFormArn' => [ 'shape' => 'ARN', ], ], ], 'CreateHoursOfOperationOverrideRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'HoursOfOperationId', 'Name', 'Config', 'EffectiveFrom', 'EffectiveTill', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'HoursOfOperationId' => [ 'shape' => 'HoursOfOperationId', 'location' => 'uri', 'locationName' => 'HoursOfOperationId', ], 'Name' => [ 'shape' => 'CommonHumanReadableName', ], 'Description' => [ 'shape' => 'CommonHumanReadableDescription', ], 'Config' => [ 'shape' => 'HoursOfOperationOverrideConfigList', ], 'EffectiveFrom' => [ 'shape' => 'HoursOfOperationOverrideYearMonthDayDateFormat', ], 'EffectiveTill' => [ 'shape' => 'HoursOfOperationOverrideYearMonthDayDateFormat', ], ], ], 'CreateHoursOfOperationOverrideResponse' => [ 'type' => 'structure', 'members' => [ 'HoursOfOperationOverrideId' => [ 'shape' => 'HoursOfOperationOverrideId', ], ], ], 'CreateHoursOfOperationRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'Name', 'TimeZone', 'Config', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'Name' => [ 'shape' => 'CommonNameLength127', ], 'Description' => [ 'shape' => 'HoursOfOperationDescription', ], 'TimeZone' => [ 'shape' => 'TimeZone', ], 'Config' => [ 'shape' => 'HoursOfOperationConfigList', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateHoursOfOperationResponse' => [ 'type' => 'structure', 'members' => [ 'HoursOfOperationId' => [ 'shape' => 'HoursOfOperationId', ], 'HoursOfOperationArn' => [ 'shape' => 'ARN', ], ], ], 'CreateInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'IdentityManagementType', 'InboundCallsEnabled', 'OutboundCallsEnabled', ], 'members' => [ 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'IdentityManagementType' => [ 'shape' => 'DirectoryType', ], 'InstanceAlias' => [ 'shape' => 'DirectoryAlias', ], 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'InboundCallsEnabled' => [ 'shape' => 'InboundCallsEnabled', ], 'OutboundCallsEnabled' => [ 'shape' => 'OutboundCallsEnabled', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateInstanceResponse' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'InstanceId', ], 'Arn' => [ 'shape' => 'ARN', ], ], ], 'CreateIntegrationAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'IntegrationType', 'IntegrationArn', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'IntegrationType' => [ 'shape' => 'IntegrationType', ], 'IntegrationArn' => [ 'shape' => 'ARN', ], 'SourceApplicationUrl' => [ 'shape' => 'URI', ], 'SourceApplicationName' => [ 'shape' => 'SourceApplicationName', ], 'SourceType' => [ 'shape' => 'SourceType', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateIntegrationAssociationResponse' => [ 'type' => 'structure', 'members' => [ 'IntegrationAssociationId' => [ 'shape' => 'IntegrationAssociationId', ], 'IntegrationAssociationArn' => [ 'shape' => 'ARN', ], ], ], 'CreateParticipantRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactId', 'ParticipantDetails', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'ContactId' => [ 'shape' => 'ContactId', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'ParticipantDetails' => [ 'shape' => 'ParticipantDetailsToAdd', ], ], ], 'CreateParticipantResponse' => [ 'type' => 'structure', 'members' => [ 'ParticipantCredentials' => [ 'shape' => 'ParticipantTokenCredentials', ], 'ParticipantId' => [ 'shape' => 'ParticipantId', ], ], ], 'CreatePersistentContactAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'InitialContactId', 'RehydrationType', 'SourceContactId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'InitialContactId' => [ 'shape' => 'ContactId', 'location' => 'uri', 'locationName' => 'InitialContactId', ], 'RehydrationType' => [ 'shape' => 'RehydrationType', ], 'SourceContactId' => [ 'shape' => 'ContactId', ], 'ClientToken' => [ 'shape' => 'ClientToken', ], ], ], 'CreatePersistentContactAssociationResponse' => [ 'type' => 'structure', 'members' => [ 'ContinuedFromContactId' => [ 'shape' => 'ContactId', ], ], ], 'CreatePredefinedAttributeRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'Name', 'Values', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'Name' => [ 'shape' => 'PredefinedAttributeName', ], 'Values' => [ 'shape' => 'PredefinedAttributeValues', ], ], ], 'CreatePromptRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'Name', 'S3Uri', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'Name' => [ 'shape' => 'CommonNameLength127', ], 'Description' => [ 'shape' => 'PromptDescription', ], 'S3Uri' => [ 'shape' => 'S3Uri', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreatePromptResponse' => [ 'type' => 'structure', 'members' => [ 'PromptARN' => [ 'shape' => 'ARN', ], 'PromptId' => [ 'shape' => 'PromptId', ], ], ], 'CreatePushNotificationRegistrationRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'PinpointAppArn', 'DeviceToken', 'DeviceType', 'ContactConfiguration', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'PinpointAppArn' => [ 'shape' => 'ARN', ], 'DeviceToken' => [ 'shape' => 'DeviceToken', ], 'DeviceType' => [ 'shape' => 'DeviceType', ], 'ContactConfiguration' => [ 'shape' => 'ContactConfiguration', ], ], ], 'CreatePushNotificationRegistrationResponse' => [ 'type' => 'structure', 'required' => [ 'RegistrationId', ], 'members' => [ 'RegistrationId' => [ 'shape' => 'RegistrationId', ], ], ], 'CreateQueueRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'Name', 'HoursOfOperationId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'Name' => [ 'shape' => 'CommonNameLength127', ], 'Description' => [ 'shape' => 'QueueDescription', ], 'OutboundCallerConfig' => [ 'shape' => 'OutboundCallerConfig', ], 'OutboundEmailConfig' => [ 'shape' => 'OutboundEmailConfig', ], 'HoursOfOperationId' => [ 'shape' => 'HoursOfOperationId', ], 'MaxContacts' => [ 'shape' => 'QueueMaxContacts', 'box' => true, ], 'QuickConnectIds' => [ 'shape' => 'QuickConnectsList', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateQueueResponse' => [ 'type' => 'structure', 'members' => [ 'QueueArn' => [ 'shape' => 'ARN', ], 'QueueId' => [ 'shape' => 'QueueId', ], ], ], 'CreateQuickConnectRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'Name', 'QuickConnectConfig', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'Name' => [ 'shape' => 'QuickConnectName', ], 'Description' => [ 'shape' => 'QuickConnectDescription', ], 'QuickConnectConfig' => [ 'shape' => 'QuickConnectConfig', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateQuickConnectResponse' => [ 'type' => 'structure', 'members' => [ 'QuickConnectARN' => [ 'shape' => 'ARN', ], 'QuickConnectId' => [ 'shape' => 'QuickConnectId', ], ], ], 'CreateRoutingProfileRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'Name', 'Description', 'DefaultOutboundQueueId', 'MediaConcurrencies', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'Name' => [ 'shape' => 'RoutingProfileName', ], 'Description' => [ 'shape' => 'RoutingProfileDescription', ], 'DefaultOutboundQueueId' => [ 'shape' => 'QueueId', ], 'QueueConfigs' => [ 'shape' => 'RoutingProfileQueueConfigList', ], 'MediaConcurrencies' => [ 'shape' => 'MediaConcurrencies', ], 'Tags' => [ 'shape' => 'TagMap', ], 'AgentAvailabilityTimer' => [ 'shape' => 'AgentAvailabilityTimer', ], ], ], 'CreateRoutingProfileResponse' => [ 'type' => 'structure', 'members' => [ 'RoutingProfileArn' => [ 'shape' => 'ARN', ], 'RoutingProfileId' => [ 'shape' => 'RoutingProfileId', ], ], ], 'CreateRuleRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'Name', 'TriggerEventSource', 'Function', 'Actions', 'PublishStatus', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'Name' => [ 'shape' => 'RuleName', ], 'TriggerEventSource' => [ 'shape' => 'RuleTriggerEventSource', ], 'Function' => [ 'shape' => 'RuleFunction', ], 'Actions' => [ 'shape' => 'RuleActions', ], 'PublishStatus' => [ 'shape' => 'RulePublishStatus', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CreateRuleResponse' => [ 'type' => 'structure', 'required' => [ 'RuleArn', 'RuleId', ], 'members' => [ 'RuleArn' => [ 'shape' => 'ARN', ], 'RuleId' => [ 'shape' => 'RuleId', ], ], ], 'CreateSecurityProfileName' => [ 'type' => 'string', 'max' => 127, 'min' => 1, 'pattern' => '^[ a-zA-Z0-9_@-]+$', ], 'CreateSecurityProfileRequest' => [ 'type' => 'structure', 'required' => [ 'SecurityProfileName', 'InstanceId', ], 'members' => [ 'SecurityProfileName' => [ 'shape' => 'CreateSecurityProfileName', ], 'Description' => [ 'shape' => 'SecurityProfileDescription', ], 'Permissions' => [ 'shape' => 'PermissionsList', ], 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'Tags' => [ 'shape' => 'TagMap', ], 'AllowedAccessControlTags' => [ 'shape' => 'AllowedAccessControlTags', ], 'TagRestrictedResources' => [ 'shape' => 'TagRestrictedResourceList', ], 'Applications' => [ 'shape' => 'Applications', ], 'HierarchyRestrictedResources' => [ 'shape' => 'HierarchyRestrictedResourceList', ], 'AllowedAccessControlHierarchyGroupId' => [ 'shape' => 'HierarchyGroupId', ], ], ], 'CreateSecurityProfileResponse' => [ 'type' => 'structure', 'members' => [ 'SecurityProfileId' => [ 'shape' => 'SecurityProfileId', ], 'SecurityProfileArn' => [ 'shape' => 'ARN', ], ], ], 'CreateTaskTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'Name', 'Fields', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'Name' => [ 'shape' => 'TaskTemplateName', ], 'Description' => [ 'shape' => 'TaskTemplateDescription', ], 'ContactFlowId' => [ 'shape' => 'ContactFlowId', ], 'SelfAssignFlowId' => [ 'shape' => 'ContactFlowId', ], 'Constraints' => [ 'shape' => 'TaskTemplateConstraints', ], 'Defaults' => [ 'shape' => 'TaskTemplateDefaults', ], 'Status' => [ 'shape' => 'TaskTemplateStatus', ], 'Fields' => [ 'shape' => 'TaskTemplateFields', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CreateTaskTemplateResponse' => [ 'type' => 'structure', 'required' => [ 'Id', 'Arn', ], 'members' => [ 'Id' => [ 'shape' => 'TaskTemplateId', ], 'Arn' => [ 'shape' => 'TaskTemplateArn', ], ], ], 'CreateTrafficDistributionGroupRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'InstanceId', ], 'members' => [ 'Name' => [ 'shape' => 'Name128', ], 'Description' => [ 'shape' => 'Description250', ], 'InstanceId' => [ 'shape' => 'InstanceIdOrArn', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateTrafficDistributionGroupResponse' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'TrafficDistributionGroupId', ], 'Arn' => [ 'shape' => 'TrafficDistributionGroupArn', ], ], ], 'CreateUseCaseRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'IntegrationAssociationId', 'UseCaseType', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'IntegrationAssociationId' => [ 'shape' => 'IntegrationAssociationId', 'location' => 'uri', 'locationName' => 'IntegrationAssociationId', ], 'UseCaseType' => [ 'shape' => 'UseCaseType', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateUseCaseResponse' => [ 'type' => 'structure', 'members' => [ 'UseCaseId' => [ 'shape' => 'UseCaseId', ], 'UseCaseArn' => [ 'shape' => 'ARN', ], ], ], 'CreateUserHierarchyGroupRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'InstanceId', ], 'members' => [ 'Name' => [ 'shape' => 'HierarchyGroupName', ], 'ParentGroupId' => [ 'shape' => 'HierarchyGroupId', ], 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateUserHierarchyGroupResponse' => [ 'type' => 'structure', 'members' => [ 'HierarchyGroupId' => [ 'shape' => 'HierarchyGroupId', ], 'HierarchyGroupArn' => [ 'shape' => 'ARN', ], ], ], 'CreateUserRequest' => [ 'type' => 'structure', 'required' => [ 'Username', 'PhoneConfig', 'SecurityProfileIds', 'RoutingProfileId', 'InstanceId', ], 'members' => [ 'Username' => [ 'shape' => 'AgentUsername', ], 'Password' => [ 'shape' => 'Password', ], 'IdentityInfo' => [ 'shape' => 'UserIdentityInfo', ], 'PhoneConfig' => [ 'shape' => 'UserPhoneConfig', ], 'DirectoryUserId' => [ 'shape' => 'DirectoryUserId', ], 'SecurityProfileIds' => [ 'shape' => 'SecurityProfileIds', ], 'RoutingProfileId' => [ 'shape' => 'RoutingProfileId', ], 'HierarchyGroupId' => [ 'shape' => 'HierarchyGroupId', ], 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateUserResponse' => [ 'type' => 'structure', 'members' => [ 'UserId' => [ 'shape' => 'UserId', ], 'UserArn' => [ 'shape' => 'ARN', ], ], ], 'CreateViewRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'Status', 'Content', 'Name', ], 'members' => [ 'InstanceId' => [ 'shape' => 'ViewsInstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ClientToken' => [ 'shape' => 'ViewsClientToken', ], 'Status' => [ 'shape' => 'ViewStatus', ], 'Content' => [ 'shape' => 'ViewInputContent', ], 'Description' => [ 'shape' => 'ViewDescription', ], 'Name' => [ 'shape' => 'ViewName', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateViewResponse' => [ 'type' => 'structure', 'members' => [ 'View' => [ 'shape' => 'View', ], ], ], 'CreateViewVersionRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ViewId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'ViewsInstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ViewId' => [ 'shape' => 'ViewId', 'location' => 'uri', 'locationName' => 'ViewId', ], 'VersionDescription' => [ 'shape' => 'ViewDescription', ], 'ViewContentSha256' => [ 'shape' => 'ViewContentSha256', ], ], ], 'CreateViewVersionResponse' => [ 'type' => 'structure', 'members' => [ 'View' => [ 'shape' => 'View', ], ], ], 'CreateVocabularyRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'VocabularyName', 'LanguageCode', 'Content', ], 'members' => [ 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'VocabularyName' => [ 'shape' => 'VocabularyName', ], 'LanguageCode' => [ 'shape' => 'VocabularyLanguageCode', ], 'Content' => [ 'shape' => 'VocabularyContent', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateVocabularyResponse' => [ 'type' => 'structure', 'required' => [ 'VocabularyArn', 'VocabularyId', 'State', ], 'members' => [ 'VocabularyArn' => [ 'shape' => 'ARN', ], 'VocabularyId' => [ 'shape' => 'VocabularyId', ], 'State' => [ 'shape' => 'VocabularyState', ], ], ], 'CreatedByInfo' => [ 'type' => 'structure', 'members' => [ 'ConnectUserArn' => [ 'shape' => 'ARN', ], 'AWSIdentityArn' => [ 'shape' => 'ARN', ], ], 'union' => true, ], 'Credentials' => [ 'type' => 'structure', 'members' => [ 'AccessToken' => [ 'shape' => 'SecurityToken', ], 'AccessTokenExpiration' => [ 'shape' => 'timestamp', ], 'RefreshToken' => [ 'shape' => 'SecurityToken', ], 'RefreshTokenExpiration' => [ 'shape' => 'timestamp', ], ], 'sensitive' => true, ], 'CrossChannelBehavior' => [ 'type' => 'structure', 'required' => [ 'BehaviorType', ], 'members' => [ 'BehaviorType' => [ 'shape' => 'BehaviorType', ], ], ], 'CurrentMetric' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'CurrentMetricName', ], 'Unit' => [ 'shape' => 'Unit', ], ], ], 'CurrentMetricData' => [ 'type' => 'structure', 'members' => [ 'Metric' => [ 'shape' => 'CurrentMetric', ], 'Value' => [ 'shape' => 'Value', 'box' => true, ], ], ], 'CurrentMetricDataCollections' => [ 'type' => 'list', 'member' => [ 'shape' => 'CurrentMetricData', ], ], 'CurrentMetricName' => [ 'type' => 'string', 'enum' => [ 'AGENTS_ONLINE', 'AGENTS_AVAILABLE', 'AGENTS_ON_CALL', 'AGENTS_NON_PRODUCTIVE', 'AGENTS_AFTER_CONTACT_WORK', 'AGENTS_ERROR', 'AGENTS_STAFFED', 'CONTACTS_IN_QUEUE', 'OLDEST_CONTACT_AGE', 'CONTACTS_SCHEDULED', 'AGENTS_ON_CONTACT', 'SLOTS_ACTIVE', 'SLOTS_AVAILABLE', ], ], 'CurrentMetricResult' => [ 'type' => 'structure', 'members' => [ 'Dimensions' => [ 'shape' => 'Dimensions', ], 'Collections' => [ 'shape' => 'CurrentMetricDataCollections', ], ], ], 'CurrentMetricResults' => [ 'type' => 'list', 'member' => [ 'shape' => 'CurrentMetricResult', ], ], 'CurrentMetricSortCriteria' => [ 'type' => 'structure', 'members' => [ 'SortByMetric' => [ 'shape' => 'CurrentMetricName', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'CurrentMetricSortCriteriaMaxOne' => [ 'type' => 'list', 'member' => [ 'shape' => 'CurrentMetricSortCriteria', ], 'max' => 1, 'min' => 0, ], 'CurrentMetrics' => [ 'type' => 'list', 'member' => [ 'shape' => 'CurrentMetric', ], ], 'Customer' => [ 'type' => 'structure', 'members' => [ 'DeviceInfo' => [ 'shape' => 'DeviceInfo', ], 'Capabilities' => [ 'shape' => 'ParticipantCapabilities', ], ], ], 'CustomerId' => [ 'type' => 'string', 'max' => 128, 'min' => 0, ], 'CustomerIdNonEmpty' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'sensitive' => true, ], 'CustomerProfileAttributesSerialized' => [ 'type' => 'string', ], 'CustomerQualityMetrics' => [ 'type' => 'structure', 'members' => [ 'Audio' => [ 'shape' => 'AudioQualityMetricsInfo', ], ], ], 'CustomerVoiceActivity' => [ 'type' => 'structure', 'members' => [ 'GreetingStartTimestamp' => [ 'shape' => 'timestamp', ], 'GreetingEndTimestamp' => [ 'shape' => 'timestamp', ], ], ], 'DataSetId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'DataSetIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataSetId', ], ], 'DateComparisonType' => [ 'type' => 'string', 'enum' => [ 'GREATER_THAN', 'LESS_THAN', 'GREATER_THAN_OR_EQUAL_TO', 'LESS_THAN_OR_EQUAL_TO', 'EQUAL_TO', ], ], 'DateCondition' => [ 'type' => 'structure', 'members' => [ 'FieldName' => [ 'shape' => 'String', ], 'Value' => [ 'shape' => 'DateYearMonthDayFormat', ], 'ComparisonType' => [ 'shape' => 'DateComparisonType', ], ], ], 'DateReference' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'ReferenceKey', ], 'Value' => [ 'shape' => 'ReferenceValue', ], ], ], 'DateYearMonthDayFormat' => [ 'type' => 'string', 'pattern' => '^\\d{4}-\\d{2}-\\d{2}$', ], 'DeactivateEvaluationFormRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'EvaluationFormId', 'EvaluationFormVersion', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'EvaluationFormId' => [ 'shape' => 'ResourceId', 'location' => 'uri', 'locationName' => 'EvaluationFormId', ], 'EvaluationFormVersion' => [ 'shape' => 'VersionNumber', ], ], ], 'DeactivateEvaluationFormResponse' => [ 'type' => 'structure', 'required' => [ 'EvaluationFormId', 'EvaluationFormArn', 'EvaluationFormVersion', ], 'members' => [ 'EvaluationFormId' => [ 'shape' => 'ResourceId', ], 'EvaluationFormArn' => [ 'shape' => 'ARN', ], 'EvaluationFormVersion' => [ 'shape' => 'VersionNumber', ], ], ], 'DefaultVocabulary' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'LanguageCode', 'VocabularyId', 'VocabularyName', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'LanguageCode' => [ 'shape' => 'VocabularyLanguageCode', ], 'VocabularyId' => [ 'shape' => 'VocabularyId', ], 'VocabularyName' => [ 'shape' => 'VocabularyName', ], ], ], 'DefaultVocabularyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DefaultVocabulary', ], ], 'Delay' => [ 'type' => 'integer', 'max' => 9999, 'min' => 0, ], 'DeleteAttachedFileRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'FileId', 'AssociatedResourceArn', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'FileId' => [ 'shape' => 'FileId', 'location' => 'uri', 'locationName' => 'FileId', ], 'AssociatedResourceArn' => [ 'shape' => 'ARN', 'location' => 'querystring', 'locationName' => 'associatedResourceArn', ], ], ], 'DeleteAttachedFileResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteContactEvaluationRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'EvaluationId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'EvaluationId' => [ 'shape' => 'ResourceId', 'location' => 'uri', 'locationName' => 'EvaluationId', ], ], ], 'DeleteContactFlowModuleRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactFlowModuleId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ContactFlowModuleId' => [ 'shape' => 'ContactFlowModuleId', 'location' => 'uri', 'locationName' => 'ContactFlowModuleId', ], ], ], 'DeleteContactFlowModuleResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteContactFlowRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactFlowId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ContactFlowId' => [ 'shape' => 'ContactFlowId', 'location' => 'uri', 'locationName' => 'ContactFlowId', ], ], ], 'DeleteContactFlowResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteContactFlowVersionRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactFlowId', 'ContactFlowVersion', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ContactFlowId' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'ContactFlowId', ], 'ContactFlowVersion' => [ 'shape' => 'ResourceVersion', 'location' => 'uri', 'locationName' => 'ContactFlowVersion', ], ], ], 'DeleteContactFlowVersionResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteEmailAddressRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'EmailAddressId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'EmailAddressId' => [ 'shape' => 'EmailAddressId', 'location' => 'uri', 'locationName' => 'EmailAddressId', ], ], ], 'DeleteEmailAddressResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteEvaluationFormRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'EvaluationFormId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'EvaluationFormId' => [ 'shape' => 'ResourceId', 'location' => 'uri', 'locationName' => 'EvaluationFormId', ], 'EvaluationFormVersion' => [ 'shape' => 'VersionNumber', 'box' => true, 'location' => 'querystring', 'locationName' => 'version', ], ], ], 'DeleteHoursOfOperationOverrideRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'HoursOfOperationId', 'HoursOfOperationOverrideId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'HoursOfOperationId' => [ 'shape' => 'HoursOfOperationId', 'location' => 'uri', 'locationName' => 'HoursOfOperationId', ], 'HoursOfOperationOverrideId' => [ 'shape' => 'HoursOfOperationOverrideId', 'location' => 'uri', 'locationName' => 'HoursOfOperationOverrideId', ], ], ], 'DeleteHoursOfOperationRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'HoursOfOperationId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'HoursOfOperationId' => [ 'shape' => 'HoursOfOperationId', 'location' => 'uri', 'locationName' => 'HoursOfOperationId', ], ], ], 'DeleteInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], ], ], 'DeleteIntegrationAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'IntegrationAssociationId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'IntegrationAssociationId' => [ 'shape' => 'IntegrationAssociationId', 'location' => 'uri', 'locationName' => 'IntegrationAssociationId', ], ], ], 'DeletePredefinedAttributeRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'Name', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'Name' => [ 'shape' => 'PredefinedAttributeName', 'location' => 'uri', 'locationName' => 'Name', ], ], ], 'DeletePromptRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'PromptId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'PromptId' => [ 'shape' => 'PromptId', 'location' => 'uri', 'locationName' => 'PromptId', ], ], ], 'DeletePushNotificationRegistrationRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'RegistrationId', 'ContactId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'RegistrationId' => [ 'shape' => 'RegistrationId', 'location' => 'uri', 'locationName' => 'RegistrationId', ], 'ContactId' => [ 'shape' => 'ContactId', 'location' => 'querystring', 'locationName' => 'contactId', ], ], ], 'DeletePushNotificationRegistrationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteQueueRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'QueueId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'QueueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'QueueId', ], ], ], 'DeleteQuickConnectRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'QuickConnectId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'QuickConnectId' => [ 'shape' => 'QuickConnectId', 'location' => 'uri', 'locationName' => 'QuickConnectId', ], ], ], 'DeleteRoutingProfileRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'RoutingProfileId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'RoutingProfileId' => [ 'shape' => 'RoutingProfileId', 'location' => 'uri', 'locationName' => 'RoutingProfileId', ], ], ], 'DeleteRuleRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'RuleId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'RuleId' => [ 'shape' => 'RuleId', 'location' => 'uri', 'locationName' => 'RuleId', ], ], ], 'DeleteSecurityProfileRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'SecurityProfileId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'SecurityProfileId' => [ 'shape' => 'SecurityProfileId', 'location' => 'uri', 'locationName' => 'SecurityProfileId', ], ], ], 'DeleteTaskTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'TaskTemplateId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'TaskTemplateId' => [ 'shape' => 'TaskTemplateId', 'location' => 'uri', 'locationName' => 'TaskTemplateId', ], ], ], 'DeleteTaskTemplateResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteTrafficDistributionGroupRequest' => [ 'type' => 'structure', 'required' => [ 'TrafficDistributionGroupId', ], 'members' => [ 'TrafficDistributionGroupId' => [ 'shape' => 'TrafficDistributionGroupIdOrArn', 'location' => 'uri', 'locationName' => 'TrafficDistributionGroupId', ], ], ], 'DeleteTrafficDistributionGroupResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteUseCaseRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'IntegrationAssociationId', 'UseCaseId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'IntegrationAssociationId' => [ 'shape' => 'IntegrationAssociationId', 'location' => 'uri', 'locationName' => 'IntegrationAssociationId', ], 'UseCaseId' => [ 'shape' => 'UseCaseId', 'location' => 'uri', 'locationName' => 'UseCaseId', ], ], ], 'DeleteUserHierarchyGroupRequest' => [ 'type' => 'structure', 'required' => [ 'HierarchyGroupId', 'InstanceId', ], 'members' => [ 'HierarchyGroupId' => [ 'shape' => 'HierarchyGroupId', 'location' => 'uri', 'locationName' => 'HierarchyGroupId', ], 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], ], ], 'DeleteUserRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'UserId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'UserId' => [ 'shape' => 'UserId', 'location' => 'uri', 'locationName' => 'UserId', ], ], ], 'DeleteViewRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ViewId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'ViewsInstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ViewId' => [ 'shape' => 'ViewId', 'location' => 'uri', 'locationName' => 'ViewId', ], ], ], 'DeleteViewResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteViewVersionRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ViewId', 'ViewVersion', ], 'members' => [ 'InstanceId' => [ 'shape' => 'ViewsInstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ViewId' => [ 'shape' => 'ViewId', 'location' => 'uri', 'locationName' => 'ViewId', ], 'ViewVersion' => [ 'shape' => 'ViewVersion', 'location' => 'uri', 'locationName' => 'ViewVersion', ], ], ], 'DeleteViewVersionResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteVocabularyRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'VocabularyId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'VocabularyId' => [ 'shape' => 'VocabularyId', 'location' => 'uri', 'locationName' => 'VocabularyId', ], ], ], 'DeleteVocabularyResponse' => [ 'type' => 'structure', 'required' => [ 'VocabularyArn', 'VocabularyId', 'State', ], 'members' => [ 'VocabularyArn' => [ 'shape' => 'ARN', ], 'VocabularyId' => [ 'shape' => 'VocabularyId', ], 'State' => [ 'shape' => 'VocabularyState', ], ], ], 'DescribeAgentStatusRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'AgentStatusId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'AgentStatusId' => [ 'shape' => 'AgentStatusId', 'location' => 'uri', 'locationName' => 'AgentStatusId', ], ], ], 'DescribeAgentStatusResponse' => [ 'type' => 'structure', 'members' => [ 'AgentStatus' => [ 'shape' => 'AgentStatus', ], ], ], 'DescribeAuthenticationProfileRequest' => [ 'type' => 'structure', 'required' => [ 'AuthenticationProfileId', 'InstanceId', ], 'members' => [ 'AuthenticationProfileId' => [ 'shape' => 'AuthenticationProfileId', 'location' => 'uri', 'locationName' => 'AuthenticationProfileId', ], 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], ], ], 'DescribeAuthenticationProfileResponse' => [ 'type' => 'structure', 'members' => [ 'AuthenticationProfile' => [ 'shape' => 'AuthenticationProfile', ], ], ], 'DescribeContactEvaluationRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'EvaluationId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'EvaluationId' => [ 'shape' => 'ResourceId', 'location' => 'uri', 'locationName' => 'EvaluationId', ], ], ], 'DescribeContactEvaluationResponse' => [ 'type' => 'structure', 'required' => [ 'Evaluation', 'EvaluationForm', ], 'members' => [ 'Evaluation' => [ 'shape' => 'Evaluation', ], 'EvaluationForm' => [ 'shape' => 'EvaluationFormContent', ], ], ], 'DescribeContactFlowModuleRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactFlowModuleId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ContactFlowModuleId' => [ 'shape' => 'ContactFlowModuleId', 'location' => 'uri', 'locationName' => 'ContactFlowModuleId', ], ], ], 'DescribeContactFlowModuleResponse' => [ 'type' => 'structure', 'members' => [ 'ContactFlowModule' => [ 'shape' => 'ContactFlowModule', ], ], ], 'DescribeContactFlowRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactFlowId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ContactFlowId' => [ 'shape' => 'ContactFlowId', 'location' => 'uri', 'locationName' => 'ContactFlowId', ], ], ], 'DescribeContactFlowResponse' => [ 'type' => 'structure', 'members' => [ 'ContactFlow' => [ 'shape' => 'ContactFlow', ], ], ], 'DescribeContactRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ContactId' => [ 'shape' => 'ContactId', 'location' => 'uri', 'locationName' => 'ContactId', ], ], ], 'DescribeContactResponse' => [ 'type' => 'structure', 'members' => [ 'Contact' => [ 'shape' => 'Contact', ], ], ], 'DescribeEmailAddressRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'EmailAddressId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'EmailAddressId' => [ 'shape' => 'EmailAddressId', 'location' => 'uri', 'locationName' => 'EmailAddressId', ], ], ], 'DescribeEmailAddressResponse' => [ 'type' => 'structure', 'members' => [ 'EmailAddressId' => [ 'shape' => 'EmailAddressId', ], 'EmailAddressArn' => [ 'shape' => 'EmailAddressArn', ], 'EmailAddress' => [ 'shape' => 'EmailAddress', ], 'DisplayName' => [ 'shape' => 'EmailAddressDisplayName', ], 'Description' => [ 'shape' => 'Description', ], 'CreateTimestamp' => [ 'shape' => 'ISO8601Datetime', ], 'ModifiedTimestamp' => [ 'shape' => 'ISO8601Datetime', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'DescribeEvaluationFormRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'EvaluationFormId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'EvaluationFormId' => [ 'shape' => 'ResourceId', 'location' => 'uri', 'locationName' => 'EvaluationFormId', ], 'EvaluationFormVersion' => [ 'shape' => 'VersionNumber', 'box' => true, 'location' => 'querystring', 'locationName' => 'version', ], ], ], 'DescribeEvaluationFormResponse' => [ 'type' => 'structure', 'required' => [ 'EvaluationForm', ], 'members' => [ 'EvaluationForm' => [ 'shape' => 'EvaluationForm', ], ], ], 'DescribeHoursOfOperationOverrideRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'HoursOfOperationId', 'HoursOfOperationOverrideId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'HoursOfOperationId' => [ 'shape' => 'HoursOfOperationId', 'location' => 'uri', 'locationName' => 'HoursOfOperationId', ], 'HoursOfOperationOverrideId' => [ 'shape' => 'HoursOfOperationOverrideId', 'location' => 'uri', 'locationName' => 'HoursOfOperationOverrideId', ], ], ], 'DescribeHoursOfOperationOverrideResponse' => [ 'type' => 'structure', 'members' => [ 'HoursOfOperationOverride' => [ 'shape' => 'HoursOfOperationOverride', ], ], ], 'DescribeHoursOfOperationRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'HoursOfOperationId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'HoursOfOperationId' => [ 'shape' => 'HoursOfOperationId', 'location' => 'uri', 'locationName' => 'HoursOfOperationId', ], ], ], 'DescribeHoursOfOperationResponse' => [ 'type' => 'structure', 'members' => [ 'HoursOfOperation' => [ 'shape' => 'HoursOfOperation', ], ], ], 'DescribeInstanceAttributeRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'AttributeType', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'AttributeType' => [ 'shape' => 'InstanceAttributeType', 'location' => 'uri', 'locationName' => 'AttributeType', ], ], ], 'DescribeInstanceAttributeResponse' => [ 'type' => 'structure', 'members' => [ 'Attribute' => [ 'shape' => 'Attribute', ], ], ], 'DescribeInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], ], ], 'DescribeInstanceResponse' => [ 'type' => 'structure', 'members' => [ 'Instance' => [ 'shape' => 'Instance', ], 'ReplicationConfiguration' => [ 'shape' => 'ReplicationConfiguration', ], ], ], 'DescribeInstanceStorageConfigRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'AssociationId', 'ResourceType', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'AssociationId' => [ 'shape' => 'AssociationId', 'location' => 'uri', 'locationName' => 'AssociationId', ], 'ResourceType' => [ 'shape' => 'InstanceStorageResourceType', 'location' => 'querystring', 'locationName' => 'resourceType', ], ], ], 'DescribeInstanceStorageConfigResponse' => [ 'type' => 'structure', 'members' => [ 'StorageConfig' => [ 'shape' => 'InstanceStorageConfig', ], ], ], 'DescribePhoneNumberRequest' => [ 'type' => 'structure', 'required' => [ 'PhoneNumberId', ], 'members' => [ 'PhoneNumberId' => [ 'shape' => 'PhoneNumberId', 'location' => 'uri', 'locationName' => 'PhoneNumberId', ], ], ], 'DescribePhoneNumberResponse' => [ 'type' => 'structure', 'members' => [ 'ClaimedPhoneNumberSummary' => [ 'shape' => 'ClaimedPhoneNumberSummary', ], ], ], 'DescribePredefinedAttributeRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'Name', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'Name' => [ 'shape' => 'PredefinedAttributeName', 'location' => 'uri', 'locationName' => 'Name', ], ], ], 'DescribePredefinedAttributeResponse' => [ 'type' => 'structure', 'members' => [ 'PredefinedAttribute' => [ 'shape' => 'PredefinedAttribute', ], ], ], 'DescribePromptRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'PromptId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'PromptId' => [ 'shape' => 'PromptId', 'location' => 'uri', 'locationName' => 'PromptId', ], ], ], 'DescribePromptResponse' => [ 'type' => 'structure', 'members' => [ 'Prompt' => [ 'shape' => 'Prompt', ], ], ], 'DescribeQueueRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'QueueId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'QueueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'QueueId', ], ], ], 'DescribeQueueResponse' => [ 'type' => 'structure', 'members' => [ 'Queue' => [ 'shape' => 'Queue', ], ], ], 'DescribeQuickConnectRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'QuickConnectId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'QuickConnectId' => [ 'shape' => 'QuickConnectId', 'location' => 'uri', 'locationName' => 'QuickConnectId', ], ], ], 'DescribeQuickConnectResponse' => [ 'type' => 'structure', 'members' => [ 'QuickConnect' => [ 'shape' => 'QuickConnect', ], ], ], 'DescribeRoutingProfileRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'RoutingProfileId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'RoutingProfileId' => [ 'shape' => 'RoutingProfileId', 'location' => 'uri', 'locationName' => 'RoutingProfileId', ], ], ], 'DescribeRoutingProfileResponse' => [ 'type' => 'structure', 'members' => [ 'RoutingProfile' => [ 'shape' => 'RoutingProfile', ], ], ], 'DescribeRuleRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'RuleId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'RuleId' => [ 'shape' => 'RuleId', 'location' => 'uri', 'locationName' => 'RuleId', ], ], ], 'DescribeRuleResponse' => [ 'type' => 'structure', 'required' => [ 'Rule', ], 'members' => [ 'Rule' => [ 'shape' => 'Rule', ], ], ], 'DescribeSecurityProfileRequest' => [ 'type' => 'structure', 'required' => [ 'SecurityProfileId', 'InstanceId', ], 'members' => [ 'SecurityProfileId' => [ 'shape' => 'SecurityProfileId', 'location' => 'uri', 'locationName' => 'SecurityProfileId', ], 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], ], ], 'DescribeSecurityProfileResponse' => [ 'type' => 'structure', 'members' => [ 'SecurityProfile' => [ 'shape' => 'SecurityProfile', ], ], ], 'DescribeTrafficDistributionGroupRequest' => [ 'type' => 'structure', 'required' => [ 'TrafficDistributionGroupId', ], 'members' => [ 'TrafficDistributionGroupId' => [ 'shape' => 'TrafficDistributionGroupIdOrArn', 'location' => 'uri', 'locationName' => 'TrafficDistributionGroupId', ], ], ], 'DescribeTrafficDistributionGroupResponse' => [ 'type' => 'structure', 'members' => [ 'TrafficDistributionGroup' => [ 'shape' => 'TrafficDistributionGroup', ], ], ], 'DescribeUserHierarchyGroupRequest' => [ 'type' => 'structure', 'required' => [ 'HierarchyGroupId', 'InstanceId', ], 'members' => [ 'HierarchyGroupId' => [ 'shape' => 'HierarchyGroupId', 'location' => 'uri', 'locationName' => 'HierarchyGroupId', ], 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], ], ], 'DescribeUserHierarchyGroupResponse' => [ 'type' => 'structure', 'members' => [ 'HierarchyGroup' => [ 'shape' => 'HierarchyGroup', ], ], ], 'DescribeUserHierarchyStructureRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], ], ], 'DescribeUserHierarchyStructureResponse' => [ 'type' => 'structure', 'members' => [ 'HierarchyStructure' => [ 'shape' => 'HierarchyStructure', ], ], ], 'DescribeUserRequest' => [ 'type' => 'structure', 'required' => [ 'UserId', 'InstanceId', ], 'members' => [ 'UserId' => [ 'shape' => 'UserId', 'location' => 'uri', 'locationName' => 'UserId', ], 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], ], ], 'DescribeUserResponse' => [ 'type' => 'structure', 'members' => [ 'User' => [ 'shape' => 'User', ], ], ], 'DescribeViewRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ViewId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'ViewsInstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ViewId' => [ 'shape' => 'ViewId', 'location' => 'uri', 'locationName' => 'ViewId', ], ], ], 'DescribeViewResponse' => [ 'type' => 'structure', 'members' => [ 'View' => [ 'shape' => 'View', ], ], ], 'DescribeVocabularyRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'VocabularyId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'VocabularyId' => [ 'shape' => 'VocabularyId', 'location' => 'uri', 'locationName' => 'VocabularyId', ], ], ], 'DescribeVocabularyResponse' => [ 'type' => 'structure', 'required' => [ 'Vocabulary', ], 'members' => [ 'Vocabulary' => [ 'shape' => 'Vocabulary', ], ], ], 'Description' => [ 'type' => 'string', 'max' => 4096, 'min' => 0, 'sensitive' => true, ], 'Description250' => [ 'type' => 'string', 'max' => 250, 'min' => 1, 'pattern' => '(^[\\S].*[\\S]$)|(^[\\S]$)', ], 'DestinationId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'DestinationNotAllowedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'DeviceInfo' => [ 'type' => 'structure', 'members' => [ 'PlatformName' => [ 'shape' => 'PlatformName', ], 'PlatformVersion' => [ 'shape' => 'PlatformVersion', ], 'OperatingSystem' => [ 'shape' => 'OperatingSystem', ], ], ], 'DeviceToken' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'DeviceType' => [ 'type' => 'string', 'enum' => [ 'GCM', 'APNS', 'APNS_SANDBOX', ], ], 'Dimensions' => [ 'type' => 'structure', 'members' => [ 'Queue' => [ 'shape' => 'QueueReference', ], 'Channel' => [ 'shape' => 'Channel', ], 'RoutingProfile' => [ 'shape' => 'RoutingProfileReference', ], 'RoutingStepExpression' => [ 'shape' => 'RoutingExpression', ], ], ], 'DimensionsV2Key' => [ 'type' => 'string', ], 'DimensionsV2Map' => [ 'type' => 'map', 'key' => [ 'shape' => 'DimensionsV2Key', ], 'value' => [ 'shape' => 'DimensionsV2Value', ], ], 'DimensionsV2Value' => [ 'type' => 'string', ], 'DirectoryAlias' => [ 'type' => 'string', 'max' => 45, 'min' => 1, 'pattern' => '^(?!d-)([\\da-zA-Z]+)([-]*[\\da-zA-Z])*$', 'sensitive' => true, ], 'DirectoryId' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => '^d-[0-9a-f]{10}$', ], 'DirectoryType' => [ 'type' => 'string', 'enum' => [ 'SAML', 'CONNECT_MANAGED', 'EXISTING_DIRECTORY', ], ], 'DirectoryUserId' => [ 'type' => 'string', ], 'DisassociateAnalyticsDataSetRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'DataSetId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'DataSetId' => [ 'shape' => 'DataSetId', ], 'TargetAccountId' => [ 'shape' => 'AWSAccountId', ], ], ], 'DisassociateApprovedOriginRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'Origin', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'Origin' => [ 'shape' => 'Origin', 'location' => 'querystring', 'locationName' => 'origin', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], ], ], 'DisassociateBotRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'LexBot' => [ 'shape' => 'LexBot', ], 'LexV2Bot' => [ 'shape' => 'LexV2Bot', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'DisassociateFlowRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ResourceId', 'ResourceType', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ResourceId' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'ResourceId', ], 'ResourceType' => [ 'shape' => 'FlowAssociationResourceType', 'location' => 'uri', 'locationName' => 'ResourceType', ], ], ], 'DisassociateFlowResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateInstanceStorageConfigRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'AssociationId', 'ResourceType', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'AssociationId' => [ 'shape' => 'AssociationId', 'location' => 'uri', 'locationName' => 'AssociationId', ], 'ResourceType' => [ 'shape' => 'InstanceStorageResourceType', 'location' => 'querystring', 'locationName' => 'resourceType', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], ], ], 'DisassociateLambdaFunctionRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'FunctionArn', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'FunctionArn' => [ 'shape' => 'FunctionArn', 'location' => 'querystring', 'locationName' => 'functionArn', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], ], ], 'DisassociateLexBotRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'BotName', 'LexRegion', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'BotName' => [ 'shape' => 'BotName', 'location' => 'querystring', 'locationName' => 'botName', ], 'LexRegion' => [ 'shape' => 'LexRegion', 'location' => 'querystring', 'locationName' => 'lexRegion', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], ], ], 'DisassociatePhoneNumberContactFlowRequest' => [ 'type' => 'structure', 'required' => [ 'PhoneNumberId', 'InstanceId', ], 'members' => [ 'PhoneNumberId' => [ 'shape' => 'PhoneNumberId', 'location' => 'uri', 'locationName' => 'PhoneNumberId', ], 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'querystring', 'locationName' => 'instanceId', ], ], ], 'DisassociateQueueQuickConnectsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'QueueId', 'QuickConnectIds', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'QueueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'QueueId', ], 'QuickConnectIds' => [ 'shape' => 'QuickConnectsList', ], ], ], 'DisassociateRoutingProfileQueuesRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'RoutingProfileId', 'QueueReferences', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'RoutingProfileId' => [ 'shape' => 'RoutingProfileId', 'location' => 'uri', 'locationName' => 'RoutingProfileId', ], 'QueueReferences' => [ 'shape' => 'RoutingProfileQueueReferenceList', ], ], ], 'DisassociateSecurityKeyRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'AssociationId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'AssociationId' => [ 'shape' => 'AssociationId', 'location' => 'uri', 'locationName' => 'AssociationId', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], ], ], 'DisassociateTrafficDistributionGroupUserRequest' => [ 'type' => 'structure', 'required' => [ 'TrafficDistributionGroupId', 'UserId', 'InstanceId', ], 'members' => [ 'TrafficDistributionGroupId' => [ 'shape' => 'TrafficDistributionGroupIdOrArn', 'location' => 'uri', 'locationName' => 'TrafficDistributionGroupId', ], 'UserId' => [ 'shape' => 'UserId', 'location' => 'querystring', 'locationName' => 'UserId', ], 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'querystring', 'locationName' => 'InstanceId', ], ], ], 'DisassociateTrafficDistributionGroupUserResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateUserProficienciesRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'UserId', 'UserProficiencies', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'UserId' => [ 'shape' => 'UserId', 'location' => 'uri', 'locationName' => 'UserId', ], 'UserProficiencies' => [ 'shape' => 'UserProficiencyDisassociateList', ], ], ], 'DisconnectDetails' => [ 'type' => 'structure', 'members' => [ 'PotentialDisconnectIssue' => [ 'shape' => 'PotentialDisconnectIssue', ], ], ], 'DisconnectReason' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'DisconnectReasonCode', ], ], ], 'DisconnectReasonCode' => [ 'type' => 'string', ], 'DismissUserContactRequest' => [ 'type' => 'structure', 'required' => [ 'UserId', 'InstanceId', 'ContactId', ], 'members' => [ 'UserId' => [ 'shape' => 'UserId', 'location' => 'uri', 'locationName' => 'UserId', ], 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ContactId' => [ 'shape' => 'ContactId', ], ], ], 'DismissUserContactResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisplayName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'Distribution' => [ 'type' => 'structure', 'required' => [ 'Region', 'Percentage', ], 'members' => [ 'Region' => [ 'shape' => 'AwsRegion', ], 'Percentage' => [ 'shape' => 'Percentage', ], ], ], 'DistributionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Distribution', ], ], 'Double' => [ 'type' => 'double', ], 'DownloadUrlMetadata' => [ 'type' => 'structure', 'members' => [ 'Url' => [ 'shape' => 'MetadataUrl', ], 'UrlExpiry' => [ 'shape' => 'ISO8601Datetime', ], ], ], 'DuplicateResourceException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'Duration' => [ 'type' => 'integer', 'min' => 0, ], 'DurationInSeconds' => [ 'type' => 'integer', ], 'EffectiveHoursOfOperationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EffectiveHoursOfOperations', ], ], 'EffectiveHoursOfOperations' => [ 'type' => 'structure', 'members' => [ 'Date' => [ 'shape' => 'HoursOfOperationOverrideYearMonthDayDateFormat', ], 'OperationalHours' => [ 'shape' => 'OperationalHours', ], ], ], 'Email' => [ 'type' => 'string', 'sensitive' => true, ], 'EmailAddress' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[^\\s@]+@[^\\s@]+\\.[^\\s@]+', 'sensitive' => true, ], 'EmailAddressArn' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'EmailAddressDisplayName' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'sensitive' => true, ], 'EmailAddressId' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'EmailAddressInfo' => [ 'type' => 'structure', 'required' => [ 'EmailAddress', ], 'members' => [ 'EmailAddress' => [ 'shape' => 'EmailAddress', ], 'DisplayName' => [ 'shape' => 'EmailAddressDisplayName', ], ], ], 'EmailAddressList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EmailAddressMetadata', ], ], 'EmailAddressMetadata' => [ 'type' => 'structure', 'members' => [ 'EmailAddressId' => [ 'shape' => 'EmailAddressId', ], 'EmailAddressArn' => [ 'shape' => 'EmailAddressArn', ], 'EmailAddress' => [ 'shape' => 'EmailAddress', ], 'Description' => [ 'shape' => 'Description', ], 'DisplayName' => [ 'shape' => 'EmailAddressDisplayName', ], ], ], 'EmailAddressRecipientList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EmailAddressInfo', ], 'max' => 50, 'min' => 1, ], 'EmailAddressSearchConditionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EmailAddressSearchCriteria', ], ], 'EmailAddressSearchCriteria' => [ 'type' => 'structure', 'members' => [ 'OrConditions' => [ 'shape' => 'EmailAddressSearchConditionList', ], 'AndConditions' => [ 'shape' => 'EmailAddressSearchConditionList', ], 'StringCondition' => [ 'shape' => 'StringCondition', ], ], ], 'EmailAddressSearchFilter' => [ 'type' => 'structure', 'members' => [ 'TagFilter' => [ 'shape' => 'ControlPlaneTagFilter', ], ], ], 'EmailAttachment' => [ 'type' => 'structure', 'required' => [ 'FileName', 'S3Url', ], 'members' => [ 'FileName' => [ 'shape' => 'FileName', ], 'S3Url' => [ 'shape' => 'PreSignedAttachmentUrl', ], ], ], 'EmailAttachments' => [ 'type' => 'list', 'member' => [ 'shape' => 'EmailAttachment', ], 'max' => 10, 'min' => 1, 'sensitive' => true, ], 'EmailHeaderType' => [ 'type' => 'string', 'enum' => [ 'REFERENCES', 'MESSAGE_ID', 'IN_REPLY_TO', 'X_SES_SPAM_VERDICT', 'X_SES_VIRUS_VERDICT', ], ], 'EmailHeaderValue' => [ 'type' => 'string', 'max' => 20000, 'min' => 1, ], 'EmailHeaders' => [ 'type' => 'map', 'key' => [ 'shape' => 'EmailHeaderType', ], 'value' => [ 'shape' => 'EmailHeaderValue', ], ], 'EmailMessageContentType' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'EmailMessageReference' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'ReferenceKey', ], 'Arn' => [ 'shape' => 'ReferenceArn', ], ], ], 'EmailRecipient' => [ 'type' => 'structure', 'members' => [ 'Address' => [ 'shape' => 'EndpointAddress', ], 'DisplayName' => [ 'shape' => 'EndpointDisplayName', ], ], ], 'EmailRecipientsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EmailRecipient', ], ], 'EmailReference' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'ReferenceKey', ], 'Value' => [ 'shape' => 'ReferenceValue', ], ], ], 'EmptyFieldValue' => [ 'type' => 'structure', 'members' => [], ], 'EncryptionConfig' => [ 'type' => 'structure', 'required' => [ 'EncryptionType', 'KeyId', ], 'members' => [ 'EncryptionType' => [ 'shape' => 'EncryptionType', ], 'KeyId' => [ 'shape' => 'KeyId', ], ], ], 'EncryptionType' => [ 'type' => 'string', 'enum' => [ 'KMS', ], ], 'EndAssociatedTasksActionDefinition' => [ 'type' => 'structure', 'members' => [], ], 'Endpoint' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'EndpointType', ], 'Address' => [ 'shape' => 'EndpointAddress', ], ], ], 'EndpointAddress' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'EndpointDisplayName' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'EndpointInfo' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'EndpointType', ], 'Address' => [ 'shape' => 'EndpointAddress', ], 'DisplayName' => [ 'shape' => 'EndpointDisplayName', ], ], ], 'EndpointType' => [ 'type' => 'string', 'enum' => [ 'TELEPHONE_NUMBER', 'VOIP', 'CONTACT_FLOW', 'CONNECT_PHONENUMBER_ARN', 'EMAIL_ADDRESS', ], ], 'ErrorCode' => [ 'type' => 'string', ], 'ErrorMessage' => [ 'type' => 'string', ], 'ErrorResult' => [ 'type' => 'structure', 'members' => [ 'ErrorCode' => [ 'shape' => 'String', ], 'ErrorMessage' => [ 'shape' => 'String', ], ], ], 'ErrorResults' => [ 'type' => 'list', 'member' => [ 'shape' => 'ErrorResult', ], ], 'Evaluation' => [ 'type' => 'structure', 'required' => [ 'EvaluationId', 'EvaluationArn', 'Metadata', 'Answers', 'Notes', 'Status', 'CreatedTime', 'LastModifiedTime', ], 'members' => [ 'EvaluationId' => [ 'shape' => 'ResourceId', ], 'EvaluationArn' => [ 'shape' => 'ARN', ], 'Metadata' => [ 'shape' => 'EvaluationMetadata', ], 'Answers' => [ 'shape' => 'EvaluationAnswersOutputMap', ], 'Notes' => [ 'shape' => 'EvaluationNotesMap', ], 'Status' => [ 'shape' => 'EvaluationStatus', ], 'Scores' => [ 'shape' => 'EvaluationScoresMap', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'EvaluationAnswerData' => [ 'type' => 'structure', 'members' => [ 'StringValue' => [ 'shape' => 'EvaluationAnswerDataStringValue', ], 'NumericValue' => [ 'shape' => 'EvaluationAnswerDataNumericValue', ], 'NotApplicable' => [ 'shape' => 'Boolean', ], ], 'union' => true, ], 'EvaluationAnswerDataNumericValue' => [ 'type' => 'double', ], 'EvaluationAnswerDataStringValue' => [ 'type' => 'string', 'max' => 128, 'min' => 0, ], 'EvaluationAnswerInput' => [ 'type' => 'structure', 'members' => [ 'Value' => [ 'shape' => 'EvaluationAnswerData', ], ], ], 'EvaluationAnswerOutput' => [ 'type' => 'structure', 'members' => [ 'Value' => [ 'shape' => 'EvaluationAnswerData', ], 'SystemSuggestedValue' => [ 'shape' => 'EvaluationAnswerData', ], ], ], 'EvaluationAnswersInputMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ResourceId', ], 'value' => [ 'shape' => 'EvaluationAnswerInput', ], 'max' => 100, ], 'EvaluationAnswersOutputMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ResourceId', ], 'value' => [ 'shape' => 'EvaluationAnswerOutput', ], 'max' => 100, ], 'EvaluationArn' => [ 'type' => 'string', ], 'EvaluationForm' => [ 'type' => 'structure', 'required' => [ 'EvaluationFormId', 'EvaluationFormVersion', 'Locked', 'EvaluationFormArn', 'Title', 'Status', 'Items', 'CreatedTime', 'CreatedBy', 'LastModifiedTime', 'LastModifiedBy', ], 'members' => [ 'EvaluationFormId' => [ 'shape' => 'ResourceId', ], 'EvaluationFormVersion' => [ 'shape' => 'VersionNumber', ], 'Locked' => [ 'shape' => 'EvaluationFormVersionIsLocked', ], 'EvaluationFormArn' => [ 'shape' => 'ARN', ], 'Title' => [ 'shape' => 'EvaluationFormTitle', ], 'Description' => [ 'shape' => 'EvaluationFormDescription', ], 'Status' => [ 'shape' => 'EvaluationFormVersionStatus', ], 'Items' => [ 'shape' => 'EvaluationFormItemsList', ], 'ScoringStrategy' => [ 'shape' => 'EvaluationFormScoringStrategy', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'CreatedBy' => [ 'shape' => 'ARN', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedBy' => [ 'shape' => 'ARN', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'EvaluationFormContent' => [ 'type' => 'structure', 'required' => [ 'EvaluationFormVersion', 'EvaluationFormId', 'EvaluationFormArn', 'Title', 'Items', ], 'members' => [ 'EvaluationFormVersion' => [ 'shape' => 'VersionNumber', ], 'EvaluationFormId' => [ 'shape' => 'ResourceId', ], 'EvaluationFormArn' => [ 'shape' => 'ARN', ], 'Title' => [ 'shape' => 'EvaluationFormTitle', ], 'Description' => [ 'shape' => 'EvaluationFormDescription', ], 'Items' => [ 'shape' => 'EvaluationFormItemsList', ], 'ScoringStrategy' => [ 'shape' => 'EvaluationFormScoringStrategy', ], ], ], 'EvaluationFormDescription' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'EvaluationFormId' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'EvaluationFormItem' => [ 'type' => 'structure', 'members' => [ 'Section' => [ 'shape' => 'EvaluationFormSection', ], 'Question' => [ 'shape' => 'EvaluationFormQuestion', ], ], 'union' => true, ], 'EvaluationFormItemWeight' => [ 'type' => 'double', 'max' => 100, 'min' => 0, ], 'EvaluationFormItemsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EvaluationFormItem', ], 'max' => 100, 'min' => 1, ], 'EvaluationFormNumericQuestionAutomation' => [ 'type' => 'structure', 'members' => [ 'PropertyValue' => [ 'shape' => 'NumericQuestionPropertyValueAutomation', ], ], 'union' => true, ], 'EvaluationFormNumericQuestionOption' => [ 'type' => 'structure', 'required' => [ 'MinValue', 'MaxValue', ], 'members' => [ 'MinValue' => [ 'shape' => 'Integer', ], 'MaxValue' => [ 'shape' => 'Integer', ], 'Score' => [ 'shape' => 'EvaluationFormQuestionAnswerScore', ], 'AutomaticFail' => [ 'shape' => 'Boolean', ], ], ], 'EvaluationFormNumericQuestionOptionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EvaluationFormNumericQuestionOption', ], 'max' => 10, 'min' => 1, ], 'EvaluationFormNumericQuestionProperties' => [ 'type' => 'structure', 'required' => [ 'MinValue', 'MaxValue', ], 'members' => [ 'MinValue' => [ 'shape' => 'Integer', ], 'MaxValue' => [ 'shape' => 'Integer', ], 'Options' => [ 'shape' => 'EvaluationFormNumericQuestionOptionList', ], 'Automation' => [ 'shape' => 'EvaluationFormNumericQuestionAutomation', ], ], ], 'EvaluationFormQuestion' => [ 'type' => 'structure', 'required' => [ 'Title', 'RefId', 'QuestionType', ], 'members' => [ 'Title' => [ 'shape' => 'EvaluationFormQuestionTitle', ], 'Instructions' => [ 'shape' => 'EvaluationFormQuestionInstructions', ], 'RefId' => [ 'shape' => 'ReferenceId', ], 'NotApplicableEnabled' => [ 'shape' => 'Boolean', ], 'QuestionType' => [ 'shape' => 'EvaluationFormQuestionType', ], 'QuestionTypeProperties' => [ 'shape' => 'EvaluationFormQuestionTypeProperties', ], 'Weight' => [ 'shape' => 'EvaluationFormItemWeight', ], ], ], 'EvaluationFormQuestionAnswerScore' => [ 'type' => 'integer', 'max' => 10, 'min' => 0, ], 'EvaluationFormQuestionInstructions' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'EvaluationFormQuestionTitle' => [ 'type' => 'string', 'max' => 350, 'min' => 0, ], 'EvaluationFormQuestionType' => [ 'type' => 'string', 'enum' => [ 'TEXT', 'SINGLESELECT', 'NUMERIC', ], ], 'EvaluationFormQuestionTypeProperties' => [ 'type' => 'structure', 'members' => [ 'Numeric' => [ 'shape' => 'EvaluationFormNumericQuestionProperties', ], 'SingleSelect' => [ 'shape' => 'EvaluationFormSingleSelectQuestionProperties', ], ], 'union' => true, ], 'EvaluationFormScoringMode' => [ 'type' => 'string', 'enum' => [ 'QUESTION_ONLY', 'SECTION_ONLY', ], ], 'EvaluationFormScoringStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'EvaluationFormScoringStrategy' => [ 'type' => 'structure', 'required' => [ 'Mode', 'Status', ], 'members' => [ 'Mode' => [ 'shape' => 'EvaluationFormScoringMode', ], 'Status' => [ 'shape' => 'EvaluationFormScoringStatus', ], ], ], 'EvaluationFormSection' => [ 'type' => 'structure', 'required' => [ 'Title', 'RefId', 'Items', ], 'members' => [ 'Title' => [ 'shape' => 'EvaluationFormSectionTitle', ], 'RefId' => [ 'shape' => 'ReferenceId', ], 'Instructions' => [ 'shape' => 'EvaluationFormQuestionInstructions', ], 'Items' => [ 'shape' => 'EvaluationFormItemsList', ], 'Weight' => [ 'shape' => 'EvaluationFormItemWeight', ], ], ], 'EvaluationFormSectionTitle' => [ 'type' => 'string', 'max' => 128, 'min' => 0, ], 'EvaluationFormSingleSelectQuestionAutomation' => [ 'type' => 'structure', 'required' => [ 'Options', ], 'members' => [ 'Options' => [ 'shape' => 'EvaluationFormSingleSelectQuestionAutomationOptionList', ], 'DefaultOptionRefId' => [ 'shape' => 'ReferenceId', ], ], ], 'EvaluationFormSingleSelectQuestionAutomationOption' => [ 'type' => 'structure', 'members' => [ 'RuleCategory' => [ 'shape' => 'SingleSelectQuestionRuleCategoryAutomation', ], ], 'union' => true, ], 'EvaluationFormSingleSelectQuestionAutomationOptionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EvaluationFormSingleSelectQuestionAutomationOption', ], 'max' => 20, 'min' => 1, ], 'EvaluationFormSingleSelectQuestionDisplayMode' => [ 'type' => 'string', 'enum' => [ 'DROPDOWN', 'RADIO', ], ], 'EvaluationFormSingleSelectQuestionOption' => [ 'type' => 'structure', 'required' => [ 'RefId', 'Text', ], 'members' => [ 'RefId' => [ 'shape' => 'ReferenceId', ], 'Text' => [ 'shape' => 'EvaluationFormSingleSelectQuestionOptionText', ], 'Score' => [ 'shape' => 'EvaluationFormQuestionAnswerScore', ], 'AutomaticFail' => [ 'shape' => 'Boolean', ], ], ], 'EvaluationFormSingleSelectQuestionOptionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EvaluationFormSingleSelectQuestionOption', ], 'max' => 256, 'min' => 1, ], 'EvaluationFormSingleSelectQuestionOptionText' => [ 'type' => 'string', 'max' => 128, 'min' => 0, ], 'EvaluationFormSingleSelectQuestionProperties' => [ 'type' => 'structure', 'required' => [ 'Options', ], 'members' => [ 'Options' => [ 'shape' => 'EvaluationFormSingleSelectQuestionOptionList', ], 'DisplayAs' => [ 'shape' => 'EvaluationFormSingleSelectQuestionDisplayMode', ], 'Automation' => [ 'shape' => 'EvaluationFormSingleSelectQuestionAutomation', ], ], ], 'EvaluationFormSummary' => [ 'type' => 'structure', 'required' => [ 'EvaluationFormId', 'EvaluationFormArn', 'Title', 'CreatedTime', 'CreatedBy', 'LastModifiedTime', 'LastModifiedBy', 'LatestVersion', ], 'members' => [ 'EvaluationFormId' => [ 'shape' => 'ResourceId', ], 'EvaluationFormArn' => [ 'shape' => 'ARN', ], 'Title' => [ 'shape' => 'EvaluationFormTitle', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'CreatedBy' => [ 'shape' => 'ARN', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedBy' => [ 'shape' => 'ARN', ], 'LastActivatedTime' => [ 'shape' => 'Timestamp', ], 'LastActivatedBy' => [ 'shape' => 'ARN', ], 'LatestVersion' => [ 'shape' => 'VersionNumber', ], 'ActiveVersion' => [ 'shape' => 'VersionNumber', 'box' => true, ], ], ], 'EvaluationFormSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EvaluationFormSummary', ], ], 'EvaluationFormTitle' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'EvaluationFormVersionIsLocked' => [ 'type' => 'boolean', ], 'EvaluationFormVersionStatus' => [ 'type' => 'string', 'enum' => [ 'DRAFT', 'ACTIVE', ], ], 'EvaluationFormVersionSummary' => [ 'type' => 'structure', 'required' => [ 'EvaluationFormArn', 'EvaluationFormId', 'EvaluationFormVersion', 'Locked', 'Status', 'CreatedTime', 'CreatedBy', 'LastModifiedTime', 'LastModifiedBy', ], 'members' => [ 'EvaluationFormArn' => [ 'shape' => 'ARN', ], 'EvaluationFormId' => [ 'shape' => 'ResourceId', ], 'EvaluationFormVersion' => [ 'shape' => 'VersionNumber', ], 'Locked' => [ 'shape' => 'EvaluationFormVersionIsLocked', ], 'Status' => [ 'shape' => 'EvaluationFormVersionStatus', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'CreatedBy' => [ 'shape' => 'ARN', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedBy' => [ 'shape' => 'ARN', ], ], ], 'EvaluationFormVersionSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EvaluationFormVersionSummary', ], ], 'EvaluationId' => [ 'type' => 'string', ], 'EvaluationMetadata' => [ 'type' => 'structure', 'required' => [ 'ContactId', 'EvaluatorArn', ], 'members' => [ 'ContactId' => [ 'shape' => 'ContactId', ], 'EvaluatorArn' => [ 'shape' => 'ARN', ], 'ContactAgentId' => [ 'shape' => 'ResourceId', ], 'Score' => [ 'shape' => 'EvaluationScore', ], ], ], 'EvaluationNote' => [ 'type' => 'structure', 'members' => [ 'Value' => [ 'shape' => 'EvaluationNoteString', ], ], ], 'EvaluationNoteString' => [ 'type' => 'string', 'max' => 3072, 'min' => 0, ], 'EvaluationNotesMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ResourceId', ], 'value' => [ 'shape' => 'EvaluationNote', ], 'max' => 100, ], 'EvaluationScore' => [ 'type' => 'structure', 'members' => [ 'Percentage' => [ 'shape' => 'EvaluationScorePercentage', ], 'NotApplicable' => [ 'shape' => 'Boolean', ], 'AutomaticFail' => [ 'shape' => 'Boolean', ], ], ], 'EvaluationScorePercentage' => [ 'type' => 'double', 'max' => 100, 'min' => 0, ], 'EvaluationScoresMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ResourceId', ], 'value' => [ 'shape' => 'EvaluationScore', ], 'max' => 100, ], 'EvaluationStatus' => [ 'type' => 'string', 'enum' => [ 'DRAFT', 'SUBMITTED', ], ], 'EvaluationSummary' => [ 'type' => 'structure', 'required' => [ 'EvaluationId', 'EvaluationArn', 'EvaluationFormTitle', 'EvaluationFormId', 'Status', 'EvaluatorArn', 'CreatedTime', 'LastModifiedTime', ], 'members' => [ 'EvaluationId' => [ 'shape' => 'ResourceId', ], 'EvaluationArn' => [ 'shape' => 'ARN', ], 'EvaluationFormTitle' => [ 'shape' => 'EvaluationFormTitle', ], 'EvaluationFormId' => [ 'shape' => 'ResourceId', ], 'Status' => [ 'shape' => 'EvaluationStatus', ], 'EvaluatorArn' => [ 'shape' => 'ARN', ], 'Score' => [ 'shape' => 'EvaluationScore', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], ], ], 'EvaluationSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EvaluationSummary', ], ], 'EventBridgeActionDefinition' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'EventBridgeActionName', ], ], ], 'EventBridgeActionName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'EventSourceName' => [ 'type' => 'string', 'enum' => [ 'OnPostCallAnalysisAvailable', 'OnRealTimeCallAnalysisAvailable', 'OnRealTimeChatAnalysisAvailable', 'OnPostChatAnalysisAvailable', 'OnZendeskTicketCreate', 'OnZendeskTicketStatusUpdate', 'OnSalesforceCaseCreate', 'OnContactEvaluationSubmit', 'OnMetricDataUpdate', 'OnCaseCreate', 'OnCaseUpdate', 'OnSlaBreach', ], ], 'Expiry' => [ 'type' => 'structure', 'members' => [ 'DurationInSeconds' => [ 'shape' => 'DurationInSeconds', ], 'ExpiryTimestamp' => [ 'shape' => 'timestamp', ], ], ], 'ExpiryDurationInMinutes' => [ 'type' => 'integer', ], 'ExportLocation' => [ 'type' => 'string', ], 'Expression' => [ 'type' => 'structure', 'members' => [ 'AttributeCondition' => [ 'shape' => 'AttributeCondition', ], 'AndExpression' => [ 'shape' => 'Expressions', ], 'OrExpression' => [ 'shape' => 'Expressions', ], 'NotAttributeCondition' => [ 'shape' => 'AttributeCondition', ], ], ], 'Expressions' => [ 'type' => 'list', 'member' => [ 'shape' => 'Expression', ], ], 'FailedRequest' => [ 'type' => 'structure', 'members' => [ 'RequestIdentifier' => [ 'shape' => 'RequestIdentifier', ], 'FailureReasonCode' => [ 'shape' => 'FailureReasonCode', ], 'FailureReasonMessage' => [ 'shape' => 'String', ], ], ], 'FailedRequestList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FailedRequest', ], ], 'FailureReasonCode' => [ 'type' => 'string', 'enum' => [ 'INVALID_ATTRIBUTE_KEY', 'INVALID_CUSTOMER_ENDPOINT', 'INVALID_SYSTEM_ENDPOINT', 'INVALID_QUEUE', 'MISSING_CAMPAIGN', 'MISSING_CUSTOMER_ENDPOINT', 'MISSING_QUEUE_ID_AND_SYSTEM_ENDPOINT', 'REQUEST_THROTTLED', 'IDEMPOTENCY_EXCEPTION', 'INTERNAL_ERROR', ], ], 'FieldStringValue' => [ 'type' => 'string', 'max' => 500, 'min' => 0, ], 'FieldValue' => [ 'type' => 'structure', 'required' => [ 'Id', 'Value', ], 'members' => [ 'Id' => [ 'shape' => 'FieldValueId', ], 'Value' => [ 'shape' => 'FieldValueUnion', ], ], ], 'FieldValueId' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'FieldValueUnion' => [ 'type' => 'structure', 'members' => [ 'BooleanValue' => [ 'shape' => 'Boolean', ], 'DoubleValue' => [ 'shape' => 'Double', ], 'EmptyValue' => [ 'shape' => 'EmptyFieldValue', ], 'StringValue' => [ 'shape' => 'FieldStringValue', ], ], ], 'FieldValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'FieldValue', ], ], 'FileId' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'FileIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FileId', ], 'max' => 100, 'min' => 1, ], 'FileName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^\\P{C}*$', ], 'FileSizeInBytes' => [ 'type' => 'long', 'box' => true, 'min' => 1, ], 'FileStatusType' => [ 'type' => 'string', 'enum' => [ 'APPROVED', 'REJECTED', 'PROCESSING', 'FAILED', ], ], 'FileUseCaseType' => [ 'type' => 'string', 'enum' => [ 'EMAIL_MESSAGE', 'ATTACHMENT', ], ], 'FilterV2' => [ 'type' => 'structure', 'members' => [ 'FilterKey' => [ 'shape' => 'ResourceArnOrId', ], 'FilterValues' => [ 'shape' => 'FilterValueList', ], ], ], 'FilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceArnOrId', ], 'max' => 100, 'min' => 1, ], 'Filters' => [ 'type' => 'structure', 'members' => [ 'Queues' => [ 'shape' => 'Queues', ], 'Channels' => [ 'shape' => 'Channels', ], 'RoutingProfiles' => [ 'shape' => 'RoutingProfiles', ], 'RoutingStepExpressions' => [ 'shape' => 'RoutingExpressions', ], ], ], 'FiltersV2List' => [ 'type' => 'list', 'member' => [ 'shape' => 'FilterV2', ], 'max' => 5, 'min' => 1, ], 'FlowAssociationResourceType' => [ 'type' => 'string', 'enum' => [ 'SMS_PHONE_NUMBER', 'INBOUND_EMAIL', 'OUTBOUND_EMAIL', 'ANALYTICS_CONNECTOR', 'WHATSAPP_MESSAGING_PHONE_NUMBER', ], ], 'FlowAssociationSummary' => [ 'type' => 'structure', 'members' => [ 'ResourceId' => [ 'shape' => 'ARN', ], 'FlowId' => [ 'shape' => 'ARN', ], 'ResourceType' => [ 'shape' => 'ListFlowAssociationResourceType', ], ], ], 'FlowAssociationSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FlowAssociationSummary', ], ], 'FlowContentSha256' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[a-zA-Z0-9]{64}$', ], 'FormId' => [ 'type' => 'string', ], 'FragmentNumber' => [ 'type' => 'string', 'max' => 128, 'min' => 0, ], 'FunctionArn' => [ 'type' => 'string', 'max' => 140, 'min' => 1, ], 'FunctionArnsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FunctionArn', ], ], 'GetAttachedFileRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'FileId', 'AssociatedResourceArn', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'FileId' => [ 'shape' => 'FileId', 'location' => 'uri', 'locationName' => 'FileId', ], 'UrlExpiryInSeconds' => [ 'shape' => 'URLExpiryInSeconds', 'location' => 'querystring', 'locationName' => 'urlExpiryInSeconds', ], 'AssociatedResourceArn' => [ 'shape' => 'ARN', 'location' => 'querystring', 'locationName' => 'associatedResourceArn', ], ], ], 'GetAttachedFileResponse' => [ 'type' => 'structure', 'required' => [ 'FileSizeInBytes', ], 'members' => [ 'FileArn' => [ 'shape' => 'ARN', ], 'FileId' => [ 'shape' => 'FileId', ], 'CreationTime' => [ 'shape' => 'ISO8601Datetime', ], 'FileStatus' => [ 'shape' => 'FileStatusType', ], 'FileName' => [ 'shape' => 'FileName', ], 'FileSizeInBytes' => [ 'shape' => 'FileSizeInBytes', 'box' => true, ], 'AssociatedResourceArn' => [ 'shape' => 'ARN', ], 'FileUseCaseType' => [ 'shape' => 'FileUseCaseType', ], 'CreatedBy' => [ 'shape' => 'CreatedByInfo', ], 'DownloadUrlMetadata' => [ 'shape' => 'DownloadUrlMetadata', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'GetContactAttributesRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'InitialContactId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'InitialContactId' => [ 'shape' => 'ContactId', 'location' => 'uri', 'locationName' => 'InitialContactId', ], ], ], 'GetContactAttributesResponse' => [ 'type' => 'structure', 'members' => [ 'Attributes' => [ 'shape' => 'Attributes', ], ], ], 'GetCurrentMetricDataRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'Filters', 'CurrentMetrics', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'Filters' => [ 'shape' => 'Filters', ], 'Groupings' => [ 'shape' => 'Groupings', ], 'CurrentMetrics' => [ 'shape' => 'CurrentMetrics', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult100', 'box' => true, ], 'SortCriteria' => [ 'shape' => 'CurrentMetricSortCriteriaMaxOne', ], ], ], 'GetCurrentMetricDataResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MetricResults' => [ 'shape' => 'CurrentMetricResults', ], 'DataSnapshotTime' => [ 'shape' => 'timestamp', ], 'ApproximateTotalCount' => [ 'shape' => 'ApproximateTotalCount', ], ], ], 'GetCurrentUserDataRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'Filters', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'Filters' => [ 'shape' => 'UserDataFilters', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult100', 'box' => true, ], ], ], 'GetCurrentUserDataResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'UserDataList' => [ 'shape' => 'UserDataList', ], 'ApproximateTotalCount' => [ 'shape' => 'ApproximateTotalCount', ], ], ], 'GetEffectiveHoursOfOperationsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'HoursOfOperationId', 'FromDate', 'ToDate', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'HoursOfOperationId' => [ 'shape' => 'HoursOfOperationId', 'location' => 'uri', 'locationName' => 'HoursOfOperationId', ], 'FromDate' => [ 'shape' => 'HoursOfOperationOverrideYearMonthDayDateFormat', 'location' => 'querystring', 'locationName' => 'fromDate', ], 'ToDate' => [ 'shape' => 'HoursOfOperationOverrideYearMonthDayDateFormat', 'location' => 'querystring', 'locationName' => 'toDate', ], ], ], 'GetEffectiveHoursOfOperationsResponse' => [ 'type' => 'structure', 'members' => [ 'EffectiveHoursOfOperationList' => [ 'shape' => 'EffectiveHoursOfOperationList', ], 'TimeZone' => [ 'shape' => 'TimeZone', ], ], ], 'GetFederationTokenRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], ], ], 'GetFederationTokenResponse' => [ 'type' => 'structure', 'members' => [ 'Credentials' => [ 'shape' => 'Credentials', ], 'SignInUrl' => [ 'shape' => 'Url', ], 'UserArn' => [ 'shape' => 'ARN', ], 'UserId' => [ 'shape' => 'AgentResourceId', ], ], ], 'GetFlowAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ResourceId', 'ResourceType', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ResourceId' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'ResourceId', ], 'ResourceType' => [ 'shape' => 'FlowAssociationResourceType', 'location' => 'uri', 'locationName' => 'ResourceType', ], ], ], 'GetFlowAssociationResponse' => [ 'type' => 'structure', 'members' => [ 'ResourceId' => [ 'shape' => 'ARN', ], 'FlowId' => [ 'shape' => 'ARN', ], 'ResourceType' => [ 'shape' => 'FlowAssociationResourceType', ], ], ], 'GetMetricDataRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'StartTime', 'EndTime', 'Filters', 'HistoricalMetrics', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'StartTime' => [ 'shape' => 'timestamp', ], 'EndTime' => [ 'shape' => 'timestamp', ], 'Filters' => [ 'shape' => 'Filters', ], 'Groupings' => [ 'shape' => 'Groupings', ], 'HistoricalMetrics' => [ 'shape' => 'HistoricalMetrics', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult100', 'box' => true, ], ], ], 'GetMetricDataResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MetricResults' => [ 'shape' => 'HistoricalMetricResults', ], ], ], 'GetMetricDataV2Request' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'StartTime', 'EndTime', 'Filters', 'Metrics', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ARN', ], 'StartTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], 'Interval' => [ 'shape' => 'IntervalDetails', ], 'Filters' => [ 'shape' => 'FiltersV2List', ], 'Groupings' => [ 'shape' => 'GroupingsV2', ], 'Metrics' => [ 'shape' => 'MetricsV2', ], 'NextToken' => [ 'shape' => 'NextToken2500', ], 'MaxResults' => [ 'shape' => 'MaxResult100', 'box' => true, ], ], ], 'GetMetricDataV2Response' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken2500', ], 'MetricResults' => [ 'shape' => 'MetricResultsV2', ], ], ], 'GetPromptFileRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'PromptId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'PromptId' => [ 'shape' => 'PromptId', 'location' => 'uri', 'locationName' => 'PromptId', ], ], ], 'GetPromptFileResponse' => [ 'type' => 'structure', 'members' => [ 'PromptPresignedUrl' => [ 'shape' => 'PromptPresignedUrl', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedRegion' => [ 'shape' => 'RegionName', ], ], ], 'GetTaskTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'TaskTemplateId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'TaskTemplateId' => [ 'shape' => 'TaskTemplateId', 'location' => 'uri', 'locationName' => 'TaskTemplateId', ], 'SnapshotVersion' => [ 'shape' => 'SnapshotVersion', 'location' => 'querystring', 'locationName' => 'snapshotVersion', ], ], ], 'GetTaskTemplateResponse' => [ 'type' => 'structure', 'required' => [ 'Id', 'Arn', 'Name', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'Id' => [ 'shape' => 'TaskTemplateId', ], 'Arn' => [ 'shape' => 'TaskTemplateArn', ], 'Name' => [ 'shape' => 'TaskTemplateName', ], 'Description' => [ 'shape' => 'TaskTemplateDescription', ], 'ContactFlowId' => [ 'shape' => 'ContactFlowId', ], 'SelfAssignFlowId' => [ 'shape' => 'ContactFlowId', ], 'Constraints' => [ 'shape' => 'TaskTemplateConstraints', ], 'Defaults' => [ 'shape' => 'TaskTemplateDefaults', ], 'Fields' => [ 'shape' => 'TaskTemplateFields', ], 'Status' => [ 'shape' => 'TaskTemplateStatus', ], 'LastModifiedTime' => [ 'shape' => 'timestamp', ], 'CreatedTime' => [ 'shape' => 'timestamp', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'GetTrafficDistributionRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'TrafficDistributionGroupIdOrArn', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetTrafficDistributionResponse' => [ 'type' => 'structure', 'members' => [ 'TelephonyConfig' => [ 'shape' => 'TelephonyConfig', ], 'Id' => [ 'shape' => 'TrafficDistributionGroupId', ], 'Arn' => [ 'shape' => 'TrafficDistributionGroupArn', ], 'SignInConfig' => [ 'shape' => 'SignInConfig', ], 'AgentConfig' => [ 'shape' => 'AgentConfig', ], ], ], 'GlobalSignInEndpoint' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'Grouping' => [ 'type' => 'string', 'enum' => [ 'QUEUE', 'CHANNEL', 'ROUTING_PROFILE', 'ROUTING_STEP_EXPRESSION', ], ], 'GroupingV2' => [ 'type' => 'string', ], 'Groupings' => [ 'type' => 'list', 'member' => [ 'shape' => 'Grouping', ], 'max' => 2, ], 'GroupingsV2' => [ 'type' => 'list', 'member' => [ 'shape' => 'GroupingV2', ], 'max' => 4, ], 'HierarchyGroup' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'HierarchyGroupId', ], 'Arn' => [ 'shape' => 'ARN', ], 'Name' => [ 'shape' => 'HierarchyGroupName', ], 'LevelId' => [ 'shape' => 'HierarchyLevelId', ], 'HierarchyPath' => [ 'shape' => 'HierarchyPath', ], 'Tags' => [ 'shape' => 'TagMap', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedRegion' => [ 'shape' => 'RegionName', ], ], ], 'HierarchyGroupCondition' => [ 'type' => 'structure', 'members' => [ 'Value' => [ 'shape' => 'String', ], 'HierarchyGroupMatchType' => [ 'shape' => 'HierarchyGroupMatchType', ], ], ], 'HierarchyGroupId' => [ 'type' => 'string', ], 'HierarchyGroupIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'HierarchyGroupId', ], 'max' => 10, 'min' => 0, ], 'HierarchyGroupMatchType' => [ 'type' => 'string', 'enum' => [ 'EXACT', 'WITH_CHILD_GROUPS', ], ], 'HierarchyGroupName' => [ 'type' => 'string', ], 'HierarchyGroupSummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'HierarchyGroupId', ], 'Arn' => [ 'shape' => 'ARN', ], 'Name' => [ 'shape' => 'HierarchyGroupName', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedRegion' => [ 'shape' => 'RegionName', ], ], ], 'HierarchyGroupSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'HierarchyGroupSummary', ], ], 'HierarchyGroupSummaryReference' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'HierarchyGroupId', ], 'Arn' => [ 'shape' => 'ARN', ], ], ], 'HierarchyGroups' => [ 'type' => 'structure', 'members' => [ 'Level1' => [ 'shape' => 'AgentHierarchyGroup', ], 'Level2' => [ 'shape' => 'AgentHierarchyGroup', ], 'Level3' => [ 'shape' => 'AgentHierarchyGroup', ], 'Level4' => [ 'shape' => 'AgentHierarchyGroup', ], 'Level5' => [ 'shape' => 'AgentHierarchyGroup', ], ], ], 'HierarchyLevel' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'HierarchyLevelId', ], 'Arn' => [ 'shape' => 'ARN', ], 'Name' => [ 'shape' => 'HierarchyLevelName', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedRegion' => [ 'shape' => 'RegionName', ], ], ], 'HierarchyLevelId' => [ 'type' => 'string', ], 'HierarchyLevelName' => [ 'type' => 'string', ], 'HierarchyLevelUpdate' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'HierarchyLevelName', ], ], ], 'HierarchyPath' => [ 'type' => 'structure', 'members' => [ 'LevelOne' => [ 'shape' => 'HierarchyGroupSummary', ], 'LevelTwo' => [ 'shape' => 'HierarchyGroupSummary', ], 'LevelThree' => [ 'shape' => 'HierarchyGroupSummary', ], 'LevelFour' => [ 'shape' => 'HierarchyGroupSummary', ], 'LevelFive' => [ 'shape' => 'HierarchyGroupSummary', ], ], ], 'HierarchyPathReference' => [ 'type' => 'structure', 'members' => [ 'LevelOne' => [ 'shape' => 'HierarchyGroupSummaryReference', ], 'LevelTwo' => [ 'shape' => 'HierarchyGroupSummaryReference', ], 'LevelThree' => [ 'shape' => 'HierarchyGroupSummaryReference', ], 'LevelFour' => [ 'shape' => 'HierarchyGroupSummaryReference', ], 'LevelFive' => [ 'shape' => 'HierarchyGroupSummaryReference', ], ], ], 'HierarchyRestrictedResourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'HierarchyRestrictedResourceName', ], ], 'HierarchyRestrictedResourceName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'HierarchyStructure' => [ 'type' => 'structure', 'members' => [ 'LevelOne' => [ 'shape' => 'HierarchyLevel', ], 'LevelTwo' => [ 'shape' => 'HierarchyLevel', ], 'LevelThree' => [ 'shape' => 'HierarchyLevel', ], 'LevelFour' => [ 'shape' => 'HierarchyLevel', ], 'LevelFive' => [ 'shape' => 'HierarchyLevel', ], ], ], 'HierarchyStructureUpdate' => [ 'type' => 'structure', 'members' => [ 'LevelOne' => [ 'shape' => 'HierarchyLevelUpdate', ], 'LevelTwo' => [ 'shape' => 'HierarchyLevelUpdate', ], 'LevelThree' => [ 'shape' => 'HierarchyLevelUpdate', ], 'LevelFour' => [ 'shape' => 'HierarchyLevelUpdate', ], 'LevelFive' => [ 'shape' => 'HierarchyLevelUpdate', ], ], ], 'HistoricalMetric' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'HistoricalMetricName', ], 'Threshold' => [ 'shape' => 'Threshold', 'box' => true, ], 'Statistic' => [ 'shape' => 'Statistic', ], 'Unit' => [ 'shape' => 'Unit', ], ], ], 'HistoricalMetricData' => [ 'type' => 'structure', 'members' => [ 'Metric' => [ 'shape' => 'HistoricalMetric', ], 'Value' => [ 'shape' => 'Value', 'box' => true, ], ], ], 'HistoricalMetricDataCollections' => [ 'type' => 'list', 'member' => [ 'shape' => 'HistoricalMetricData', ], ], 'HistoricalMetricName' => [ 'type' => 'string', 'enum' => [ 'CONTACTS_QUEUED', 'CONTACTS_HANDLED', 'CONTACTS_ABANDONED', 'CONTACTS_CONSULTED', 'CONTACTS_AGENT_HUNG_UP_FIRST', 'CONTACTS_HANDLED_INCOMING', 'CONTACTS_HANDLED_OUTBOUND', 'CONTACTS_HOLD_ABANDONS', 'CONTACTS_TRANSFERRED_IN', 'CONTACTS_TRANSFERRED_OUT', 'CONTACTS_TRANSFERRED_IN_FROM_QUEUE', 'CONTACTS_TRANSFERRED_OUT_FROM_QUEUE', 'CONTACTS_MISSED', 'CALLBACK_CONTACTS_HANDLED', 'API_CONTACTS_HANDLED', 'OCCUPANCY', 'HANDLE_TIME', 'AFTER_CONTACT_WORK_TIME', 'QUEUED_TIME', 'ABANDON_TIME', 'QUEUE_ANSWER_TIME', 'HOLD_TIME', 'INTERACTION_TIME', 'INTERACTION_AND_HOLD_TIME', 'SERVICE_LEVEL', ], ], 'HistoricalMetricResult' => [ 'type' => 'structure', 'members' => [ 'Dimensions' => [ 'shape' => 'Dimensions', ], 'Collections' => [ 'shape' => 'HistoricalMetricDataCollections', ], ], ], 'HistoricalMetricResults' => [ 'type' => 'list', 'member' => [ 'shape' => 'HistoricalMetricResult', ], ], 'HistoricalMetrics' => [ 'type' => 'list', 'member' => [ 'shape' => 'HistoricalMetric', ], ], 'Hours' => [ 'type' => 'integer', 'max' => 87600, 'min' => 0, ], 'Hours24Format' => [ 'type' => 'integer', 'max' => 23, 'min' => 0, ], 'HoursOfOperation' => [ 'type' => 'structure', 'members' => [ 'HoursOfOperationId' => [ 'shape' => 'HoursOfOperationId', ], 'HoursOfOperationArn' => [ 'shape' => 'ARN', ], 'Name' => [ 'shape' => 'CommonNameLength127', ], 'Description' => [ 'shape' => 'HoursOfOperationDescription', ], 'TimeZone' => [ 'shape' => 'TimeZone', ], 'Config' => [ 'shape' => 'HoursOfOperationConfigList', ], 'Tags' => [ 'shape' => 'TagMap', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedRegion' => [ 'shape' => 'RegionName', ], ], ], 'HoursOfOperationConfig' => [ 'type' => 'structure', 'required' => [ 'Day', 'StartTime', 'EndTime', ], 'members' => [ 'Day' => [ 'shape' => 'HoursOfOperationDays', ], 'StartTime' => [ 'shape' => 'HoursOfOperationTimeSlice', ], 'EndTime' => [ 'shape' => 'HoursOfOperationTimeSlice', ], ], ], 'HoursOfOperationConfigList' => [ 'type' => 'list', 'member' => [ 'shape' => 'HoursOfOperationConfig', ], 'max' => 100, 'min' => 0, ], 'HoursOfOperationDays' => [ 'type' => 'string', 'enum' => [ 'SUNDAY', 'MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY', ], ], 'HoursOfOperationDescription' => [ 'type' => 'string', 'max' => 250, 'min' => 1, ], 'HoursOfOperationId' => [ 'type' => 'string', ], 'HoursOfOperationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'HoursOfOperation', ], ], 'HoursOfOperationName' => [ 'type' => 'string', ], 'HoursOfOperationOverride' => [ 'type' => 'structure', 'members' => [ 'HoursOfOperationOverrideId' => [ 'shape' => 'HoursOfOperationOverrideId', ], 'HoursOfOperationId' => [ 'shape' => 'HoursOfOperationId', ], 'HoursOfOperationArn' => [ 'shape' => 'ARN', ], 'Name' => [ 'shape' => 'CommonHumanReadableName', ], 'Description' => [ 'shape' => 'CommonHumanReadableDescription', ], 'Config' => [ 'shape' => 'HoursOfOperationOverrideConfigList', ], 'EffectiveFrom' => [ 'shape' => 'HoursOfOperationOverrideYearMonthDayDateFormat', ], 'EffectiveTill' => [ 'shape' => 'HoursOfOperationOverrideYearMonthDayDateFormat', ], ], ], 'HoursOfOperationOverrideConfig' => [ 'type' => 'structure', 'members' => [ 'Day' => [ 'shape' => 'OverrideDays', ], 'StartTime' => [ 'shape' => 'OverrideTimeSlice', ], 'EndTime' => [ 'shape' => 'OverrideTimeSlice', ], ], ], 'HoursOfOperationOverrideConfigList' => [ 'type' => 'list', 'member' => [ 'shape' => 'HoursOfOperationOverrideConfig', ], 'max' => 100, 'min' => 0, ], 'HoursOfOperationOverrideId' => [ 'type' => 'string', 'max' => 36, 'min' => 1, ], 'HoursOfOperationOverrideList' => [ 'type' => 'list', 'member' => [ 'shape' => 'HoursOfOperationOverride', ], ], 'HoursOfOperationOverrideSearchConditionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'HoursOfOperationOverrideSearchCriteria', ], ], 'HoursOfOperationOverrideSearchCriteria' => [ 'type' => 'structure', 'members' => [ 'OrConditions' => [ 'shape' => 'HoursOfOperationOverrideSearchConditionList', ], 'AndConditions' => [ 'shape' => 'HoursOfOperationOverrideSearchConditionList', ], 'StringCondition' => [ 'shape' => 'StringCondition', ], 'DateCondition' => [ 'shape' => 'DateCondition', ], ], ], 'HoursOfOperationOverrideYearMonthDayDateFormat' => [ 'type' => 'string', 'pattern' => '^\\d{4}-\\d{2}-\\d{2}$', ], 'HoursOfOperationSearchConditionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'HoursOfOperationSearchCriteria', ], ], 'HoursOfOperationSearchCriteria' => [ 'type' => 'structure', 'members' => [ 'OrConditions' => [ 'shape' => 'HoursOfOperationSearchConditionList', ], 'AndConditions' => [ 'shape' => 'HoursOfOperationSearchConditionList', ], 'StringCondition' => [ 'shape' => 'StringCondition', ], ], ], 'HoursOfOperationSearchFilter' => [ 'type' => 'structure', 'members' => [ 'TagFilter' => [ 'shape' => 'ControlPlaneTagFilter', ], ], ], 'HoursOfOperationSummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'HoursOfOperationId', ], 'Arn' => [ 'shape' => 'ARN', ], 'Name' => [ 'shape' => 'HoursOfOperationName', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedRegion' => [ 'shape' => 'RegionName', ], ], ], 'HoursOfOperationSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'HoursOfOperationSummary', ], ], 'HoursOfOperationTimeSlice' => [ 'type' => 'structure', 'required' => [ 'Hours', 'Minutes', ], 'members' => [ 'Hours' => [ 'shape' => 'Hours24Format', 'box' => true, ], 'Minutes' => [ 'shape' => 'MinutesLimit60', 'box' => true, ], ], ], 'ISO8601Datetime' => [ 'type' => 'string', ], 'IdempotencyException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ImportPhoneNumberRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'SourcePhoneNumberArn', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'SourcePhoneNumberArn' => [ 'shape' => 'ARN', ], 'PhoneNumberDescription' => [ 'shape' => 'PhoneNumberDescription', ], 'Tags' => [ 'shape' => 'TagMap', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'ImportPhoneNumberResponse' => [ 'type' => 'structure', 'members' => [ 'PhoneNumberId' => [ 'shape' => 'PhoneNumberId', ], 'PhoneNumberArn' => [ 'shape' => 'ARN', ], ], ], 'InboundAdditionalRecipients' => [ 'type' => 'structure', 'members' => [ 'ToAddresses' => [ 'shape' => 'EmailAddressRecipientList', ], 'CcAddresses' => [ 'shape' => 'EmailAddressRecipientList', ], ], ], 'InboundCallsEnabled' => [ 'type' => 'boolean', ], 'InboundEmailContent' => [ 'type' => 'structure', 'required' => [ 'MessageSourceType', ], 'members' => [ 'MessageSourceType' => [ 'shape' => 'InboundMessageSourceType', ], 'RawMessage' => [ 'shape' => 'InboundRawMessage', ], ], ], 'InboundMessageSourceType' => [ 'type' => 'string', 'enum' => [ 'RAW', ], ], 'InboundRawMessage' => [ 'type' => 'structure', 'required' => [ 'Subject', 'Body', 'ContentType', ], 'members' => [ 'Subject' => [ 'shape' => 'InboundSubject', ], 'Body' => [ 'shape' => 'Body', ], 'ContentType' => [ 'shape' => 'EmailMessageContentType', ], 'Headers' => [ 'shape' => 'EmailHeaders', ], ], ], 'InboundSubject' => [ 'type' => 'string', 'max' => 998, 'min' => 0, 'sensitive' => true, ], 'IncludeRawMessage' => [ 'type' => 'boolean', ], 'Index' => [ 'type' => 'integer', ], 'InitiateAs' => [ 'type' => 'string', 'enum' => [ 'CONNECTED_TO_USER', ], ], 'InitiationMethodList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContactInitiationMethod', ], ], 'Instance' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'InstanceId', ], 'Arn' => [ 'shape' => 'ARN', ], 'IdentityManagementType' => [ 'shape' => 'DirectoryType', ], 'InstanceAlias' => [ 'shape' => 'DirectoryAlias', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'ServiceRole' => [ 'shape' => 'ARN', ], 'InstanceStatus' => [ 'shape' => 'InstanceStatus', ], 'StatusReason' => [ 'shape' => 'InstanceStatusReason', ], 'InboundCallsEnabled' => [ 'shape' => 'InboundCallsEnabled', ], 'OutboundCallsEnabled' => [ 'shape' => 'OutboundCallsEnabled', ], 'InstanceAccessUrl' => [ 'shape' => 'Url', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'InstanceArn' => [ 'type' => 'string', 'pattern' => 'arn:(aws|aws-us-gov):connect:[a-z]{2}-[a-z]+-[0-9-]{1}:[0-9]{1,20}:instance/[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}', ], 'InstanceAttributeType' => [ 'type' => 'string', 'enum' => [ 'INBOUND_CALLS', 'OUTBOUND_CALLS', 'CONTACTFLOW_LOGS', 'CONTACT_LENS', 'AUTO_RESOLVE_BEST_VOICES', 'USE_CUSTOM_TTS_VOICES', 'EARLY_MEDIA', 'MULTI_PARTY_CONFERENCE', 'HIGH_VOLUME_OUTBOUND', 'ENHANCED_CONTACT_MONITORING', 'ENHANCED_CHAT_MONITORING', 'MULTI_PARTY_CHAT_CONFERENCE', ], ], 'InstanceAttributeValue' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'InstanceId' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'InstanceIdOrArn' => [ 'type' => 'string', 'max' => 250, 'min' => 1, 'pattern' => '^(arn:(aws|aws-us-gov):connect:[a-z]{2}-[a-z]+-[0-9]{1}:[0-9]{1,20}:instance/)?[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$', ], 'InstanceReplicationStatus' => [ 'type' => 'string', 'enum' => [ 'INSTANCE_REPLICATION_COMPLETE', 'INSTANCE_REPLICATION_IN_PROGRESS', 'INSTANCE_REPLICATION_FAILED', 'INSTANCE_REPLICA_DELETING', 'INSTANCE_REPLICATION_DELETION_FAILED', 'RESOURCE_REPLICATION_NOT_STARTED', ], ], 'InstanceStatus' => [ 'type' => 'string', 'enum' => [ 'CREATION_IN_PROGRESS', 'ACTIVE', 'CREATION_FAILED', ], ], 'InstanceStatusReason' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], ], 'InstanceStorageConfig' => [ 'type' => 'structure', 'required' => [ 'StorageType', ], 'members' => [ 'AssociationId' => [ 'shape' => 'AssociationId', ], 'StorageType' => [ 'shape' => 'StorageType', ], 'S3Config' => [ 'shape' => 'S3Config', ], 'KinesisVideoStreamConfig' => [ 'shape' => 'KinesisVideoStreamConfig', ], 'KinesisStreamConfig' => [ 'shape' => 'KinesisStreamConfig', ], 'KinesisFirehoseConfig' => [ 'shape' => 'KinesisFirehoseConfig', ], ], ], 'InstanceStorageConfigs' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceStorageConfig', ], ], 'InstanceStorageResourceType' => [ 'type' => 'string', 'enum' => [ 'CHAT_TRANSCRIPTS', 'CALL_RECORDINGS', 'SCHEDULED_REPORTS', 'MEDIA_STREAMS', 'CONTACT_TRACE_RECORDS', 'AGENT_EVENTS', 'REAL_TIME_CONTACT_ANALYSIS_SEGMENTS', 'ATTACHMENTS', 'CONTACT_EVALUATIONS', 'SCREEN_RECORDINGS', 'REAL_TIME_CONTACT_ANALYSIS_CHAT_SEGMENTS', 'REAL_TIME_CONTACT_ANALYSIS_VOICE_SEGMENTS', 'EMAIL_MESSAGES', ], ], 'InstanceSummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'InstanceId', ], 'Arn' => [ 'shape' => 'ARN', ], 'IdentityManagementType' => [ 'shape' => 'DirectoryType', ], 'InstanceAlias' => [ 'shape' => 'DirectoryAlias', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'ServiceRole' => [ 'shape' => 'ARN', ], 'InstanceStatus' => [ 'shape' => 'InstanceStatus', ], 'InboundCallsEnabled' => [ 'shape' => 'InboundCallsEnabled', ], 'OutboundCallsEnabled' => [ 'shape' => 'OutboundCallsEnabled', ], 'InstanceAccessUrl' => [ 'shape' => 'Url', ], ], ], 'InstanceSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceSummary', ], ], 'Integer' => [ 'type' => 'integer', ], 'IntegerCount' => [ 'type' => 'integer', 'min' => 0, ], 'IntegrationAssociationId' => [ 'type' => 'string', 'max' => 200, 'min' => 1, ], 'IntegrationAssociationSummary' => [ 'type' => 'structure', 'members' => [ 'IntegrationAssociationId' => [ 'shape' => 'IntegrationAssociationId', ], 'IntegrationAssociationArn' => [ 'shape' => 'ARN', ], 'InstanceId' => [ 'shape' => 'InstanceId', ], 'IntegrationType' => [ 'shape' => 'IntegrationType', ], 'IntegrationArn' => [ 'shape' => 'ARN', ], 'SourceApplicationUrl' => [ 'shape' => 'URI', ], 'SourceApplicationName' => [ 'shape' => 'SourceApplicationName', ], 'SourceType' => [ 'shape' => 'SourceType', ], ], ], 'IntegrationAssociationSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IntegrationAssociationSummary', ], ], 'IntegrationType' => [ 'type' => 'string', 'enum' => [ 'EVENT', 'VOICE_ID', 'PINPOINT_APP', 'WISDOM_ASSISTANT', 'WISDOM_KNOWLEDGE_BASE', 'WISDOM_QUICK_RESPONSES', 'Q_MESSAGE_TEMPLATES', 'CASES_DOMAIN', 'APPLICATION', 'FILE_SCANNER', 'SES_IDENTITY', 'ANALYTICS_CONNECTOR', 'CALL_TRANSFER_CONNECTOR', 'COGNITO_USER_POOL', ], ], 'InternalServiceException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, ], 'IntervalDetails' => [ 'type' => 'structure', 'members' => [ 'TimeZone' => [ 'shape' => 'String', ], 'IntervalPeriod' => [ 'shape' => 'IntervalPeriod', ], ], ], 'IntervalPeriod' => [ 'type' => 'string', 'enum' => [ 'FIFTEEN_MIN', 'THIRTY_MIN', 'HOUR', 'DAY', 'WEEK', 'TOTAL', ], ], 'InvalidContactFlowException' => [ 'type' => 'structure', 'members' => [ 'problems' => [ 'shape' => 'Problems', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidContactFlowModuleException' => [ 'type' => 'structure', 'members' => [ 'Problems' => [ 'shape' => 'Problems', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidParameterException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidRequestException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], 'Reason' => [ 'shape' => 'InvalidRequestExceptionReason', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidRequestExceptionReason' => [ 'type' => 'structure', 'members' => [ 'AttachedFileInvalidRequestExceptionReason' => [ 'shape' => 'AttachedFileInvalidRequestExceptionReason', ], ], 'union' => true, ], 'InvisibleFieldInfo' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'TaskTemplateFieldIdentifier', ], ], ], 'InvisibleTaskTemplateFields' => [ 'type' => 'list', 'member' => [ 'shape' => 'InvisibleFieldInfo', ], ], 'IpCidr' => [ 'type' => 'string', 'max' => 50, 'min' => 2, 'pattern' => '^[A-Za-z0-9:/]*$', ], 'IpCidrList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IpCidr', ], ], 'IvrRecordingTrack' => [ 'type' => 'string', 'enum' => [ 'ALL', ], ], 'JoinToken' => [ 'type' => 'string', 'sensitive' => true, ], 'KeyId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'KinesisFirehoseConfig' => [ 'type' => 'structure', 'required' => [ 'FirehoseArn', ], 'members' => [ 'FirehoseArn' => [ 'shape' => 'ARN', ], ], ], 'KinesisStreamConfig' => [ 'type' => 'structure', 'required' => [ 'StreamArn', ], 'members' => [ 'StreamArn' => [ 'shape' => 'ARN', ], ], ], 'KinesisVideoStreamConfig' => [ 'type' => 'structure', 'required' => [ 'Prefix', 'RetentionPeriodHours', 'EncryptionConfig', ], 'members' => [ 'Prefix' => [ 'shape' => 'Prefix', ], 'RetentionPeriodHours' => [ 'shape' => 'Hours', ], 'EncryptionConfig' => [ 'shape' => 'EncryptionConfig', ], ], ], 'LargeNextToken' => [ 'type' => 'string', 'max' => 100000, 'min' => 1, ], 'LexBot' => [ 'type' => 'structure', 'required' => [ 'Name', 'LexRegion', ], 'members' => [ 'Name' => [ 'shape' => 'BotName', ], 'LexRegion' => [ 'shape' => 'LexRegion', ], ], ], 'LexBotConfig' => [ 'type' => 'structure', 'members' => [ 'LexBot' => [ 'shape' => 'LexBot', ], 'LexV2Bot' => [ 'shape' => 'LexV2Bot', ], ], ], 'LexBotConfigList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LexBotConfig', ], ], 'LexBotsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LexBot', ], ], 'LexRegion' => [ 'type' => 'string', 'max' => 60, ], 'LexV2Bot' => [ 'type' => 'structure', 'members' => [ 'AliasArn' => [ 'shape' => 'AliasArn', ], ], ], 'LexVersion' => [ 'type' => 'string', 'enum' => [ 'V1', 'V2', ], ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], 'ListAgentStatusRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult1000', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], 'AgentStatusTypes' => [ 'shape' => 'AgentStatusTypes', 'location' => 'querystring', 'locationName' => 'AgentStatusTypes', ], ], ], 'ListAgentStatusResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'AgentStatusSummaryList' => [ 'shape' => 'AgentStatusSummaryList', ], ], ], 'ListAnalyticsDataAssociationsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'DataSetId' => [ 'shape' => 'DataSetId', 'location' => 'querystring', 'locationName' => 'DataSetId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult1000', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListAnalyticsDataAssociationsResponse' => [ 'type' => 'structure', 'members' => [ 'Results' => [ 'shape' => 'AnalyticsDataAssociationResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAnalyticsDataLakeDataSetsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult1000', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListAnalyticsDataLakeDataSetsResponse' => [ 'type' => 'structure', 'members' => [ 'Results' => [ 'shape' => 'AnalyticsDataSetsResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListApprovedOriginsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult25', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListApprovedOriginsResponse' => [ 'type' => 'structure', 'members' => [ 'Origins' => [ 'shape' => 'OriginsList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAssociatedContactsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ContactId' => [ 'shape' => 'ContactId', 'location' => 'querystring', 'locationName' => 'contactId', ], 'MaxResults' => [ 'shape' => 'ListAssociatedContactsRequestMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListAssociatedContactsRequestMaxResults' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'ListAssociatedContactsResponse' => [ 'type' => 'structure', 'members' => [ 'ContactSummaryList' => [ 'shape' => 'AssociatedContactSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAuthenticationProfilesRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'MaxResults' => [ 'shape' => 'MaxResult1000', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListAuthenticationProfilesResponse' => [ 'type' => 'structure', 'members' => [ 'AuthenticationProfileSummaryList' => [ 'shape' => 'AuthenticationProfileSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListBotsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'LexVersion', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult25', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], 'LexVersion' => [ 'shape' => 'LexVersion', 'location' => 'querystring', 'locationName' => 'lexVersion', ], ], ], 'ListBotsResponse' => [ 'type' => 'structure', 'members' => [ 'LexBots' => [ 'shape' => 'LexBotConfigList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListCondition' => [ 'type' => 'structure', 'members' => [ 'TargetListType' => [ 'shape' => 'TargetListType', ], 'Conditions' => [ 'shape' => 'Conditions', ], ], ], 'ListContactEvaluationsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ContactId' => [ 'shape' => 'ContactId', 'location' => 'querystring', 'locationName' => 'contactId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListContactEvaluationsResponse' => [ 'type' => 'structure', 'required' => [ 'EvaluationSummaryList', ], 'members' => [ 'EvaluationSummaryList' => [ 'shape' => 'EvaluationSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListContactFlowModulesRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult1000', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], 'ContactFlowModuleState' => [ 'shape' => 'ContactFlowModuleState', 'location' => 'querystring', 'locationName' => 'state', ], ], ], 'ListContactFlowModulesResponse' => [ 'type' => 'structure', 'members' => [ 'ContactFlowModulesSummaryList' => [ 'shape' => 'ContactFlowModulesSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListContactFlowVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactFlowId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ContactFlowId' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'ContactFlowId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult1000', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListContactFlowVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'ContactFlowVersionSummaryList' => [ 'shape' => 'ContactFlowVersionSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListContactFlowsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ContactFlowTypes' => [ 'shape' => 'ContactFlowTypes', 'location' => 'querystring', 'locationName' => 'contactFlowTypes', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult1000', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListContactFlowsResponse' => [ 'type' => 'structure', 'members' => [ 'ContactFlowSummaryList' => [ 'shape' => 'ContactFlowSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListContactReferencesRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactId', 'ReferenceTypes', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ContactId' => [ 'shape' => 'ContactId', 'location' => 'uri', 'locationName' => 'ContactId', ], 'ReferenceTypes' => [ 'shape' => 'ReferenceTypes', 'location' => 'querystring', 'locationName' => 'referenceTypes', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListContactReferencesResponse' => [ 'type' => 'structure', 'members' => [ 'ReferenceSummaryList' => [ 'shape' => 'ReferenceSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDefaultVocabulariesRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'LanguageCode' => [ 'shape' => 'VocabularyLanguageCode', ], 'MaxResults' => [ 'shape' => 'MaxResult100', ], 'NextToken' => [ 'shape' => 'VocabularyNextToken', ], ], ], 'ListDefaultVocabulariesResponse' => [ 'type' => 'structure', 'required' => [ 'DefaultVocabularyList', ], 'members' => [ 'DefaultVocabularyList' => [ 'shape' => 'DefaultVocabularyList', ], 'NextToken' => [ 'shape' => 'VocabularyNextToken', ], ], ], 'ListEvaluationFormVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'EvaluationFormId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'EvaluationFormId' => [ 'shape' => 'ResourceId', 'location' => 'uri', 'locationName' => 'EvaluationFormId', ], 'MaxResults' => [ 'shape' => 'MaxResult100', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListEvaluationFormVersionsResponse' => [ 'type' => 'structure', 'required' => [ 'EvaluationFormVersionSummaryList', ], 'members' => [ 'EvaluationFormVersionSummaryList' => [ 'shape' => 'EvaluationFormVersionSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListEvaluationFormsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'MaxResults' => [ 'shape' => 'MaxResult100', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListEvaluationFormsResponse' => [ 'type' => 'structure', 'required' => [ 'EvaluationFormSummaryList', ], 'members' => [ 'EvaluationFormSummaryList' => [ 'shape' => 'EvaluationFormSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListFlowAssociationResourceType' => [ 'type' => 'string', 'enum' => [ 'WHATSAPP_MESSAGING_PHONE_NUMBER', 'VOICE_PHONE_NUMBER', 'INBOUND_EMAIL', 'OUTBOUND_EMAIL', 'ANALYTICS_CONNECTOR', ], ], 'ListFlowAssociationsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ResourceType' => [ 'shape' => 'ListFlowAssociationResourceType', 'location' => 'querystring', 'locationName' => 'ResourceType', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult1000', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListFlowAssociationsResponse' => [ 'type' => 'structure', 'members' => [ 'FlowAssociationSummaryList' => [ 'shape' => 'FlowAssociationSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListHoursOfOperationOverridesRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'HoursOfOperationId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'HoursOfOperationId' => [ 'shape' => 'HoursOfOperationId', 'location' => 'uri', 'locationName' => 'HoursOfOperationId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult100', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListHoursOfOperationOverridesResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'HoursOfOperationOverrideList' => [ 'shape' => 'HoursOfOperationOverrideList', ], 'LastModifiedRegion' => [ 'shape' => 'RegionName', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], ], ], 'ListHoursOfOperationsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult1000', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListHoursOfOperationsResponse' => [ 'type' => 'structure', 'members' => [ 'HoursOfOperationSummaryList' => [ 'shape' => 'HoursOfOperationSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListInstanceAttributesRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult7', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListInstanceAttributesResponse' => [ 'type' => 'structure', 'members' => [ 'Attributes' => [ 'shape' => 'AttributesList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListInstanceStorageConfigsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ResourceType', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ResourceType' => [ 'shape' => 'InstanceStorageResourceType', 'location' => 'querystring', 'locationName' => 'resourceType', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult10', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListInstanceStorageConfigsResponse' => [ 'type' => 'structure', 'members' => [ 'StorageConfigs' => [ 'shape' => 'InstanceStorageConfigs', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListInstancesRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult10', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListInstancesResponse' => [ 'type' => 'structure', 'members' => [ 'InstanceSummaryList' => [ 'shape' => 'InstanceSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListIntegrationAssociationsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'IntegrationType' => [ 'shape' => 'IntegrationType', 'location' => 'querystring', 'locationName' => 'integrationType', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult100', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], 'IntegrationArn' => [ 'shape' => 'ARN', 'location' => 'querystring', 'locationName' => 'integrationArn', ], ], ], 'ListIntegrationAssociationsResponse' => [ 'type' => 'structure', 'members' => [ 'IntegrationAssociationSummaryList' => [ 'shape' => 'IntegrationAssociationSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListLambdaFunctionsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult25', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListLambdaFunctionsResponse' => [ 'type' => 'structure', 'members' => [ 'LambdaFunctions' => [ 'shape' => 'FunctionArnsList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListLexBotsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult25', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListLexBotsResponse' => [ 'type' => 'structure', 'members' => [ 'LexBots' => [ 'shape' => 'LexBotsList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListPhoneNumbersRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'PhoneNumberTypes' => [ 'shape' => 'PhoneNumberTypes', 'location' => 'querystring', 'locationName' => 'phoneNumberTypes', ], 'PhoneNumberCountryCodes' => [ 'shape' => 'PhoneNumberCountryCodes', 'location' => 'querystring', 'locationName' => 'phoneNumberCountryCodes', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult1000', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListPhoneNumbersResponse' => [ 'type' => 'structure', 'members' => [ 'PhoneNumberSummaryList' => [ 'shape' => 'PhoneNumberSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListPhoneNumbersSummary' => [ 'type' => 'structure', 'members' => [ 'PhoneNumberId' => [ 'shape' => 'PhoneNumberId', ], 'PhoneNumberArn' => [ 'shape' => 'ARN', ], 'PhoneNumber' => [ 'shape' => 'PhoneNumber', ], 'PhoneNumberCountryCode' => [ 'shape' => 'PhoneNumberCountryCode', ], 'PhoneNumberType' => [ 'shape' => 'PhoneNumberType', ], 'TargetArn' => [ 'shape' => 'ARN', ], 'InstanceId' => [ 'shape' => 'InstanceId', ], 'PhoneNumberDescription' => [ 'shape' => 'PhoneNumberDescription', ], 'SourcePhoneNumberArn' => [ 'shape' => 'ARN', ], ], ], 'ListPhoneNumbersSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListPhoneNumbersSummary', ], ], 'ListPhoneNumbersV2Request' => [ 'type' => 'structure', 'members' => [ 'TargetArn' => [ 'shape' => 'ARN', ], 'InstanceId' => [ 'shape' => 'InstanceId', ], 'MaxResults' => [ 'shape' => 'MaxResult1000', 'box' => true, ], 'NextToken' => [ 'shape' => 'LargeNextToken', ], 'PhoneNumberCountryCodes' => [ 'shape' => 'PhoneNumberCountryCodes', ], 'PhoneNumberTypes' => [ 'shape' => 'PhoneNumberTypes', ], 'PhoneNumberPrefix' => [ 'shape' => 'PhoneNumberPrefix', ], ], ], 'ListPhoneNumbersV2Response' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'LargeNextToken', ], 'ListPhoneNumbersSummaryList' => [ 'shape' => 'ListPhoneNumbersSummaryList', ], ], ], 'ListPredefinedAttributesRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult100', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListPredefinedAttributesResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'PredefinedAttributeSummaryList' => [ 'shape' => 'PredefinedAttributeSummaryList', ], ], ], 'ListPromptsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult1000', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListPromptsResponse' => [ 'type' => 'structure', 'members' => [ 'PromptSummaryList' => [ 'shape' => 'PromptSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListQueueQuickConnectsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'QueueId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'QueueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'QueueId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult100', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListQueueQuickConnectsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'QuickConnectSummaryList' => [ 'shape' => 'QuickConnectSummaryList', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedRegion' => [ 'shape' => 'RegionName', ], ], ], 'ListQueuesRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'QueueTypes' => [ 'shape' => 'QueueTypes', 'location' => 'querystring', 'locationName' => 'queueTypes', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult1000', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListQueuesResponse' => [ 'type' => 'structure', 'members' => [ 'QueueSummaryList' => [ 'shape' => 'QueueSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListQuickConnectsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult1000', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], 'QuickConnectTypes' => [ 'shape' => 'QuickConnectTypes', 'location' => 'querystring', 'locationName' => 'QuickConnectTypes', ], ], ], 'ListQuickConnectsResponse' => [ 'type' => 'structure', 'members' => [ 'QuickConnectSummaryList' => [ 'shape' => 'QuickConnectSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListRealtimeContactAnalysisSegmentsV2Request' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactId', 'OutputType', 'SegmentTypes', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ContactId' => [ 'shape' => 'ContactId', 'location' => 'uri', 'locationName' => 'ContactId', ], 'MaxResults' => [ 'shape' => 'MaxResult100', ], 'NextToken' => [ 'shape' => 'LargeNextToken', ], 'OutputType' => [ 'shape' => 'RealTimeContactAnalysisOutputType', ], 'SegmentTypes' => [ 'shape' => 'RealTimeContactAnalysisSegmentTypes', ], ], ], 'ListRealtimeContactAnalysisSegmentsV2Response' => [ 'type' => 'structure', 'required' => [ 'Channel', 'Status', 'Segments', ], 'members' => [ 'Channel' => [ 'shape' => 'RealTimeContactAnalysisSupportedChannel', ], 'Status' => [ 'shape' => 'RealTimeContactAnalysisStatus', ], 'Segments' => [ 'shape' => 'RealtimeContactAnalysisSegments', ], 'NextToken' => [ 'shape' => 'LargeNextToken', ], ], ], 'ListRoutingProfileQueuesRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'RoutingProfileId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'RoutingProfileId' => [ 'shape' => 'RoutingProfileId', 'location' => 'uri', 'locationName' => 'RoutingProfileId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult100', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListRoutingProfileQueuesResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'RoutingProfileQueueConfigSummaryList' => [ 'shape' => 'RoutingProfileQueueConfigSummaryList', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedRegion' => [ 'shape' => 'RegionName', ], ], ], 'ListRoutingProfilesRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult1000', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListRoutingProfilesResponse' => [ 'type' => 'structure', 'members' => [ 'RoutingProfileSummaryList' => [ 'shape' => 'RoutingProfileSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListRulesRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'PublishStatus' => [ 'shape' => 'RulePublishStatus', 'location' => 'querystring', 'locationName' => 'publishStatus', ], 'EventSourceName' => [ 'shape' => 'EventSourceName', 'location' => 'querystring', 'locationName' => 'eventSourceName', ], 'MaxResults' => [ 'shape' => 'MaxResult200', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListRulesResponse' => [ 'type' => 'structure', 'required' => [ 'RuleSummaryList', ], 'members' => [ 'RuleSummaryList' => [ 'shape' => 'RuleSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSecurityKeysRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult2', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListSecurityKeysResponse' => [ 'type' => 'structure', 'members' => [ 'SecurityKeys' => [ 'shape' => 'SecurityKeysList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSecurityProfileApplicationsRequest' => [ 'type' => 'structure', 'required' => [ 'SecurityProfileId', 'InstanceId', ], 'members' => [ 'SecurityProfileId' => [ 'shape' => 'SecurityProfileId', 'location' => 'uri', 'locationName' => 'SecurityProfileId', ], 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult1000', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListSecurityProfileApplicationsResponse' => [ 'type' => 'structure', 'members' => [ 'Applications' => [ 'shape' => 'Applications', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedRegion' => [ 'shape' => 'RegionName', ], ], ], 'ListSecurityProfilePermissionsRequest' => [ 'type' => 'structure', 'required' => [ 'SecurityProfileId', 'InstanceId', ], 'members' => [ 'SecurityProfileId' => [ 'shape' => 'SecurityProfileId', 'location' => 'uri', 'locationName' => 'SecurityProfileId', ], 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult1000', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListSecurityProfilePermissionsResponse' => [ 'type' => 'structure', 'members' => [ 'Permissions' => [ 'shape' => 'PermissionsList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedRegion' => [ 'shape' => 'RegionName', ], ], ], 'ListSecurityProfilesRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult1000', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListSecurityProfilesResponse' => [ 'type' => 'structure', 'members' => [ 'SecurityProfileSummaryList' => [ 'shape' => 'SecurityProfileSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'ListTaskTemplatesRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult100', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], 'Status' => [ 'shape' => 'TaskTemplateStatus', 'location' => 'querystring', 'locationName' => 'status', ], 'Name' => [ 'shape' => 'TaskTemplateName', 'location' => 'querystring', 'locationName' => 'name', ], ], ], 'ListTaskTemplatesResponse' => [ 'type' => 'structure', 'members' => [ 'TaskTemplates' => [ 'shape' => 'TaskTemplateList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTrafficDistributionGroupUsersRequest' => [ 'type' => 'structure', 'required' => [ 'TrafficDistributionGroupId', ], 'members' => [ 'TrafficDistributionGroupId' => [ 'shape' => 'TrafficDistributionGroupIdOrArn', 'location' => 'uri', 'locationName' => 'TrafficDistributionGroupId', ], 'MaxResults' => [ 'shape' => 'MaxResult10', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListTrafficDistributionGroupUsersResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'TrafficDistributionGroupUserSummaryList' => [ 'shape' => 'TrafficDistributionGroupUserSummaryList', ], ], ], 'ListTrafficDistributionGroupsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResult10', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'InstanceId' => [ 'shape' => 'InstanceIdOrArn', 'location' => 'querystring', 'locationName' => 'instanceId', ], ], ], 'ListTrafficDistributionGroupsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'TrafficDistributionGroupSummaryList' => [ 'shape' => 'TrafficDistributionGroupSummaryList', ], ], ], 'ListUseCasesRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'IntegrationAssociationId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'IntegrationAssociationId' => [ 'shape' => 'IntegrationAssociationId', 'location' => 'uri', 'locationName' => 'IntegrationAssociationId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult100', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListUseCasesResponse' => [ 'type' => 'structure', 'members' => [ 'UseCaseSummaryList' => [ 'shape' => 'UseCaseSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListUserHierarchyGroupsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult1000', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListUserHierarchyGroupsResponse' => [ 'type' => 'structure', 'members' => [ 'UserHierarchyGroupSummaryList' => [ 'shape' => 'HierarchyGroupSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListUserProficienciesRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'UserId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'UserId' => [ 'shape' => 'UserId', 'location' => 'uri', 'locationName' => 'UserId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult100', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListUserProficienciesResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'UserProficiencyList' => [ 'shape' => 'UserProficiencyList', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedRegion' => [ 'shape' => 'RegionName', ], ], ], 'ListUsersRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResult1000', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListUsersResponse' => [ 'type' => 'structure', 'members' => [ 'UserSummaryList' => [ 'shape' => 'UserSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListViewVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ViewId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'ViewsInstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ViewId' => [ 'shape' => 'ViewId', 'location' => 'uri', 'locationName' => 'ViewId', ], 'NextToken' => [ 'shape' => 'ViewsNextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListViewVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'ViewVersionSummaryList' => [ 'shape' => 'ViewVersionSummaryList', ], 'NextToken' => [ 'shape' => 'ViewsNextToken', ], ], ], 'ListViewsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'ViewsInstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'Type' => [ 'shape' => 'ViewType', 'location' => 'querystring', 'locationName' => 'type', ], 'NextToken' => [ 'shape' => 'ViewsNextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListViewsResponse' => [ 'type' => 'structure', 'members' => [ 'ViewsSummaryList' => [ 'shape' => 'ViewsSummaryList', ], 'NextToken' => [ 'shape' => 'ViewsNextToken', ], ], ], 'Long' => [ 'type' => 'long', ], 'MatchCriteria' => [ 'type' => 'structure', 'members' => [ 'AgentsCriteria' => [ 'shape' => 'AgentsCriteria', ], ], ], 'MaxResult10' => [ 'type' => 'integer', 'max' => 10, 'min' => 1, ], 'MaxResult100' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'MaxResult1000' => [ 'type' => 'integer', 'max' => 1000, 'min' => 1, ], 'MaxResult2' => [ 'type' => 'integer', 'max' => 2, 'min' => 1, ], 'MaxResult200' => [ 'type' => 'integer', 'max' => 200, 'min' => 1, ], 'MaxResult25' => [ 'type' => 'integer', 'max' => 25, 'min' => 1, ], 'MaxResult500' => [ 'type' => 'integer', 'box' => true, 'max' => 500, 'min' => 1, ], 'MaxResult7' => [ 'type' => 'integer', 'max' => 7, 'min' => 1, ], 'MaxResults' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'MaximumResultReturnedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'MediaConcurrencies' => [ 'type' => 'list', 'member' => [ 'shape' => 'MediaConcurrency', ], ], 'MediaConcurrency' => [ 'type' => 'structure', 'required' => [ 'Channel', 'Concurrency', ], 'members' => [ 'Channel' => [ 'shape' => 'Channel', ], 'Concurrency' => [ 'shape' => 'Concurrency', ], 'CrossChannelBehavior' => [ 'shape' => 'CrossChannelBehavior', ], ], ], 'MediaPlacement' => [ 'type' => 'structure', 'members' => [ 'AudioHostUrl' => [ 'shape' => 'URI', ], 'AudioFallbackUrl' => [ 'shape' => 'URI', ], 'SignalingUrl' => [ 'shape' => 'URI', ], 'TurnControlUrl' => [ 'shape' => 'URI', ], 'EventIngestionUrl' => [ 'shape' => 'URI', ], ], ], 'MediaRegion' => [ 'type' => 'string', ], 'MediaStreamType' => [ 'type' => 'string', 'enum' => [ 'AUDIO', 'VIDEO', ], ], 'Meeting' => [ 'type' => 'structure', 'members' => [ 'MediaRegion' => [ 'shape' => 'MediaRegion', ], 'MediaPlacement' => [ 'shape' => 'MediaPlacement', ], 'MeetingFeatures' => [ 'shape' => 'MeetingFeaturesConfiguration', ], 'MeetingId' => [ 'shape' => 'MeetingId', ], ], ], 'MeetingFeatureStatus' => [ 'type' => 'string', 'enum' => [ 'AVAILABLE', 'UNAVAILABLE', ], ], 'MeetingFeaturesConfiguration' => [ 'type' => 'structure', 'members' => [ 'Audio' => [ 'shape' => 'AudioFeatures', ], ], ], 'MeetingId' => [ 'type' => 'string', ], 'Message' => [ 'type' => 'string', ], 'MessageTemplateId' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'MessageTemplateKnowledgeBaseId' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'MetadataUrl' => [ 'type' => 'string', 'max' => 2000, 'min' => 1, ], 'MetricDataCollectionsV2' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricDataV2', ], ], 'MetricDataV2' => [ 'type' => 'structure', 'members' => [ 'Metric' => [ 'shape' => 'MetricV2', ], 'Value' => [ 'shape' => 'Value', 'box' => true, ], ], ], 'MetricFilterV2' => [ 'type' => 'structure', 'members' => [ 'MetricFilterKey' => [ 'shape' => 'String', ], 'MetricFilterValues' => [ 'shape' => 'MetricFilterValueList', ], 'Negate' => [ 'shape' => 'Boolean', ], ], ], 'MetricFilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'max' => 10, 'min' => 1, ], 'MetricFiltersV2List' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricFilterV2', ], 'max' => 2, ], 'MetricInterval' => [ 'type' => 'structure', 'members' => [ 'Interval' => [ 'shape' => 'IntervalPeriod', ], 'StartTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], ], ], 'MetricNameV2' => [ 'type' => 'string', ], 'MetricResultV2' => [ 'type' => 'structure', 'members' => [ 'Dimensions' => [ 'shape' => 'DimensionsV2Map', ], 'MetricInterval' => [ 'shape' => 'MetricInterval', ], 'Collections' => [ 'shape' => 'MetricDataCollectionsV2', ], ], ], 'MetricResultsV2' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricResultV2', ], ], 'MetricV2' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'MetricNameV2', ], 'Threshold' => [ 'shape' => 'ThresholdCollections', ], 'MetricFilters' => [ 'shape' => 'MetricFiltersV2List', ], ], ], 'MetricsV2' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricV2', ], ], 'MinutesLimit60' => [ 'type' => 'integer', 'max' => 59, 'min' => 0, ], 'MonitorCapability' => [ 'type' => 'string', 'enum' => [ 'SILENT_MONITOR', 'BARGE', ], ], 'MonitorContactRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactId', 'UserId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'ContactId' => [ 'shape' => 'ContactId', ], 'UserId' => [ 'shape' => 'AgentResourceId', ], 'AllowedMonitorCapabilities' => [ 'shape' => 'AllowedMonitorCapabilities', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'MonitorContactResponse' => [ 'type' => 'structure', 'members' => [ 'ContactId' => [ 'shape' => 'ContactId', ], 'ContactArn' => [ 'shape' => 'ARN', ], ], ], 'Name' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'sensitive' => true, ], 'Name128' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '(^[\\S].*[\\S]$)|(^[\\S]$)', ], 'Namespace' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'NewChatCreated' => [ 'type' => 'boolean', ], 'NewSessionDetails' => [ 'type' => 'structure', 'members' => [ 'SupportedMessagingContentTypes' => [ 'shape' => 'SupportedMessagingContentTypes', ], 'ParticipantDetails' => [ 'shape' => 'ParticipantDetails', ], 'Attributes' => [ 'shape' => 'Attributes', ], 'StreamingConfiguration' => [ 'shape' => 'ChatStreamingConfiguration', ], ], ], 'NextToken' => [ 'type' => 'string', ], 'NextToken2500' => [ 'type' => 'string', 'max' => 2500, 'min' => 1, ], 'NotificationContentType' => [ 'type' => 'string', 'enum' => [ 'PLAIN_TEXT', ], ], 'NotificationDeliveryType' => [ 'type' => 'string', 'enum' => [ 'EMAIL', ], ], 'NotificationRecipientType' => [ 'type' => 'structure', 'members' => [ 'UserTags' => [ 'shape' => 'UserTagMap', ], 'UserIds' => [ 'shape' => 'UserIdList', ], ], ], 'NullableProficiencyLevel' => [ 'type' => 'float', 'max' => 5.0, 'min' => 1.0, ], 'NullableProficiencyLimitValue' => [ 'type' => 'integer', ], 'NumberComparisonType' => [ 'type' => 'string', 'enum' => [ 'GREATER_OR_EQUAL', 'GREATER', 'LESSER_OR_EQUAL', 'LESSER', 'EQUAL', 'NOT_EQUAL', 'RANGE', ], ], 'NumberCondition' => [ 'type' => 'structure', 'members' => [ 'FieldName' => [ 'shape' => 'String', ], 'MinValue' => [ 'shape' => 'NullableProficiencyLimitValue', ], 'MaxValue' => [ 'shape' => 'NullableProficiencyLimitValue', ], 'ComparisonType' => [ 'shape' => 'NumberComparisonType', ], ], ], 'NumberReference' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'ReferenceKey', ], 'Value' => [ 'shape' => 'ReferenceValue', ], ], ], 'NumericQuestionPropertyAutomationLabel' => [ 'type' => 'string', 'enum' => [ 'OVERALL_CUSTOMER_SENTIMENT_SCORE', 'OVERALL_AGENT_SENTIMENT_SCORE', 'NON_TALK_TIME', 'NON_TALK_TIME_PERCENTAGE', 'NUMBER_OF_INTERRUPTIONS', 'CONTACT_DURATION', 'AGENT_INTERACTION_DURATION', 'CUSTOMER_HOLD_TIME', ], ], 'NumericQuestionPropertyValueAutomation' => [ 'type' => 'structure', 'required' => [ 'Label', ], 'members' => [ 'Label' => [ 'shape' => 'NumericQuestionPropertyAutomationLabel', ], ], ], 'OperatingSystem' => [ 'type' => 'string', 'max' => 128, 'min' => 0, ], 'OperationalHour' => [ 'type' => 'structure', 'members' => [ 'Start' => [ 'shape' => 'OverrideTimeSlice', ], 'End' => [ 'shape' => 'OverrideTimeSlice', ], ], ], 'OperationalHours' => [ 'type' => 'list', 'member' => [ 'shape' => 'OperationalHour', ], ], 'Origin' => [ 'type' => 'string', 'max' => 267, ], 'OriginsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Origin', ], ], 'OutboundAdditionalRecipients' => [ 'type' => 'structure', 'members' => [ 'CcEmailAddresses' => [ 'shape' => 'EmailAddressRecipientList', ], ], ], 'OutboundCallerConfig' => [ 'type' => 'structure', 'members' => [ 'OutboundCallerIdName' => [ 'shape' => 'OutboundCallerIdName', ], 'OutboundCallerIdNumberId' => [ 'shape' => 'PhoneNumberId', ], 'OutboundFlowId' => [ 'shape' => 'ContactFlowId', ], ], ], 'OutboundCallerIdName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'OutboundCallsEnabled' => [ 'type' => 'boolean', ], 'OutboundContactNotPermittedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'OutboundEmailConfig' => [ 'type' => 'structure', 'members' => [ 'OutboundEmailAddressId' => [ 'shape' => 'EmailAddressId', ], ], ], 'OutboundEmailContent' => [ 'type' => 'structure', 'required' => [ 'MessageSourceType', ], 'members' => [ 'MessageSourceType' => [ 'shape' => 'OutboundMessageSourceType', ], 'TemplatedMessageConfig' => [ 'shape' => 'TemplatedMessageConfig', ], 'RawMessage' => [ 'shape' => 'OutboundRawMessage', ], ], ], 'OutboundMessageSourceType' => [ 'type' => 'string', 'enum' => [ 'TEMPLATE', 'RAW', ], ], 'OutboundRawMessage' => [ 'type' => 'structure', 'required' => [ 'Subject', 'Body', 'ContentType', ], 'members' => [ 'Subject' => [ 'shape' => 'OutboundSubject', ], 'Body' => [ 'shape' => 'Body', ], 'ContentType' => [ 'shape' => 'EmailMessageContentType', ], ], ], 'OutboundRequestId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, ], 'OutboundSubject' => [ 'type' => 'string', 'max' => 998, 'min' => 1, 'sensitive' => true, ], 'OutputTypeNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'OverrideDays' => [ 'type' => 'string', 'enum' => [ 'SUNDAY', 'MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY', ], ], 'OverrideTimeSlice' => [ 'type' => 'structure', 'required' => [ 'Hours', 'Minutes', ], 'members' => [ 'Hours' => [ 'shape' => 'Hours24Format', 'box' => true, ], 'Minutes' => [ 'shape' => 'MinutesLimit60', 'box' => true, ], ], ], 'PEM' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'ParticipantCapabilities' => [ 'type' => 'structure', 'members' => [ 'Video' => [ 'shape' => 'VideoCapability', ], 'ScreenShare' => [ 'shape' => 'ScreenShareCapability', ], ], ], 'ParticipantDetails' => [ 'type' => 'structure', 'required' => [ 'DisplayName', ], 'members' => [ 'DisplayName' => [ 'shape' => 'DisplayName', ], ], ], 'ParticipantDetailsToAdd' => [ 'type' => 'structure', 'members' => [ 'ParticipantRole' => [ 'shape' => 'ParticipantRole', ], 'DisplayName' => [ 'shape' => 'DisplayName', ], ], ], 'ParticipantId' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'ParticipantRole' => [ 'type' => 'string', 'enum' => [ 'AGENT', 'CUSTOMER', 'SYSTEM', 'CUSTOM_BOT', 'SUPERVISOR', ], ], 'ParticipantState' => [ 'type' => 'string', 'enum' => [ 'INITIAL', 'CONNECTED', 'DISCONNECTED', 'MISSED', ], ], 'ParticipantTimerAction' => [ 'type' => 'string', 'enum' => [ 'Unset', ], ], 'ParticipantTimerConfigList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ParticipantTimerConfiguration', ], 'max' => 6, 'min' => 1, ], 'ParticipantTimerConfiguration' => [ 'type' => 'structure', 'required' => [ 'ParticipantRole', 'TimerType', 'TimerValue', ], 'members' => [ 'ParticipantRole' => [ 'shape' => 'TimerEligibleParticipantRoles', ], 'TimerType' => [ 'shape' => 'ParticipantTimerType', ], 'TimerValue' => [ 'shape' => 'ParticipantTimerValue', ], ], ], 'ParticipantTimerDurationInMinutes' => [ 'type' => 'integer', 'max' => 480, 'min' => 2, ], 'ParticipantTimerType' => [ 'type' => 'string', 'enum' => [ 'IDLE', 'DISCONNECT_NONCUSTOMER', ], ], 'ParticipantTimerValue' => [ 'type' => 'structure', 'members' => [ 'ParticipantTimerAction' => [ 'shape' => 'ParticipantTimerAction', ], 'ParticipantTimerDurationInMinutes' => [ 'shape' => 'ParticipantTimerDurationInMinutes', ], ], 'union' => true, ], 'ParticipantToken' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, ], 'ParticipantTokenCredentials' => [ 'type' => 'structure', 'members' => [ 'ParticipantToken' => [ 'shape' => 'ParticipantToken', ], 'Expiry' => [ 'shape' => 'ISO8601Datetime', ], ], ], 'ParticipantType' => [ 'type' => 'string', 'enum' => [ 'ALL', 'MANAGER', 'AGENT', 'CUSTOMER', 'THIRDPARTY', ], ], 'Password' => [ 'type' => 'string', 'pattern' => '/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d\\S]{8,64}$/', 'sensitive' => true, ], 'PauseContactRequest' => [ 'type' => 'structure', 'required' => [ 'ContactId', 'InstanceId', ], 'members' => [ 'ContactId' => [ 'shape' => 'ContactId', ], 'InstanceId' => [ 'shape' => 'InstanceId', ], 'ContactFlowId' => [ 'shape' => 'ContactFlowId', ], ], ], 'PauseContactResponse' => [ 'type' => 'structure', 'members' => [], ], 'Percentage' => [ 'type' => 'integer', 'max' => 100, 'min' => 0, ], 'Permission' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'PermissionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityProfilePermission', ], 'max' => 500, ], 'PersistentChat' => [ 'type' => 'structure', 'members' => [ 'RehydrationType' => [ 'shape' => 'RehydrationType', ], 'SourceContactId' => [ 'shape' => 'ContactId', ], ], ], 'PhoneNumber' => [ 'type' => 'string', 'pattern' => '\\\\+[1-9]\\\\d{1,14}$', ], 'PhoneNumberCountryCode' => [ 'type' => 'string', 'enum' => [ 'AF', 'AL', 'DZ', 'AS', 'AD', 'AO', 'AI', 'AQ', 'AG', 'AR', 'AM', 'AW', 'AU', 'AT', 'AZ', 'BS', 'BH', 'BD', 'BB', 'BY', 'BE', 'BZ', 'BJ', 'BM', 'BT', 'BO', 'BA', 'BW', 'BR', 'IO', 'VG', 'BN', 'BG', 'BF', 'BI', 'KH', 'CM', 'CA', 'CV', 'KY', 'CF', 'TD', 'CL', 'CN', 'CX', 'CC', 'CO', 'KM', 'CK', 'CR', 'HR', 'CU', 'CW', 'CY', 'CZ', 'CD', 'DK', 'DJ', 'DM', 'DO', 'TL', 'EC', 'EG', 'SV', 'GQ', 'ER', 'EE', 'ET', 'FK', 'FO', 'FJ', 'FI', 'FR', 'PF', 'GA', 'GM', 'GE', 'DE', 'GH', 'GI', 'GR', 'GL', 'GD', 'GU', 'GT', 'GG', 'GN', 'GW', 'GY', 'HT', 'HN', 'HK', 'HU', 'IS', 'IN', 'ID', 'IR', 'IQ', 'IE', 'IM', 'IL', 'IT', 'CI', 'JM', 'JP', 'JE', 'JO', 'KZ', 'KE', 'KI', 'KW', 'KG', 'LA', 'LV', 'LB', 'LS', 'LR', 'LY', 'LI', 'LT', 'LU', 'MO', 'MK', 'MG', 'MW', 'MY', 'MV', 'ML', 'MT', 'MH', 'MR', 'MU', 'YT', 'MX', 'FM', 'MD', 'MC', 'MN', 'ME', 'MS', 'MA', 'MZ', 'MM', 'NA', 'NR', 'NP', 'NL', 'AN', 'NC', 'NZ', 'NI', 'NE', 'NG', 'NU', 'KP', 'MP', 'NO', 'OM', 'PK', 'PW', 'PA', 'PG', 'PY', 'PE', 'PH', 'PN', 'PL', 'PT', 'PR', 'QA', 'CG', 'RE', 'RO', 'RU', 'RW', 'BL', 'SH', 'KN', 'LC', 'MF', 'PM', 'VC', 'WS', 'SM', 'ST', 'SA', 'SN', 'RS', 'SC', 'SL', 'SG', 'SX', 'SK', 'SI', 'SB', 'SO', 'ZA', 'KR', 'ES', 'LK', 'SD', 'SR', 'SJ', 'SZ', 'SE', 'CH', 'SY', 'TW', 'TJ', 'TZ', 'TH', 'TG', 'TK', 'TO', 'TT', 'TN', 'TR', 'TM', 'TC', 'TV', 'VI', 'UG', 'UA', 'AE', 'GB', 'US', 'UY', 'UZ', 'VU', 'VA', 'VE', 'VN', 'WF', 'EH', 'YE', 'ZM', 'ZW', ], ], 'PhoneNumberCountryCodes' => [ 'type' => 'list', 'member' => [ 'shape' => 'PhoneNumberCountryCode', ], 'max' => 10, ], 'PhoneNumberDescription' => [ 'type' => 'string', 'max' => 500, 'min' => 0, 'pattern' => '^[\\W\\S_]*', ], 'PhoneNumberId' => [ 'type' => 'string', ], 'PhoneNumberPrefix' => [ 'type' => 'string', 'pattern' => '\\\\+?[0-9]{1,11}', ], 'PhoneNumberQuickConnectConfig' => [ 'type' => 'structure', 'required' => [ 'PhoneNumber', ], 'members' => [ 'PhoneNumber' => [ 'shape' => 'PhoneNumber', ], ], ], 'PhoneNumberStatus' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'PhoneNumberWorkflowStatus', ], 'Message' => [ 'shape' => 'PhoneNumberWorkflowMessage', ], ], ], 'PhoneNumberSummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'PhoneNumberId', ], 'Arn' => [ 'shape' => 'ARN', ], 'PhoneNumber' => [ 'shape' => 'PhoneNumber', ], 'PhoneNumberType' => [ 'shape' => 'PhoneNumberType', ], 'PhoneNumberCountryCode' => [ 'shape' => 'PhoneNumberCountryCode', ], ], ], 'PhoneNumberSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PhoneNumberSummary', ], ], 'PhoneNumberType' => [ 'type' => 'string', 'enum' => [ 'TOLL_FREE', 'DID', 'UIFN', 'SHARED', 'THIRD_PARTY_TF', 'THIRD_PARTY_DID', 'SHORT_CODE', ], ], 'PhoneNumberTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'PhoneNumberType', ], 'max' => 6, ], 'PhoneNumberWorkflowMessage' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'pattern' => '^[\\W\\S_]*', ], 'PhoneNumberWorkflowStatus' => [ 'type' => 'string', 'enum' => [ 'CLAIMED', 'IN_PROGRESS', 'FAILED', ], ], 'PhoneType' => [ 'type' => 'string', 'enum' => [ 'SOFT_PHONE', 'DESK_PHONE', ], ], 'PlatformName' => [ 'type' => 'string', 'max' => 128, 'min' => 0, ], 'PlatformVersion' => [ 'type' => 'string', 'max' => 128, 'min' => 0, ], 'PotentialAudioQualityIssue' => [ 'type' => 'string', 'max' => 128, 'min' => 0, ], 'PotentialAudioQualityIssues' => [ 'type' => 'list', 'member' => [ 'shape' => 'PotentialAudioQualityIssue', ], 'max' => 3, 'min' => 0, ], 'PotentialDisconnectIssue' => [ 'type' => 'string', 'max' => 128, 'min' => 0, ], 'PreSignedAttachmentUrl' => [ 'type' => 'string', 'max' => 2000, 'min' => 1, ], 'PredefinedAttribute' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'PredefinedAttributeName', ], 'Values' => [ 'shape' => 'PredefinedAttributeValues', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedRegion' => [ 'shape' => 'RegionName', ], ], ], 'PredefinedAttributeName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'PredefinedAttributeSearchConditionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PredefinedAttributeSearchCriteria', ], ], 'PredefinedAttributeSearchCriteria' => [ 'type' => 'structure', 'members' => [ 'OrConditions' => [ 'shape' => 'PredefinedAttributeSearchConditionList', ], 'AndConditions' => [ 'shape' => 'PredefinedAttributeSearchConditionList', ], 'StringCondition' => [ 'shape' => 'StringCondition', ], ], ], 'PredefinedAttributeSearchSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PredefinedAttribute', ], ], 'PredefinedAttributeStringValue' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'PredefinedAttributeStringValuesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PredefinedAttributeStringValue', ], 'max' => 128, 'min' => 1, ], 'PredefinedAttributeSummary' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'PredefinedAttributeName', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedRegion' => [ 'shape' => 'RegionName', ], ], ], 'PredefinedAttributeSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PredefinedAttributeSummary', ], ], 'PredefinedAttributeValues' => [ 'type' => 'structure', 'members' => [ 'StringList' => [ 'shape' => 'PredefinedAttributeStringValuesList', ], ], 'union' => true, ], 'Prefix' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'Priority' => [ 'type' => 'integer', 'max' => 99, 'min' => 1, ], 'ProblemDetail' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ProblemMessageString', ], ], ], 'ProblemMessageString' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'Problems' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProblemDetail', ], 'max' => 50, 'min' => 1, ], 'ProficiencyLevel' => [ 'type' => 'float', 'box' => true, 'max' => 5.0, 'min' => 1.0, ], 'ProficiencyValue' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'Prompt' => [ 'type' => 'structure', 'members' => [ 'PromptARN' => [ 'shape' => 'ARN', ], 'PromptId' => [ 'shape' => 'PromptId', ], 'Name' => [ 'shape' => 'CommonNameLength127', ], 'Description' => [ 'shape' => 'PromptDescription', ], 'Tags' => [ 'shape' => 'TagMap', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedRegion' => [ 'shape' => 'RegionName', ], ], ], 'PromptDescription' => [ 'type' => 'string', 'max' => 250, 'min' => 1, ], 'PromptId' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'PromptList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Prompt', ], ], 'PromptName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'PromptPresignedUrl' => [ 'type' => 'string', 'max' => 2000, 'min' => 1, ], 'PromptSearchConditionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PromptSearchCriteria', ], ], 'PromptSearchCriteria' => [ 'type' => 'structure', 'members' => [ 'OrConditions' => [ 'shape' => 'PromptSearchConditionList', ], 'AndConditions' => [ 'shape' => 'PromptSearchConditionList', ], 'StringCondition' => [ 'shape' => 'StringCondition', ], ], ], 'PromptSearchFilter' => [ 'type' => 'structure', 'members' => [ 'TagFilter' => [ 'shape' => 'ControlPlaneTagFilter', ], ], ], 'PromptSummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'PromptId', ], 'Arn' => [ 'shape' => 'ARN', ], 'Name' => [ 'shape' => 'PromptName', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedRegion' => [ 'shape' => 'RegionName', ], ], ], 'PromptSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PromptSummary', ], ], 'PropertyValidationException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'Message', ], 'PropertyList' => [ 'shape' => 'PropertyValidationExceptionPropertyList', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'PropertyValidationExceptionProperty' => [ 'type' => 'structure', 'required' => [ 'PropertyPath', 'Reason', 'Message', ], 'members' => [ 'PropertyPath' => [ 'shape' => 'String', ], 'Reason' => [ 'shape' => 'PropertyValidationExceptionReason', ], 'Message' => [ 'shape' => 'Message', ], ], ], 'PropertyValidationExceptionPropertyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PropertyValidationExceptionProperty', ], ], 'PropertyValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'INVALID_FORMAT', 'UNIQUE_CONSTRAINT_VIOLATED', 'REFERENCED_RESOURCE_NOT_FOUND', 'RESOURCE_NAME_ALREADY_EXISTS', 'REQUIRED_PROPERTY_MISSING', 'NOT_SUPPORTED', ], ], 'PutUserStatusRequest' => [ 'type' => 'structure', 'required' => [ 'UserId', 'InstanceId', 'AgentStatusId', ], 'members' => [ 'UserId' => [ 'shape' => 'UserId', 'location' => 'uri', 'locationName' => 'UserId', ], 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'AgentStatusId' => [ 'shape' => 'AgentStatusId', ], ], ], 'PutUserStatusResponse' => [ 'type' => 'structure', 'members' => [], ], 'QualityMetrics' => [ 'type' => 'structure', 'members' => [ 'Agent' => [ 'shape' => 'AgentQualityMetrics', ], 'Customer' => [ 'shape' => 'CustomerQualityMetrics', ], ], ], 'Queue' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'CommonNameLength127', ], 'QueueArn' => [ 'shape' => 'ARN', ], 'QueueId' => [ 'shape' => 'QueueId', ], 'Description' => [ 'shape' => 'QueueDescription', ], 'OutboundCallerConfig' => [ 'shape' => 'OutboundCallerConfig', ], 'OutboundEmailConfig' => [ 'shape' => 'OutboundEmailConfig', ], 'HoursOfOperationId' => [ 'shape' => 'HoursOfOperationId', ], 'MaxContacts' => [ 'shape' => 'QueueMaxContacts', 'box' => true, ], 'Status' => [ 'shape' => 'QueueStatus', ], 'Tags' => [ 'shape' => 'TagMap', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedRegion' => [ 'shape' => 'RegionName', ], ], ], 'QueueDescription' => [ 'type' => 'string', 'max' => 250, 'min' => 1, ], 'QueueId' => [ 'type' => 'string', ], 'QueueIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'QueueId', ], 'max' => 100, 'min' => 0, ], 'QueueInfo' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'QueueId', ], 'EnqueueTimestamp' => [ 'shape' => 'timestamp', ], ], ], 'QueueInfoInput' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'QueueId', ], ], ], 'QueueMaxContacts' => [ 'type' => 'integer', 'min' => 0, ], 'QueueName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'QueuePriority' => [ 'type' => 'long', 'max' => 9223372036854775807, 'min' => 1, ], 'QueueQuickConnectConfig' => [ 'type' => 'structure', 'required' => [ 'QueueId', 'ContactFlowId', ], 'members' => [ 'QueueId' => [ 'shape' => 'QueueId', ], 'ContactFlowId' => [ 'shape' => 'ContactFlowId', ], ], ], 'QueueReference' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'QueueId', ], 'Arn' => [ 'shape' => 'ARN', ], ], ], 'QueueSearchConditionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'QueueSearchCriteria', ], ], 'QueueSearchCriteria' => [ 'type' => 'structure', 'members' => [ 'OrConditions' => [ 'shape' => 'QueueSearchConditionList', ], 'AndConditions' => [ 'shape' => 'QueueSearchConditionList', ], 'StringCondition' => [ 'shape' => 'StringCondition', ], 'QueueTypeCondition' => [ 'shape' => 'SearchableQueueType', ], ], ], 'QueueSearchFilter' => [ 'type' => 'structure', 'members' => [ 'TagFilter' => [ 'shape' => 'ControlPlaneTagFilter', ], ], ], 'QueueSearchSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Queue', ], ], 'QueueStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'QueueSummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'QueueId', ], 'Arn' => [ 'shape' => 'ARN', ], 'Name' => [ 'shape' => 'QueueName', ], 'QueueType' => [ 'shape' => 'QueueType', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedRegion' => [ 'shape' => 'RegionName', ], ], ], 'QueueSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'QueueSummary', ], ], 'QueueTimeAdjustmentSeconds' => [ 'type' => 'integer', ], 'QueueType' => [ 'type' => 'string', 'enum' => [ 'STANDARD', 'AGENT', ], ], 'QueueTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'QueueType', ], 'max' => 2, ], 'Queues' => [ 'type' => 'list', 'member' => [ 'shape' => 'QueueId', ], 'max' => 100, 'min' => 1, ], 'QuickConnect' => [ 'type' => 'structure', 'members' => [ 'QuickConnectARN' => [ 'shape' => 'ARN', ], 'QuickConnectId' => [ 'shape' => 'QuickConnectId', ], 'Name' => [ 'shape' => 'QuickConnectName', ], 'Description' => [ 'shape' => 'QuickConnectDescription', ], 'QuickConnectConfig' => [ 'shape' => 'QuickConnectConfig', ], 'Tags' => [ 'shape' => 'TagMap', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedRegion' => [ 'shape' => 'RegionName', ], ], ], 'QuickConnectConfig' => [ 'type' => 'structure', 'required' => [ 'QuickConnectType', ], 'members' => [ 'QuickConnectType' => [ 'shape' => 'QuickConnectType', ], 'UserConfig' => [ 'shape' => 'UserQuickConnectConfig', ], 'QueueConfig' => [ 'shape' => 'QueueQuickConnectConfig', ], 'PhoneConfig' => [ 'shape' => 'PhoneNumberQuickConnectConfig', ], ], ], 'QuickConnectDescription' => [ 'type' => 'string', 'max' => 250, 'min' => 1, ], 'QuickConnectId' => [ 'type' => 'string', ], 'QuickConnectName' => [ 'type' => 'string', 'max' => 127, 'min' => 1, ], 'QuickConnectSearchConditionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'QuickConnectSearchCriteria', ], ], 'QuickConnectSearchCriteria' => [ 'type' => 'structure', 'members' => [ 'OrConditions' => [ 'shape' => 'QuickConnectSearchConditionList', ], 'AndConditions' => [ 'shape' => 'QuickConnectSearchConditionList', ], 'StringCondition' => [ 'shape' => 'StringCondition', ], ], ], 'QuickConnectSearchFilter' => [ 'type' => 'structure', 'members' => [ 'TagFilter' => [ 'shape' => 'ControlPlaneTagFilter', ], ], ], 'QuickConnectSearchSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'QuickConnect', ], ], 'QuickConnectSummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'QuickConnectId', ], 'Arn' => [ 'shape' => 'ARN', ], 'Name' => [ 'shape' => 'QuickConnectName', ], 'QuickConnectType' => [ 'shape' => 'QuickConnectType', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedRegion' => [ 'shape' => 'RegionName', ], ], ], 'QuickConnectSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'QuickConnectSummary', ], ], 'QuickConnectType' => [ 'type' => 'string', 'enum' => [ 'USER', 'QUEUE', 'PHONE_NUMBER', ], ], 'QuickConnectTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'QuickConnectType', ], 'max' => 3, ], 'QuickConnectsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'QuickConnectId', ], 'max' => 50, 'min' => 1, ], 'Range' => [ 'type' => 'structure', 'members' => [ 'MinProficiencyLevel' => [ 'shape' => 'NullableProficiencyLevel', ], 'MaxProficiencyLevel' => [ 'shape' => 'NullableProficiencyLevel', ], ], ], 'ReadOnlyFieldInfo' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'TaskTemplateFieldIdentifier', ], ], ], 'ReadOnlyTaskTemplateFields' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReadOnlyFieldInfo', ], ], 'RealTimeContactAnalysisAttachment' => [ 'type' => 'structure', 'required' => [ 'AttachmentName', 'AttachmentId', ], 'members' => [ 'AttachmentName' => [ 'shape' => 'AttachmentName', ], 'ContentType' => [ 'shape' => 'ContentType', ], 'AttachmentId' => [ 'shape' => 'ArtifactId', ], 'Status' => [ 'shape' => 'ArtifactStatus', ], ], ], 'RealTimeContactAnalysisAttachments' => [ 'type' => 'list', 'member' => [ 'shape' => 'RealTimeContactAnalysisAttachment', ], 'max' => 10, ], 'RealTimeContactAnalysisCategoryDetails' => [ 'type' => 'structure', 'required' => [ 'PointsOfInterest', ], 'members' => [ 'PointsOfInterest' => [ 'shape' => 'RealTimeContactAnalysisPointsOfInterest', ], ], ], 'RealTimeContactAnalysisCategoryName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'RealTimeContactAnalysisCharacterInterval' => [ 'type' => 'structure', 'required' => [ 'BeginOffsetChar', 'EndOffsetChar', ], 'members' => [ 'BeginOffsetChar' => [ 'shape' => 'RealTimeContactAnalysisOffset', ], 'EndOffsetChar' => [ 'shape' => 'RealTimeContactAnalysisOffset', ], ], ], 'RealTimeContactAnalysisCharacterIntervals' => [ 'type' => 'list', 'member' => [ 'shape' => 'RealTimeContactAnalysisCharacterInterval', ], ], 'RealTimeContactAnalysisContentType' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'RealTimeContactAnalysisEventType' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'RealTimeContactAnalysisId256' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'RealTimeContactAnalysisIssueDetected' => [ 'type' => 'structure', 'required' => [ 'TranscriptItems', ], 'members' => [ 'TranscriptItems' => [ 'shape' => 'RealTimeContactAnalysisTranscriptItemsWithContent', ], ], ], 'RealTimeContactAnalysisIssuesDetected' => [ 'type' => 'list', 'member' => [ 'shape' => 'RealTimeContactAnalysisIssueDetected', ], ], 'RealTimeContactAnalysisMatchedDetails' => [ 'type' => 'map', 'key' => [ 'shape' => 'RealTimeContactAnalysisCategoryName', ], 'value' => [ 'shape' => 'RealTimeContactAnalysisCategoryDetails', ], 'max' => 150, 'min' => 0, ], 'RealTimeContactAnalysisOffset' => [ 'type' => 'integer', 'min' => 0, ], 'RealTimeContactAnalysisOutputType' => [ 'type' => 'string', 'enum' => [ 'Raw', 'Redacted', ], ], 'RealTimeContactAnalysisPointOfInterest' => [ 'type' => 'structure', 'members' => [ 'TranscriptItems' => [ 'shape' => 'RealTimeContactAnalysisTranscriptItemsWithCharacterOffsets', ], ], ], 'RealTimeContactAnalysisPointsOfInterest' => [ 'type' => 'list', 'member' => [ 'shape' => 'RealTimeContactAnalysisPointOfInterest', ], 'max' => 5, 'min' => 0, ], 'RealTimeContactAnalysisPostContactSummaryContent' => [ 'type' => 'string', 'max' => 1270, 'min' => 1, ], 'RealTimeContactAnalysisPostContactSummaryFailureCode' => [ 'type' => 'string', 'enum' => [ 'QUOTA_EXCEEDED', 'INSUFFICIENT_CONVERSATION_CONTENT', 'FAILED_SAFETY_GUIDELINES', 'INVALID_ANALYSIS_CONFIGURATION', 'INTERNAL_ERROR', ], ], 'RealTimeContactAnalysisPostContactSummaryStatus' => [ 'type' => 'string', 'enum' => [ 'FAILED', 'COMPLETED', ], ], 'RealTimeContactAnalysisSegmentAttachments' => [ 'type' => 'structure', 'required' => [ 'Id', 'ParticipantId', 'ParticipantRole', 'Attachments', 'Time', ], 'members' => [ 'Id' => [ 'shape' => 'RealTimeContactAnalysisId256', ], 'ParticipantId' => [ 'shape' => 'ParticipantId', ], 'ParticipantRole' => [ 'shape' => 'ParticipantRole', ], 'DisplayName' => [ 'shape' => 'DisplayName', ], 'Attachments' => [ 'shape' => 'RealTimeContactAnalysisAttachments', ], 'Time' => [ 'shape' => 'RealTimeContactAnalysisTimeData', ], ], ], 'RealTimeContactAnalysisSegmentCategories' => [ 'type' => 'structure', 'required' => [ 'MatchedDetails', ], 'members' => [ 'MatchedDetails' => [ 'shape' => 'RealTimeContactAnalysisMatchedDetails', ], ], ], 'RealTimeContactAnalysisSegmentEvent' => [ 'type' => 'structure', 'required' => [ 'Id', 'EventType', 'Time', ], 'members' => [ 'Id' => [ 'shape' => 'RealTimeContactAnalysisId256', ], 'ParticipantId' => [ 'shape' => 'ParticipantId', ], 'ParticipantRole' => [ 'shape' => 'ParticipantRole', ], 'DisplayName' => [ 'shape' => 'DisplayName', ], 'EventType' => [ 'shape' => 'RealTimeContactAnalysisEventType', ], 'Time' => [ 'shape' => 'RealTimeContactAnalysisTimeData', ], ], ], 'RealTimeContactAnalysisSegmentIssues' => [ 'type' => 'structure', 'required' => [ 'IssuesDetected', ], 'members' => [ 'IssuesDetected' => [ 'shape' => 'RealTimeContactAnalysisIssuesDetected', ], ], ], 'RealTimeContactAnalysisSegmentPostContactSummary' => [ 'type' => 'structure', 'required' => [ 'Status', ], 'members' => [ 'Content' => [ 'shape' => 'RealTimeContactAnalysisPostContactSummaryContent', ], 'Status' => [ 'shape' => 'RealTimeContactAnalysisPostContactSummaryStatus', ], 'FailureCode' => [ 'shape' => 'RealTimeContactAnalysisPostContactSummaryFailureCode', ], ], ], 'RealTimeContactAnalysisSegmentTranscript' => [ 'type' => 'structure', 'required' => [ 'Id', 'ParticipantId', 'ParticipantRole', 'Content', 'Time', ], 'members' => [ 'Id' => [ 'shape' => 'RealTimeContactAnalysisId256', ], 'ParticipantId' => [ 'shape' => 'ParticipantId', ], 'ParticipantRole' => [ 'shape' => 'ParticipantRole', ], 'DisplayName' => [ 'shape' => 'DisplayName', ], 'Content' => [ 'shape' => 'RealTimeContactAnalysisTranscriptContent', ], 'ContentType' => [ 'shape' => 'RealTimeContactAnalysisContentType', ], 'Time' => [ 'shape' => 'RealTimeContactAnalysisTimeData', ], 'Redaction' => [ 'shape' => 'RealTimeContactAnalysisTranscriptItemRedaction', ], 'Sentiment' => [ 'shape' => 'RealTimeContactAnalysisSentimentLabel', ], ], ], 'RealTimeContactAnalysisSegmentType' => [ 'type' => 'string', 'enum' => [ 'Transcript', 'Categories', 'Issues', 'Event', 'Attachments', 'PostContactSummary', ], ], 'RealTimeContactAnalysisSegmentTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'RealTimeContactAnalysisSegmentType', ], 'max' => 6, ], 'RealTimeContactAnalysisSentimentLabel' => [ 'type' => 'string', 'enum' => [ 'POSITIVE', 'NEGATIVE', 'NEUTRAL', ], ], 'RealTimeContactAnalysisStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'FAILED', 'COMPLETED', ], ], 'RealTimeContactAnalysisSupportedChannel' => [ 'type' => 'string', 'enum' => [ 'VOICE', 'CHAT', ], ], 'RealTimeContactAnalysisTimeData' => [ 'type' => 'structure', 'members' => [ 'AbsoluteTime' => [ 'shape' => 'RealTimeContactAnalysisTimeInstant', ], ], 'union' => true, ], 'RealTimeContactAnalysisTimeInstant' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'RealTimeContactAnalysisTranscriptContent' => [ 'type' => 'string', 'max' => 16384, 'min' => 1, ], 'RealTimeContactAnalysisTranscriptItemRedaction' => [ 'type' => 'structure', 'members' => [ 'CharacterOffsets' => [ 'shape' => 'RealTimeContactAnalysisCharacterIntervals', ], ], ], 'RealTimeContactAnalysisTranscriptItemWithCharacterOffsets' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'RealTimeContactAnalysisId256', ], 'CharacterOffsets' => [ 'shape' => 'RealTimeContactAnalysisCharacterInterval', ], ], ], 'RealTimeContactAnalysisTranscriptItemWithContent' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Content' => [ 'shape' => 'RealTimeContactAnalysisTranscriptContent', ], 'Id' => [ 'shape' => 'RealTimeContactAnalysisId256', ], 'CharacterOffsets' => [ 'shape' => 'RealTimeContactAnalysisCharacterInterval', ], ], ], 'RealTimeContactAnalysisTranscriptItemsWithCharacterOffsets' => [ 'type' => 'list', 'member' => [ 'shape' => 'RealTimeContactAnalysisTranscriptItemWithCharacterOffsets', ], 'max' => 10, 'min' => 0, ], 'RealTimeContactAnalysisTranscriptItemsWithContent' => [ 'type' => 'list', 'member' => [ 'shape' => 'RealTimeContactAnalysisTranscriptItemWithContent', ], ], 'RealtimeContactAnalysisSegment' => [ 'type' => 'structure', 'members' => [ 'Transcript' => [ 'shape' => 'RealTimeContactAnalysisSegmentTranscript', ], 'Categories' => [ 'shape' => 'RealTimeContactAnalysisSegmentCategories', ], 'Issues' => [ 'shape' => 'RealTimeContactAnalysisSegmentIssues', ], 'Event' => [ 'shape' => 'RealTimeContactAnalysisSegmentEvent', ], 'Attachments' => [ 'shape' => 'RealTimeContactAnalysisSegmentAttachments', ], 'PostContactSummary' => [ 'shape' => 'RealTimeContactAnalysisSegmentPostContactSummary', ], ], 'union' => true, ], 'RealtimeContactAnalysisSegments' => [ 'type' => 'list', 'member' => [ 'shape' => 'RealtimeContactAnalysisSegment', ], ], 'RecordingDeletionReason' => [ 'type' => 'string', ], 'RecordingInfo' => [ 'type' => 'structure', 'members' => [ 'StorageType' => [ 'shape' => 'StorageType', ], 'Location' => [ 'shape' => 'RecordingLocation', ], 'MediaStreamType' => [ 'shape' => 'MediaStreamType', ], 'ParticipantType' => [ 'shape' => 'ParticipantType', ], 'FragmentStartNumber' => [ 'shape' => 'FragmentNumber', ], 'FragmentStopNumber' => [ 'shape' => 'FragmentNumber', ], 'StartTimestamp' => [ 'shape' => 'timestamp', ], 'StopTimestamp' => [ 'shape' => 'timestamp', ], 'Status' => [ 'shape' => 'RecordingStatus', ], 'DeletionReason' => [ 'shape' => 'RecordingDeletionReason', ], ], ], 'RecordingLocation' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'RecordingStatus' => [ 'type' => 'string', 'enum' => [ 'AVAILABLE', 'DELETED', ], ], 'Recordings' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecordingInfo', ], ], 'Reference' => [ 'type' => 'structure', 'required' => [ 'Type', ], 'members' => [ 'Value' => [ 'shape' => 'ReferenceValue', ], 'Type' => [ 'shape' => 'ReferenceType', ], 'Status' => [ 'shape' => 'ReferenceStatus', ], 'Arn' => [ 'shape' => 'ReferenceArn', ], 'StatusReason' => [ 'shape' => 'ReferenceStatusReason', ], ], ], 'ReferenceArn' => [ 'type' => 'string', 'max' => 256, 'min' => 20, 'pattern' => '^[-:/A-Za-z0-9]+', ], 'ReferenceId' => [ 'type' => 'string', 'max' => 40, 'min' => 1, ], 'ReferenceKey' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, ], 'ReferenceStatus' => [ 'type' => 'string', 'enum' => [ 'AVAILABLE', 'DELETED', 'APPROVED', 'REJECTED', 'PROCESSING', 'FAILED', ], ], 'ReferenceStatusReason' => [ 'type' => 'string', 'max' => 100, 'min' => 0, ], 'ReferenceSummary' => [ 'type' => 'structure', 'members' => [ 'Url' => [ 'shape' => 'UrlReference', ], 'Attachment' => [ 'shape' => 'AttachmentReference', ], 'EmailMessage' => [ 'shape' => 'EmailMessageReference', ], 'String' => [ 'shape' => 'StringReference', ], 'Number' => [ 'shape' => 'NumberReference', ], 'Date' => [ 'shape' => 'DateReference', ], 'Email' => [ 'shape' => 'EmailReference', ], ], 'union' => true, ], 'ReferenceSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReferenceSummary', ], ], 'ReferenceType' => [ 'type' => 'string', 'enum' => [ 'URL', 'ATTACHMENT', 'CONTACT_ANALYSIS', 'NUMBER', 'STRING', 'DATE', 'EMAIL', 'EMAIL_MESSAGE', ], ], 'ReferenceTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReferenceType', ], 'max' => 6, ], 'ReferenceValue' => [ 'type' => 'string', 'max' => 4096, 'min' => 0, ], 'RefreshTokenDuration' => [ 'type' => 'integer', 'box' => true, 'max' => 720, 'min' => 360, ], 'RegionName' => [ 'type' => 'string', 'pattern' => '[a-z]{2}(-[a-z]+){1,2}(-[0-9])?', ], 'RegistrationId' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'RehydrationType' => [ 'type' => 'string', 'enum' => [ 'ENTIRE_PAST_SESSION', 'FROM_SEGMENT', ], ], 'ReleasePhoneNumberRequest' => [ 'type' => 'structure', 'required' => [ 'PhoneNumberId', ], 'members' => [ 'PhoneNumberId' => [ 'shape' => 'PhoneNumberId', 'location' => 'uri', 'locationName' => 'PhoneNumberId', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], ], ], 'ReplicateInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ReplicaRegion', 'ReplicaAlias', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceIdOrArn', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ReplicaRegion' => [ 'shape' => 'AwsRegion', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'ReplicaAlias' => [ 'shape' => 'DirectoryAlias', ], ], ], 'ReplicateInstanceResponse' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'InstanceId', ], 'Arn' => [ 'shape' => 'ARN', ], ], ], 'ReplicationConfiguration' => [ 'type' => 'structure', 'members' => [ 'ReplicationStatusSummaryList' => [ 'shape' => 'ReplicationStatusSummaryList', ], 'SourceRegion' => [ 'shape' => 'AwsRegion', ], 'GlobalSignInEndpoint' => [ 'shape' => 'GlobalSignInEndpoint', ], ], ], 'ReplicationStatusReason' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'ReplicationStatusSummary' => [ 'type' => 'structure', 'members' => [ 'Region' => [ 'shape' => 'AwsRegion', ], 'ReplicationStatus' => [ 'shape' => 'InstanceReplicationStatus', ], 'ReplicationStatusReason' => [ 'shape' => 'ReplicationStatusReason', ], ], ], 'ReplicationStatusSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplicationStatusSummary', ], 'max' => 11, 'min' => 0, ], 'RequestIdentifier' => [ 'type' => 'string', 'max' => 80, ], 'RequiredFieldInfo' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'TaskTemplateFieldIdentifier', ], ], ], 'RequiredTaskTemplateFields' => [ 'type' => 'list', 'member' => [ 'shape' => 'RequiredFieldInfo', ], ], 'ResourceArnOrId' => [ 'type' => 'string', 'max' => 250, 'min' => 1, ], 'ResourceConflictException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ResourceId' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'ResourceInUseException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'ResourceId' => [ 'shape' => 'ARN', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'ResourceNotReadyException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ResourceTagsSearchCriteria' => [ 'type' => 'structure', 'members' => [ 'TagSearchCondition' => [ 'shape' => 'TagSearchCondition', ], ], ], 'ResourceType' => [ 'type' => 'string', 'enum' => [ 'CONTACT', 'CONTACT_FLOW', 'INSTANCE', 'PARTICIPANT', 'HIERARCHY_LEVEL', 'HIERARCHY_GROUP', 'USER', 'PHONE_NUMBER', ], ], 'ResourceTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'ResourceVersion' => [ 'type' => 'long', 'min' => 1, ], 'ResumeContactRecordingRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactId', 'InitialContactId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'ContactId' => [ 'shape' => 'ContactId', ], 'InitialContactId' => [ 'shape' => 'ContactId', ], 'ContactRecordingType' => [ 'shape' => 'ContactRecordingType', ], ], ], 'ResumeContactRecordingResponse' => [ 'type' => 'structure', 'members' => [], ], 'ResumeContactRequest' => [ 'type' => 'structure', 'required' => [ 'ContactId', 'InstanceId', ], 'members' => [ 'ContactId' => [ 'shape' => 'ContactId', ], 'InstanceId' => [ 'shape' => 'InstanceId', ], 'ContactFlowId' => [ 'shape' => 'ContactFlowId', ], ], ], 'ResumeContactResponse' => [ 'type' => 'structure', 'members' => [], ], 'RoutingCriteria' => [ 'type' => 'structure', 'members' => [ 'Steps' => [ 'shape' => 'Steps', ], 'ActivationTimestamp' => [ 'shape' => 'timestamp', ], 'Index' => [ 'shape' => 'Index', ], ], ], 'RoutingCriteriaInput' => [ 'type' => 'structure', 'members' => [ 'Steps' => [ 'shape' => 'RoutingCriteriaInputSteps', ], ], ], 'RoutingCriteriaInputStep' => [ 'type' => 'structure', 'members' => [ 'Expiry' => [ 'shape' => 'RoutingCriteriaInputStepExpiry', ], 'Expression' => [ 'shape' => 'Expression', ], ], ], 'RoutingCriteriaInputStepExpiry' => [ 'type' => 'structure', 'members' => [ 'DurationInSeconds' => [ 'shape' => 'DurationInSeconds', ], ], ], 'RoutingCriteriaInputSteps' => [ 'type' => 'list', 'member' => [ 'shape' => 'RoutingCriteriaInputStep', ], ], 'RoutingCriteriaStepStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'INACTIVE', 'JOINED', 'EXPIRED', ], ], 'RoutingExpression' => [ 'type' => 'string', 'max' => 3000, 'min' => 1, ], 'RoutingExpressions' => [ 'type' => 'list', 'member' => [ 'shape' => 'RoutingExpression', ], 'max' => 50, ], 'RoutingProfile' => [ 'type' => 'structure', 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'Name' => [ 'shape' => 'RoutingProfileName', ], 'RoutingProfileArn' => [ 'shape' => 'ARN', ], 'RoutingProfileId' => [ 'shape' => 'RoutingProfileId', ], 'Description' => [ 'shape' => 'RoutingProfileDescription', ], 'MediaConcurrencies' => [ 'shape' => 'MediaConcurrencies', ], 'DefaultOutboundQueueId' => [ 'shape' => 'QueueId', ], 'Tags' => [ 'shape' => 'TagMap', ], 'NumberOfAssociatedQueues' => [ 'shape' => 'Long', ], 'NumberOfAssociatedUsers' => [ 'shape' => 'Long', ], 'AgentAvailabilityTimer' => [ 'shape' => 'AgentAvailabilityTimer', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedRegion' => [ 'shape' => 'RegionName', ], 'IsDefault' => [ 'shape' => 'Boolean', ], 'AssociatedQueueIds' => [ 'shape' => 'AssociatedQueueIdList', ], ], ], 'RoutingProfileDescription' => [ 'type' => 'string', 'max' => 250, 'min' => 1, ], 'RoutingProfileId' => [ 'type' => 'string', ], 'RoutingProfileList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RoutingProfile', ], ], 'RoutingProfileName' => [ 'type' => 'string', 'max' => 127, 'min' => 1, ], 'RoutingProfileQueueConfig' => [ 'type' => 'structure', 'required' => [ 'QueueReference', 'Priority', 'Delay', ], 'members' => [ 'QueueReference' => [ 'shape' => 'RoutingProfileQueueReference', ], 'Priority' => [ 'shape' => 'Priority', 'box' => true, ], 'Delay' => [ 'shape' => 'Delay', 'box' => true, ], ], ], 'RoutingProfileQueueConfigList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RoutingProfileQueueConfig', ], 'max' => 10, 'min' => 1, ], 'RoutingProfileQueueConfigSummary' => [ 'type' => 'structure', 'required' => [ 'QueueId', 'QueueArn', 'QueueName', 'Priority', 'Delay', 'Channel', ], 'members' => [ 'QueueId' => [ 'shape' => 'QueueId', ], 'QueueArn' => [ 'shape' => 'ARN', ], 'QueueName' => [ 'shape' => 'QueueName', ], 'Priority' => [ 'shape' => 'Priority', ], 'Delay' => [ 'shape' => 'Delay', ], 'Channel' => [ 'shape' => 'Channel', ], ], ], 'RoutingProfileQueueConfigSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RoutingProfileQueueConfigSummary', ], ], 'RoutingProfileQueueReference' => [ 'type' => 'structure', 'required' => [ 'QueueId', 'Channel', ], 'members' => [ 'QueueId' => [ 'shape' => 'QueueId', ], 'Channel' => [ 'shape' => 'Channel', ], ], ], 'RoutingProfileQueueReferenceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RoutingProfileQueueReference', ], ], 'RoutingProfileReference' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'RoutingProfileId', ], 'Arn' => [ 'shape' => 'ARN', ], ], ], 'RoutingProfileSearchConditionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RoutingProfileSearchCriteria', ], ], 'RoutingProfileSearchCriteria' => [ 'type' => 'structure', 'members' => [ 'OrConditions' => [ 'shape' => 'RoutingProfileSearchConditionList', ], 'AndConditions' => [ 'shape' => 'RoutingProfileSearchConditionList', ], 'StringCondition' => [ 'shape' => 'StringCondition', ], ], ], 'RoutingProfileSearchFilter' => [ 'type' => 'structure', 'members' => [ 'TagFilter' => [ 'shape' => 'ControlPlaneTagFilter', ], ], ], 'RoutingProfileSummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'RoutingProfileId', ], 'Arn' => [ 'shape' => 'ARN', ], 'Name' => [ 'shape' => 'RoutingProfileName', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedRegion' => [ 'shape' => 'RegionName', ], ], ], 'RoutingProfileSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RoutingProfileSummary', ], ], 'RoutingProfiles' => [ 'type' => 'list', 'member' => [ 'shape' => 'RoutingProfileId', ], 'max' => 100, 'min' => 1, ], 'Rule' => [ 'type' => 'structure', 'required' => [ 'Name', 'RuleId', 'RuleArn', 'TriggerEventSource', 'Function', 'Actions', 'PublishStatus', 'CreatedTime', 'LastUpdatedTime', 'LastUpdatedBy', ], 'members' => [ 'Name' => [ 'shape' => 'RuleName', ], 'RuleId' => [ 'shape' => 'RuleId', ], 'RuleArn' => [ 'shape' => 'ARN', ], 'TriggerEventSource' => [ 'shape' => 'RuleTriggerEventSource', ], 'Function' => [ 'shape' => 'RuleFunction', ], 'Actions' => [ 'shape' => 'RuleActions', ], 'PublishStatus' => [ 'shape' => 'RulePublishStatus', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'LastUpdatedTime' => [ 'shape' => 'Timestamp', ], 'LastUpdatedBy' => [ 'shape' => 'ARN', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'RuleAction' => [ 'type' => 'structure', 'required' => [ 'ActionType', ], 'members' => [ 'ActionType' => [ 'shape' => 'ActionType', ], 'TaskAction' => [ 'shape' => 'TaskActionDefinition', ], 'EventBridgeAction' => [ 'shape' => 'EventBridgeActionDefinition', ], 'AssignContactCategoryAction' => [ 'shape' => 'AssignContactCategoryActionDefinition', ], 'SendNotificationAction' => [ 'shape' => 'SendNotificationActionDefinition', ], 'CreateCaseAction' => [ 'shape' => 'CreateCaseActionDefinition', ], 'UpdateCaseAction' => [ 'shape' => 'UpdateCaseActionDefinition', ], 'AssignSlaAction' => [ 'shape' => 'AssignSlaActionDefinition', ], 'EndAssociatedTasksAction' => [ 'shape' => 'EndAssociatedTasksActionDefinition', ], 'SubmitAutoEvaluationAction' => [ 'shape' => 'SubmitAutoEvaluationActionDefinition', ], ], ], 'RuleActions' => [ 'type' => 'list', 'member' => [ 'shape' => 'RuleAction', ], ], 'RuleFunction' => [ 'type' => 'string', ], 'RuleId' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'RuleName' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '^[0-9a-zA-Z._-]+', ], 'RulePublishStatus' => [ 'type' => 'string', 'enum' => [ 'DRAFT', 'PUBLISHED', ], ], 'RuleSummary' => [ 'type' => 'structure', 'required' => [ 'Name', 'RuleId', 'RuleArn', 'EventSourceName', 'PublishStatus', 'ActionSummaries', 'CreatedTime', 'LastUpdatedTime', ], 'members' => [ 'Name' => [ 'shape' => 'RuleName', ], 'RuleId' => [ 'shape' => 'RuleId', ], 'RuleArn' => [ 'shape' => 'ARN', ], 'EventSourceName' => [ 'shape' => 'EventSourceName', ], 'PublishStatus' => [ 'shape' => 'RulePublishStatus', ], 'ActionSummaries' => [ 'shape' => 'ActionSummaries', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'LastUpdatedTime' => [ 'shape' => 'Timestamp', ], ], ], 'RuleSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RuleSummary', ], ], 'RuleTriggerEventSource' => [ 'type' => 'structure', 'required' => [ 'EventSourceName', ], 'members' => [ 'EventSourceName' => [ 'shape' => 'EventSourceName', ], 'IntegrationAssociationId' => [ 'shape' => 'IntegrationAssociationId', ], ], ], 'S3Config' => [ 'type' => 'structure', 'required' => [ 'BucketName', 'BucketPrefix', ], 'members' => [ 'BucketName' => [ 'shape' => 'BucketName', ], 'BucketPrefix' => [ 'shape' => 'Prefix', ], 'EncryptionConfig' => [ 'shape' => 'EncryptionConfig', ], ], ], 'S3Uri' => [ 'type' => 'string', 'max' => 2000, 'min' => 1, 'pattern' => 's3://\\S+/.+|https://\\\\S+\\\\.s3\\\\.\\\\S+\\\\.amazonaws\\\\.com/\\\\S+', ], 'ScreenShareCapability' => [ 'type' => 'string', 'enum' => [ 'SEND', ], ], 'SearchAgentStatusesRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'NextToken' => [ 'shape' => 'NextToken2500', ], 'MaxResults' => [ 'shape' => 'MaxResult100', 'box' => true, ], 'SearchFilter' => [ 'shape' => 'AgentStatusSearchFilter', ], 'SearchCriteria' => [ 'shape' => 'AgentStatusSearchCriteria', ], ], ], 'SearchAgentStatusesResponse' => [ 'type' => 'structure', 'members' => [ 'AgentStatuses' => [ 'shape' => 'AgentStatusList', ], 'NextToken' => [ 'shape' => 'NextToken2500', ], 'ApproximateTotalCount' => [ 'shape' => 'ApproximateTotalCount', ], ], ], 'SearchAvailablePhoneNumbersRequest' => [ 'type' => 'structure', 'required' => [ 'PhoneNumberCountryCode', 'PhoneNumberType', ], 'members' => [ 'TargetArn' => [ 'shape' => 'ARN', ], 'InstanceId' => [ 'shape' => 'InstanceId', ], 'PhoneNumberCountryCode' => [ 'shape' => 'PhoneNumberCountryCode', ], 'PhoneNumberType' => [ 'shape' => 'PhoneNumberType', ], 'PhoneNumberPrefix' => [ 'shape' => 'PhoneNumberPrefix', ], 'MaxResults' => [ 'shape' => 'MaxResult10', 'box' => true, ], 'NextToken' => [ 'shape' => 'LargeNextToken', ], ], ], 'SearchAvailablePhoneNumbersResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'LargeNextToken', ], 'AvailableNumbersList' => [ 'shape' => 'AvailableNumbersList', ], ], ], 'SearchContactFlowModulesRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'NextToken' => [ 'shape' => 'NextToken2500', ], 'MaxResults' => [ 'shape' => 'MaxResult100', 'box' => true, ], 'SearchFilter' => [ 'shape' => 'ContactFlowModuleSearchFilter', ], 'SearchCriteria' => [ 'shape' => 'ContactFlowModuleSearchCriteria', ], ], ], 'SearchContactFlowModulesResponse' => [ 'type' => 'structure', 'members' => [ 'ContactFlowModules' => [ 'shape' => 'ContactFlowModuleSearchSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken2500', ], 'ApproximateTotalCount' => [ 'shape' => 'ApproximateTotalCount', ], ], ], 'SearchContactFlowsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'NextToken' => [ 'shape' => 'NextToken2500', ], 'MaxResults' => [ 'shape' => 'MaxResult100', 'box' => true, ], 'SearchFilter' => [ 'shape' => 'ContactFlowSearchFilter', ], 'SearchCriteria' => [ 'shape' => 'ContactFlowSearchCriteria', ], ], ], 'SearchContactFlowsResponse' => [ 'type' => 'structure', 'members' => [ 'ContactFlows' => [ 'shape' => 'ContactFlowSearchSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken2500', ], 'ApproximateTotalCount' => [ 'shape' => 'ApproximateTotalCount', ], ], ], 'SearchContactsMatchType' => [ 'type' => 'string', 'enum' => [ 'MATCH_ALL', 'MATCH_ANY', ], ], 'SearchContactsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'TimeRange', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'TimeRange' => [ 'shape' => 'SearchContactsTimeRange', ], 'SearchCriteria' => [ 'shape' => 'SearchCriteria', ], 'MaxResults' => [ 'shape' => 'MaxResult100', 'box' => true, ], 'NextToken' => [ 'shape' => 'LargeNextToken', ], 'Sort' => [ 'shape' => 'Sort', ], ], ], 'SearchContactsResponse' => [ 'type' => 'structure', 'required' => [ 'Contacts', ], 'members' => [ 'Contacts' => [ 'shape' => 'Contacts', ], 'NextToken' => [ 'shape' => 'LargeNextToken', ], 'TotalCount' => [ 'shape' => 'TotalCount', ], ], ], 'SearchContactsTimeRange' => [ 'type' => 'structure', 'required' => [ 'Type', 'StartTime', 'EndTime', ], 'members' => [ 'Type' => [ 'shape' => 'SearchContactsTimeRangeType', ], 'StartTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], ], ], 'SearchContactsTimeRangeType' => [ 'type' => 'string', 'enum' => [ 'INITIATION_TIMESTAMP', 'SCHEDULED_TIMESTAMP', 'CONNECTED_TO_AGENT_TIMESTAMP', 'DISCONNECT_TIMESTAMP', ], ], 'SearchCriteria' => [ 'type' => 'structure', 'members' => [ 'AgentIds' => [ 'shape' => 'AgentResourceIdList', ], 'AgentHierarchyGroups' => [ 'shape' => 'AgentHierarchyGroups', ], 'Channels' => [ 'shape' => 'ChannelList', ], 'ContactAnalysis' => [ 'shape' => 'ContactAnalysis', ], 'InitiationMethods' => [ 'shape' => 'InitiationMethodList', ], 'QueueIds' => [ 'shape' => 'QueueIdList', ], 'SearchableContactAttributes' => [ 'shape' => 'SearchableContactAttributes', ], 'SearchableSegmentAttributes' => [ 'shape' => 'SearchableSegmentAttributes', ], ], ], 'SearchEmailAddressesRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'MaxResults' => [ 'shape' => 'MaxResult100', ], 'NextToken' => [ 'shape' => 'NextToken2500', ], 'SearchCriteria' => [ 'shape' => 'EmailAddressSearchCriteria', ], 'SearchFilter' => [ 'shape' => 'EmailAddressSearchFilter', ], ], ], 'SearchEmailAddressesResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'EmailAddresses' => [ 'shape' => 'EmailAddressList', ], 'ApproximateTotalCount' => [ 'shape' => 'ApproximateTotalCount', ], ], ], 'SearchHoursOfOperationOverridesRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'NextToken' => [ 'shape' => 'NextToken2500', ], 'MaxResults' => [ 'shape' => 'MaxResult100', 'box' => true, ], 'SearchFilter' => [ 'shape' => 'HoursOfOperationSearchFilter', ], 'SearchCriteria' => [ 'shape' => 'HoursOfOperationOverrideSearchCriteria', ], ], ], 'SearchHoursOfOperationOverridesResponse' => [ 'type' => 'structure', 'members' => [ 'HoursOfOperationOverrides' => [ 'shape' => 'HoursOfOperationOverrideList', ], 'NextToken' => [ 'shape' => 'NextToken2500', ], 'ApproximateTotalCount' => [ 'shape' => 'ApproximateTotalCount', ], ], ], 'SearchHoursOfOperationsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'NextToken' => [ 'shape' => 'NextToken2500', ], 'MaxResults' => [ 'shape' => 'MaxResult100', 'box' => true, ], 'SearchFilter' => [ 'shape' => 'HoursOfOperationSearchFilter', ], 'SearchCriteria' => [ 'shape' => 'HoursOfOperationSearchCriteria', ], ], ], 'SearchHoursOfOperationsResponse' => [ 'type' => 'structure', 'members' => [ 'HoursOfOperations' => [ 'shape' => 'HoursOfOperationList', ], 'NextToken' => [ 'shape' => 'NextToken2500', ], 'ApproximateTotalCount' => [ 'shape' => 'ApproximateTotalCount', ], ], ], 'SearchPredefinedAttributesRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'NextToken' => [ 'shape' => 'NextToken2500', ], 'MaxResults' => [ 'shape' => 'MaxResult100', 'box' => true, ], 'SearchCriteria' => [ 'shape' => 'PredefinedAttributeSearchCriteria', ], ], ], 'SearchPredefinedAttributesResponse' => [ 'type' => 'structure', 'members' => [ 'PredefinedAttributes' => [ 'shape' => 'PredefinedAttributeSearchSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken2500', ], 'ApproximateTotalCount' => [ 'shape' => 'ApproximateTotalCount', ], ], ], 'SearchPromptsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'NextToken' => [ 'shape' => 'NextToken2500', ], 'MaxResults' => [ 'shape' => 'MaxResult100', 'box' => true, ], 'SearchFilter' => [ 'shape' => 'PromptSearchFilter', ], 'SearchCriteria' => [ 'shape' => 'PromptSearchCriteria', ], ], ], 'SearchPromptsResponse' => [ 'type' => 'structure', 'members' => [ 'Prompts' => [ 'shape' => 'PromptList', ], 'NextToken' => [ 'shape' => 'NextToken2500', ], 'ApproximateTotalCount' => [ 'shape' => 'ApproximateTotalCount', ], ], ], 'SearchQueuesRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'NextToken' => [ 'shape' => 'NextToken2500', ], 'MaxResults' => [ 'shape' => 'MaxResult500', 'box' => true, ], 'SearchFilter' => [ 'shape' => 'QueueSearchFilter', ], 'SearchCriteria' => [ 'shape' => 'QueueSearchCriteria', ], ], ], 'SearchQueuesResponse' => [ 'type' => 'structure', 'members' => [ 'Queues' => [ 'shape' => 'QueueSearchSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken2500', ], 'ApproximateTotalCount' => [ 'shape' => 'ApproximateTotalCount', ], ], ], 'SearchQuickConnectsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'NextToken' => [ 'shape' => 'NextToken2500', ], 'MaxResults' => [ 'shape' => 'MaxResult100', 'box' => true, ], 'SearchFilter' => [ 'shape' => 'QuickConnectSearchFilter', ], 'SearchCriteria' => [ 'shape' => 'QuickConnectSearchCriteria', ], ], ], 'SearchQuickConnectsResponse' => [ 'type' => 'structure', 'members' => [ 'QuickConnects' => [ 'shape' => 'QuickConnectSearchSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken2500', ], 'ApproximateTotalCount' => [ 'shape' => 'ApproximateTotalCount', ], ], ], 'SearchResourceTagsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceIdOrArn', ], 'ResourceTypes' => [ 'shape' => 'ResourceTypeList', ], 'NextToken' => [ 'shape' => 'NextToken2500', ], 'MaxResults' => [ 'shape' => 'MaxResult100', 'box' => true, ], 'SearchCriteria' => [ 'shape' => 'ResourceTagsSearchCriteria', ], ], ], 'SearchResourceTagsResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagsList', ], 'NextToken' => [ 'shape' => 'NextToken2500', ], ], ], 'SearchRoutingProfilesRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'NextToken' => [ 'shape' => 'NextToken2500', ], 'MaxResults' => [ 'shape' => 'MaxResult500', 'box' => true, ], 'SearchFilter' => [ 'shape' => 'RoutingProfileSearchFilter', ], 'SearchCriteria' => [ 'shape' => 'RoutingProfileSearchCriteria', ], ], ], 'SearchRoutingProfilesResponse' => [ 'type' => 'structure', 'members' => [ 'RoutingProfiles' => [ 'shape' => 'RoutingProfileList', ], 'NextToken' => [ 'shape' => 'NextToken2500', ], 'ApproximateTotalCount' => [ 'shape' => 'ApproximateTotalCount', ], ], ], 'SearchSecurityProfilesRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'NextToken' => [ 'shape' => 'NextToken2500', ], 'MaxResults' => [ 'shape' => 'MaxResult100', 'box' => true, ], 'SearchCriteria' => [ 'shape' => 'SecurityProfileSearchCriteria', ], 'SearchFilter' => [ 'shape' => 'SecurityProfilesSearchFilter', ], ], ], 'SearchSecurityProfilesResponse' => [ 'type' => 'structure', 'members' => [ 'SecurityProfiles' => [ 'shape' => 'SecurityProfilesSearchSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken2500', ], 'ApproximateTotalCount' => [ 'shape' => 'ApproximateTotalCount', ], ], ], 'SearchText' => [ 'type' => 'string', 'max' => 128, 'sensitive' => true, ], 'SearchTextList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SearchText', ], 'max' => 100, 'min' => 0, ], 'SearchUserHierarchyGroupsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'NextToken' => [ 'shape' => 'NextToken2500', ], 'MaxResults' => [ 'shape' => 'MaxResult100', 'box' => true, ], 'SearchFilter' => [ 'shape' => 'UserHierarchyGroupSearchFilter', ], 'SearchCriteria' => [ 'shape' => 'UserHierarchyGroupSearchCriteria', ], ], ], 'SearchUserHierarchyGroupsResponse' => [ 'type' => 'structure', 'members' => [ 'UserHierarchyGroups' => [ 'shape' => 'UserHierarchyGroupList', ], 'NextToken' => [ 'shape' => 'NextToken2500', ], 'ApproximateTotalCount' => [ 'shape' => 'ApproximateTotalCount', ], ], ], 'SearchUsersRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'NextToken' => [ 'shape' => 'NextToken2500', ], 'MaxResults' => [ 'shape' => 'MaxResult500', 'box' => true, ], 'SearchFilter' => [ 'shape' => 'UserSearchFilter', ], 'SearchCriteria' => [ 'shape' => 'UserSearchCriteria', ], ], ], 'SearchUsersResponse' => [ 'type' => 'structure', 'members' => [ 'Users' => [ 'shape' => 'UserSearchSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken2500', ], 'ApproximateTotalCount' => [ 'shape' => 'ApproximateTotalCount', ], ], ], 'SearchVocabulariesRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'MaxResults' => [ 'shape' => 'MaxResult100', ], 'NextToken' => [ 'shape' => 'VocabularyNextToken', ], 'State' => [ 'shape' => 'VocabularyState', ], 'NameStartsWith' => [ 'shape' => 'VocabularyName', ], 'LanguageCode' => [ 'shape' => 'VocabularyLanguageCode', ], ], ], 'SearchVocabulariesResponse' => [ 'type' => 'structure', 'members' => [ 'VocabularySummaryList' => [ 'shape' => 'VocabularySummaryList', ], 'NextToken' => [ 'shape' => 'VocabularyNextToken', ], ], ], 'SearchableContactAttributeKey' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'sensitive' => true, ], 'SearchableContactAttributeValue' => [ 'type' => 'string', 'max' => 100, 'min' => 0, 'sensitive' => true, ], 'SearchableContactAttributeValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SearchableContactAttributeValue', ], 'max' => 20, 'min' => 0, ], 'SearchableContactAttributes' => [ 'type' => 'structure', 'required' => [ 'Criteria', ], 'members' => [ 'Criteria' => [ 'shape' => 'SearchableContactAttributesCriteriaList', ], 'MatchType' => [ 'shape' => 'SearchContactsMatchType', ], ], ], 'SearchableContactAttributesCriteria' => [ 'type' => 'structure', 'required' => [ 'Key', 'Values', ], 'members' => [ 'Key' => [ 'shape' => 'SearchableContactAttributeKey', ], 'Values' => [ 'shape' => 'SearchableContactAttributeValueList', ], ], ], 'SearchableContactAttributesCriteriaList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SearchableContactAttributesCriteria', ], 'max' => 15, 'min' => 0, ], 'SearchableQueueType' => [ 'type' => 'string', 'enum' => [ 'STANDARD', ], ], 'SearchableSegmentAttributeKey' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'sensitive' => true, ], 'SearchableSegmentAttributeValue' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'sensitive' => true, ], 'SearchableSegmentAttributeValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SearchableSegmentAttributeValue', ], 'max' => 20, 'min' => 1, 'sensitive' => true, ], 'SearchableSegmentAttributes' => [ 'type' => 'structure', 'required' => [ 'Criteria', ], 'members' => [ 'Criteria' => [ 'shape' => 'SearchableSegmentAttributesCriteriaList', ], 'MatchType' => [ 'shape' => 'SearchContactsMatchType', ], ], ], 'SearchableSegmentAttributesCriteria' => [ 'type' => 'structure', 'required' => [ 'Key', 'Values', ], 'members' => [ 'Key' => [ 'shape' => 'SearchableSegmentAttributeKey', ], 'Values' => [ 'shape' => 'SearchableSegmentAttributeValueList', ], ], ], 'SearchableSegmentAttributesCriteriaList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SearchableSegmentAttributesCriteria', ], 'max' => 15, 'min' => 1, ], 'SecurityKey' => [ 'type' => 'structure', 'members' => [ 'AssociationId' => [ 'shape' => 'AssociationId', ], 'Key' => [ 'shape' => 'PEM', ], 'CreationTime' => [ 'shape' => 'timestamp', ], ], ], 'SecurityKeysList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityKey', ], ], 'SecurityProfile' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'SecurityProfileId', ], 'OrganizationResourceId' => [ 'shape' => 'InstanceId', ], 'Arn' => [ 'shape' => 'ARN', ], 'SecurityProfileName' => [ 'shape' => 'SecurityProfileName', ], 'Description' => [ 'shape' => 'SecurityProfileDescription', ], 'Tags' => [ 'shape' => 'TagMap', ], 'AllowedAccessControlTags' => [ 'shape' => 'AllowedAccessControlTags', ], 'TagRestrictedResources' => [ 'shape' => 'TagRestrictedResourceList', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedRegion' => [ 'shape' => 'RegionName', ], 'HierarchyRestrictedResources' => [ 'shape' => 'HierarchyRestrictedResourceList', ], 'AllowedAccessControlHierarchyGroupId' => [ 'shape' => 'HierarchyGroupId', ], ], ], 'SecurityProfileDescription' => [ 'type' => 'string', 'max' => 250, ], 'SecurityProfileId' => [ 'type' => 'string', ], 'SecurityProfileIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityProfileId', ], 'max' => 10, 'min' => 1, ], 'SecurityProfileName' => [ 'type' => 'string', ], 'SecurityProfilePermission' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'SecurityProfilePolicyKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'SecurityProfilePolicyValue' => [ 'type' => 'string', 'max' => 256, ], 'SecurityProfileSearchConditionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityProfileSearchCriteria', ], ], 'SecurityProfileSearchCriteria' => [ 'type' => 'structure', 'members' => [ 'OrConditions' => [ 'shape' => 'SecurityProfileSearchConditionList', ], 'AndConditions' => [ 'shape' => 'SecurityProfileSearchConditionList', ], 'StringCondition' => [ 'shape' => 'StringCondition', ], ], ], 'SecurityProfileSearchSummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'SecurityProfileId', ], 'OrganizationResourceId' => [ 'shape' => 'InstanceId', ], 'Arn' => [ 'shape' => 'ARN', ], 'SecurityProfileName' => [ 'shape' => 'SecurityProfileName', ], 'Description' => [ 'shape' => 'SecurityProfileDescription', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'SecurityProfileSummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'SecurityProfileId', ], 'Arn' => [ 'shape' => 'ARN', ], 'Name' => [ 'shape' => 'SecurityProfileName', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedRegion' => [ 'shape' => 'RegionName', ], ], ], 'SecurityProfileSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityProfileSummary', ], ], 'SecurityProfilesSearchFilter' => [ 'type' => 'structure', 'members' => [ 'TagFilter' => [ 'shape' => 'ControlPlaneTagFilter', ], ], ], 'SecurityProfilesSearchSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityProfileSearchSummary', ], ], 'SecurityToken' => [ 'type' => 'string', 'sensitive' => true, ], 'SegmentAttributeName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'SegmentAttributeValue' => [ 'type' => 'structure', 'members' => [ 'ValueString' => [ 'shape' => 'SegmentAttributeValueString', ], 'ValueMap' => [ 'shape' => 'SegmentAttributeValueMap', ], 'ValueInteger' => [ 'shape' => 'SegmentAttributeValueInteger', ], ], ], 'SegmentAttributeValueInteger' => [ 'type' => 'integer', ], 'SegmentAttributeValueMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'SegmentAttributeName', ], 'value' => [ 'shape' => 'SegmentAttributeValue', ], ], 'SegmentAttributeValueString' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'SegmentAttributes' => [ 'type' => 'map', 'key' => [ 'shape' => 'SegmentAttributeName', ], 'value' => [ 'shape' => 'SegmentAttributeValue', ], ], 'SendChatIntegrationEventRequest' => [ 'type' => 'structure', 'required' => [ 'SourceId', 'DestinationId', 'Event', ], 'members' => [ 'SourceId' => [ 'shape' => 'SourceId', ], 'DestinationId' => [ 'shape' => 'DestinationId', ], 'Subtype' => [ 'shape' => 'Subtype', ], 'Event' => [ 'shape' => 'ChatEvent', ], 'NewSessionDetails' => [ 'shape' => 'NewSessionDetails', ], ], ], 'SendChatIntegrationEventResponse' => [ 'type' => 'structure', 'members' => [ 'InitialContactId' => [ 'shape' => 'ContactId', ], 'NewChatCreated' => [ 'shape' => 'NewChatCreated', ], ], ], 'SendNotificationActionDefinition' => [ 'type' => 'structure', 'required' => [ 'DeliveryMethod', 'Content', 'ContentType', 'Recipient', ], 'members' => [ 'DeliveryMethod' => [ 'shape' => 'NotificationDeliveryType', ], 'Subject' => [ 'shape' => 'Subject', ], 'Content' => [ 'shape' => 'Content', ], 'ContentType' => [ 'shape' => 'NotificationContentType', ], 'Recipient' => [ 'shape' => 'NotificationRecipientType', ], ], ], 'SendOutboundEmailRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'FromEmailAddress', 'DestinationEmailAddress', 'EmailMessage', 'TrafficType', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'FromEmailAddress' => [ 'shape' => 'EmailAddressInfo', ], 'DestinationEmailAddress' => [ 'shape' => 'EmailAddressInfo', ], 'AdditionalRecipients' => [ 'shape' => 'OutboundAdditionalRecipients', ], 'EmailMessage' => [ 'shape' => 'OutboundEmailContent', ], 'TrafficType' => [ 'shape' => 'TrafficType', ], 'SourceCampaign' => [ 'shape' => 'SourceCampaign', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'SendOutboundEmailResponse' => [ 'type' => 'structure', 'members' => [], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], 'Reason' => [ 'shape' => 'ServiceQuotaExceededExceptionReason', ], ], 'error' => [ 'httpStatusCode' => 402, ], 'exception' => true, ], 'ServiceQuotaExceededExceptionReason' => [ 'type' => 'structure', 'members' => [ 'AttachedFileServiceQuotaExceededExceptionReason' => [ 'shape' => 'AttachedFileServiceQuotaExceededExceptionReason', ], ], 'union' => true, ], 'SignInConfig' => [ 'type' => 'structure', 'required' => [ 'Distributions', ], 'members' => [ 'Distributions' => [ 'shape' => 'SignInDistributionList', ], ], ], 'SignInDistribution' => [ 'type' => 'structure', 'required' => [ 'Region', 'Enabled', ], 'members' => [ 'Region' => [ 'shape' => 'AwsRegion', ], 'Enabled' => [ 'shape' => 'Boolean', ], ], ], 'SignInDistributionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SignInDistribution', ], ], 'SingleSelectOptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'TaskTemplateSingleSelectOption', ], ], 'SingleSelectQuestionRuleCategoryAutomation' => [ 'type' => 'structure', 'required' => [ 'Category', 'Condition', 'OptionRefId', ], 'members' => [ 'Category' => [ 'shape' => 'SingleSelectQuestionRuleCategoryAutomationLabel', ], 'Condition' => [ 'shape' => 'SingleSelectQuestionRuleCategoryAutomationCondition', ], 'OptionRefId' => [ 'shape' => 'ReferenceId', ], ], ], 'SingleSelectQuestionRuleCategoryAutomationCondition' => [ 'type' => 'string', 'enum' => [ 'PRESENT', 'NOT_PRESENT', ], ], 'SingleSelectQuestionRuleCategoryAutomationLabel' => [ 'type' => 'string', 'max' => 50, 'min' => 1, ], 'SlaAssignmentType' => [ 'type' => 'string', 'enum' => [ 'CASES', ], ], 'SlaFieldValueUnionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FieldValueUnion', ], 'max' => 1, ], 'SlaName' => [ 'type' => 'string', 'max' => 500, 'min' => 1, 'pattern' => '^.*[\\S]$', ], 'SlaType' => [ 'type' => 'string', 'enum' => [ 'CaseField', ], ], 'SnapshotVersion' => [ 'type' => 'string', ], 'Sort' => [ 'type' => 'structure', 'required' => [ 'FieldName', 'Order', ], 'members' => [ 'FieldName' => [ 'shape' => 'SortableFieldName', ], 'Order' => [ 'shape' => 'SortOrder', ], ], ], 'SortOrder' => [ 'type' => 'string', 'enum' => [ 'ASCENDING', 'DESCENDING', ], ], 'SortableFieldName' => [ 'type' => 'string', 'enum' => [ 'INITIATION_TIMESTAMP', 'SCHEDULED_TIMESTAMP', 'CONNECTED_TO_AGENT_TIMESTAMP', 'DISCONNECT_TIMESTAMP', 'INITIATION_METHOD', 'CHANNEL', ], ], 'SourceApplicationName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^[a-zA-Z0-9_ -]+$', ], 'SourceCampaign' => [ 'type' => 'structure', 'members' => [ 'CampaignId' => [ 'shape' => 'CampaignId', ], 'OutboundRequestId' => [ 'shape' => 'OutboundRequestId', ], ], ], 'SourceId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'SourceType' => [ 'type' => 'string', 'enum' => [ 'SALESFORCE', 'ZENDESK', 'CASES', ], ], 'StartAttachedFileUploadRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'FileName', 'FileSizeInBytes', 'FileUseCaseType', 'AssociatedResourceArn', ], 'members' => [ 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'FileName' => [ 'shape' => 'FileName', ], 'FileSizeInBytes' => [ 'shape' => 'FileSizeInBytes', 'box' => true, ], 'UrlExpiryInSeconds' => [ 'shape' => 'URLExpiryInSeconds', ], 'FileUseCaseType' => [ 'shape' => 'FileUseCaseType', ], 'AssociatedResourceArn' => [ 'shape' => 'ARN', 'location' => 'querystring', 'locationName' => 'associatedResourceArn', ], 'CreatedBy' => [ 'shape' => 'CreatedByInfo', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'StartAttachedFileUploadResponse' => [ 'type' => 'structure', 'members' => [ 'FileArn' => [ 'shape' => 'ARN', ], 'FileId' => [ 'shape' => 'FileId', ], 'CreationTime' => [ 'shape' => 'ISO8601Datetime', ], 'FileStatus' => [ 'shape' => 'FileStatusType', ], 'CreatedBy' => [ 'shape' => 'CreatedByInfo', ], 'UploadUrlMetadata' => [ 'shape' => 'UploadUrlMetadata', ], ], ], 'StartChatContactRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactFlowId', 'ParticipantDetails', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'ContactFlowId' => [ 'shape' => 'ContactFlowId', ], 'Attributes' => [ 'shape' => 'Attributes', ], 'ParticipantDetails' => [ 'shape' => 'ParticipantDetails', ], 'InitialMessage' => [ 'shape' => 'ChatMessage', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'ChatDurationInMinutes' => [ 'shape' => 'ChatDurationInMinutes', ], 'SupportedMessagingContentTypes' => [ 'shape' => 'SupportedMessagingContentTypes', ], 'PersistentChat' => [ 'shape' => 'PersistentChat', ], 'RelatedContactId' => [ 'shape' => 'ContactId', ], 'SegmentAttributes' => [ 'shape' => 'SegmentAttributes', ], 'CustomerId' => [ 'shape' => 'CustomerIdNonEmpty', ], ], ], 'StartChatContactResponse' => [ 'type' => 'structure', 'members' => [ 'ContactId' => [ 'shape' => 'ContactId', ], 'ParticipantId' => [ 'shape' => 'ParticipantId', ], 'ParticipantToken' => [ 'shape' => 'ParticipantToken', ], 'ContinuedFromContactId' => [ 'shape' => 'ContactId', ], ], ], 'StartContactEvaluationRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactId', 'EvaluationFormId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ContactId' => [ 'shape' => 'ContactId', ], 'EvaluationFormId' => [ 'shape' => 'ResourceId', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'StartContactEvaluationResponse' => [ 'type' => 'structure', 'required' => [ 'EvaluationId', 'EvaluationArn', ], 'members' => [ 'EvaluationId' => [ 'shape' => 'ResourceId', ], 'EvaluationArn' => [ 'shape' => 'ARN', ], ], ], 'StartContactRecordingRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactId', 'InitialContactId', 'VoiceRecordingConfiguration', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'ContactId' => [ 'shape' => 'ContactId', ], 'InitialContactId' => [ 'shape' => 'ContactId', ], 'VoiceRecordingConfiguration' => [ 'shape' => 'VoiceRecordingConfiguration', ], ], ], 'StartContactRecordingResponse' => [ 'type' => 'structure', 'members' => [], ], 'StartContactStreamingRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactId', 'ChatStreamingConfiguration', 'ClientToken', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'ContactId' => [ 'shape' => 'ContactId', ], 'ChatStreamingConfiguration' => [ 'shape' => 'ChatStreamingConfiguration', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'StartContactStreamingResponse' => [ 'type' => 'structure', 'required' => [ 'StreamingId', ], 'members' => [ 'StreamingId' => [ 'shape' => 'StreamingId', ], ], ], 'StartEmailContactRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'FromEmailAddress', 'DestinationEmailAddress', 'EmailMessage', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'FromEmailAddress' => [ 'shape' => 'EmailAddressInfo', ], 'DestinationEmailAddress' => [ 'shape' => 'EmailAddress', ], 'Description' => [ 'shape' => 'Description', ], 'References' => [ 'shape' => 'ContactReferences', ], 'Name' => [ 'shape' => 'Name', ], 'EmailMessage' => [ 'shape' => 'InboundEmailContent', ], 'AdditionalRecipients' => [ 'shape' => 'InboundAdditionalRecipients', ], 'Attachments' => [ 'shape' => 'EmailAttachments', ], 'ContactFlowId' => [ 'shape' => 'ContactFlowId', ], 'RelatedContactId' => [ 'shape' => 'ContactId', ], 'Attributes' => [ 'shape' => 'Attributes', ], 'SegmentAttributes' => [ 'shape' => 'SegmentAttributes', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'StartEmailContactResponse' => [ 'type' => 'structure', 'members' => [ 'ContactId' => [ 'shape' => 'ContactId', ], ], ], 'StartOutboundChatContactRequest' => [ 'type' => 'structure', 'required' => [ 'SourceEndpoint', 'DestinationEndpoint', 'InstanceId', 'SegmentAttributes', 'ContactFlowId', ], 'members' => [ 'SourceEndpoint' => [ 'shape' => 'Endpoint', ], 'DestinationEndpoint' => [ 'shape' => 'Endpoint', ], 'InstanceId' => [ 'shape' => 'InstanceId', ], 'SegmentAttributes' => [ 'shape' => 'SegmentAttributes', ], 'Attributes' => [ 'shape' => 'Attributes', ], 'ContactFlowId' => [ 'shape' => 'ContactFlowId', ], 'ChatDurationInMinutes' => [ 'shape' => 'ChatDurationInMinutes', ], 'ParticipantDetails' => [ 'shape' => 'ParticipantDetails', ], 'InitialSystemMessage' => [ 'shape' => 'ChatMessage', ], 'RelatedContactId' => [ 'shape' => 'ContactId', ], 'SupportedMessagingContentTypes' => [ 'shape' => 'SupportedMessagingContentTypes', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'StartOutboundChatContactResponse' => [ 'type' => 'structure', 'members' => [ 'ContactId' => [ 'shape' => 'ContactId', ], ], ], 'StartOutboundEmailContactRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactId', 'DestinationEmailAddress', 'EmailMessage', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'ContactId' => [ 'shape' => 'ContactId', ], 'FromEmailAddress' => [ 'shape' => 'EmailAddressInfo', ], 'DestinationEmailAddress' => [ 'shape' => 'EmailAddressInfo', ], 'AdditionalRecipients' => [ 'shape' => 'OutboundAdditionalRecipients', ], 'EmailMessage' => [ 'shape' => 'OutboundEmailContent', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'StartOutboundEmailContactResponse' => [ 'type' => 'structure', 'members' => [ 'ContactId' => [ 'shape' => 'ContactId', ], ], ], 'StartOutboundVoiceContactRequest' => [ 'type' => 'structure', 'required' => [ 'DestinationPhoneNumber', 'ContactFlowId', 'InstanceId', ], 'members' => [ 'Name' => [ 'shape' => 'Name', ], 'Description' => [ 'shape' => 'Description', ], 'References' => [ 'shape' => 'ContactReferences', ], 'RelatedContactId' => [ 'shape' => 'ContactId', ], 'DestinationPhoneNumber' => [ 'shape' => 'PhoneNumber', ], 'ContactFlowId' => [ 'shape' => 'ContactFlowId', ], 'InstanceId' => [ 'shape' => 'InstanceId', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'SourcePhoneNumber' => [ 'shape' => 'PhoneNumber', ], 'QueueId' => [ 'shape' => 'QueueId', ], 'Attributes' => [ 'shape' => 'Attributes', ], 'AnswerMachineDetectionConfig' => [ 'shape' => 'AnswerMachineDetectionConfig', ], 'CampaignId' => [ 'shape' => 'CampaignId', ], 'TrafficType' => [ 'shape' => 'TrafficType', ], ], ], 'StartOutboundVoiceContactResponse' => [ 'type' => 'structure', 'members' => [ 'ContactId' => [ 'shape' => 'ContactId', ], ], ], 'StartScreenSharingRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactId', ], 'members' => [ 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'InstanceId' => [ 'shape' => 'InstanceId', ], 'ContactId' => [ 'shape' => 'ContactId', ], ], ], 'StartScreenSharingResponse' => [ 'type' => 'structure', 'members' => [], ], 'StartTaskContactRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'Name', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'PreviousContactId' => [ 'shape' => 'ContactId', ], 'ContactFlowId' => [ 'shape' => 'ContactFlowId', ], 'Attributes' => [ 'shape' => 'Attributes', ], 'Name' => [ 'shape' => 'Name', ], 'References' => [ 'shape' => 'ContactReferences', ], 'Description' => [ 'shape' => 'Description', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'ScheduledTime' => [ 'shape' => 'Timestamp', ], 'TaskTemplateId' => [ 'shape' => 'TaskTemplateId', ], 'QuickConnectId' => [ 'shape' => 'QuickConnectId', ], 'RelatedContactId' => [ 'shape' => 'ContactId', ], 'SegmentAttributes' => [ 'shape' => 'SegmentAttributes', ], ], ], 'StartTaskContactResponse' => [ 'type' => 'structure', 'members' => [ 'ContactId' => [ 'shape' => 'ContactId', ], ], ], 'StartWebRTCContactRequest' => [ 'type' => 'structure', 'required' => [ 'ContactFlowId', 'InstanceId', 'ParticipantDetails', ], 'members' => [ 'Attributes' => [ 'shape' => 'Attributes', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'ContactFlowId' => [ 'shape' => 'ContactFlowId', ], 'InstanceId' => [ 'shape' => 'InstanceId', ], 'AllowedCapabilities' => [ 'shape' => 'AllowedCapabilities', ], 'ParticipantDetails' => [ 'shape' => 'ParticipantDetails', ], 'RelatedContactId' => [ 'shape' => 'ContactId', ], 'References' => [ 'shape' => 'ContactReferences', ], 'Description' => [ 'shape' => 'Description', ], ], ], 'StartWebRTCContactResponse' => [ 'type' => 'structure', 'members' => [ 'ConnectionData' => [ 'shape' => 'ConnectionData', ], 'ContactId' => [ 'shape' => 'ContactId', ], 'ParticipantId' => [ 'shape' => 'ParticipantId', ], 'ParticipantToken' => [ 'shape' => 'ParticipantToken', ], ], ], 'StateTransition' => [ 'type' => 'structure', 'members' => [ 'State' => [ 'shape' => 'ParticipantState', ], 'StateStartTimestamp' => [ 'shape' => 'timestamp', ], 'StateEndTimestamp' => [ 'shape' => 'timestamp', ], ], ], 'StateTransitions' => [ 'type' => 'list', 'member' => [ 'shape' => 'StateTransition', ], ], 'Statistic' => [ 'type' => 'string', 'enum' => [ 'SUM', 'MAX', 'AVG', ], ], 'Status' => [ 'type' => 'string', 'enum' => [ 'COMPLETE', 'IN_PROGRESS', 'DELETED', ], ], 'Step' => [ 'type' => 'structure', 'members' => [ 'Expiry' => [ 'shape' => 'Expiry', ], 'Expression' => [ 'shape' => 'Expression', ], 'Status' => [ 'shape' => 'RoutingCriteriaStepStatus', ], ], ], 'Steps' => [ 'type' => 'list', 'member' => [ 'shape' => 'Step', ], ], 'StopContactRecordingRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactId', 'InitialContactId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'ContactId' => [ 'shape' => 'ContactId', ], 'InitialContactId' => [ 'shape' => 'ContactId', ], 'ContactRecordingType' => [ 'shape' => 'ContactRecordingType', ], ], ], 'StopContactRecordingResponse' => [ 'type' => 'structure', 'members' => [], ], 'StopContactRequest' => [ 'type' => 'structure', 'required' => [ 'ContactId', 'InstanceId', ], 'members' => [ 'ContactId' => [ 'shape' => 'ContactId', ], 'InstanceId' => [ 'shape' => 'InstanceId', ], 'DisconnectReason' => [ 'shape' => 'DisconnectReason', ], ], ], 'StopContactResponse' => [ 'type' => 'structure', 'members' => [], ], 'StopContactStreamingRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactId', 'StreamingId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'ContactId' => [ 'shape' => 'ContactId', ], 'StreamingId' => [ 'shape' => 'StreamingId', ], ], ], 'StopContactStreamingResponse' => [ 'type' => 'structure', 'members' => [], ], 'StorageType' => [ 'type' => 'string', 'enum' => [ 'S3', 'KINESIS_VIDEO_STREAM', 'KINESIS_STREAM', 'KINESIS_FIREHOSE', ], ], 'StreamingId' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'String' => [ 'type' => 'string', ], 'StringComparisonType' => [ 'type' => 'string', 'enum' => [ 'STARTS_WITH', 'CONTAINS', 'EXACT', ], ], 'StringCondition' => [ 'type' => 'structure', 'members' => [ 'FieldName' => [ 'shape' => 'String', ], 'Value' => [ 'shape' => 'String', ], 'ComparisonType' => [ 'shape' => 'StringComparisonType', ], ], ], 'StringReference' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'ReferenceKey', ], 'Value' => [ 'shape' => 'ReferenceValue', ], ], ], 'Subject' => [ 'type' => 'string', 'max' => 200, 'min' => 1, ], 'SubmitAutoEvaluationActionDefinition' => [ 'type' => 'structure', 'required' => [ 'EvaluationFormId', ], 'members' => [ 'EvaluationFormId' => [ 'shape' => 'EvaluationFormId', ], ], ], 'SubmitContactEvaluationRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'EvaluationId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'EvaluationId' => [ 'shape' => 'ResourceId', 'location' => 'uri', 'locationName' => 'EvaluationId', ], 'Answers' => [ 'shape' => 'EvaluationAnswersInputMap', ], 'Notes' => [ 'shape' => 'EvaluationNotesMap', ], ], ], 'SubmitContactEvaluationResponse' => [ 'type' => 'structure', 'required' => [ 'EvaluationId', 'EvaluationArn', ], 'members' => [ 'EvaluationId' => [ 'shape' => 'ResourceId', ], 'EvaluationArn' => [ 'shape' => 'ARN', ], ], ], 'Subtype' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'SuccessfulRequest' => [ 'type' => 'structure', 'members' => [ 'RequestIdentifier' => [ 'shape' => 'RequestIdentifier', ], 'ContactId' => [ 'shape' => 'ContactId', ], ], ], 'SuccessfulRequestList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SuccessfulRequest', ], ], 'SupportedMessagingContentType' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'SupportedMessagingContentTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'SupportedMessagingContentType', ], ], 'SuspendContactRecordingRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactId', 'InitialContactId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'ContactId' => [ 'shape' => 'ContactId', ], 'InitialContactId' => [ 'shape' => 'ContactId', ], 'ContactRecordingType' => [ 'shape' => 'ContactRecordingType', ], ], ], 'SuspendContactRecordingResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagAndConditionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagCondition', ], ], 'TagCondition' => [ 'type' => 'structure', 'members' => [ 'TagKey' => [ 'shape' => 'String', ], 'TagValue' => [ 'shape' => 'String', ], ], ], 'TagContactRequest' => [ 'type' => 'structure', 'required' => [ 'ContactId', 'InstanceId', 'Tags', ], 'members' => [ 'ContactId' => [ 'shape' => 'ContactId', ], 'InstanceId' => [ 'shape' => 'InstanceId', ], 'Tags' => [ 'shape' => 'ContactTagMap', ], ], ], 'TagContactResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^(?!aws:)[\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 1, ], 'TagKeyString' => [ 'type' => 'string', 'max' => 128, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 50, 'min' => 1, ], 'TagOrConditionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagAndConditionList', ], ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagRestrictedResourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagRestrictedResourceName', ], 'max' => 10, ], 'TagRestrictedResourceName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagSearchCondition' => [ 'type' => 'structure', 'members' => [ 'tagKey' => [ 'shape' => 'TagKeyString', ], 'tagValue' => [ 'shape' => 'TagValueString', ], 'tagKeyComparisonType' => [ 'shape' => 'StringComparisonType', ], 'tagValueComparisonType' => [ 'shape' => 'StringComparisonType', ], ], ], 'TagSet' => [ 'type' => 'structure', 'members' => [ 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], ], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, ], 'TagValueString' => [ 'type' => 'string', 'max' => 256, ], 'TagsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagSet', ], ], 'TargetListType' => [ 'type' => 'string', 'enum' => [ 'PROFICIENCIES', ], ], 'TargetSlaMinutes' => [ 'type' => 'long', 'max' => 129600, 'min' => 1, ], 'TaskActionDefinition' => [ 'type' => 'structure', 'required' => [ 'Name', 'ContactFlowId', ], 'members' => [ 'Name' => [ 'shape' => 'TaskNameExpression', ], 'Description' => [ 'shape' => 'TaskDescriptionExpression', ], 'ContactFlowId' => [ 'shape' => 'ContactFlowId', ], 'References' => [ 'shape' => 'ContactReferences', ], ], ], 'TaskDescriptionExpression' => [ 'type' => 'string', 'max' => 4096, 'min' => 0, ], 'TaskNameExpression' => [ 'type' => 'string', 'max' => 512, 'min' => 1, ], 'TaskTemplateArn' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'TaskTemplateConstraints' => [ 'type' => 'structure', 'members' => [ 'RequiredFields' => [ 'shape' => 'RequiredTaskTemplateFields', ], 'ReadOnlyFields' => [ 'shape' => 'ReadOnlyTaskTemplateFields', ], 'InvisibleFields' => [ 'shape' => 'InvisibleTaskTemplateFields', ], ], ], 'TaskTemplateDefaultFieldValue' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'TaskTemplateFieldIdentifier', ], 'DefaultValue' => [ 'shape' => 'TaskTemplateFieldValue', ], ], ], 'TaskTemplateDefaultFieldValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TaskTemplateDefaultFieldValue', ], ], 'TaskTemplateDefaults' => [ 'type' => 'structure', 'members' => [ 'DefaultFieldValues' => [ 'shape' => 'TaskTemplateDefaultFieldValueList', ], ], ], 'TaskTemplateDescription' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'TaskTemplateField' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'TaskTemplateFieldIdentifier', ], 'Description' => [ 'shape' => 'TaskTemplateFieldDescription', ], 'Type' => [ 'shape' => 'TaskTemplateFieldType', ], 'SingleSelectOptions' => [ 'shape' => 'SingleSelectOptions', ], ], ], 'TaskTemplateFieldDescription' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'TaskTemplateFieldIdentifier' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'TaskTemplateFieldName', ], ], ], 'TaskTemplateFieldName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'TaskTemplateFieldType' => [ 'type' => 'string', 'enum' => [ 'NAME', 'DESCRIPTION', 'SCHEDULED_TIME', 'QUICK_CONNECT', 'URL', 'NUMBER', 'TEXT', 'TEXT_AREA', 'DATE_TIME', 'BOOLEAN', 'SINGLE_SELECT', 'EMAIL', 'SELF_ASSIGN', 'EXPIRY_DURATION', ], ], 'TaskTemplateFieldValue' => [ 'type' => 'string', 'max' => 4096, 'min' => 0, ], 'TaskTemplateFields' => [ 'type' => 'list', 'member' => [ 'shape' => 'TaskTemplateField', ], ], 'TaskTemplateId' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'TaskTemplateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TaskTemplateMetadata', ], ], 'TaskTemplateMetadata' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'TaskTemplateId', ], 'Arn' => [ 'shape' => 'TaskTemplateArn', ], 'Name' => [ 'shape' => 'TaskTemplateName', ], 'Description' => [ 'shape' => 'TaskTemplateDescription', ], 'Status' => [ 'shape' => 'TaskTemplateStatus', ], 'LastModifiedTime' => [ 'shape' => 'timestamp', ], 'CreatedTime' => [ 'shape' => 'timestamp', ], ], ], 'TaskTemplateName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'TaskTemplateSingleSelectOption' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'TaskTemplateStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'INACTIVE', ], ], 'TelephonyConfig' => [ 'type' => 'structure', 'required' => [ 'Distributions', ], 'members' => [ 'Distributions' => [ 'shape' => 'DistributionList', ], ], ], 'TemplateAttributes' => [ 'type' => 'structure', 'members' => [ 'CustomAttributes' => [ 'shape' => 'Attributes', ], 'CustomerProfileAttributes' => [ 'shape' => 'CustomerProfileAttributesSerialized', ], ], ], 'TemplateId' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'TemplatedMessageConfig' => [ 'type' => 'structure', 'required' => [ 'KnowledgeBaseId', 'MessageTemplateId', 'TemplateAttributes', ], 'members' => [ 'KnowledgeBaseId' => [ 'shape' => 'MessageTemplateKnowledgeBaseId', ], 'MessageTemplateId' => [ 'shape' => 'MessageTemplateId', ], 'TemplateAttributes' => [ 'shape' => 'TemplateAttributes', ], ], ], 'Threshold' => [ 'type' => 'structure', 'members' => [ 'Comparison' => [ 'shape' => 'Comparison', ], 'ThresholdValue' => [ 'shape' => 'ThresholdValue', 'box' => true, ], ], ], 'ThresholdCollections' => [ 'type' => 'list', 'member' => [ 'shape' => 'ThresholdV2', ], 'max' => 1, ], 'ThresholdV2' => [ 'type' => 'structure', 'members' => [ 'Comparison' => [ 'shape' => 'ResourceArnOrId', ], 'ThresholdValue' => [ 'shape' => 'ThresholdValue', 'box' => true, ], ], ], 'ThresholdValue' => [ 'type' => 'double', ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], 'TimeZone' => [ 'type' => 'string', ], 'TimerEligibleParticipantRoles' => [ 'type' => 'string', 'enum' => [ 'CUSTOMER', 'AGENT', ], ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TooManyRequestsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], 'TotalCount' => [ 'type' => 'long', ], 'TotalPauseCount' => [ 'type' => 'integer', 'max' => 10, 'min' => 0, ], 'TotalPauseDurationInSeconds' => [ 'type' => 'integer', 'min' => 0, ], 'TrafficDistributionGroup' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'TrafficDistributionGroupId', ], 'Arn' => [ 'shape' => 'TrafficDistributionGroupArn', ], 'Name' => [ 'shape' => 'Name128', ], 'Description' => [ 'shape' => 'Description250', ], 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'Status' => [ 'shape' => 'TrafficDistributionGroupStatus', ], 'Tags' => [ 'shape' => 'TagMap', ], 'IsDefault' => [ 'shape' => 'Boolean', ], ], ], 'TrafficDistributionGroupArn' => [ 'type' => 'string', 'pattern' => '^arn:(aws|aws-us-gov):connect:[a-z]{2}-[a-z]+-[0-9]{1}:[0-9]{1,20}:traffic-distribution-group/[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$', ], 'TrafficDistributionGroupId' => [ 'type' => 'string', 'pattern' => '^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$', ], 'TrafficDistributionGroupIdOrArn' => [ 'type' => 'string', 'pattern' => '^(arn:(aws|aws-us-gov):connect:[a-z]{2}-[a-z-]+-[0-9]{1}:[0-9]{1,20}:traffic-distribution-group/)?[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$', ], 'TrafficDistributionGroupStatus' => [ 'type' => 'string', 'enum' => [ 'CREATION_IN_PROGRESS', 'ACTIVE', 'CREATION_FAILED', 'PENDING_DELETION', 'DELETION_FAILED', 'UPDATE_IN_PROGRESS', ], ], 'TrafficDistributionGroupSummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'TrafficDistributionGroupId', ], 'Arn' => [ 'shape' => 'TrafficDistributionGroupArn', ], 'Name' => [ 'shape' => 'Name128', ], 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'Status' => [ 'shape' => 'TrafficDistributionGroupStatus', ], 'IsDefault' => [ 'shape' => 'Boolean', ], ], ], 'TrafficDistributionGroupSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TrafficDistributionGroupSummary', ], 'max' => 10, 'min' => 0, ], 'TrafficDistributionGroupUserSummary' => [ 'type' => 'structure', 'members' => [ 'UserId' => [ 'shape' => 'UserId', ], ], ], 'TrafficDistributionGroupUserSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TrafficDistributionGroupUserSummary', ], 'max' => 10, 'min' => 0, ], 'TrafficType' => [ 'type' => 'string', 'enum' => [ 'GENERAL', 'CAMPAIGN', ], ], 'Transcript' => [ 'type' => 'structure', 'required' => [ 'Criteria', ], 'members' => [ 'Criteria' => [ 'shape' => 'TranscriptCriteriaList', ], 'MatchType' => [ 'shape' => 'SearchContactsMatchType', ], ], ], 'TranscriptCriteria' => [ 'type' => 'structure', 'required' => [ 'ParticipantRole', 'SearchText', 'MatchType', ], 'members' => [ 'ParticipantRole' => [ 'shape' => 'ParticipantRole', ], 'SearchText' => [ 'shape' => 'SearchTextList', ], 'MatchType' => [ 'shape' => 'SearchContactsMatchType', ], ], ], 'TranscriptCriteriaList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TranscriptCriteria', ], 'max' => 6, 'min' => 0, ], 'TransferContactRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactId', 'ContactFlowId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'ContactId' => [ 'shape' => 'ContactId', ], 'QueueId' => [ 'shape' => 'QueueId', ], 'UserId' => [ 'shape' => 'AgentResourceId', ], 'ContactFlowId' => [ 'shape' => 'ContactFlowId', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'TransferContactResponse' => [ 'type' => 'structure', 'members' => [ 'ContactId' => [ 'shape' => 'ContactId', ], 'ContactArn' => [ 'shape' => 'ARN', ], ], ], 'URI' => [ 'type' => 'string', 'max' => 2000, 'min' => 1, ], 'URLExpiryInSeconds' => [ 'type' => 'integer', 'max' => 300, 'min' => 5, ], 'Unit' => [ 'type' => 'string', 'enum' => [ 'SECONDS', 'COUNT', 'PERCENT', ], ], 'UntagContactRequest' => [ 'type' => 'structure', 'required' => [ 'ContactId', 'InstanceId', 'TagKeys', ], 'members' => [ 'ContactId' => [ 'shape' => 'ContactId', 'location' => 'uri', 'locationName' => 'ContactId', ], 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'TagKeys' => [ 'shape' => 'ContactTagKeys', 'location' => 'querystring', 'locationName' => 'TagKeys', ], ], ], 'UntagContactResponse' => [ 'type' => 'structure', 'members' => [], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UpdateAgentStatusDescription' => [ 'type' => 'string', 'max' => 250, 'min' => 0, ], 'UpdateAgentStatusRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'AgentStatusId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'AgentStatusId' => [ 'shape' => 'AgentStatusId', 'location' => 'uri', 'locationName' => 'AgentStatusId', ], 'Name' => [ 'shape' => 'AgentStatusName', ], 'Description' => [ 'shape' => 'UpdateAgentStatusDescription', ], 'State' => [ 'shape' => 'AgentStatusState', ], 'DisplayOrder' => [ 'shape' => 'AgentStatusOrderNumber', 'box' => true, ], 'ResetOrderNumber' => [ 'shape' => 'Boolean', ], ], ], 'UpdateAuthenticationProfileRequest' => [ 'type' => 'structure', 'required' => [ 'AuthenticationProfileId', 'InstanceId', ], 'members' => [ 'AuthenticationProfileId' => [ 'shape' => 'AuthenticationProfileId', 'location' => 'uri', 'locationName' => 'AuthenticationProfileId', ], 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'Name' => [ 'shape' => 'AuthenticationProfileName', ], 'Description' => [ 'shape' => 'AuthenticationProfileDescription', ], 'AllowedIps' => [ 'shape' => 'IpCidrList', ], 'BlockedIps' => [ 'shape' => 'IpCidrList', ], 'PeriodicSessionDuration' => [ 'shape' => 'AccessTokenDuration', 'box' => true, ], ], ], 'UpdateCaseActionDefinition' => [ 'type' => 'structure', 'required' => [ 'Fields', ], 'members' => [ 'Fields' => [ 'shape' => 'FieldValues', ], ], ], 'UpdateContactAttributesRequest' => [ 'type' => 'structure', 'required' => [ 'InitialContactId', 'InstanceId', 'Attributes', ], 'members' => [ 'InitialContactId' => [ 'shape' => 'ContactId', ], 'InstanceId' => [ 'shape' => 'InstanceId', ], 'Attributes' => [ 'shape' => 'Attributes', ], ], ], 'UpdateContactAttributesResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateContactEvaluationRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'EvaluationId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'EvaluationId' => [ 'shape' => 'ResourceId', 'location' => 'uri', 'locationName' => 'EvaluationId', ], 'Answers' => [ 'shape' => 'EvaluationAnswersInputMap', ], 'Notes' => [ 'shape' => 'EvaluationNotesMap', ], ], ], 'UpdateContactEvaluationResponse' => [ 'type' => 'structure', 'required' => [ 'EvaluationId', 'EvaluationArn', ], 'members' => [ 'EvaluationId' => [ 'shape' => 'ResourceId', ], 'EvaluationArn' => [ 'shape' => 'ARN', ], ], ], 'UpdateContactFlowContentRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactFlowId', 'Content', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ContactFlowId' => [ 'shape' => 'ContactFlowId', 'location' => 'uri', 'locationName' => 'ContactFlowId', ], 'Content' => [ 'shape' => 'ContactFlowContent', ], ], ], 'UpdateContactFlowContentResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateContactFlowMetadataRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactFlowId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ContactFlowId' => [ 'shape' => 'ContactFlowId', 'location' => 'uri', 'locationName' => 'ContactFlowId', ], 'Name' => [ 'shape' => 'ContactFlowName', ], 'Description' => [ 'shape' => 'ContactFlowDescription', ], 'ContactFlowState' => [ 'shape' => 'ContactFlowState', ], ], ], 'UpdateContactFlowMetadataResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateContactFlowModuleContentRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactFlowModuleId', 'Content', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ContactFlowModuleId' => [ 'shape' => 'ContactFlowModuleId', 'location' => 'uri', 'locationName' => 'ContactFlowModuleId', ], 'Content' => [ 'shape' => 'ContactFlowModuleContent', ], ], ], 'UpdateContactFlowModuleContentResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateContactFlowModuleMetadataRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactFlowModuleId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ContactFlowModuleId' => [ 'shape' => 'ContactFlowModuleId', 'location' => 'uri', 'locationName' => 'ContactFlowModuleId', ], 'Name' => [ 'shape' => 'ContactFlowModuleName', ], 'Description' => [ 'shape' => 'ContactFlowModuleDescription', ], 'State' => [ 'shape' => 'ContactFlowModuleState', ], ], ], 'UpdateContactFlowModuleMetadataResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateContactFlowNameRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactFlowId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ContactFlowId' => [ 'shape' => 'ContactFlowId', 'location' => 'uri', 'locationName' => 'ContactFlowId', ], 'Name' => [ 'shape' => 'ContactFlowName', ], 'Description' => [ 'shape' => 'ContactFlowDescription', ], ], ], 'UpdateContactFlowNameResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateContactRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ContactId' => [ 'shape' => 'ContactId', 'location' => 'uri', 'locationName' => 'ContactId', ], 'Name' => [ 'shape' => 'Name', ], 'Description' => [ 'shape' => 'Description', ], 'References' => [ 'shape' => 'ContactReferences', ], 'SegmentAttributes' => [ 'shape' => 'SegmentAttributes', ], 'QueueInfo' => [ 'shape' => 'QueueInfoInput', ], 'UserInfo' => [ 'shape' => 'UserInfo', ], 'CustomerEndpoint' => [ 'shape' => 'Endpoint', ], 'SystemEndpoint' => [ 'shape' => 'Endpoint', ], ], ], 'UpdateContactResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateContactRoutingDataRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ContactId' => [ 'shape' => 'ContactId', 'location' => 'uri', 'locationName' => 'ContactId', ], 'QueueTimeAdjustmentSeconds' => [ 'shape' => 'QueueTimeAdjustmentSeconds', ], 'QueuePriority' => [ 'shape' => 'QueuePriority', ], 'RoutingCriteria' => [ 'shape' => 'RoutingCriteriaInput', ], ], ], 'UpdateContactRoutingDataResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateContactScheduleRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactId', 'ScheduledTime', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'ContactId' => [ 'shape' => 'ContactId', ], 'ScheduledTime' => [ 'shape' => 'Timestamp', ], ], ], 'UpdateContactScheduleResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateEmailAddressMetadataRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'EmailAddressId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'EmailAddressId' => [ 'shape' => 'EmailAddressId', 'location' => 'uri', 'locationName' => 'EmailAddressId', ], 'Description' => [ 'shape' => 'Description', ], 'DisplayName' => [ 'shape' => 'EmailAddressDisplayName', ], 'ClientToken' => [ 'shape' => 'ClientToken', ], ], ], 'UpdateEmailAddressMetadataResponse' => [ 'type' => 'structure', 'members' => [ 'EmailAddressId' => [ 'shape' => 'EmailAddressId', ], 'EmailAddressArn' => [ 'shape' => 'EmailAddressArn', ], ], ], 'UpdateEvaluationFormRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'EvaluationFormId', 'EvaluationFormVersion', 'Title', 'Items', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'EvaluationFormId' => [ 'shape' => 'ResourceId', 'location' => 'uri', 'locationName' => 'EvaluationFormId', ], 'EvaluationFormVersion' => [ 'shape' => 'VersionNumber', ], 'CreateNewVersion' => [ 'shape' => 'BoxedBoolean', 'box' => true, ], 'Title' => [ 'shape' => 'EvaluationFormTitle', ], 'Description' => [ 'shape' => 'EvaluationFormDescription', ], 'Items' => [ 'shape' => 'EvaluationFormItemsList', ], 'ScoringStrategy' => [ 'shape' => 'EvaluationFormScoringStrategy', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'UpdateEvaluationFormResponse' => [ 'type' => 'structure', 'required' => [ 'EvaluationFormId', 'EvaluationFormArn', 'EvaluationFormVersion', ], 'members' => [ 'EvaluationFormId' => [ 'shape' => 'ResourceId', ], 'EvaluationFormArn' => [ 'shape' => 'ARN', ], 'EvaluationFormVersion' => [ 'shape' => 'VersionNumber', ], ], ], 'UpdateHoursOfOperationDescription' => [ 'type' => 'string', 'max' => 250, 'min' => 0, ], 'UpdateHoursOfOperationOverrideRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'HoursOfOperationId', 'HoursOfOperationOverrideId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'HoursOfOperationId' => [ 'shape' => 'HoursOfOperationId', 'location' => 'uri', 'locationName' => 'HoursOfOperationId', ], 'HoursOfOperationOverrideId' => [ 'shape' => 'HoursOfOperationOverrideId', 'location' => 'uri', 'locationName' => 'HoursOfOperationOverrideId', ], 'Name' => [ 'shape' => 'CommonHumanReadableName', ], 'Description' => [ 'shape' => 'CommonHumanReadableDescription', ], 'Config' => [ 'shape' => 'HoursOfOperationOverrideConfigList', ], 'EffectiveFrom' => [ 'shape' => 'HoursOfOperationOverrideYearMonthDayDateFormat', ], 'EffectiveTill' => [ 'shape' => 'HoursOfOperationOverrideYearMonthDayDateFormat', ], ], ], 'UpdateHoursOfOperationRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'HoursOfOperationId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'HoursOfOperationId' => [ 'shape' => 'HoursOfOperationId', 'location' => 'uri', 'locationName' => 'HoursOfOperationId', ], 'Name' => [ 'shape' => 'CommonNameLength127', ], 'Description' => [ 'shape' => 'UpdateHoursOfOperationDescription', ], 'TimeZone' => [ 'shape' => 'TimeZone', ], 'Config' => [ 'shape' => 'HoursOfOperationConfigList', ], ], ], 'UpdateInstanceAttributeRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'AttributeType', 'Value', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'AttributeType' => [ 'shape' => 'InstanceAttributeType', 'location' => 'uri', 'locationName' => 'AttributeType', ], 'Value' => [ 'shape' => 'InstanceAttributeValue', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'UpdateInstanceStorageConfigRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'AssociationId', 'ResourceType', 'StorageConfig', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'AssociationId' => [ 'shape' => 'AssociationId', 'location' => 'uri', 'locationName' => 'AssociationId', ], 'ResourceType' => [ 'shape' => 'InstanceStorageResourceType', 'location' => 'querystring', 'locationName' => 'resourceType', ], 'StorageConfig' => [ 'shape' => 'InstanceStorageConfig', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'UpdateParticipantAuthenticationRequest' => [ 'type' => 'structure', 'required' => [ 'State', 'InstanceId', ], 'members' => [ 'State' => [ 'shape' => 'ParticipantToken', ], 'InstanceId' => [ 'shape' => 'InstanceId', ], 'Code' => [ 'shape' => 'AuthorizationCode', ], 'Error' => [ 'shape' => 'AuthenticationError', ], 'ErrorDescription' => [ 'shape' => 'AuthenticationErrorDescription', ], ], ], 'UpdateParticipantAuthenticationResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateParticipantRoleConfigChannelInfo' => [ 'type' => 'structure', 'members' => [ 'Chat' => [ 'shape' => 'ChatParticipantRoleConfig', ], ], 'union' => true, ], 'UpdateParticipantRoleConfigRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ContactId', 'ChannelConfiguration', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ContactId' => [ 'shape' => 'ContactId', 'location' => 'uri', 'locationName' => 'ContactId', ], 'ChannelConfiguration' => [ 'shape' => 'UpdateParticipantRoleConfigChannelInfo', ], ], ], 'UpdateParticipantRoleConfigResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdatePhoneNumberMetadataRequest' => [ 'type' => 'structure', 'required' => [ 'PhoneNumberId', ], 'members' => [ 'PhoneNumberId' => [ 'shape' => 'PhoneNumberId', 'location' => 'uri', 'locationName' => 'PhoneNumberId', ], 'PhoneNumberDescription' => [ 'shape' => 'PhoneNumberDescription', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'UpdatePhoneNumberRequest' => [ 'type' => 'structure', 'required' => [ 'PhoneNumberId', ], 'members' => [ 'PhoneNumberId' => [ 'shape' => 'PhoneNumberId', 'location' => 'uri', 'locationName' => 'PhoneNumberId', ], 'TargetArn' => [ 'shape' => 'ARN', ], 'InstanceId' => [ 'shape' => 'InstanceId', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'UpdatePhoneNumberResponse' => [ 'type' => 'structure', 'members' => [ 'PhoneNumberId' => [ 'shape' => 'PhoneNumberId', ], 'PhoneNumberArn' => [ 'shape' => 'ARN', ], ], ], 'UpdatePredefinedAttributeRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'Name', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'Name' => [ 'shape' => 'PredefinedAttributeName', 'location' => 'uri', 'locationName' => 'Name', ], 'Values' => [ 'shape' => 'PredefinedAttributeValues', ], ], ], 'UpdatePromptRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'PromptId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'PromptId' => [ 'shape' => 'PromptId', 'location' => 'uri', 'locationName' => 'PromptId', ], 'Name' => [ 'shape' => 'CommonNameLength127', ], 'Description' => [ 'shape' => 'PromptDescription', ], 'S3Uri' => [ 'shape' => 'S3Uri', ], ], ], 'UpdatePromptResponse' => [ 'type' => 'structure', 'members' => [ 'PromptARN' => [ 'shape' => 'ARN', ], 'PromptId' => [ 'shape' => 'PromptId', ], ], ], 'UpdateQueueHoursOfOperationRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'QueueId', 'HoursOfOperationId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'QueueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'QueueId', ], 'HoursOfOperationId' => [ 'shape' => 'HoursOfOperationId', ], ], ], 'UpdateQueueMaxContactsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'QueueId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'QueueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'QueueId', ], 'MaxContacts' => [ 'shape' => 'QueueMaxContacts', 'box' => true, ], ], ], 'UpdateQueueNameRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'QueueId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'QueueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'QueueId', ], 'Name' => [ 'shape' => 'CommonNameLength127', ], 'Description' => [ 'shape' => 'QueueDescription', ], ], ], 'UpdateQueueOutboundCallerConfigRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'QueueId', 'OutboundCallerConfig', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'QueueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'QueueId', ], 'OutboundCallerConfig' => [ 'shape' => 'OutboundCallerConfig', ], ], ], 'UpdateQueueOutboundEmailConfigRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'QueueId', 'OutboundEmailConfig', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'QueueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'QueueId', ], 'OutboundEmailConfig' => [ 'shape' => 'OutboundEmailConfig', ], ], ], 'UpdateQueueStatusRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'QueueId', 'Status', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'QueueId' => [ 'shape' => 'QueueId', 'location' => 'uri', 'locationName' => 'QueueId', ], 'Status' => [ 'shape' => 'QueueStatus', ], ], ], 'UpdateQuickConnectConfigRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'QuickConnectId', 'QuickConnectConfig', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'QuickConnectId' => [ 'shape' => 'QuickConnectId', 'location' => 'uri', 'locationName' => 'QuickConnectId', ], 'QuickConnectConfig' => [ 'shape' => 'QuickConnectConfig', ], ], ], 'UpdateQuickConnectDescription' => [ 'type' => 'string', 'max' => 250, 'min' => 0, ], 'UpdateQuickConnectNameRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'QuickConnectId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'QuickConnectId' => [ 'shape' => 'QuickConnectId', 'location' => 'uri', 'locationName' => 'QuickConnectId', ], 'Name' => [ 'shape' => 'QuickConnectName', ], 'Description' => [ 'shape' => 'UpdateQuickConnectDescription', ], ], ], 'UpdateRoutingProfileAgentAvailabilityTimerRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'RoutingProfileId', 'AgentAvailabilityTimer', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'RoutingProfileId' => [ 'shape' => 'RoutingProfileId', 'location' => 'uri', 'locationName' => 'RoutingProfileId', ], 'AgentAvailabilityTimer' => [ 'shape' => 'AgentAvailabilityTimer', ], ], ], 'UpdateRoutingProfileConcurrencyRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'RoutingProfileId', 'MediaConcurrencies', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'RoutingProfileId' => [ 'shape' => 'RoutingProfileId', 'location' => 'uri', 'locationName' => 'RoutingProfileId', ], 'MediaConcurrencies' => [ 'shape' => 'MediaConcurrencies', ], ], ], 'UpdateRoutingProfileDefaultOutboundQueueRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'RoutingProfileId', 'DefaultOutboundQueueId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'RoutingProfileId' => [ 'shape' => 'RoutingProfileId', 'location' => 'uri', 'locationName' => 'RoutingProfileId', ], 'DefaultOutboundQueueId' => [ 'shape' => 'QueueId', ], ], ], 'UpdateRoutingProfileNameRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'RoutingProfileId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'RoutingProfileId' => [ 'shape' => 'RoutingProfileId', 'location' => 'uri', 'locationName' => 'RoutingProfileId', ], 'Name' => [ 'shape' => 'RoutingProfileName', ], 'Description' => [ 'shape' => 'RoutingProfileDescription', ], ], ], 'UpdateRoutingProfileQueuesRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'RoutingProfileId', 'QueueConfigs', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'RoutingProfileId' => [ 'shape' => 'RoutingProfileId', 'location' => 'uri', 'locationName' => 'RoutingProfileId', ], 'QueueConfigs' => [ 'shape' => 'RoutingProfileQueueConfigList', ], ], ], 'UpdateRuleRequest' => [ 'type' => 'structure', 'required' => [ 'RuleId', 'InstanceId', 'Name', 'Function', 'Actions', 'PublishStatus', ], 'members' => [ 'RuleId' => [ 'shape' => 'RuleId', 'location' => 'uri', 'locationName' => 'RuleId', ], 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'Name' => [ 'shape' => 'RuleName', ], 'Function' => [ 'shape' => 'RuleFunction', ], 'Actions' => [ 'shape' => 'RuleActions', ], 'PublishStatus' => [ 'shape' => 'RulePublishStatus', ], ], ], 'UpdateSecurityProfileRequest' => [ 'type' => 'structure', 'required' => [ 'SecurityProfileId', 'InstanceId', ], 'members' => [ 'Description' => [ 'shape' => 'SecurityProfileDescription', ], 'Permissions' => [ 'shape' => 'PermissionsList', ], 'SecurityProfileId' => [ 'shape' => 'SecurityProfileId', 'location' => 'uri', 'locationName' => 'SecurityProfileId', ], 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'AllowedAccessControlTags' => [ 'shape' => 'AllowedAccessControlTags', ], 'TagRestrictedResources' => [ 'shape' => 'TagRestrictedResourceList', ], 'Applications' => [ 'shape' => 'Applications', ], 'HierarchyRestrictedResources' => [ 'shape' => 'HierarchyRestrictedResourceList', ], 'AllowedAccessControlHierarchyGroupId' => [ 'shape' => 'HierarchyGroupId', ], ], ], 'UpdateTaskTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'TaskTemplateId', 'InstanceId', ], 'members' => [ 'TaskTemplateId' => [ 'shape' => 'TaskTemplateId', 'location' => 'uri', 'locationName' => 'TaskTemplateId', ], 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'Name' => [ 'shape' => 'TaskTemplateName', ], 'Description' => [ 'shape' => 'TaskTemplateDescription', ], 'ContactFlowId' => [ 'shape' => 'ContactFlowId', ], 'SelfAssignFlowId' => [ 'shape' => 'ContactFlowId', ], 'Constraints' => [ 'shape' => 'TaskTemplateConstraints', ], 'Defaults' => [ 'shape' => 'TaskTemplateDefaults', ], 'Status' => [ 'shape' => 'TaskTemplateStatus', ], 'Fields' => [ 'shape' => 'TaskTemplateFields', ], ], ], 'UpdateTaskTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', ], 'Id' => [ 'shape' => 'TaskTemplateId', ], 'Arn' => [ 'shape' => 'TaskTemplateArn', ], 'Name' => [ 'shape' => 'TaskTemplateName', ], 'Description' => [ 'shape' => 'TaskTemplateDescription', ], 'ContactFlowId' => [ 'shape' => 'ContactFlowId', ], 'SelfAssignFlowId' => [ 'shape' => 'ContactFlowId', ], 'Constraints' => [ 'shape' => 'TaskTemplateConstraints', ], 'Defaults' => [ 'shape' => 'TaskTemplateDefaults', ], 'Fields' => [ 'shape' => 'TaskTemplateFields', ], 'Status' => [ 'shape' => 'TaskTemplateStatus', ], 'LastModifiedTime' => [ 'shape' => 'timestamp', ], 'CreatedTime' => [ 'shape' => 'timestamp', ], ], ], 'UpdateTrafficDistributionRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'TrafficDistributionGroupIdOrArn', 'location' => 'uri', 'locationName' => 'Id', ], 'TelephonyConfig' => [ 'shape' => 'TelephonyConfig', ], 'SignInConfig' => [ 'shape' => 'SignInConfig', ], 'AgentConfig' => [ 'shape' => 'AgentConfig', ], ], ], 'UpdateTrafficDistributionResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateUserHierarchyGroupNameRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'HierarchyGroupId', 'InstanceId', ], 'members' => [ 'Name' => [ 'shape' => 'HierarchyGroupName', ], 'HierarchyGroupId' => [ 'shape' => 'HierarchyGroupId', 'location' => 'uri', 'locationName' => 'HierarchyGroupId', ], 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], ], ], 'UpdateUserHierarchyRequest' => [ 'type' => 'structure', 'required' => [ 'UserId', 'InstanceId', ], 'members' => [ 'HierarchyGroupId' => [ 'shape' => 'HierarchyGroupId', ], 'UserId' => [ 'shape' => 'UserId', 'location' => 'uri', 'locationName' => 'UserId', ], 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], ], ], 'UpdateUserHierarchyStructureRequest' => [ 'type' => 'structure', 'required' => [ 'HierarchyStructure', 'InstanceId', ], 'members' => [ 'HierarchyStructure' => [ 'shape' => 'HierarchyStructureUpdate', ], 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], ], ], 'UpdateUserIdentityInfoRequest' => [ 'type' => 'structure', 'required' => [ 'IdentityInfo', 'UserId', 'InstanceId', ], 'members' => [ 'IdentityInfo' => [ 'shape' => 'UserIdentityInfo', ], 'UserId' => [ 'shape' => 'UserId', 'location' => 'uri', 'locationName' => 'UserId', ], 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], ], ], 'UpdateUserPhoneConfigRequest' => [ 'type' => 'structure', 'required' => [ 'PhoneConfig', 'UserId', 'InstanceId', ], 'members' => [ 'PhoneConfig' => [ 'shape' => 'UserPhoneConfig', ], 'UserId' => [ 'shape' => 'UserId', 'location' => 'uri', 'locationName' => 'UserId', ], 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], ], ], 'UpdateUserProficienciesRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'UserId', 'UserProficiencies', ], 'members' => [ 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'UserId' => [ 'shape' => 'UserId', 'location' => 'uri', 'locationName' => 'UserId', ], 'UserProficiencies' => [ 'shape' => 'UserProficiencyList', ], ], ], 'UpdateUserRoutingProfileRequest' => [ 'type' => 'structure', 'required' => [ 'RoutingProfileId', 'UserId', 'InstanceId', ], 'members' => [ 'RoutingProfileId' => [ 'shape' => 'RoutingProfileId', ], 'UserId' => [ 'shape' => 'UserId', 'location' => 'uri', 'locationName' => 'UserId', ], 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], ], ], 'UpdateUserSecurityProfilesRequest' => [ 'type' => 'structure', 'required' => [ 'SecurityProfileIds', 'UserId', 'InstanceId', ], 'members' => [ 'SecurityProfileIds' => [ 'shape' => 'SecurityProfileIds', ], 'UserId' => [ 'shape' => 'UserId', 'location' => 'uri', 'locationName' => 'UserId', ], 'InstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], ], ], 'UpdateViewContentRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ViewId', 'Status', 'Content', ], 'members' => [ 'InstanceId' => [ 'shape' => 'ViewsInstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ViewId' => [ 'shape' => 'ViewId', 'location' => 'uri', 'locationName' => 'ViewId', ], 'Status' => [ 'shape' => 'ViewStatus', ], 'Content' => [ 'shape' => 'ViewInputContent', ], ], ], 'UpdateViewContentResponse' => [ 'type' => 'structure', 'members' => [ 'View' => [ 'shape' => 'View', ], ], ], 'UpdateViewMetadataRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceId', 'ViewId', ], 'members' => [ 'InstanceId' => [ 'shape' => 'ViewsInstanceId', 'location' => 'uri', 'locationName' => 'InstanceId', ], 'ViewId' => [ 'shape' => 'ViewId', 'location' => 'uri', 'locationName' => 'ViewId', ], 'Name' => [ 'shape' => 'ViewName', ], 'Description' => [ 'shape' => 'ViewDescription', ], ], ], 'UpdateViewMetadataResponse' => [ 'type' => 'structure', 'members' => [], ], 'UploadUrlMetadata' => [ 'type' => 'structure', 'members' => [ 'Url' => [ 'shape' => 'MetadataUrl', ], 'UrlExpiry' => [ 'shape' => 'ISO8601Datetime', ], 'HeadersToInclude' => [ 'shape' => 'UrlMetadataSignedHeaders', ], ], ], 'Url' => [ 'type' => 'string', ], 'UrlMetadataSignedHeaders' => [ 'type' => 'map', 'key' => [ 'shape' => 'UrlMetadataSignedHeadersKey', ], 'value' => [ 'shape' => 'UrlMetadataSignedHeadersValue', ], ], 'UrlMetadataSignedHeadersKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'UrlMetadataSignedHeadersValue' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'UrlReference' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'ReferenceKey', ], 'Value' => [ 'shape' => 'ReferenceValue', ], ], ], 'UseCase' => [ 'type' => 'structure', 'members' => [ 'UseCaseId' => [ 'shape' => 'UseCaseId', ], 'UseCaseArn' => [ 'shape' => 'ARN', ], 'UseCaseType' => [ 'shape' => 'UseCaseType', ], ], ], 'UseCaseId' => [ 'type' => 'string', 'max' => 200, 'min' => 1, ], 'UseCaseSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UseCase', ], ], 'UseCaseType' => [ 'type' => 'string', 'enum' => [ 'RULES_EVALUATION', 'CONNECT_CAMPAIGNS', ], ], 'User' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'UserId', ], 'Arn' => [ 'shape' => 'ARN', ], 'Username' => [ 'shape' => 'AgentUsername', ], 'IdentityInfo' => [ 'shape' => 'UserIdentityInfo', ], 'PhoneConfig' => [ 'shape' => 'UserPhoneConfig', ], 'DirectoryUserId' => [ 'shape' => 'DirectoryUserId', ], 'SecurityProfileIds' => [ 'shape' => 'SecurityProfileIds', ], 'RoutingProfileId' => [ 'shape' => 'RoutingProfileId', ], 'HierarchyGroupId' => [ 'shape' => 'HierarchyGroupId', ], 'Tags' => [ 'shape' => 'TagMap', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedRegion' => [ 'shape' => 'RegionName', ], ], ], 'UserData' => [ 'type' => 'structure', 'members' => [ 'User' => [ 'shape' => 'UserReference', ], 'RoutingProfile' => [ 'shape' => 'RoutingProfileReference', ], 'HierarchyPath' => [ 'shape' => 'HierarchyPathReference', ], 'Status' => [ 'shape' => 'AgentStatusReference', ], 'AvailableSlotsByChannel' => [ 'shape' => 'ChannelToCountMap', ], 'MaxSlotsByChannel' => [ 'shape' => 'ChannelToCountMap', ], 'ActiveSlotsByChannel' => [ 'shape' => 'ChannelToCountMap', ], 'Contacts' => [ 'shape' => 'AgentContactReferenceList', ], 'NextStatus' => [ 'shape' => 'AgentStatusName', ], ], ], 'UserDataFilters' => [ 'type' => 'structure', 'members' => [ 'Queues' => [ 'shape' => 'Queues', ], 'ContactFilter' => [ 'shape' => 'ContactFilter', ], 'RoutingProfiles' => [ 'shape' => 'RoutingProfiles', ], 'Agents' => [ 'shape' => 'AgentsMinOneMaxHundred', ], 'UserHierarchyGroups' => [ 'shape' => 'UserDataHierarchyGroups', ], ], ], 'UserDataHierarchyGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'HierarchyGroupId', ], 'max' => 1, 'min' => 1, ], 'UserDataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserData', ], ], 'UserHierarchyGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'HierarchyGroup', ], ], 'UserHierarchyGroupSearchConditionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserHierarchyGroupSearchCriteria', ], ], 'UserHierarchyGroupSearchCriteria' => [ 'type' => 'structure', 'members' => [ 'OrConditions' => [ 'shape' => 'UserHierarchyGroupSearchConditionList', ], 'AndConditions' => [ 'shape' => 'UserHierarchyGroupSearchConditionList', ], 'StringCondition' => [ 'shape' => 'StringCondition', ], ], ], 'UserHierarchyGroupSearchFilter' => [ 'type' => 'structure', 'members' => [ 'AttributeFilter' => [ 'shape' => 'ControlPlaneAttributeFilter', ], ], ], 'UserId' => [ 'type' => 'string', ], 'UserIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserId', ], ], 'UserIdentityInfo' => [ 'type' => 'structure', 'members' => [ 'FirstName' => [ 'shape' => 'AgentFirstName', ], 'LastName' => [ 'shape' => 'AgentLastName', ], 'Email' => [ 'shape' => 'Email', ], 'SecondaryEmail' => [ 'shape' => 'Email', ], 'Mobile' => [ 'shape' => 'PhoneNumber', ], ], ], 'UserIdentityInfoLite' => [ 'type' => 'structure', 'members' => [ 'FirstName' => [ 'shape' => 'AgentFirstName', ], 'LastName' => [ 'shape' => 'AgentLastName', ], ], ], 'UserInfo' => [ 'type' => 'structure', 'members' => [ 'UserId' => [ 'shape' => 'AgentResourceId', ], ], ], 'UserNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'UserPhoneConfig' => [ 'type' => 'structure', 'required' => [ 'PhoneType', ], 'members' => [ 'PhoneType' => [ 'shape' => 'PhoneType', ], 'AutoAccept' => [ 'shape' => 'AutoAccept', ], 'AfterContactWorkTimeLimit' => [ 'shape' => 'AfterContactWorkTimeLimit', ], 'DeskPhoneNumber' => [ 'shape' => 'PhoneNumber', ], ], ], 'UserProficiency' => [ 'type' => 'structure', 'required' => [ 'AttributeName', 'AttributeValue', 'Level', ], 'members' => [ 'AttributeName' => [ 'shape' => 'PredefinedAttributeName', ], 'AttributeValue' => [ 'shape' => 'PredefinedAttributeStringValue', ], 'Level' => [ 'shape' => 'ProficiencyLevel', ], ], ], 'UserProficiencyDisassociate' => [ 'type' => 'structure', 'required' => [ 'AttributeName', 'AttributeValue', ], 'members' => [ 'AttributeName' => [ 'shape' => 'PredefinedAttributeName', ], 'AttributeValue' => [ 'shape' => 'PredefinedAttributeStringValue', ], ], ], 'UserProficiencyDisassociateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserProficiencyDisassociate', ], ], 'UserProficiencyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserProficiency', ], ], 'UserQuickConnectConfig' => [ 'type' => 'structure', 'required' => [ 'UserId', 'ContactFlowId', ], 'members' => [ 'UserId' => [ 'shape' => 'UserId', ], 'ContactFlowId' => [ 'shape' => 'ContactFlowId', ], ], ], 'UserReference' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'UserId', ], 'Arn' => [ 'shape' => 'ARN', ], ], ], 'UserSearchConditionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserSearchCriteria', ], ], 'UserSearchCriteria' => [ 'type' => 'structure', 'members' => [ 'OrConditions' => [ 'shape' => 'UserSearchConditionList', ], 'AndConditions' => [ 'shape' => 'UserSearchConditionList', ], 'StringCondition' => [ 'shape' => 'StringCondition', ], 'ListCondition' => [ 'shape' => 'ListCondition', ], 'HierarchyGroupCondition' => [ 'shape' => 'HierarchyGroupCondition', ], ], ], 'UserSearchFilter' => [ 'type' => 'structure', 'members' => [ 'TagFilter' => [ 'shape' => 'ControlPlaneTagFilter', ], 'UserAttributeFilter' => [ 'shape' => 'ControlPlaneUserAttributeFilter', ], ], ], 'UserSearchSummary' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'ARN', ], 'DirectoryUserId' => [ 'shape' => 'DirectoryUserId', ], 'HierarchyGroupId' => [ 'shape' => 'HierarchyGroupId', ], 'Id' => [ 'shape' => 'UserId', ], 'IdentityInfo' => [ 'shape' => 'UserIdentityInfoLite', ], 'PhoneConfig' => [ 'shape' => 'UserPhoneConfig', ], 'RoutingProfileId' => [ 'shape' => 'RoutingProfileId', ], 'SecurityProfileIds' => [ 'shape' => 'SecurityProfileIds', ], 'Tags' => [ 'shape' => 'TagMap', ], 'Username' => [ 'shape' => 'AgentUsername', ], ], ], 'UserSearchSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserSearchSummary', ], ], 'UserSummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'UserId', ], 'Arn' => [ 'shape' => 'ARN', ], 'Username' => [ 'shape' => 'AgentUsername', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedRegion' => [ 'shape' => 'RegionName', ], ], ], 'UserSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserSummary', ], ], 'UserTagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'Value' => [ 'type' => 'double', ], 'VersionNumber' => [ 'type' => 'integer', 'min' => 1, ], 'VideoCapability' => [ 'type' => 'string', 'enum' => [ 'SEND', ], ], 'View' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ViewId', ], 'Arn' => [ 'shape' => 'ARN', ], 'Name' => [ 'shape' => 'ViewName', ], 'Status' => [ 'shape' => 'ViewStatus', ], 'Type' => [ 'shape' => 'ViewType', ], 'Description' => [ 'shape' => 'ViewDescription', ], 'Version' => [ 'shape' => 'ViewVersion', ], 'VersionDescription' => [ 'shape' => 'ViewDescription', ], 'Content' => [ 'shape' => 'ViewContent', ], 'Tags' => [ 'shape' => 'TagMap', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'ViewContentSha256' => [ 'shape' => 'ViewContentSha256', ], ], ], 'ViewAction' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^([\\p{L}\\p{N}_.:\\/=+\\-@()\']+[\\p{L}\\p{Z}\\p{N}_.:\\/=+\\-@()\']*)$', 'sensitive' => true, ], 'ViewActions' => [ 'type' => 'list', 'member' => [ 'shape' => 'ViewAction', ], ], 'ViewContent' => [ 'type' => 'structure', 'members' => [ 'InputSchema' => [ 'shape' => 'ViewInputSchema', ], 'Template' => [ 'shape' => 'ViewTemplate', ], 'Actions' => [ 'shape' => 'ViewActions', ], ], ], 'ViewContentSha256' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[a-zA-Z0-9]$', ], 'ViewDescription' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, 'pattern' => '^([\\p{L}\\p{N}_.:\\/=+\\-@,()\']+[\\p{L}\\p{Z}\\p{N}_.:\\/=+\\-@,()\']*)$', ], 'ViewId' => [ 'type' => 'string', 'max' => 500, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\_\\-:\\/$]+$', ], 'ViewInputContent' => [ 'type' => 'structure', 'members' => [ 'Template' => [ 'shape' => 'ViewTemplate', ], 'Actions' => [ 'shape' => 'ViewActions', ], ], ], 'ViewInputSchema' => [ 'type' => 'string', 'sensitive' => true, ], 'ViewName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^([\\p{L}\\p{N}_.:\\/=+\\-@()\']+[\\p{L}\\p{Z}\\p{N}_.:\\/=+\\-@()\']*)$', 'sensitive' => true, ], 'ViewStatus' => [ 'type' => 'string', 'enum' => [ 'PUBLISHED', 'SAVED', ], ], 'ViewSummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ViewId', ], 'Arn' => [ 'shape' => 'ARN', ], 'Name' => [ 'shape' => 'ViewName', ], 'Type' => [ 'shape' => 'ViewType', ], 'Status' => [ 'shape' => 'ViewStatus', ], 'Description' => [ 'shape' => 'ViewDescription', ], ], ], 'ViewTemplate' => [ 'type' => 'string', ], 'ViewType' => [ 'type' => 'string', 'enum' => [ 'CUSTOMER_MANAGED', 'AWS_MANAGED', ], ], 'ViewVersion' => [ 'type' => 'integer', ], 'ViewVersionSummary' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ViewId', ], 'Arn' => [ 'shape' => 'ARN', ], 'Description' => [ 'shape' => 'ViewDescription', ], 'Name' => [ 'shape' => 'ViewName', ], 'Type' => [ 'shape' => 'ViewType', ], 'Version' => [ 'shape' => 'ViewVersion', ], 'VersionDescription' => [ 'shape' => 'ViewDescription', ], ], ], 'ViewVersionSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ViewVersionSummary', ], ], 'ViewsClientToken' => [ 'type' => 'string', 'max' => 500, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:\\/=+\\-@]*)$', ], 'ViewsInstanceId' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\_\\-:\\/]+$', ], 'ViewsNextToken' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, 'pattern' => '^[a-zA-Z0-9=\\/+_.-]+$', ], 'ViewsSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ViewSummary', ], ], 'Vocabulary' => [ 'type' => 'structure', 'required' => [ 'Name', 'Id', 'Arn', 'LanguageCode', 'State', 'LastModifiedTime', ], 'members' => [ 'Name' => [ 'shape' => 'VocabularyName', ], 'Id' => [ 'shape' => 'VocabularyId', ], 'Arn' => [ 'shape' => 'ARN', ], 'LanguageCode' => [ 'shape' => 'VocabularyLanguageCode', ], 'State' => [ 'shape' => 'VocabularyState', ], 'LastModifiedTime' => [ 'shape' => 'VocabularyLastModifiedTime', ], 'FailureReason' => [ 'shape' => 'VocabularyFailureReason', ], 'Content' => [ 'shape' => 'VocabularyContent', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'VocabularyContent' => [ 'type' => 'string', 'max' => 60000, 'min' => 1, ], 'VocabularyFailureReason' => [ 'type' => 'string', ], 'VocabularyId' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'VocabularyLanguageCode' => [ 'type' => 'string', 'enum' => [ 'ar-AE', 'de-CH', 'de-DE', 'en-AB', 'en-AU', 'en-GB', 'en-IE', 'en-IN', 'en-US', 'en-WL', 'es-ES', 'es-US', 'fr-CA', 'fr-FR', 'hi-IN', 'it-IT', 'ja-JP', 'ko-KR', 'pt-BR', 'pt-PT', 'zh-CN', 'en-NZ', 'en-ZA', 'ca-ES', 'da-DK', 'fi-FI', 'id-ID', 'ms-MY', 'nl-NL', 'no-NO', 'pl-PL', 'sv-SE', 'tl-PH', ], ], 'VocabularyLastModifiedTime' => [ 'type' => 'timestamp', ], 'VocabularyName' => [ 'type' => 'string', 'max' => 140, 'min' => 1, 'pattern' => '^[0-9a-zA-Z._-]+', ], 'VocabularyNextToken' => [ 'type' => 'string', 'max' => 131070, 'min' => 1, 'pattern' => '.*\\S.*', ], 'VocabularyState' => [ 'type' => 'string', 'enum' => [ 'CREATION_IN_PROGRESS', 'ACTIVE', 'CREATION_FAILED', 'DELETE_IN_PROGRESS', ], ], 'VocabularySummary' => [ 'type' => 'structure', 'required' => [ 'Name', 'Id', 'Arn', 'LanguageCode', 'State', 'LastModifiedTime', ], 'members' => [ 'Name' => [ 'shape' => 'VocabularyName', ], 'Id' => [ 'shape' => 'VocabularyId', ], 'Arn' => [ 'shape' => 'ARN', ], 'LanguageCode' => [ 'shape' => 'VocabularyLanguageCode', ], 'State' => [ 'shape' => 'VocabularyState', ], 'LastModifiedTime' => [ 'shape' => 'VocabularyLastModifiedTime', ], 'FailureReason' => [ 'shape' => 'VocabularyFailureReason', ], ], ], 'VocabularySummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VocabularySummary', ], ], 'VoiceRecordingConfiguration' => [ 'type' => 'structure', 'members' => [ 'VoiceRecordingTrack' => [ 'shape' => 'VoiceRecordingTrack', ], 'IvrRecordingTrack' => [ 'shape' => 'IvrRecordingTrack', ], ], ], 'VoiceRecordingTrack' => [ 'type' => 'string', 'enum' => [ 'FROM_AGENT', 'TO_AGENT', 'ALL', ], ], 'WisdomInfo' => [ 'type' => 'structure', 'members' => [ 'SessionArn' => [ 'shape' => 'ARN', ], ], ], 'resourceArnListMaxLimit100' => [ 'type' => 'list', 'member' => [ 'shape' => 'ARN', ], 'max' => 100, 'min' => 1, ], 'timestamp' => [ 'type' => 'timestamp', ], ],];
