<?php
// This file was auto-generated from sdk-root/src/data/gamelift/2015-10-01/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2015-10-01', 'endpointPrefix' => 'gamelift', 'jsonVersion' => '1.1', 'protocol' => 'json', 'protocols' => [ 'json', ], 'serviceFullName' => 'Amazon GameLift', 'serviceId' => 'GameLift', 'signatureVersion' => 'v4', 'targetPrefix' => 'GameLift', 'uid' => 'gamelift-2015-10-01', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'AcceptMatch' => [ 'name' => 'AcceptMatch', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AcceptMatchInput', ], 'output' => [ 'shape' => 'AcceptMatchOutput', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'UnsupportedRegionException', ], ], ], 'ClaimGameServer' => [ 'name' => 'ClaimGameServer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ClaimGameServerInput', ], 'output' => [ 'shape' => 'ClaimGameServerOutput', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'OutOfCapacityException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'CreateAlias' => [ 'name' => 'CreateAlias', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateAliasInput', ], 'output' => [ 'shape' => 'CreateAliasOutput', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'TaggingFailedException', ], ], ], 'CreateBuild' => [ 'name' => 'CreateBuild', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateBuildInput', ], 'output' => [ 'shape' => 'CreateBuildOutput', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'TaggingFailedException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'CreateContainerFleet' => [ 'name' => 'CreateContainerFleet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateContainerFleetInput', ], 'output' => [ 'shape' => 'CreateContainerFleetOutput', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'TaggingFailedException', ], [ 'shape' => 'UnsupportedRegionException', ], ], ], 'CreateContainerGroupDefinition' => [ 'name' => 'CreateContainerGroupDefinition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateContainerGroupDefinitionInput', ], 'output' => [ 'shape' => 'CreateContainerGroupDefinitionOutput', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'TaggingFailedException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'UnsupportedRegionException', ], ], ], 'CreateFleet' => [ 'name' => 'CreateFleet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateFleetInput', ], 'output' => [ 'shape' => 'CreateFleetOutput', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'NotReadyException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'TaggingFailedException', ], [ 'shape' => 'UnsupportedRegionException', ], ], ], 'CreateFleetLocations' => [ 'name' => 'CreateFleetLocations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateFleetLocationsInput', ], 'output' => [ 'shape' => 'CreateFleetLocationsOutput', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'NotReadyException', ], [ 'shape' => 'InvalidFleetStatusException', ], [ 'shape' => 'UnsupportedRegionException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'CreateGameServerGroup' => [ 'name' => 'CreateGameServerGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateGameServerGroupInput', ], 'output' => [ 'shape' => 'CreateGameServerGroupOutput', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'CreateGameSession' => [ 'name' => 'CreateGameSession', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateGameSessionInput', ], 'output' => [ 'shape' => 'CreateGameSessionOutput', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InvalidFleetStatusException', ], [ 'shape' => 'TerminalRoutingStrategyException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'FleetCapacityExceededException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], [ 'shape' => 'UnsupportedRegionException', ], ], ], 'CreateGameSessionQueue' => [ 'name' => 'CreateGameSessionQueue', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateGameSessionQueueInput', ], 'output' => [ 'shape' => 'CreateGameSessionQueueOutput', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TaggingFailedException', ], ], ], 'CreateLocation' => [ 'name' => 'CreateLocation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateLocationInput', ], 'output' => [ 'shape' => 'CreateLocationOutput', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'TaggingFailedException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'CreateMatchmakingConfiguration' => [ 'name' => 'CreateMatchmakingConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateMatchmakingConfigurationInput', ], 'output' => [ 'shape' => 'CreateMatchmakingConfigurationOutput', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'UnsupportedRegionException', ], [ 'shape' => 'TaggingFailedException', ], ], ], 'CreateMatchmakingRuleSet' => [ 'name' => 'CreateMatchmakingRuleSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateMatchmakingRuleSetInput', ], 'output' => [ 'shape' => 'CreateMatchmakingRuleSetOutput', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'UnsupportedRegionException', ], [ 'shape' => 'TaggingFailedException', ], ], ], 'CreatePlayerSession' => [ 'name' => 'CreatePlayerSession', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreatePlayerSessionInput', ], 'output' => [ 'shape' => 'CreatePlayerSessionOutput', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InvalidGameSessionStatusException', ], [ 'shape' => 'GameSessionFullException', ], [ 'shape' => 'TerminalRoutingStrategyException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'NotFoundException', ], ], ], 'CreatePlayerSessions' => [ 'name' => 'CreatePlayerSessions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreatePlayerSessionsInput', ], 'output' => [ 'shape' => 'CreatePlayerSessionsOutput', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InvalidGameSessionStatusException', ], [ 'shape' => 'GameSessionFullException', ], [ 'shape' => 'TerminalRoutingStrategyException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'NotFoundException', ], ], ], 'CreateScript' => [ 'name' => 'CreateScript', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateScriptInput', ], 'output' => [ 'shape' => 'CreateScriptOutput', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'TaggingFailedException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'CreateVpcPeeringAuthorization' => [ 'name' => 'CreateVpcPeeringAuthorization', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateVpcPeeringAuthorizationInput', ], 'output' => [ 'shape' => 'CreateVpcPeeringAuthorizationOutput', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'CreateVpcPeeringConnection' => [ 'name' => 'CreateVpcPeeringConnection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateVpcPeeringConnectionInput', ], 'output' => [ 'shape' => 'CreateVpcPeeringConnectionOutput', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DeleteAlias' => [ 'name' => 'DeleteAlias', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteAliasInput', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TaggingFailedException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DeleteBuild' => [ 'name' => 'DeleteBuild', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteBuildInput', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'TaggingFailedException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'DeleteContainerFleet' => [ 'name' => 'DeleteContainerFleet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteContainerFleetInput', ], 'output' => [ 'shape' => 'DeleteContainerFleetOutput', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TaggingFailedException', ], [ 'shape' => 'UnsupportedRegionException', ], ], ], 'DeleteContainerGroupDefinition' => [ 'name' => 'DeleteContainerGroupDefinition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteContainerGroupDefinitionInput', ], 'output' => [ 'shape' => 'DeleteContainerGroupDefinitionOutput', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TaggingFailedException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'UnsupportedRegionException', ], ], ], 'DeleteFleet' => [ 'name' => 'DeleteFleet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteFleetInput', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidFleetStatusException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TaggingFailedException', ], ], ], 'DeleteFleetLocations' => [ 'name' => 'DeleteFleetLocations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteFleetLocationsInput', ], 'output' => [ 'shape' => 'DeleteFleetLocationsOutput', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnsupportedRegionException', ], ], ], 'DeleteGameServerGroup' => [ 'name' => 'DeleteGameServerGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteGameServerGroupInput', ], 'output' => [ 'shape' => 'DeleteGameServerGroupOutput', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DeleteGameSessionQueue' => [ 'name' => 'DeleteGameSessionQueue', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteGameSessionQueueInput', ], 'output' => [ 'shape' => 'DeleteGameSessionQueueOutput', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'TaggingFailedException', ], ], ], 'DeleteLocation' => [ 'name' => 'DeleteLocation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteLocationInput', ], 'output' => [ 'shape' => 'DeleteLocationOutput', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DeleteMatchmakingConfiguration' => [ 'name' => 'DeleteMatchmakingConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteMatchmakingConfigurationInput', ], 'output' => [ 'shape' => 'DeleteMatchmakingConfigurationOutput', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'UnsupportedRegionException', ], [ 'shape' => 'TaggingFailedException', ], ], ], 'DeleteMatchmakingRuleSet' => [ 'name' => 'DeleteMatchmakingRuleSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteMatchmakingRuleSetInput', ], 'output' => [ 'shape' => 'DeleteMatchmakingRuleSetOutput', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'UnsupportedRegionException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TaggingFailedException', ], ], ], 'DeleteScalingPolicy' => [ 'name' => 'DeleteScalingPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteScalingPolicyInput', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnsupportedRegionException', ], ], ], 'DeleteScript' => [ 'name' => 'DeleteScript', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteScriptInput', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TaggingFailedException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DeleteVpcPeeringAuthorization' => [ 'name' => 'DeleteVpcPeeringAuthorization', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteVpcPeeringAuthorizationInput', ], 'output' => [ 'shape' => 'DeleteVpcPeeringAuthorizationOutput', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DeleteVpcPeeringConnection' => [ 'name' => 'DeleteVpcPeeringConnection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteVpcPeeringConnectionInput', ], 'output' => [ 'shape' => 'DeleteVpcPeeringConnectionOutput', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DeregisterCompute' => [ 'name' => 'DeregisterCompute', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeregisterComputeInput', ], 'output' => [ 'shape' => 'DeregisterComputeOutput', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DeregisterGameServer' => [ 'name' => 'DeregisterGameServer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeregisterGameServerInput', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DescribeAlias' => [ 'name' => 'DescribeAlias', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAliasInput', ], 'output' => [ 'shape' => 'DescribeAliasOutput', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DescribeBuild' => [ 'name' => 'DescribeBuild', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeBuildInput', ], 'output' => [ 'shape' => 'DescribeBuildOutput', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DescribeCompute' => [ 'name' => 'DescribeCompute', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeComputeInput', ], 'output' => [ 'shape' => 'DescribeComputeOutput', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'UnsupportedRegionException', ], ], ], 'DescribeContainerFleet' => [ 'name' => 'DescribeContainerFleet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeContainerFleetInput', ], 'output' => [ 'shape' => 'DescribeContainerFleetOutput', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'UnsupportedRegionException', ], ], ], 'DescribeContainerGroupDefinition' => [ 'name' => 'DescribeContainerGroupDefinition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeContainerGroupDefinitionInput', ], 'output' => [ 'shape' => 'DescribeContainerGroupDefinitionOutput', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'UnsupportedRegionException', ], ], ], 'DescribeEC2InstanceLimits' => [ 'name' => 'DescribeEC2InstanceLimits', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEC2InstanceLimitsInput', ], 'output' => [ 'shape' => 'DescribeEC2InstanceLimitsOutput', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'UnsupportedRegionException', ], ], ], 'DescribeFleetAttributes' => [ 'name' => 'DescribeFleetAttributes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeFleetAttributesInput', ], 'output' => [ 'shape' => 'DescribeFleetAttributesOutput', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'DescribeFleetCapacity' => [ 'name' => 'DescribeFleetCapacity', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeFleetCapacityInput', ], 'output' => [ 'shape' => 'DescribeFleetCapacityOutput', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'UnsupportedRegionException', ], ], ], 'DescribeFleetDeployment' => [ 'name' => 'DescribeFleetDeployment', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeFleetDeploymentInput', ], 'output' => [ 'shape' => 'DescribeFleetDeploymentOutput', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'UnsupportedRegionException', ], ], ], 'DescribeFleetEvents' => [ 'name' => 'DescribeFleetEvents', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeFleetEventsInput', ], 'output' => [ 'shape' => 'DescribeFleetEventsOutput', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'UnsupportedRegionException', ], ], ], 'DescribeFleetLocationAttributes' => [ 'name' => 'DescribeFleetLocationAttributes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeFleetLocationAttributesInput', ], 'output' => [ 'shape' => 'DescribeFleetLocationAttributesOutput', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnsupportedRegionException', ], ], ], 'DescribeFleetLocationCapacity' => [ 'name' => 'DescribeFleetLocationCapacity', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeFleetLocationCapacityInput', ], 'output' => [ 'shape' => 'DescribeFleetLocationCapacityOutput', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnsupportedRegionException', ], ], ], 'DescribeFleetLocationUtilization' => [ 'name' => 'DescribeFleetLocationUtilization', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeFleetLocationUtilizationInput', ], 'output' => [ 'shape' => 'DescribeFleetLocationUtilizationOutput', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnsupportedRegionException', ], ], ], 'DescribeFleetPortSettings' => [ 'name' => 'DescribeFleetPortSettings', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeFleetPortSettingsInput', ], 'output' => [ 'shape' => 'DescribeFleetPortSettingsOutput', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'UnsupportedRegionException', ], ], ], 'DescribeFleetUtilization' => [ 'name' => 'DescribeFleetUtilization', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeFleetUtilizationInput', ], 'output' => [ 'shape' => 'DescribeFleetUtilizationOutput', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'DescribeGameServer' => [ 'name' => 'DescribeGameServer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeGameServerInput', ], 'output' => [ 'shape' => 'DescribeGameServerOutput', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DescribeGameServerGroup' => [ 'name' => 'DescribeGameServerGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeGameServerGroupInput', ], 'output' => [ 'shape' => 'DescribeGameServerGroupOutput', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DescribeGameServerInstances' => [ 'name' => 'DescribeGameServerInstances', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeGameServerInstancesInput', ], 'output' => [ 'shape' => 'DescribeGameServerInstancesOutput', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DescribeGameSessionDetails' => [ 'name' => 'DescribeGameSessionDetails', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeGameSessionDetailsInput', ], 'output' => [ 'shape' => 'DescribeGameSessionDetailsOutput', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'TerminalRoutingStrategyException', ], [ 'shape' => 'UnsupportedRegionException', ], ], ], 'DescribeGameSessionPlacement' => [ 'name' => 'DescribeGameSessionPlacement', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeGameSessionPlacementInput', ], 'output' => [ 'shape' => 'DescribeGameSessionPlacementOutput', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'DescribeGameSessionQueues' => [ 'name' => 'DescribeGameSessionQueues', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeGameSessionQueuesInput', ], 'output' => [ 'shape' => 'DescribeGameSessionQueuesOutput', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'DescribeGameSessions' => [ 'name' => 'DescribeGameSessions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeGameSessionsInput', ], 'output' => [ 'shape' => 'DescribeGameSessionsOutput', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'TerminalRoutingStrategyException', ], [ 'shape' => 'UnsupportedRegionException', ], ], ], 'DescribeInstances' => [ 'name' => 'DescribeInstances', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeInstancesInput', ], 'output' => [ 'shape' => 'DescribeInstancesOutput', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'UnsupportedRegionException', ], ], ], 'DescribeMatchmaking' => [ 'name' => 'DescribeMatchmaking', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeMatchmakingInput', ], 'output' => [ 'shape' => 'DescribeMatchmakingOutput', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'UnsupportedRegionException', ], ], ], 'DescribeMatchmakingConfigurations' => [ 'name' => 'DescribeMatchmakingConfigurations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeMatchmakingConfigurationsInput', ], 'output' => [ 'shape' => 'DescribeMatchmakingConfigurationsOutput', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'UnsupportedRegionException', ], ], ], 'DescribeMatchmakingRuleSets' => [ 'name' => 'DescribeMatchmakingRuleSets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeMatchmakingRuleSetsInput', ], 'output' => [ 'shape' => 'DescribeMatchmakingRuleSetsOutput', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnsupportedRegionException', ], ], ], 'DescribePlayerSessions' => [ 'name' => 'DescribePlayerSessions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribePlayerSessionsInput', ], 'output' => [ 'shape' => 'DescribePlayerSessionsOutput', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'DescribeRuntimeConfiguration' => [ 'name' => 'DescribeRuntimeConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeRuntimeConfigurationInput', ], 'output' => [ 'shape' => 'DescribeRuntimeConfigurationOutput', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'DescribeScalingPolicies' => [ 'name' => 'DescribeScalingPolicies', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeScalingPoliciesInput', ], 'output' => [ 'shape' => 'DescribeScalingPoliciesOutput', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnsupportedRegionException', ], ], ], 'DescribeScript' => [ 'name' => 'DescribeScript', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeScriptInput', ], 'output' => [ 'shape' => 'DescribeScriptOutput', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'NotFoundException', ], ], ], 'DescribeVpcPeeringAuthorizations' => [ 'name' => 'DescribeVpcPeeringAuthorizations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeVpcPeeringAuthorizationsInput', ], 'output' => [ 'shape' => 'DescribeVpcPeeringAuthorizationsOutput', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DescribeVpcPeeringConnections' => [ 'name' => 'DescribeVpcPeeringConnections', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeVpcPeeringConnectionsInput', ], 'output' => [ 'shape' => 'DescribeVpcPeeringConnectionsOutput', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'GetComputeAccess' => [ 'name' => 'GetComputeAccess', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetComputeAccessInput', ], 'output' => [ 'shape' => 'GetComputeAccessOutput', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'UnsupportedRegionException', ], ], ], 'GetComputeAuthToken' => [ 'name' => 'GetComputeAuthToken', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetComputeAuthTokenInput', ], 'output' => [ 'shape' => 'GetComputeAuthTokenOutput', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'UnsupportedRegionException', ], ], ], 'GetGameSessionLogUrl' => [ 'name' => 'GetGameSessionLogUrl', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetGameSessionLogUrlInput', ], 'output' => [ 'shape' => 'GetGameSessionLogUrlOutput', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'GetInstanceAccess' => [ 'name' => 'GetInstanceAccess', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetInstanceAccessInput', ], 'output' => [ 'shape' => 'GetInstanceAccessOutput', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListAliases' => [ 'name' => 'ListAliases', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAliasesInput', ], 'output' => [ 'shape' => 'ListAliasesOutput', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListBuilds' => [ 'name' => 'ListBuilds', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListBuildsInput', ], 'output' => [ 'shape' => 'ListBuildsOutput', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListCompute' => [ 'name' => 'ListCompute', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListComputeInput', ], 'output' => [ 'shape' => 'ListComputeOutput', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'UnsupportedRegionException', ], ], ], 'ListContainerFleets' => [ 'name' => 'ListContainerFleets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListContainerFleetsInput', ], 'output' => [ 'shape' => 'ListContainerFleetsOutput', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'UnsupportedRegionException', ], ], ], 'ListContainerGroupDefinitionVersions' => [ 'name' => 'ListContainerGroupDefinitionVersions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListContainerGroupDefinitionVersionsInput', ], 'output' => [ 'shape' => 'ListContainerGroupDefinitionVersionsOutput', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'UnsupportedRegionException', ], ], ], 'ListContainerGroupDefinitions' => [ 'name' => 'ListContainerGroupDefinitions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListContainerGroupDefinitionsInput', ], 'output' => [ 'shape' => 'ListContainerGroupDefinitionsOutput', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'UnsupportedRegionException', ], ], ], 'ListFleetDeployments' => [ 'name' => 'ListFleetDeployments', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListFleetDeploymentsInput', ], 'output' => [ 'shape' => 'ListFleetDeploymentsOutput', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'UnsupportedRegionException', ], ], ], 'ListFleets' => [ 'name' => 'ListFleets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListFleetsInput', ], 'output' => [ 'shape' => 'ListFleetsOutput', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'ListGameServerGroups' => [ 'name' => 'ListGameServerGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListGameServerGroupsInput', ], 'output' => [ 'shape' => 'ListGameServerGroupsOutput', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListGameServers' => [ 'name' => 'ListGameServers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListGameServersInput', ], 'output' => [ 'shape' => 'ListGameServersOutput', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListLocations' => [ 'name' => 'ListLocations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListLocationsInput', ], 'output' => [ 'shape' => 'ListLocationsOutput', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListScripts' => [ 'name' => 'ListScripts', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListScriptsInput', ], 'output' => [ 'shape' => 'ListScriptsOutput', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TaggingFailedException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'UnsupportedRegionException', ], ], ], 'PutScalingPolicy' => [ 'name' => 'PutScalingPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutScalingPolicyInput', ], 'output' => [ 'shape' => 'PutScalingPolicyOutput', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnsupportedRegionException', ], ], ], 'RegisterCompute' => [ 'name' => 'RegisterCompute', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RegisterComputeInput', ], 'output' => [ 'shape' => 'RegisterComputeOutput', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'NotReadyException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'RegisterGameServer' => [ 'name' => 'RegisterGameServer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RegisterGameServerInput', ], 'output' => [ 'shape' => 'RegisterGameServerOutput', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'RequestUploadCredentials' => [ 'name' => 'RequestUploadCredentials', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RequestUploadCredentialsInput', ], 'output' => [ 'shape' => 'RequestUploadCredentialsOutput', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ResolveAlias' => [ 'name' => 'ResolveAlias', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ResolveAliasInput', ], 'output' => [ 'shape' => 'ResolveAliasOutput', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TerminalRoutingStrategyException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ResumeGameServerGroup' => [ 'name' => 'ResumeGameServerGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ResumeGameServerGroupInput', ], 'output' => [ 'shape' => 'ResumeGameServerGroupOutput', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'SearchGameSessions' => [ 'name' => 'SearchGameSessions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SearchGameSessionsInput', ], 'output' => [ 'shape' => 'SearchGameSessionsOutput', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'TerminalRoutingStrategyException', ], [ 'shape' => 'UnsupportedRegionException', ], ], ], 'StartFleetActions' => [ 'name' => 'StartFleetActions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartFleetActionsInput', ], 'output' => [ 'shape' => 'StartFleetActionsOutput', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnsupportedRegionException', ], ], ], 'StartGameSessionPlacement' => [ 'name' => 'StartGameSessionPlacement', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartGameSessionPlacementInput', ], 'output' => [ 'shape' => 'StartGameSessionPlacementOutput', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'UnsupportedRegionException', ], ], ], 'StartMatchBackfill' => [ 'name' => 'StartMatchBackfill', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartMatchBackfillInput', ], 'output' => [ 'shape' => 'StartMatchBackfillOutput', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'UnsupportedRegionException', ], ], ], 'StartMatchmaking' => [ 'name' => 'StartMatchmaking', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartMatchmakingInput', ], 'output' => [ 'shape' => 'StartMatchmakingOutput', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'UnsupportedRegionException', ], ], ], 'StopFleetActions' => [ 'name' => 'StopFleetActions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopFleetActionsInput', ], 'output' => [ 'shape' => 'StopFleetActionsOutput', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnsupportedRegionException', ], ], ], 'StopGameSessionPlacement' => [ 'name' => 'StopGameSessionPlacement', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopGameSessionPlacementInput', ], 'output' => [ 'shape' => 'StopGameSessionPlacementOutput', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'StopMatchmaking' => [ 'name' => 'StopMatchmaking', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopMatchmakingInput', ], 'output' => [ 'shape' => 'StopMatchmakingOutput', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'UnsupportedRegionException', ], ], ], 'SuspendGameServerGroup' => [ 'name' => 'SuspendGameServerGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SuspendGameServerGroupInput', ], 'output' => [ 'shape' => 'SuspendGameServerGroupOutput', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TaggingFailedException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'UnsupportedRegionException', ], ], ], 'TerminateGameSession' => [ 'name' => 'TerminateGameSession', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TerminateGameSessionInput', ], 'output' => [ 'shape' => 'TerminateGameSessionOutput', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InvalidGameSessionStatusException', ], [ 'shape' => 'NotReadyException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'TaggingFailedException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'UnsupportedRegionException', ], ], ], 'UpdateAlias' => [ 'name' => 'UpdateAlias', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateAliasInput', ], 'output' => [ 'shape' => 'UpdateAliasOutput', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateBuild' => [ 'name' => 'UpdateBuild', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateBuildInput', ], 'output' => [ 'shape' => 'UpdateBuildOutput', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateContainerFleet' => [ 'name' => 'UpdateContainerFleet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateContainerFleetInput', ], 'output' => [ 'shape' => 'UpdateContainerFleetOutput', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'NotReadyException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'UnsupportedRegionException', ], ], ], 'UpdateContainerGroupDefinition' => [ 'name' => 'UpdateContainerGroupDefinition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateContainerGroupDefinitionInput', ], 'output' => [ 'shape' => 'UpdateContainerGroupDefinitionOutput', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'UnsupportedRegionException', ], ], ], 'UpdateFleetAttributes' => [ 'name' => 'UpdateFleetAttributes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateFleetAttributesInput', ], 'output' => [ 'shape' => 'UpdateFleetAttributesOutput', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InvalidFleetStatusException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'UpdateFleetCapacity' => [ 'name' => 'UpdateFleetCapacity', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateFleetCapacityInput', ], 'output' => [ 'shape' => 'UpdateFleetCapacityOutput', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidFleetStatusException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'UnsupportedRegionException', ], ], ], 'UpdateFleetPortSettings' => [ 'name' => 'UpdateFleetPortSettings', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateFleetPortSettingsInput', ], 'output' => [ 'shape' => 'UpdateFleetPortSettingsOutput', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InvalidFleetStatusException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'UpdateGameServer' => [ 'name' => 'UpdateGameServer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateGameServerInput', ], 'output' => [ 'shape' => 'UpdateGameServerOutput', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateGameServerGroup' => [ 'name' => 'UpdateGameServerGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateGameServerGroupInput', ], 'output' => [ 'shape' => 'UpdateGameServerGroupOutput', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateGameSession' => [ 'name' => 'UpdateGameSession', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateGameSessionInput', ], 'output' => [ 'shape' => 'UpdateGameSessionOutput', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InvalidGameSessionStatusException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'UpdateGameSessionQueue' => [ 'name' => 'UpdateGameSessionQueue', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateGameSessionQueueInput', ], 'output' => [ 'shape' => 'UpdateGameSessionQueueOutput', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'UpdateMatchmakingConfiguration' => [ 'name' => 'UpdateMatchmakingConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateMatchmakingConfigurationInput', ], 'output' => [ 'shape' => 'UpdateMatchmakingConfigurationOutput', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'UnsupportedRegionException', ], ], ], 'UpdateRuntimeConfiguration' => [ 'name' => 'UpdateRuntimeConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateRuntimeConfigurationInput', ], 'output' => [ 'shape' => 'UpdateRuntimeConfigurationOutput', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidFleetStatusException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'UpdateScript' => [ 'name' => 'UpdateScript', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateScriptInput', ], 'output' => [ 'shape' => 'UpdateScriptOutput', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ValidateMatchmakingRuleSet' => [ 'name' => 'ValidateMatchmakingRuleSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ValidateMatchmakingRuleSetInput', ], 'output' => [ 'shape' => 'ValidateMatchmakingRuleSetOutput', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'UnsupportedRegionException', ], [ 'shape' => 'InvalidRequestException', ], ], ], ], 'shapes' => [ 'AcceptMatchInput' => [ 'type' => 'structure', 'required' => [ 'TicketId', 'PlayerIds', 'AcceptanceType', ], 'members' => [ 'TicketId' => [ 'shape' => 'MatchmakingIdStringModel', ], 'PlayerIds' => [ 'shape' => 'PlayerIdsForAcceptMatch', ], 'AcceptanceType' => [ 'shape' => 'AcceptanceType', ], ], ], 'AcceptMatchOutput' => [ 'type' => 'structure', 'members' => [], ], 'AcceptanceType' => [ 'type' => 'string', 'enum' => [ 'ACCEPT', 'REJECT', ], ], 'Alias' => [ 'type' => 'structure', 'members' => [ 'AliasId' => [ 'shape' => 'AliasId', ], 'Name' => [ 'shape' => 'NonBlankAndLengthConstraintString', ], 'AliasArn' => [ 'shape' => 'AliasArn', ], 'Description' => [ 'shape' => 'FreeText', ], 'RoutingStrategy' => [ 'shape' => 'RoutingStrategy', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastUpdatedTime' => [ 'shape' => 'Timestamp', ], ], ], 'AliasArn' => [ 'type' => 'string', 'pattern' => '^arn:.*:alias\\/alias-\\S+', ], 'AliasId' => [ 'type' => 'string', 'pattern' => '^alias-\\S+', ], 'AliasIdOrArn' => [ 'type' => 'string', 'pattern' => '^alias-\\S+|^arn:.*:alias\\/alias-\\S+', ], 'AliasList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Alias', ], ], 'AmazonResourceName' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, ], 'AnywhereConfiguration' => [ 'type' => 'structure', 'required' => [ 'Cost', ], 'members' => [ 'Cost' => [ 'shape' => 'NonNegativeLimitedLengthDouble', ], ], ], 'ArnStringModel' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '[a-zA-Z0-9:/-]+', ], 'AttributeValue' => [ 'type' => 'structure', 'members' => [ 'S' => [ 'shape' => 'PlayerAttributeString', ], 'N' => [ 'shape' => 'DoubleObject', ], 'SL' => [ 'shape' => 'PlayerAttributeStringList', ], 'SDM' => [ 'shape' => 'PlayerAttributeStringDoubleMap', ], ], ], 'AutoScalingGroupArn' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]*', ], 'AwsCredentials' => [ 'type' => 'structure', 'members' => [ 'AccessKeyId' => [ 'shape' => 'NonEmptyString', ], 'SecretAccessKey' => [ 'shape' => 'NonEmptyString', ], 'SessionToken' => [ 'shape' => 'NonEmptyString', ], ], 'sensitive' => true, ], 'BackfillMode' => [ 'type' => 'string', 'enum' => [ 'AUTOMATIC', 'MANUAL', ], ], 'BalancingStrategy' => [ 'type' => 'string', 'enum' => [ 'SPOT_ONLY', 'SPOT_PREFERRED', 'ON_DEMAND_ONLY', ], ], 'BooleanModel' => [ 'type' => 'boolean', ], 'Build' => [ 'type' => 'structure', 'members' => [ 'BuildId' => [ 'shape' => 'BuildId', ], 'BuildArn' => [ 'shape' => 'BuildArn', ], 'Name' => [ 'shape' => 'FreeText', ], 'Version' => [ 'shape' => 'FreeText', ], 'Status' => [ 'shape' => 'BuildStatus', ], 'SizeOnDisk' => [ 'shape' => 'PositiveLong', ], 'OperatingSystem' => [ 'shape' => 'OperatingSystem', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'ServerSdkVersion' => [ 'shape' => 'ServerSdkVersion', ], ], ], 'BuildArn' => [ 'type' => 'string', 'pattern' => '^arn:.*:build\\/build-\\S+', ], 'BuildId' => [ 'type' => 'string', 'pattern' => '^build-\\S+', ], 'BuildIdOrArn' => [ 'type' => 'string', 'pattern' => '^build-\\S+|^arn:.*:build\\/build-\\S+', ], 'BuildList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Build', ], ], 'BuildStatus' => [ 'type' => 'string', 'enum' => [ 'INITIALIZED', 'READY', 'FAILED', ], ], 'CertificateConfiguration' => [ 'type' => 'structure', 'required' => [ 'CertificateType', ], 'members' => [ 'CertificateType' => [ 'shape' => 'CertificateType', ], ], ], 'CertificateType' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'GENERATED', ], ], 'ClaimFilterOption' => [ 'type' => 'structure', 'members' => [ 'InstanceStatuses' => [ 'shape' => 'FilterInstanceStatuses', ], ], ], 'ClaimGameServerInput' => [ 'type' => 'structure', 'required' => [ 'GameServerGroupName', ], 'members' => [ 'GameServerGroupName' => [ 'shape' => 'GameServerGroupNameOrArn', ], 'GameServerId' => [ 'shape' => 'GameServerId', ], 'GameServerData' => [ 'shape' => 'GameServerData', ], 'FilterOption' => [ 'shape' => 'ClaimFilterOption', ], ], ], 'ClaimGameServerOutput' => [ 'type' => 'structure', 'members' => [ 'GameServer' => [ 'shape' => 'GameServer', ], ], ], 'ComparisonOperatorType' => [ 'type' => 'string', 'enum' => [ 'GreaterThanOrEqualToThreshold', 'GreaterThanThreshold', 'LessThanThreshold', 'LessThanOrEqualToThreshold', ], ], 'Compute' => [ 'type' => 'structure', 'members' => [ 'FleetId' => [ 'shape' => 'FleetId', ], 'FleetArn' => [ 'shape' => 'FleetArn', ], 'ComputeName' => [ 'shape' => 'ComputeName', ], 'ComputeArn' => [ 'shape' => 'ComputeArn', ], 'IpAddress' => [ 'shape' => 'IpAddress', ], 'DnsName' => [ 'shape' => 'DnsName', ], 'ComputeStatus' => [ 'shape' => 'ComputeStatus', ], 'Location' => [ 'shape' => 'LocationStringModel', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'OperatingSystem' => [ 'shape' => 'OperatingSystem', ], 'Type' => [ 'shape' => 'EC2InstanceType', ], 'GameLiftServiceSdkEndpoint' => [ 'shape' => 'GameLiftServiceSdkEndpointOutput', ], 'GameLiftAgentEndpoint' => [ 'shape' => 'GameLiftAgentEndpointOutput', ], 'InstanceId' => [ 'shape' => 'InstanceId', ], 'ContainerAttributes' => [ 'shape' => 'ContainerAttributes', ], 'GameServerContainerGroupDefinitionArn' => [ 'shape' => 'ContainerGroupDefinitionNameOrArn', ], ], ], 'ComputeArn' => [ 'type' => 'string', 'max' => 1024, 'pattern' => '^arn:.*:compute\\/[a-zA-Z0-9\\-]+(\\/[a-zA-Z0-9\\-]+)?', ], 'ComputeAuthToken' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\-]+', ], 'ComputeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Compute', ], ], 'ComputeName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\-]+(\\/[a-zA-Z0-9\\-]+)?', ], 'ComputeNameOrArn' => [ 'type' => 'string', 'max' => 1024, 'pattern' => '^[a-zA-Z0-9\\-]+(\\/[a-zA-Z0-9\\-]+)?$|^arn:.*:compute\\/[a-zA-Z0-9\\-]+(\\/[a-zA-Z0-9\\-]+)?', ], 'ComputeStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'ACTIVE', 'TERMINATING', 'IMPAIRED', ], ], 'ComputeType' => [ 'type' => 'string', 'enum' => [ 'EC2', 'ANYWHERE', ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'NonEmptyString', ], ], 'exception' => true, ], 'ConnectionPortRange' => [ 'type' => 'structure', 'required' => [ 'FromPort', 'ToPort', ], 'members' => [ 'FromPort' => [ 'shape' => 'PortNumber', ], 'ToPort' => [ 'shape' => 'PortNumber', ], ], ], 'ContainerAttribute' => [ 'type' => 'structure', 'members' => [ 'ContainerName' => [ 'shape' => 'NonZeroAnd128MaxAsciiString', ], 'ContainerRuntimeId' => [ 'shape' => 'NonEmptyString', ], ], ], 'ContainerAttributes' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContainerAttribute', ], 'max' => 10, 'min' => 1, ], 'ContainerCommandStringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonZeroAnd255MaxString', ], 'max' => 20, 'min' => 1, ], 'ContainerDependency' => [ 'type' => 'structure', 'required' => [ 'ContainerName', 'Condition', ], 'members' => [ 'ContainerName' => [ 'shape' => 'NonZeroAnd128MaxAsciiString', ], 'Condition' => [ 'shape' => 'ContainerDependencyCondition', ], ], ], 'ContainerDependencyCondition' => [ 'type' => 'string', 'enum' => [ 'START', 'COMPLETE', 'SUCCESS', 'HEALTHY', ], ], 'ContainerDependencyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContainerDependency', ], 'max' => 10, 'min' => 1, ], 'ContainerEnvironment' => [ 'type' => 'structure', 'required' => [ 'Name', 'Value', ], 'members' => [ 'Name' => [ 'shape' => 'NonZeroAnd255MaxString', ], 'Value' => [ 'shape' => 'NonZeroAnd255MaxString', ], ], ], 'ContainerEnvironmentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContainerEnvironment', ], 'max' => 20, 'min' => 1, ], 'ContainerFleet' => [ 'type' => 'structure', 'members' => [ 'FleetId' => [ 'shape' => 'FleetId', ], 'FleetArn' => [ 'shape' => 'FleetArn', ], 'FleetRoleArn' => [ 'shape' => 'IamRoleArn', ], 'GameServerContainerGroupDefinitionName' => [ 'shape' => 'ContainerGroupDefinitionName', ], 'GameServerContainerGroupDefinitionArn' => [ 'shape' => 'ContainerGroupDefinitionArn', ], 'PerInstanceContainerGroupDefinitionName' => [ 'shape' => 'ContainerGroupDefinitionName', ], 'PerInstanceContainerGroupDefinitionArn' => [ 'shape' => 'ContainerGroupDefinitionArn', ], 'InstanceConnectionPortRange' => [ 'shape' => 'ConnectionPortRange', ], 'InstanceInboundPermissions' => [ 'shape' => 'IpPermissionsList', ], 'GameServerContainerGroupsPerInstance' => [ 'shape' => 'GameServerContainerGroupsPerInstance', ], 'MaximumGameServerContainerGroupsPerInstance' => [ 'shape' => 'MaximumGameServerContainerGroupsPerInstance', ], 'InstanceType' => [ 'shape' => 'NonZeroAndMaxString', ], 'BillingType' => [ 'shape' => 'ContainerFleetBillingType', ], 'Description' => [ 'shape' => 'NonZeroAndMaxString', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'MetricGroups' => [ 'shape' => 'MetricGroupList', ], 'NewGameSessionProtectionPolicy' => [ 'shape' => 'ProtectionPolicy', ], 'GameSessionCreationLimitPolicy' => [ 'shape' => 'GameSessionCreationLimitPolicy', ], 'Status' => [ 'shape' => 'ContainerFleetStatus', ], 'DeploymentDetails' => [ 'shape' => 'DeploymentDetails', ], 'LogConfiguration' => [ 'shape' => 'LogConfiguration', ], 'LocationAttributes' => [ 'shape' => 'ContainerFleetLocationAttributesList', ], ], ], 'ContainerFleetBillingType' => [ 'type' => 'string', 'enum' => [ 'ON_DEMAND', 'SPOT', ], ], 'ContainerFleetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContainerFleet', ], 'min' => 1, ], 'ContainerFleetLocationAttributes' => [ 'type' => 'structure', 'members' => [ 'Location' => [ 'shape' => 'LocationStringModel', ], 'Status' => [ 'shape' => 'ContainerFleetLocationStatus', ], ], ], 'ContainerFleetLocationAttributesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContainerFleetLocationAttributes', ], ], 'ContainerFleetLocationStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'CREATING', 'CREATED', 'ACTIVATING', 'ACTIVE', 'UPDATING', 'DELETING', ], ], 'ContainerFleetRemoveAttribute' => [ 'type' => 'string', 'enum' => [ 'PER_INSTANCE_CONTAINER_GROUP_DEFINITION', ], ], 'ContainerFleetRemoveAttributeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContainerFleetRemoveAttribute', ], 'max' => 1, 'min' => 1, ], 'ContainerFleetStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'CREATING', 'CREATED', 'ACTIVATING', 'ACTIVE', 'UPDATING', 'DELETING', ], ], 'ContainerGroupDefinition' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'ContainerGroupDefinitionArn' => [ 'shape' => 'ContainerGroupDefinitionArn', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'OperatingSystem' => [ 'shape' => 'ContainerOperatingSystem', ], 'Name' => [ 'shape' => 'ContainerGroupDefinitionName', ], 'ContainerGroupType' => [ 'shape' => 'ContainerGroupType', ], 'TotalMemoryLimitMebibytes' => [ 'shape' => 'ContainerTotalMemoryLimit', ], 'TotalVcpuLimit' => [ 'shape' => 'ContainerTotalVcpuLimit', ], 'GameServerContainerDefinition' => [ 'shape' => 'GameServerContainerDefinition', ], 'SupportContainerDefinitions' => [ 'shape' => 'SupportContainerDefinitionList', ], 'VersionNumber' => [ 'shape' => 'PositiveInteger', ], 'VersionDescription' => [ 'shape' => 'NonZeroAndMaxString', ], 'Status' => [ 'shape' => 'ContainerGroupDefinitionStatus', ], 'StatusReason' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'ContainerGroupDefinitionArn' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '^arn:.*:containergroupdefinition\\/[a-zA-Z0-9\\-]+(:[0-9]+)?$', ], 'ContainerGroupDefinitionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContainerGroupDefinition', ], ], 'ContainerGroupDefinitionName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\-]+$', ], 'ContainerGroupDefinitionNameOrArn' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\-]+$|^arn:.*:containergroupdefinition\\/[a-zA-Z0-9\\-]+(:[0-9]+)?$', ], 'ContainerGroupDefinitionStatus' => [ 'type' => 'string', 'enum' => [ 'READY', 'COPYING', 'FAILED', ], ], 'ContainerGroupType' => [ 'type' => 'string', 'enum' => [ 'GAME_SERVER', 'PER_INSTANCE', ], ], 'ContainerHealthCheck' => [ 'type' => 'structure', 'required' => [ 'Command', ], 'members' => [ 'Command' => [ 'shape' => 'ContainerCommandStringList', ], 'Interval' => [ 'shape' => 'ContainerHealthCheckInterval', ], 'Retries' => [ 'shape' => 'ContainerHealthCheckRetries', ], 'StartPeriod' => [ 'shape' => 'ContainerHealthCheckStartPeriod', ], 'Timeout' => [ 'shape' => 'ContainerHealthCheckTimeout', ], ], ], 'ContainerHealthCheckInterval' => [ 'type' => 'integer', 'max' => 300, 'min' => 60, ], 'ContainerHealthCheckRetries' => [ 'type' => 'integer', 'max' => 10, 'min' => 5, ], 'ContainerHealthCheckStartPeriod' => [ 'type' => 'integer', 'max' => 300, 'min' => 0, ], 'ContainerHealthCheckTimeout' => [ 'type' => 'integer', 'max' => 60, 'min' => 30, ], 'ContainerIdentifier' => [ 'type' => 'structure', 'members' => [ 'ContainerName' => [ 'shape' => 'NonZeroAnd128MaxAsciiString', ], 'ContainerRuntimeId' => [ 'shape' => 'NonEmptyString', ], ], ], 'ContainerIdentifierList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContainerIdentifier', ], 'max' => 10, 'min' => 1, ], 'ContainerMemoryLimit' => [ 'type' => 'integer', 'max' => 1024000, 'min' => 4, ], 'ContainerMountPoint' => [ 'type' => 'structure', 'required' => [ 'InstancePath', ], 'members' => [ 'InstancePath' => [ 'shape' => 'InstancePathString', ], 'ContainerPath' => [ 'shape' => 'ContainerPathString', ], 'AccessLevel' => [ 'shape' => 'ContainerMountPointAccessLevel', ], ], ], 'ContainerMountPointAccessLevel' => [ 'type' => 'string', 'enum' => [ 'READ_ONLY', 'READ_AND_WRITE', ], ], 'ContainerMountPointList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContainerMountPoint', ], 'max' => 10, 'min' => 1, ], 'ContainerOperatingSystem' => [ 'type' => 'string', 'enum' => [ 'AMAZON_LINUX_2023', ], ], 'ContainerPathString' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^(\\/+[^\\/]+\\/*)+$', ], 'ContainerPortConfiguration' => [ 'type' => 'structure', 'required' => [ 'ContainerPortRanges', ], 'members' => [ 'ContainerPortRanges' => [ 'shape' => 'ContainerPortRangeList', ], ], ], 'ContainerPortRange' => [ 'type' => 'structure', 'required' => [ 'FromPort', 'ToPort', 'Protocol', ], 'members' => [ 'FromPort' => [ 'shape' => 'PortNumber', ], 'ToPort' => [ 'shape' => 'PortNumber', ], 'Protocol' => [ 'shape' => 'IpProtocol', ], ], ], 'ContainerPortRangeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContainerPortRange', ], 'max' => 100, 'min' => 1, ], 'ContainerTotalMemoryLimit' => [ 'type' => 'integer', 'max' => 1024000, 'min' => 4, ], 'ContainerTotalVcpuLimit' => [ 'type' => 'double', 'max' => 10, 'min' => 0.125, ], 'ContainerVcpu' => [ 'type' => 'double', 'max' => 10, 'min' => 0.125, ], 'CreateAliasInput' => [ 'type' => 'structure', 'required' => [ 'Name', 'RoutingStrategy', ], 'members' => [ 'Name' => [ 'shape' => 'NonBlankAndLengthConstraintString', ], 'Description' => [ 'shape' => 'NonZeroAndMaxString', ], 'RoutingStrategy' => [ 'shape' => 'RoutingStrategy', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateAliasOutput' => [ 'type' => 'structure', 'members' => [ 'Alias' => [ 'shape' => 'Alias', ], ], ], 'CreateBuildInput' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonZeroAndMaxString', ], 'Version' => [ 'shape' => 'NonZeroAndMaxString', ], 'StorageLocation' => [ 'shape' => 'S3Location', ], 'OperatingSystem' => [ 'shape' => 'OperatingSystem', ], 'Tags' => [ 'shape' => 'TagList', ], 'ServerSdkVersion' => [ 'shape' => 'ServerSdkVersion', ], ], ], 'CreateBuildOutput' => [ 'type' => 'structure', 'members' => [ 'Build' => [ 'shape' => 'Build', ], 'UploadCredentials' => [ 'shape' => 'AwsCredentials', ], 'StorageLocation' => [ 'shape' => 'S3Location', ], ], ], 'CreateContainerFleetInput' => [ 'type' => 'structure', 'required' => [ 'FleetRoleArn', ], 'members' => [ 'FleetRoleArn' => [ 'shape' => 'IamRoleArn', ], 'Description' => [ 'shape' => 'NonZeroAndMaxString', ], 'GameServerContainerGroupDefinitionName' => [ 'shape' => 'ContainerGroupDefinitionNameOrArn', ], 'PerInstanceContainerGroupDefinitionName' => [ 'shape' => 'ContainerGroupDefinitionNameOrArn', ], 'InstanceConnectionPortRange' => [ 'shape' => 'ConnectionPortRange', ], 'InstanceInboundPermissions' => [ 'shape' => 'IpPermissionsList', ], 'GameServerContainerGroupsPerInstance' => [ 'shape' => 'GameServerContainerGroupsPerInstance', ], 'InstanceType' => [ 'shape' => 'NonZeroAndMaxString', ], 'BillingType' => [ 'shape' => 'ContainerFleetBillingType', ], 'Locations' => [ 'shape' => 'LocationConfigurationList', ], 'MetricGroups' => [ 'shape' => 'MetricGroupList', ], 'NewGameSessionProtectionPolicy' => [ 'shape' => 'ProtectionPolicy', ], 'GameSessionCreationLimitPolicy' => [ 'shape' => 'GameSessionCreationLimitPolicy', ], 'LogConfiguration' => [ 'shape' => 'LogConfiguration', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateContainerFleetOutput' => [ 'type' => 'structure', 'members' => [ 'ContainerFleet' => [ 'shape' => 'ContainerFleet', ], ], ], 'CreateContainerGroupDefinitionInput' => [ 'type' => 'structure', 'required' => [ 'Name', 'TotalMemoryLimitMebibytes', 'TotalVcpuLimit', 'OperatingSystem', ], 'members' => [ 'Name' => [ 'shape' => 'ContainerGroupDefinitionName', ], 'ContainerGroupType' => [ 'shape' => 'ContainerGroupType', ], 'TotalMemoryLimitMebibytes' => [ 'shape' => 'ContainerTotalMemoryLimit', ], 'TotalVcpuLimit' => [ 'shape' => 'ContainerTotalVcpuLimit', ], 'GameServerContainerDefinition' => [ 'shape' => 'GameServerContainerDefinitionInput', ], 'SupportContainerDefinitions' => [ 'shape' => 'SupportContainerDefinitionInputList', ], 'OperatingSystem' => [ 'shape' => 'ContainerOperatingSystem', ], 'VersionDescription' => [ 'shape' => 'NonZeroAndMaxString', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateContainerGroupDefinitionOutput' => [ 'type' => 'structure', 'members' => [ 'ContainerGroupDefinition' => [ 'shape' => 'ContainerGroupDefinition', ], ], ], 'CreateFleetInput' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NonZeroAndMaxString', ], 'Description' => [ 'shape' => 'NonZeroAndMaxString', ], 'BuildId' => [ 'shape' => 'BuildIdOrArn', ], 'ScriptId' => [ 'shape' => 'ScriptIdOrArn', ], 'ServerLaunchPath' => [ 'shape' => 'LaunchPathStringModel', ], 'ServerLaunchParameters' => [ 'shape' => 'LaunchParametersStringModel', ], 'LogPaths' => [ 'shape' => 'StringList', ], 'EC2InstanceType' => [ 'shape' => 'EC2InstanceType', ], 'EC2InboundPermissions' => [ 'shape' => 'IpPermissionsList', ], 'NewGameSessionProtectionPolicy' => [ 'shape' => 'ProtectionPolicy', ], 'RuntimeConfiguration' => [ 'shape' => 'RuntimeConfiguration', ], 'ResourceCreationLimitPolicy' => [ 'shape' => 'ResourceCreationLimitPolicy', ], 'MetricGroups' => [ 'shape' => 'MetricGroupList', ], 'PeerVpcAwsAccountId' => [ 'shape' => 'NonZeroAndMaxString', ], 'PeerVpcId' => [ 'shape' => 'NonZeroAndMaxString', ], 'FleetType' => [ 'shape' => 'FleetType', ], 'InstanceRoleArn' => [ 'shape' => 'NonEmptyString', ], 'CertificateConfiguration' => [ 'shape' => 'CertificateConfiguration', ], 'Locations' => [ 'shape' => 'LocationConfigurationList', ], 'Tags' => [ 'shape' => 'TagList', ], 'ComputeType' => [ 'shape' => 'ComputeType', ], 'AnywhereConfiguration' => [ 'shape' => 'AnywhereConfiguration', ], 'InstanceRoleCredentialsProvider' => [ 'shape' => 'InstanceRoleCredentialsProvider', ], ], ], 'CreateFleetLocationsInput' => [ 'type' => 'structure', 'required' => [ 'FleetId', 'Locations', ], 'members' => [ 'FleetId' => [ 'shape' => 'FleetIdOrArn', ], 'Locations' => [ 'shape' => 'LocationConfigurationList', ], ], ], 'CreateFleetLocationsOutput' => [ 'type' => 'structure', 'members' => [ 'FleetId' => [ 'shape' => 'FleetIdOrArn', ], 'FleetArn' => [ 'shape' => 'FleetArn', ], 'LocationStates' => [ 'shape' => 'LocationStateList', ], ], ], 'CreateFleetOutput' => [ 'type' => 'structure', 'members' => [ 'FleetAttributes' => [ 'shape' => 'FleetAttributes', ], 'LocationStates' => [ 'shape' => 'LocationStateList', ], ], ], 'CreateGameServerGroupInput' => [ 'type' => 'structure', 'required' => [ 'GameServerGroupName', 'RoleArn', 'MinSize', 'MaxSize', 'LaunchTemplate', 'InstanceDefinitions', ], 'members' => [ 'GameServerGroupName' => [ 'shape' => 'GameServerGroupName', ], 'RoleArn' => [ 'shape' => 'IamRoleArn', ], 'MinSize' => [ 'shape' => 'WholeNumber', ], 'MaxSize' => [ 'shape' => 'PositiveInteger', ], 'LaunchTemplate' => [ 'shape' => 'LaunchTemplateSpecification', ], 'InstanceDefinitions' => [ 'shape' => 'InstanceDefinitions', ], 'AutoScalingPolicy' => [ 'shape' => 'GameServerGroupAutoScalingPolicy', ], 'BalancingStrategy' => [ 'shape' => 'BalancingStrategy', ], 'GameServerProtectionPolicy' => [ 'shape' => 'GameServerProtectionPolicy', ], 'VpcSubnets' => [ 'shape' => 'VpcSubnets', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateGameServerGroupOutput' => [ 'type' => 'structure', 'members' => [ 'GameServerGroup' => [ 'shape' => 'GameServerGroup', ], ], ], 'CreateGameSessionInput' => [ 'type' => 'structure', 'required' => [ 'MaximumPlayerSessionCount', ], 'members' => [ 'FleetId' => [ 'shape' => 'FleetIdOrArn', ], 'AliasId' => [ 'shape' => 'AliasIdOrArn', ], 'MaximumPlayerSessionCount' => [ 'shape' => 'WholeNumber', ], 'Name' => [ 'shape' => 'NonZeroAndMaxString', ], 'GameProperties' => [ 'shape' => 'GamePropertyList', ], 'CreatorId' => [ 'shape' => 'NonZeroAndMaxString', ], 'GameSessionId' => [ 'shape' => 'IdStringModel', ], 'IdempotencyToken' => [ 'shape' => 'IdStringModel', ], 'GameSessionData' => [ 'shape' => 'LargeGameSessionData', ], 'Location' => [ 'shape' => 'LocationStringModel', ], ], ], 'CreateGameSessionOutput' => [ 'type' => 'structure', 'members' => [ 'GameSession' => [ 'shape' => 'GameSession', ], ], ], 'CreateGameSessionQueueInput' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'GameSessionQueueName', ], 'TimeoutInSeconds' => [ 'shape' => 'WholeNumber', ], 'PlayerLatencyPolicies' => [ 'shape' => 'PlayerLatencyPolicyList', ], 'Destinations' => [ 'shape' => 'GameSessionQueueDestinationList', ], 'FilterConfiguration' => [ 'shape' => 'FilterConfiguration', ], 'PriorityConfiguration' => [ 'shape' => 'PriorityConfiguration', ], 'CustomEventData' => [ 'shape' => 'QueueCustomEventData', ], 'NotificationTarget' => [ 'shape' => 'QueueSnsArnStringModel', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateGameSessionQueueOutput' => [ 'type' => 'structure', 'members' => [ 'GameSessionQueue' => [ 'shape' => 'GameSessionQueue', ], ], ], 'CreateLocationInput' => [ 'type' => 'structure', 'required' => [ 'LocationName', ], 'members' => [ 'LocationName' => [ 'shape' => 'CustomInputLocationStringModel', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateLocationOutput' => [ 'type' => 'structure', 'members' => [ 'Location' => [ 'shape' => 'LocationModel', ], ], ], 'CreateMatchmakingConfigurationInput' => [ 'type' => 'structure', 'required' => [ 'Name', 'RequestTimeoutSeconds', 'AcceptanceRequired', 'RuleSetName', ], 'members' => [ 'Name' => [ 'shape' => 'MatchmakingIdStringModel', ], 'Description' => [ 'shape' => 'NonZeroAndMaxString', ], 'GameSessionQueueArns' => [ 'shape' => 'QueueArnsList', ], 'RequestTimeoutSeconds' => [ 'shape' => 'MatchmakingRequestTimeoutInteger', ], 'AcceptanceTimeoutSeconds' => [ 'shape' => 'MatchmakingAcceptanceTimeoutInteger', ], 'AcceptanceRequired' => [ 'shape' => 'BooleanModel', ], 'RuleSetName' => [ 'shape' => 'MatchmakingRuleSetName', ], 'NotificationTarget' => [ 'shape' => 'SnsArnStringModel', ], 'AdditionalPlayerCount' => [ 'shape' => 'WholeNumber', ], 'CustomEventData' => [ 'shape' => 'CustomEventData', ], 'GameProperties' => [ 'shape' => 'GamePropertyList', ], 'GameSessionData' => [ 'shape' => 'GameSessionData', ], 'BackfillMode' => [ 'shape' => 'BackfillMode', ], 'FlexMatchMode' => [ 'shape' => 'FlexMatchMode', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateMatchmakingConfigurationOutput' => [ 'type' => 'structure', 'members' => [ 'Configuration' => [ 'shape' => 'MatchmakingConfiguration', ], ], ], 'CreateMatchmakingRuleSetInput' => [ 'type' => 'structure', 'required' => [ 'Name', 'RuleSetBody', ], 'members' => [ 'Name' => [ 'shape' => 'MatchmakingIdStringModel', ], 'RuleSetBody' => [ 'shape' => 'RuleSetBody', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateMatchmakingRuleSetOutput' => [ 'type' => 'structure', 'required' => [ 'RuleSet', ], 'members' => [ 'RuleSet' => [ 'shape' => 'MatchmakingRuleSet', ], ], ], 'CreatePlayerSessionInput' => [ 'type' => 'structure', 'required' => [ 'GameSessionId', 'PlayerId', ], 'members' => [ 'GameSessionId' => [ 'shape' => 'ArnStringModel', ], 'PlayerId' => [ 'shape' => 'PlayerId', ], 'PlayerData' => [ 'shape' => 'PlayerData', ], ], ], 'CreatePlayerSessionOutput' => [ 'type' => 'structure', 'members' => [ 'PlayerSession' => [ 'shape' => 'PlayerSession', ], ], ], 'CreatePlayerSessionsInput' => [ 'type' => 'structure', 'required' => [ 'GameSessionId', 'PlayerIds', ], 'members' => [ 'GameSessionId' => [ 'shape' => 'ArnStringModel', ], 'PlayerIds' => [ 'shape' => 'PlayerIdList', ], 'PlayerDataMap' => [ 'shape' => 'PlayerDataMap', ], ], ], 'CreatePlayerSessionsOutput' => [ 'type' => 'structure', 'members' => [ 'PlayerSessions' => [ 'shape' => 'PlayerSessionList', ], ], ], 'CreateScriptInput' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonZeroAndMaxString', ], 'Version' => [ 'shape' => 'NonZeroAndMaxString', ], 'StorageLocation' => [ 'shape' => 'S3Location', ], 'ZipFile' => [ 'shape' => 'ZipBlob', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateScriptOutput' => [ 'type' => 'structure', 'members' => [ 'Script' => [ 'shape' => 'Script', ], ], ], 'CreateVpcPeeringAuthorizationInput' => [ 'type' => 'structure', 'required' => [ 'GameLiftAwsAccountId', 'PeerVpcId', ], 'members' => [ 'GameLiftAwsAccountId' => [ 'shape' => 'NonZeroAndMaxString', ], 'PeerVpcId' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'CreateVpcPeeringAuthorizationOutput' => [ 'type' => 'structure', 'members' => [ 'VpcPeeringAuthorization' => [ 'shape' => 'VpcPeeringAuthorization', ], ], ], 'CreateVpcPeeringConnectionInput' => [ 'type' => 'structure', 'required' => [ 'FleetId', 'PeerVpcAwsAccountId', 'PeerVpcId', ], 'members' => [ 'FleetId' => [ 'shape' => 'FleetId', ], 'PeerVpcAwsAccountId' => [ 'shape' => 'NonZeroAndMaxString', ], 'PeerVpcId' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'CreateVpcPeeringConnectionOutput' => [ 'type' => 'structure', 'members' => [], ], 'CustomEventData' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'CustomInputLocationStringModel' => [ 'type' => 'string', 'max' => 64, 'min' => 8, 'pattern' => '^custom-[A-Za-z0-9\\-]+', ], 'CustomLocationNameOrArnModel' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^custom-[A-Za-z0-9\\-]+|^arn:.*:location\\/custom-\\S+', ], 'DeleteAliasInput' => [ 'type' => 'structure', 'required' => [ 'AliasId', ], 'members' => [ 'AliasId' => [ 'shape' => 'AliasIdOrArn', ], ], ], 'DeleteBuildInput' => [ 'type' => 'structure', 'required' => [ 'BuildId', ], 'members' => [ 'BuildId' => [ 'shape' => 'BuildIdOrArn', ], ], ], 'DeleteContainerFleetInput' => [ 'type' => 'structure', 'required' => [ 'FleetId', ], 'members' => [ 'FleetId' => [ 'shape' => 'FleetIdOrArn', ], ], ], 'DeleteContainerFleetOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteContainerGroupDefinitionInput' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'ContainerGroupDefinitionNameOrArn', ], 'VersionNumber' => [ 'shape' => 'PositiveInteger', ], 'VersionCountToRetain' => [ 'shape' => 'WholeNumber', ], ], ], 'DeleteContainerGroupDefinitionOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteFleetInput' => [ 'type' => 'structure', 'required' => [ 'FleetId', ], 'members' => [ 'FleetId' => [ 'shape' => 'FleetIdOrArn', ], ], ], 'DeleteFleetLocationsInput' => [ 'type' => 'structure', 'required' => [ 'FleetId', 'Locations', ], 'members' => [ 'FleetId' => [ 'shape' => 'FleetIdOrArn', ], 'Locations' => [ 'shape' => 'LocationList', ], ], ], 'DeleteFleetLocationsOutput' => [ 'type' => 'structure', 'members' => [ 'FleetId' => [ 'shape' => 'FleetIdOrArn', ], 'FleetArn' => [ 'shape' => 'FleetArn', ], 'LocationStates' => [ 'shape' => 'LocationStateList', ], ], ], 'DeleteGameServerGroupInput' => [ 'type' => 'structure', 'required' => [ 'GameServerGroupName', ], 'members' => [ 'GameServerGroupName' => [ 'shape' => 'GameServerGroupNameOrArn', ], 'DeleteOption' => [ 'shape' => 'GameServerGroupDeleteOption', ], ], ], 'DeleteGameServerGroupOutput' => [ 'type' => 'structure', 'members' => [ 'GameServerGroup' => [ 'shape' => 'GameServerGroup', ], ], ], 'DeleteGameSessionQueueInput' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'GameSessionQueueNameOrArn', ], ], ], 'DeleteGameSessionQueueOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteLocationInput' => [ 'type' => 'structure', 'required' => [ 'LocationName', ], 'members' => [ 'LocationName' => [ 'shape' => 'CustomLocationNameOrArnModel', ], ], ], 'DeleteLocationOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteMatchmakingConfigurationInput' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'MatchmakingConfigurationName', ], ], ], 'DeleteMatchmakingConfigurationOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteMatchmakingRuleSetInput' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'MatchmakingRuleSetName', ], ], ], 'DeleteMatchmakingRuleSetOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteScalingPolicyInput' => [ 'type' => 'structure', 'required' => [ 'Name', 'FleetId', ], 'members' => [ 'Name' => [ 'shape' => 'NonZeroAndMaxString', ], 'FleetId' => [ 'shape' => 'FleetIdOrArn', ], ], ], 'DeleteScriptInput' => [ 'type' => 'structure', 'required' => [ 'ScriptId', ], 'members' => [ 'ScriptId' => [ 'shape' => 'ScriptIdOrArn', ], ], ], 'DeleteVpcPeeringAuthorizationInput' => [ 'type' => 'structure', 'required' => [ 'GameLiftAwsAccountId', 'PeerVpcId', ], 'members' => [ 'GameLiftAwsAccountId' => [ 'shape' => 'NonZeroAndMaxString', ], 'PeerVpcId' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'DeleteVpcPeeringAuthorizationOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteVpcPeeringConnectionInput' => [ 'type' => 'structure', 'required' => [ 'FleetId', 'VpcPeeringConnectionId', ], 'members' => [ 'FleetId' => [ 'shape' => 'FleetId', ], 'VpcPeeringConnectionId' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'DeleteVpcPeeringConnectionOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeploymentConfiguration' => [ 'type' => 'structure', 'members' => [ 'ProtectionStrategy' => [ 'shape' => 'DeploymentProtectionStrategy', ], 'MinimumHealthyPercentage' => [ 'shape' => 'MinimumHealthyPercentage', ], 'ImpairmentStrategy' => [ 'shape' => 'DeploymentImpairmentStrategy', ], ], ], 'DeploymentDetails' => [ 'type' => 'structure', 'members' => [ 'LatestDeploymentId' => [ 'shape' => 'DeploymentId', ], ], ], 'DeploymentId' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\-]+$', ], 'DeploymentImpairmentStrategy' => [ 'type' => 'string', 'enum' => [ 'MAINTAIN', 'ROLLBACK', ], ], 'DeploymentProtectionStrategy' => [ 'type' => 'string', 'enum' => [ 'WITH_PROTECTION', 'IGNORE_PROTECTION', ], ], 'DeploymentStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'IMPAIRED', 'COMPLETE', 'ROLLBACK_IN_PROGRESS', 'ROLLBACK_COMPLETE', 'CANCELLED', 'PENDING', ], ], 'DeregisterComputeInput' => [ 'type' => 'structure', 'required' => [ 'FleetId', 'ComputeName', ], 'members' => [ 'FleetId' => [ 'shape' => 'FleetIdOrArn', ], 'ComputeName' => [ 'shape' => 'ComputeNameOrArn', ], ], ], 'DeregisterComputeOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeregisterGameServerInput' => [ 'type' => 'structure', 'required' => [ 'GameServerGroupName', 'GameServerId', ], 'members' => [ 'GameServerGroupName' => [ 'shape' => 'GameServerGroupNameOrArn', ], 'GameServerId' => [ 'shape' => 'GameServerId', ], ], ], 'DescribeAliasInput' => [ 'type' => 'structure', 'required' => [ 'AliasId', ], 'members' => [ 'AliasId' => [ 'shape' => 'AliasIdOrArn', ], ], ], 'DescribeAliasOutput' => [ 'type' => 'structure', 'members' => [ 'Alias' => [ 'shape' => 'Alias', ], ], ], 'DescribeBuildInput' => [ 'type' => 'structure', 'required' => [ 'BuildId', ], 'members' => [ 'BuildId' => [ 'shape' => 'BuildIdOrArn', ], ], ], 'DescribeBuildOutput' => [ 'type' => 'structure', 'members' => [ 'Build' => [ 'shape' => 'Build', ], ], ], 'DescribeComputeInput' => [ 'type' => 'structure', 'required' => [ 'FleetId', 'ComputeName', ], 'members' => [ 'FleetId' => [ 'shape' => 'FleetIdOrArn', ], 'ComputeName' => [ 'shape' => 'ComputeNameOrArn', ], ], ], 'DescribeComputeOutput' => [ 'type' => 'structure', 'members' => [ 'Compute' => [ 'shape' => 'Compute', ], ], ], 'DescribeContainerFleetInput' => [ 'type' => 'structure', 'required' => [ 'FleetId', ], 'members' => [ 'FleetId' => [ 'shape' => 'FleetIdOrArn', ], ], ], 'DescribeContainerFleetOutput' => [ 'type' => 'structure', 'members' => [ 'ContainerFleet' => [ 'shape' => 'ContainerFleet', ], ], ], 'DescribeContainerGroupDefinitionInput' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'ContainerGroupDefinitionNameOrArn', ], 'VersionNumber' => [ 'shape' => 'PositiveInteger', ], ], ], 'DescribeContainerGroupDefinitionOutput' => [ 'type' => 'structure', 'members' => [ 'ContainerGroupDefinition' => [ 'shape' => 'ContainerGroupDefinition', ], ], ], 'DescribeEC2InstanceLimitsInput' => [ 'type' => 'structure', 'members' => [ 'EC2InstanceType' => [ 'shape' => 'EC2InstanceType', ], 'Location' => [ 'shape' => 'LocationStringModel', ], ], ], 'DescribeEC2InstanceLimitsOutput' => [ 'type' => 'structure', 'members' => [ 'EC2InstanceLimits' => [ 'shape' => 'EC2InstanceLimitList', ], ], ], 'DescribeFleetAttributesInput' => [ 'type' => 'structure', 'members' => [ 'FleetIds' => [ 'shape' => 'FleetIdOrArnList', ], 'Limit' => [ 'shape' => 'PositiveInteger', ], 'NextToken' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'DescribeFleetAttributesOutput' => [ 'type' => 'structure', 'members' => [ 'FleetAttributes' => [ 'shape' => 'FleetAttributesList', ], 'NextToken' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'DescribeFleetCapacityInput' => [ 'type' => 'structure', 'members' => [ 'FleetIds' => [ 'shape' => 'FleetIdOrArnList', ], 'Limit' => [ 'shape' => 'PositiveInteger', ], 'NextToken' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'DescribeFleetCapacityOutput' => [ 'type' => 'structure', 'members' => [ 'FleetCapacity' => [ 'shape' => 'FleetCapacityList', ], 'NextToken' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'DescribeFleetDeploymentInput' => [ 'type' => 'structure', 'required' => [ 'FleetId', ], 'members' => [ 'FleetId' => [ 'shape' => 'FleetIdOrArn', ], 'DeploymentId' => [ 'shape' => 'DeploymentId', ], ], ], 'DescribeFleetDeploymentOutput' => [ 'type' => 'structure', 'members' => [ 'FleetDeployment' => [ 'shape' => 'FleetDeployment', ], 'LocationalDeployments' => [ 'shape' => 'LocationalDeployments', ], ], ], 'DescribeFleetEventsInput' => [ 'type' => 'structure', 'required' => [ 'FleetId', ], 'members' => [ 'FleetId' => [ 'shape' => 'FleetIdOrArn', ], 'StartTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], 'Limit' => [ 'shape' => 'PositiveInteger', ], 'NextToken' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'DescribeFleetEventsOutput' => [ 'type' => 'structure', 'members' => [ 'Events' => [ 'shape' => 'EventList', ], 'NextToken' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'DescribeFleetLocationAttributesInput' => [ 'type' => 'structure', 'required' => [ 'FleetId', ], 'members' => [ 'FleetId' => [ 'shape' => 'FleetIdOrArn', ], 'Locations' => [ 'shape' => 'LocationList', ], 'Limit' => [ 'shape' => 'PositiveInteger', ], 'NextToken' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'DescribeFleetLocationAttributesOutput' => [ 'type' => 'structure', 'members' => [ 'FleetId' => [ 'shape' => 'FleetIdOrArn', ], 'FleetArn' => [ 'shape' => 'FleetArn', ], 'LocationAttributes' => [ 'shape' => 'LocationAttributesList', ], 'NextToken' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'DescribeFleetLocationCapacityInput' => [ 'type' => 'structure', 'required' => [ 'FleetId', 'Location', ], 'members' => [ 'FleetId' => [ 'shape' => 'FleetIdOrArn', ], 'Location' => [ 'shape' => 'LocationStringModel', ], ], ], 'DescribeFleetLocationCapacityOutput' => [ 'type' => 'structure', 'members' => [ 'FleetCapacity' => [ 'shape' => 'FleetCapacity', ], ], ], 'DescribeFleetLocationUtilizationInput' => [ 'type' => 'structure', 'required' => [ 'FleetId', 'Location', ], 'members' => [ 'FleetId' => [ 'shape' => 'FleetIdOrArn', ], 'Location' => [ 'shape' => 'LocationStringModel', ], ], ], 'DescribeFleetLocationUtilizationOutput' => [ 'type' => 'structure', 'members' => [ 'FleetUtilization' => [ 'shape' => 'FleetUtilization', ], ], ], 'DescribeFleetPortSettingsInput' => [ 'type' => 'structure', 'required' => [ 'FleetId', ], 'members' => [ 'FleetId' => [ 'shape' => 'FleetIdOrArn', ], 'Location' => [ 'shape' => 'LocationStringModel', ], ], ], 'DescribeFleetPortSettingsOutput' => [ 'type' => 'structure', 'members' => [ 'FleetId' => [ 'shape' => 'FleetId', ], 'FleetArn' => [ 'shape' => 'FleetArn', ], 'InboundPermissions' => [ 'shape' => 'IpPermissionsList', ], 'UpdateStatus' => [ 'shape' => 'LocationUpdateStatus', ], 'Location' => [ 'shape' => 'LocationStringModel', ], ], ], 'DescribeFleetUtilizationInput' => [ 'type' => 'structure', 'members' => [ 'FleetIds' => [ 'shape' => 'FleetIdOrArnList', ], 'Limit' => [ 'shape' => 'PositiveInteger', ], 'NextToken' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'DescribeFleetUtilizationOutput' => [ 'type' => 'structure', 'members' => [ 'FleetUtilization' => [ 'shape' => 'FleetUtilizationList', ], 'NextToken' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'DescribeGameServerGroupInput' => [ 'type' => 'structure', 'required' => [ 'GameServerGroupName', ], 'members' => [ 'GameServerGroupName' => [ 'shape' => 'GameServerGroupNameOrArn', ], ], ], 'DescribeGameServerGroupOutput' => [ 'type' => 'structure', 'members' => [ 'GameServerGroup' => [ 'shape' => 'GameServerGroup', ], ], ], 'DescribeGameServerInput' => [ 'type' => 'structure', 'required' => [ 'GameServerGroupName', 'GameServerId', ], 'members' => [ 'GameServerGroupName' => [ 'shape' => 'GameServerGroupNameOrArn', ], 'GameServerId' => [ 'shape' => 'GameServerId', ], ], ], 'DescribeGameServerInstancesInput' => [ 'type' => 'structure', 'required' => [ 'GameServerGroupName', ], 'members' => [ 'GameServerGroupName' => [ 'shape' => 'GameServerGroupNameOrArn', ], 'InstanceIds' => [ 'shape' => 'GameServerInstanceIds', ], 'Limit' => [ 'shape' => 'PositiveInteger', ], 'NextToken' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'DescribeGameServerInstancesOutput' => [ 'type' => 'structure', 'members' => [ 'GameServerInstances' => [ 'shape' => 'GameServerInstances', ], 'NextToken' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'DescribeGameServerOutput' => [ 'type' => 'structure', 'members' => [ 'GameServer' => [ 'shape' => 'GameServer', ], ], ], 'DescribeGameSessionDetailsInput' => [ 'type' => 'structure', 'members' => [ 'FleetId' => [ 'shape' => 'FleetIdOrArn', ], 'GameSessionId' => [ 'shape' => 'ArnStringModel', ], 'AliasId' => [ 'shape' => 'AliasIdOrArn', ], 'Location' => [ 'shape' => 'LocationStringModel', ], 'StatusFilter' => [ 'shape' => 'NonZeroAndMaxString', ], 'Limit' => [ 'shape' => 'PositiveInteger', ], 'NextToken' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'DescribeGameSessionDetailsOutput' => [ 'type' => 'structure', 'members' => [ 'GameSessionDetails' => [ 'shape' => 'GameSessionDetailList', ], 'NextToken' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'DescribeGameSessionPlacementInput' => [ 'type' => 'structure', 'required' => [ 'PlacementId', ], 'members' => [ 'PlacementId' => [ 'shape' => 'IdStringModel', ], ], ], 'DescribeGameSessionPlacementOutput' => [ 'type' => 'structure', 'members' => [ 'GameSessionPlacement' => [ 'shape' => 'GameSessionPlacement', ], ], ], 'DescribeGameSessionQueuesInput' => [ 'type' => 'structure', 'members' => [ 'Names' => [ 'shape' => 'GameSessionQueueNameOrArnList', ], 'Limit' => [ 'shape' => 'PositiveInteger', ], 'NextToken' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'DescribeGameSessionQueuesOutput' => [ 'type' => 'structure', 'members' => [ 'GameSessionQueues' => [ 'shape' => 'GameSessionQueueList', ], 'NextToken' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'DescribeGameSessionsInput' => [ 'type' => 'structure', 'members' => [ 'FleetId' => [ 'shape' => 'FleetIdOrArn', ], 'GameSessionId' => [ 'shape' => 'ArnStringModel', ], 'AliasId' => [ 'shape' => 'AliasIdOrArn', ], 'Location' => [ 'shape' => 'LocationStringModel', ], 'StatusFilter' => [ 'shape' => 'NonZeroAndMaxString', ], 'Limit' => [ 'shape' => 'PositiveInteger', ], 'NextToken' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'DescribeGameSessionsOutput' => [ 'type' => 'structure', 'members' => [ 'GameSessions' => [ 'shape' => 'GameSessionList', ], 'NextToken' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'DescribeInstancesInput' => [ 'type' => 'structure', 'required' => [ 'FleetId', ], 'members' => [ 'FleetId' => [ 'shape' => 'FleetIdOrArn', ], 'InstanceId' => [ 'shape' => 'InstanceId', ], 'Limit' => [ 'shape' => 'PositiveInteger', ], 'NextToken' => [ 'shape' => 'NonZeroAndMaxString', ], 'Location' => [ 'shape' => 'LocationStringModel', ], ], ], 'DescribeInstancesOutput' => [ 'type' => 'structure', 'members' => [ 'Instances' => [ 'shape' => 'InstanceList', ], 'NextToken' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'DescribeMatchmakingConfigurationsInput' => [ 'type' => 'structure', 'members' => [ 'Names' => [ 'shape' => 'MatchmakingConfigurationNameList', ], 'RuleSetName' => [ 'shape' => 'MatchmakingRuleSetName', ], 'Limit' => [ 'shape' => 'PositiveInteger', ], 'NextToken' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'DescribeMatchmakingConfigurationsOutput' => [ 'type' => 'structure', 'members' => [ 'Configurations' => [ 'shape' => 'MatchmakingConfigurationList', ], 'NextToken' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'DescribeMatchmakingInput' => [ 'type' => 'structure', 'required' => [ 'TicketIds', ], 'members' => [ 'TicketIds' => [ 'shape' => 'MatchmakingIdList', ], ], ], 'DescribeMatchmakingOutput' => [ 'type' => 'structure', 'members' => [ 'TicketList' => [ 'shape' => 'MatchmakingTicketList', ], ], ], 'DescribeMatchmakingRuleSetsInput' => [ 'type' => 'structure', 'members' => [ 'Names' => [ 'shape' => 'MatchmakingRuleSetNameList', ], 'Limit' => [ 'shape' => 'RuleSetLimit', ], 'NextToken' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'DescribeMatchmakingRuleSetsOutput' => [ 'type' => 'structure', 'required' => [ 'RuleSets', ], 'members' => [ 'RuleSets' => [ 'shape' => 'MatchmakingRuleSetList', ], 'NextToken' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'DescribePlayerSessionsInput' => [ 'type' => 'structure', 'members' => [ 'GameSessionId' => [ 'shape' => 'ArnStringModel', ], 'PlayerId' => [ 'shape' => 'PlayerId', ], 'PlayerSessionId' => [ 'shape' => 'PlayerSessionId', ], 'PlayerSessionStatusFilter' => [ 'shape' => 'NonZeroAndMaxString', ], 'Limit' => [ 'shape' => 'PositiveInteger', ], 'NextToken' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'DescribePlayerSessionsOutput' => [ 'type' => 'structure', 'members' => [ 'PlayerSessions' => [ 'shape' => 'PlayerSessionList', ], 'NextToken' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'DescribeRuntimeConfigurationInput' => [ 'type' => 'structure', 'required' => [ 'FleetId', ], 'members' => [ 'FleetId' => [ 'shape' => 'FleetIdOrArn', ], ], ], 'DescribeRuntimeConfigurationOutput' => [ 'type' => 'structure', 'members' => [ 'RuntimeConfiguration' => [ 'shape' => 'RuntimeConfiguration', ], ], ], 'DescribeScalingPoliciesInput' => [ 'type' => 'structure', 'required' => [ 'FleetId', ], 'members' => [ 'FleetId' => [ 'shape' => 'FleetIdOrArn', ], 'StatusFilter' => [ 'shape' => 'ScalingStatusType', ], 'Limit' => [ 'shape' => 'PositiveInteger', ], 'NextToken' => [ 'shape' => 'NonZeroAndMaxString', ], 'Location' => [ 'shape' => 'LocationStringModel', ], ], ], 'DescribeScalingPoliciesOutput' => [ 'type' => 'structure', 'members' => [ 'ScalingPolicies' => [ 'shape' => 'ScalingPolicyList', ], 'NextToken' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'DescribeScriptInput' => [ 'type' => 'structure', 'required' => [ 'ScriptId', ], 'members' => [ 'ScriptId' => [ 'shape' => 'ScriptIdOrArn', ], ], ], 'DescribeScriptOutput' => [ 'type' => 'structure', 'members' => [ 'Script' => [ 'shape' => 'Script', ], ], ], 'DescribeVpcPeeringAuthorizationsInput' => [ 'type' => 'structure', 'members' => [], ], 'DescribeVpcPeeringAuthorizationsOutput' => [ 'type' => 'structure', 'members' => [ 'VpcPeeringAuthorizations' => [ 'shape' => 'VpcPeeringAuthorizationList', ], ], ], 'DescribeVpcPeeringConnectionsInput' => [ 'type' => 'structure', 'members' => [ 'FleetId' => [ 'shape' => 'FleetId', ], ], ], 'DescribeVpcPeeringConnectionsOutput' => [ 'type' => 'structure', 'members' => [ 'VpcPeeringConnections' => [ 'shape' => 'VpcPeeringConnectionList', ], ], ], 'DesiredPlayerSession' => [ 'type' => 'structure', 'members' => [ 'PlayerId' => [ 'shape' => 'PlayerId', ], 'PlayerData' => [ 'shape' => 'PlayerData', ], ], ], 'DesiredPlayerSessionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DesiredPlayerSession', ], ], 'DnsName' => [ 'type' => 'string', ], 'DnsNameInput' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^[0-9a-zA-Z_\\-\\.]+', ], 'Double' => [ 'type' => 'double', ], 'DoubleObject' => [ 'type' => 'double', ], 'EC2InstanceCounts' => [ 'type' => 'structure', 'members' => [ 'DESIRED' => [ 'shape' => 'WholeNumber', ], 'MINIMUM' => [ 'shape' => 'WholeNumber', ], 'MAXIMUM' => [ 'shape' => 'WholeNumber', ], 'PENDING' => [ 'shape' => 'WholeNumber', ], 'ACTIVE' => [ 'shape' => 'WholeNumber', ], 'IDLE' => [ 'shape' => 'WholeNumber', ], 'TERMINATING' => [ 'shape' => 'WholeNumber', ], ], ], 'EC2InstanceLimit' => [ 'type' => 'structure', 'members' => [ 'EC2InstanceType' => [ 'shape' => 'EC2InstanceType', ], 'CurrentInstances' => [ 'shape' => 'WholeNumber', ], 'InstanceLimit' => [ 'shape' => 'WholeNumber', ], 'Location' => [ 'shape' => 'LocationStringModel', ], ], ], 'EC2InstanceLimitList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EC2InstanceLimit', ], ], 'EC2InstanceType' => [ 'type' => 'string', 'enum' => [ 't2.micro', 't2.small', 't2.medium', 't2.large', 'c3.large', 'c3.xlarge', 'c3.2xlarge', 'c3.4xlarge', 'c3.8xlarge', 'c4.large', 'c4.xlarge', 'c4.2xlarge', 'c4.4xlarge', 'c4.8xlarge', 'c5.large', 'c5.xlarge', 'c5.2xlarge', 'c5.4xlarge', 'c5.9xlarge', 'c5.12xlarge', 'c5.18xlarge', 'c5.24xlarge', 'c5a.large', 'c5a.xlarge', 'c5a.2xlarge', 'c5a.4xlarge', 'c5a.8xlarge', 'c5a.12xlarge', 'c5a.16xlarge', 'c5a.24xlarge', 'r3.large', 'r3.xlarge', 'r3.2xlarge', 'r3.4xlarge', 'r3.8xlarge', 'r4.large', 'r4.xlarge', 'r4.2xlarge', 'r4.4xlarge', 'r4.8xlarge', 'r4.16xlarge', 'r5.large', 'r5.xlarge', 'r5.2xlarge', 'r5.4xlarge', 'r5.8xlarge', 'r5.12xlarge', 'r5.16xlarge', 'r5.24xlarge', 'r5a.large', 'r5a.xlarge', 'r5a.2xlarge', 'r5a.4xlarge', 'r5a.8xlarge', 'r5a.12xlarge', 'r5a.16xlarge', 'r5a.24xlarge', 'm3.medium', 'm3.large', 'm3.xlarge', 'm3.2xlarge', 'm4.large', 'm4.xlarge', 'm4.2xlarge', 'm4.4xlarge', 'm4.10xlarge', 'm5.large', 'm5.xlarge', 'm5.2xlarge', 'm5.4xlarge', 'm5.8xlarge', 'm5.12xlarge', 'm5.16xlarge', 'm5.24xlarge', 'm5a.large', 'm5a.xlarge', 'm5a.2xlarge', 'm5a.4xlarge', 'm5a.8xlarge', 'm5a.12xlarge', 'm5a.16xlarge', 'm5a.24xlarge', 'c5d.large', 'c5d.xlarge', 'c5d.2xlarge', 'c5d.4xlarge', 'c5d.9xlarge', 'c5d.12xlarge', 'c5d.18xlarge', 'c5d.24xlarge', 'c6a.large', 'c6a.xlarge', 'c6a.2xlarge', 'c6a.4xlarge', 'c6a.8xlarge', 'c6a.12xlarge', 'c6a.16xlarge', 'c6a.24xlarge', 'c6i.large', 'c6i.xlarge', 'c6i.2xlarge', 'c6i.4xlarge', 'c6i.8xlarge', 'c6i.12xlarge', 'c6i.16xlarge', 'c6i.24xlarge', 'r5d.large', 'r5d.xlarge', 'r5d.2xlarge', 'r5d.4xlarge', 'r5d.8xlarge', 'r5d.12xlarge', 'r5d.16xlarge', 'r5d.24xlarge', 'm6g.medium', 'm6g.large', 'm6g.xlarge', 'm6g.2xlarge', 'm6g.4xlarge', 'm6g.8xlarge', 'm6g.12xlarge', 'm6g.16xlarge', 'c6g.medium', 'c6g.large', 'c6g.xlarge', 'c6g.2xlarge', 'c6g.4xlarge', 'c6g.8xlarge', 'c6g.12xlarge', 'c6g.16xlarge', 'r6g.medium', 'r6g.large', 'r6g.xlarge', 'r6g.2xlarge', 'r6g.4xlarge', 'r6g.8xlarge', 'r6g.12xlarge', 'r6g.16xlarge', 'c6gn.medium', 'c6gn.large', 'c6gn.xlarge', 'c6gn.2xlarge', 'c6gn.4xlarge', 'c6gn.8xlarge', 'c6gn.12xlarge', 'c6gn.16xlarge', 'c7g.medium', 'c7g.large', 'c7g.xlarge', 'c7g.2xlarge', 'c7g.4xlarge', 'c7g.8xlarge', 'c7g.12xlarge', 'c7g.16xlarge', 'r7g.medium', 'r7g.large', 'r7g.xlarge', 'r7g.2xlarge', 'r7g.4xlarge', 'r7g.8xlarge', 'r7g.12xlarge', 'r7g.16xlarge', 'm7g.medium', 'm7g.large', 'm7g.xlarge', 'm7g.2xlarge', 'm7g.4xlarge', 'm7g.8xlarge', 'm7g.12xlarge', 'm7g.16xlarge', 'g5g.xlarge', 'g5g.2xlarge', 'g5g.4xlarge', 'g5g.8xlarge', 'g5g.16xlarge', 'r6i.large', 'r6i.xlarge', 'r6i.2xlarge', 'r6i.4xlarge', 'r6i.8xlarge', 'r6i.12xlarge', 'r6i.16xlarge', 'c6gd.medium', 'c6gd.large', 'c6gd.xlarge', 'c6gd.2xlarge', 'c6gd.4xlarge', 'c6gd.8xlarge', 'c6gd.12xlarge', 'c6gd.16xlarge', 'c6in.large', 'c6in.xlarge', 'c6in.2xlarge', 'c6in.4xlarge', 'c6in.8xlarge', 'c6in.12xlarge', 'c6in.16xlarge', 'c7a.medium', 'c7a.large', 'c7a.xlarge', 'c7a.2xlarge', 'c7a.4xlarge', 'c7a.8xlarge', 'c7a.12xlarge', 'c7a.16xlarge', 'c7gd.medium', 'c7gd.large', 'c7gd.xlarge', 'c7gd.2xlarge', 'c7gd.4xlarge', 'c7gd.8xlarge', 'c7gd.12xlarge', 'c7gd.16xlarge', 'c7gn.medium', 'c7gn.large', 'c7gn.xlarge', 'c7gn.2xlarge', 'c7gn.4xlarge', 'c7gn.8xlarge', 'c7gn.12xlarge', 'c7gn.16xlarge', 'c7i.large', 'c7i.xlarge', 'c7i.2xlarge', 'c7i.4xlarge', 'c7i.8xlarge', 'c7i.12xlarge', 'c7i.16xlarge', 'm6a.large', 'm6a.xlarge', 'm6a.2xlarge', 'm6a.4xlarge', 'm6a.8xlarge', 'm6a.12xlarge', 'm6a.16xlarge', 'm6gd.medium', 'm6gd.large', 'm6gd.xlarge', 'm6gd.2xlarge', 'm6gd.4xlarge', 'm6gd.8xlarge', 'm6gd.12xlarge', 'm6gd.16xlarge', 'm6i.large', 'm6i.xlarge', 'm6i.2xlarge', 'm6i.4xlarge', 'm6i.8xlarge', 'm6i.12xlarge', 'm6i.16xlarge', 'm7a.medium', 'm7a.large', 'm7a.xlarge', 'm7a.2xlarge', 'm7a.4xlarge', 'm7a.8xlarge', 'm7a.12xlarge', 'm7a.16xlarge', 'm7gd.medium', 'm7gd.large', 'm7gd.xlarge', 'm7gd.2xlarge', 'm7gd.4xlarge', 'm7gd.8xlarge', 'm7gd.12xlarge', 'm7gd.16xlarge', 'm7i.large', 'm7i.xlarge', 'm7i.2xlarge', 'm7i.4xlarge', 'm7i.8xlarge', 'm7i.12xlarge', 'm7i.16xlarge', 'r6gd.medium', 'r6gd.large', 'r6gd.xlarge', 'r6gd.2xlarge', 'r6gd.4xlarge', 'r6gd.8xlarge', 'r6gd.12xlarge', 'r6gd.16xlarge', 'r7a.medium', 'r7a.large', 'r7a.xlarge', 'r7a.2xlarge', 'r7a.4xlarge', 'r7a.8xlarge', 'r7a.12xlarge', 'r7a.16xlarge', 'r7gd.medium', 'r7gd.large', 'r7gd.xlarge', 'r7gd.2xlarge', 'r7gd.4xlarge', 'r7gd.8xlarge', 'r7gd.12xlarge', 'r7gd.16xlarge', 'r7i.large', 'r7i.xlarge', 'r7i.2xlarge', 'r7i.4xlarge', 'r7i.8xlarge', 'r7i.12xlarge', 'r7i.16xlarge', 'r7i.24xlarge', 'r7i.48xlarge', 'c5ad.large', 'c5ad.xlarge', 'c5ad.2xlarge', 'c5ad.4xlarge', 'c5ad.8xlarge', 'c5ad.12xlarge', 'c5ad.16xlarge', 'c5ad.24xlarge', 'c5n.large', 'c5n.xlarge', 'c5n.2xlarge', 'c5n.4xlarge', 'c5n.9xlarge', 'c5n.18xlarge', 'r5ad.large', 'r5ad.xlarge', 'r5ad.2xlarge', 'r5ad.4xlarge', 'r5ad.8xlarge', 'r5ad.12xlarge', 'r5ad.16xlarge', 'r5ad.24xlarge', 'c6id.large', 'c6id.xlarge', 'c6id.2xlarge', 'c6id.4xlarge', 'c6id.8xlarge', 'c6id.12xlarge', 'c6id.16xlarge', 'c6id.24xlarge', 'c6id.32xlarge', 'c8g.medium', 'c8g.large', 'c8g.xlarge', 'c8g.2xlarge', 'c8g.4xlarge', 'c8g.8xlarge', 'c8g.12xlarge', 'c8g.16xlarge', 'c8g.24xlarge', 'c8g.48xlarge', 'm5ad.large', 'm5ad.xlarge', 'm5ad.2xlarge', 'm5ad.4xlarge', 'm5ad.8xlarge', 'm5ad.12xlarge', 'm5ad.16xlarge', 'm5ad.24xlarge', 'm5d.large', 'm5d.xlarge', 'm5d.2xlarge', 'm5d.4xlarge', 'm5d.8xlarge', 'm5d.12xlarge', 'm5d.16xlarge', 'm5d.24xlarge', 'm5dn.large', 'm5dn.xlarge', 'm5dn.2xlarge', 'm5dn.4xlarge', 'm5dn.8xlarge', 'm5dn.12xlarge', 'm5dn.16xlarge', 'm5dn.24xlarge', 'm5n.large', 'm5n.xlarge', 'm5n.2xlarge', 'm5n.4xlarge', 'm5n.8xlarge', 'm5n.12xlarge', 'm5n.16xlarge', 'm5n.24xlarge', 'm6id.large', 'm6id.xlarge', 'm6id.2xlarge', 'm6id.4xlarge', 'm6id.8xlarge', 'm6id.12xlarge', 'm6id.16xlarge', 'm6id.24xlarge', 'm6id.32xlarge', 'm6idn.large', 'm6idn.xlarge', 'm6idn.2xlarge', 'm6idn.4xlarge', 'm6idn.8xlarge', 'm6idn.12xlarge', 'm6idn.16xlarge', 'm6idn.24xlarge', 'm6idn.32xlarge', 'm6in.large', 'm6in.xlarge', 'm6in.2xlarge', 'm6in.4xlarge', 'm6in.8xlarge', 'm6in.12xlarge', 'm6in.16xlarge', 'm6in.24xlarge', 'm6in.32xlarge', 'm8g.medium', 'm8g.large', 'm8g.xlarge', 'm8g.2xlarge', 'm8g.4xlarge', 'm8g.8xlarge', 'm8g.12xlarge', 'm8g.16xlarge', 'm8g.24xlarge', 'm8g.48xlarge', 'r5dn.large', 'r5dn.xlarge', 'r5dn.2xlarge', 'r5dn.4xlarge', 'r5dn.8xlarge', 'r5dn.12xlarge', 'r5dn.16xlarge', 'r5dn.24xlarge', 'r5n.large', 'r5n.xlarge', 'r5n.2xlarge', 'r5n.4xlarge', 'r5n.8xlarge', 'r5n.12xlarge', 'r5n.16xlarge', 'r5n.24xlarge', 'r6a.large', 'r6a.xlarge', 'r6a.2xlarge', 'r6a.4xlarge', 'r6a.8xlarge', 'r6a.12xlarge', 'r6a.16xlarge', 'r6a.24xlarge', 'r6a.32xlarge', 'r6a.48xlarge', 'r6id.large', 'r6id.xlarge', 'r6id.2xlarge', 'r6id.4xlarge', 'r6id.8xlarge', 'r6id.12xlarge', 'r6id.16xlarge', 'r6id.24xlarge', 'r6id.32xlarge', 'r6idn.large', 'r6idn.xlarge', 'r6idn.2xlarge', 'r6idn.4xlarge', 'r6idn.8xlarge', 'r6idn.12xlarge', 'r6idn.16xlarge', 'r6idn.24xlarge', 'r6idn.32xlarge', 'r6in.large', 'r6in.xlarge', 'r6in.2xlarge', 'r6in.4xlarge', 'r6in.8xlarge', 'r6in.12xlarge', 'r6in.16xlarge', 'r6in.24xlarge', 'r6in.32xlarge', 'r8g.medium', 'r8g.large', 'r8g.xlarge', 'r8g.2xlarge', 'r8g.4xlarge', 'r8g.8xlarge', 'r8g.12xlarge', 'r8g.16xlarge', 'r8g.24xlarge', 'r8g.48xlarge', 'm4.16xlarge', 'c6a.32xlarge', 'c6a.48xlarge', 'c6i.32xlarge', 'r6i.24xlarge', 'r6i.32xlarge', 'c6in.24xlarge', 'c6in.32xlarge', 'c7a.24xlarge', 'c7a.32xlarge', 'c7a.48xlarge', 'c7i.24xlarge', 'c7i.48xlarge', 'm6a.24xlarge', 'm6a.32xlarge', 'm6a.48xlarge', 'm6i.24xlarge', 'm6i.32xlarge', 'm7a.24xlarge', 'm7a.32xlarge', 'm7a.48xlarge', 'm7i.24xlarge', 'm7i.48xlarge', 'r7a.24xlarge', 'r7a.32xlarge', 'r7a.48xlarge', ], ], 'Event' => [ 'type' => 'structure', 'members' => [ 'EventId' => [ 'shape' => 'NonZeroAndMaxString', ], 'ResourceId' => [ 'shape' => 'NonZeroAndMaxString', ], 'EventCode' => [ 'shape' => 'EventCode', ], 'Message' => [ 'shape' => 'NonEmptyString', ], 'EventTime' => [ 'shape' => 'Timestamp', ], 'PreSignedLogUrl' => [ 'shape' => 'NonZeroAndMaxString', ], 'Count' => [ 'shape' => 'EventCount', ], ], ], 'EventCode' => [ 'type' => 'string', 'enum' => [ 'GENERIC_EVENT', 'FLEET_CREATED', 'FLEET_DELETED', 'FLEET_SCALING_EVENT', 'FLEET_STATE_DOWNLOADING', 'FLEET_STATE_VALIDATING', 'FLEET_STATE_BUILDING', 'FLEET_STATE_ACTIVATING', 'FLEET_STATE_ACTIVE', 'FLEET_STATE_ERROR', 'FLEET_STATE_PENDING', 'FLEET_STATE_CREATING', 'FLEET_STATE_CREATED', 'FLEET_STATE_UPDATING', 'FLEET_INITIALIZATION_FAILED', 'FLEET_BINARY_DOWNLOAD_FAILED', 'FLEET_VALIDATION_LAUNCH_PATH_NOT_FOUND', 'FLEET_VALIDATION_EXECUTABLE_RUNTIME_FAILURE', 'FLEET_VALIDATION_TIMED_OUT', 'FLEET_ACTIVATION_FAILED', 'FLEET_ACTIVATION_FAILED_NO_INSTANCES', 'FLEET_NEW_GAME_SESSION_PROTECTION_POLICY_UPDATED', 'SERVER_PROCESS_INVALID_PATH', 'SERVER_PROCESS_SDK_INITIALIZATION_TIMEOUT', 'SERVER_PROCESS_PROCESS_READY_TIMEOUT', 'SERVER_PROCESS_CRASHED', 'SERVER_PROCESS_TERMINATED_UNHEALTHY', 'SERVER_PROCESS_FORCE_TERMINATED', 'SERVER_PROCESS_PROCESS_EXIT_TIMEOUT', 'SERVER_PROCESS_SDK_INITIALIZATION_FAILED', 'SERVER_PROCESS_MISCONFIGURED_CONTAINER_PORT', 'GAME_SESSION_ACTIVATION_TIMEOUT', 'FLEET_CREATION_EXTRACTING_BUILD', 'FLEET_CREATION_RUNNING_INSTALLER', 'FLEET_CREATION_VALIDATING_RUNTIME_CONFIG', 'FLEET_VPC_PEERING_SUCCEEDED', 'FLEET_VPC_PEERING_FAILED', 'FLEET_VPC_PEERING_DELETED', 'INSTANCE_INTERRUPTED', 'INSTANCE_RECYCLED', 'INSTANCE_REPLACED_UNHEALTHY', 'FLEET_CREATION_COMPLETED_INSTALLER', 'FLEET_CREATION_FAILED_INSTALLER', 'COMPUTE_LOG_UPLOAD_FAILED', 'GAME_SERVER_CONTAINER_GROUP_CRASHED', 'PER_INSTANCE_CONTAINER_GROUP_CRASHED', 'GAME_SERVER_CONTAINER_GROUP_REPLACED_UNHEALTHY', 'LOCATION_STATE_PENDING', 'LOCATION_STATE_CREATING', 'LOCATION_STATE_CREATED', 'LOCATION_STATE_ACTIVATING', 'LOCATION_STATE_ACTIVE', 'LOCATION_STATE_UPDATING', 'LOCATION_STATE_ERROR', 'LOCATION_STATE_DELETING', 'LOCATION_STATE_DELETED', ], ], 'EventCount' => [ 'type' => 'long', 'max' => 101, 'min' => 1, ], 'EventList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Event', ], ], 'FilterConfiguration' => [ 'type' => 'structure', 'members' => [ 'AllowedLocations' => [ 'shape' => 'LocationList', ], ], ], 'FilterInstanceStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'DRAINING', ], ], 'FilterInstanceStatuses' => [ 'type' => 'list', 'member' => [ 'shape' => 'FilterInstanceStatus', ], ], 'FleetAction' => [ 'type' => 'string', 'enum' => [ 'AUTO_SCALING', ], ], 'FleetActionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FleetAction', ], 'max' => 1, 'min' => 1, ], 'FleetArn' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '^arn:.*:[a-z]*fleet\\/[a-z]*fleet-[a-zA-Z0-9\\-]+$', ], 'FleetAttributes' => [ 'type' => 'structure', 'members' => [ 'FleetId' => [ 'shape' => 'FleetId', ], 'FleetArn' => [ 'shape' => 'FleetArn', ], 'FleetType' => [ 'shape' => 'FleetType', ], 'InstanceType' => [ 'shape' => 'EC2InstanceType', ], 'Description' => [ 'shape' => 'NonZeroAndMaxString', ], 'Name' => [ 'shape' => 'NonZeroAndMaxString', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'TerminationTime' => [ 'shape' => 'Timestamp', ], 'Status' => [ 'shape' => 'FleetStatus', ], 'BuildId' => [ 'shape' => 'BuildId', ], 'BuildArn' => [ 'shape' => 'BuildArn', ], 'ScriptId' => [ 'shape' => 'ScriptId', ], 'ScriptArn' => [ 'shape' => 'ScriptArn', ], 'ServerLaunchPath' => [ 'shape' => 'LaunchPathStringModel', ], 'ServerLaunchParameters' => [ 'shape' => 'LaunchParametersStringModel', ], 'LogPaths' => [ 'shape' => 'StringList', ], 'NewGameSessionProtectionPolicy' => [ 'shape' => 'ProtectionPolicy', ], 'OperatingSystem' => [ 'shape' => 'OperatingSystem', ], 'ResourceCreationLimitPolicy' => [ 'shape' => 'ResourceCreationLimitPolicy', ], 'MetricGroups' => [ 'shape' => 'MetricGroupList', ], 'StoppedActions' => [ 'shape' => 'FleetActionList', ], 'InstanceRoleArn' => [ 'shape' => 'NonEmptyString', ], 'CertificateConfiguration' => [ 'shape' => 'CertificateConfiguration', ], 'ComputeType' => [ 'shape' => 'ComputeType', ], 'AnywhereConfiguration' => [ 'shape' => 'AnywhereConfiguration', ], 'InstanceRoleCredentialsProvider' => [ 'shape' => 'InstanceRoleCredentialsProvider', ], ], ], 'FleetAttributesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FleetAttributes', ], ], 'FleetBinaryArn' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[a-zA-Z0-9:/-]+', ], 'FleetCapacity' => [ 'type' => 'structure', 'members' => [ 'FleetId' => [ 'shape' => 'FleetId', ], 'FleetArn' => [ 'shape' => 'FleetArn', ], 'InstanceType' => [ 'shape' => 'EC2InstanceType', ], 'InstanceCounts' => [ 'shape' => 'EC2InstanceCounts', ], 'Location' => [ 'shape' => 'LocationStringModel', ], 'GameServerContainerGroupCounts' => [ 'shape' => 'GameServerContainerGroupCounts', ], ], ], 'FleetCapacityExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'NonEmptyString', ], ], 'exception' => true, ], 'FleetCapacityList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FleetCapacity', ], ], 'FleetDeployment' => [ 'type' => 'structure', 'members' => [ 'DeploymentId' => [ 'shape' => 'DeploymentId', ], 'FleetId' => [ 'shape' => 'FleetId', ], 'GameServerBinaryArn' => [ 'shape' => 'FleetBinaryArn', ], 'RollbackGameServerBinaryArn' => [ 'shape' => 'FleetBinaryArn', ], 'PerInstanceBinaryArn' => [ 'shape' => 'FleetBinaryArn', ], 'RollbackPerInstanceBinaryArn' => [ 'shape' => 'FleetBinaryArn', ], 'DeploymentStatus' => [ 'shape' => 'DeploymentStatus', ], 'DeploymentConfiguration' => [ 'shape' => 'DeploymentConfiguration', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], ], ], 'FleetDeployments' => [ 'type' => 'list', 'member' => [ 'shape' => 'FleetDeployment', ], ], 'FleetId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[a-z]*fleet-[a-zA-Z0-9\\-]+', ], 'FleetIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FleetId', ], 'min' => 1, ], 'FleetIdOrArn' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '^[a-z]*fleet-[a-zA-Z0-9\\-]+$|^arn:.*:[a-z]*fleet\\/[a-z]*fleet-[a-zA-Z0-9\\-]+$', ], 'FleetIdOrArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FleetIdOrArn', ], 'min' => 1, ], 'FleetStatus' => [ 'type' => 'string', 'enum' => [ 'NEW', 'DOWNLOADING', 'VALIDATING', 'BUILDING', 'ACTIVATING', 'ACTIVE', 'DELETING', 'ERROR', 'TERMINATED', 'NOT_FOUND', ], ], 'FleetType' => [ 'type' => 'string', 'enum' => [ 'ON_DEMAND', 'SPOT', ], ], 'FleetUtilization' => [ 'type' => 'structure', 'members' => [ 'FleetId' => [ 'shape' => 'FleetId', ], 'FleetArn' => [ 'shape' => 'FleetArn', ], 'ActiveServerProcessCount' => [ 'shape' => 'WholeNumber', ], 'ActiveGameSessionCount' => [ 'shape' => 'WholeNumber', ], 'CurrentPlayerSessionCount' => [ 'shape' => 'WholeNumber', ], 'MaximumPlayerSessionCount' => [ 'shape' => 'WholeNumber', ], 'Location' => [ 'shape' => 'LocationStringModel', ], ], ], 'FleetUtilizationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FleetUtilization', ], ], 'FlexMatchMode' => [ 'type' => 'string', 'enum' => [ 'STANDALONE', 'WITH_QUEUE', ], ], 'Float' => [ 'type' => 'float', ], 'FreeText' => [ 'type' => 'string', ], 'GameLiftAgentEndpointOutput' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'GameLiftServiceSdkEndpointOutput' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'GameProperty' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'GamePropertyKey', ], 'Value' => [ 'shape' => 'GamePropertyValue', ], ], ], 'GamePropertyKey' => [ 'type' => 'string', 'max' => 32, ], 'GamePropertyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GameProperty', ], 'max' => 16, ], 'GamePropertyValue' => [ 'type' => 'string', 'max' => 96, ], 'GameServer' => [ 'type' => 'structure', 'members' => [ 'GameServerGroupName' => [ 'shape' => 'GameServerGroupName', ], 'GameServerGroupArn' => [ 'shape' => 'GameServerGroupArn', ], 'GameServerId' => [ 'shape' => 'GameServerId', ], 'InstanceId' => [ 'shape' => 'GameServerInstanceId', ], 'ConnectionInfo' => [ 'shape' => 'GameServerConnectionInfo', ], 'GameServerData' => [ 'shape' => 'GameServerData', ], 'ClaimStatus' => [ 'shape' => 'GameServerClaimStatus', ], 'UtilizationStatus' => [ 'shape' => 'GameServerUtilizationStatus', ], 'RegistrationTime' => [ 'shape' => 'Timestamp', ], 'LastClaimTime' => [ 'shape' => 'Timestamp', ], 'LastHealthCheckTime' => [ 'shape' => 'Timestamp', ], ], ], 'GameServerClaimStatus' => [ 'type' => 'string', 'enum' => [ 'CLAIMED', ], ], 'GameServerConnectionInfo' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '.*\\S.*', ], 'GameServerContainerDefinition' => [ 'type' => 'structure', 'members' => [ 'ContainerName' => [ 'shape' => 'NonZeroAnd128MaxAsciiString', ], 'DependsOn' => [ 'shape' => 'ContainerDependencyList', ], 'MountPoints' => [ 'shape' => 'ContainerMountPointList', ], 'EnvironmentOverride' => [ 'shape' => 'ContainerEnvironmentList', ], 'ImageUri' => [ 'shape' => 'ImageUriString', ], 'PortConfiguration' => [ 'shape' => 'ContainerPortConfiguration', ], 'ResolvedImageDigest' => [ 'shape' => 'Sha256', ], 'ServerSdkVersion' => [ 'shape' => 'ServerSdkVersion', ], ], ], 'GameServerContainerDefinitionInput' => [ 'type' => 'structure', 'required' => [ 'ContainerName', 'ImageUri', 'PortConfiguration', 'ServerSdkVersion', ], 'members' => [ 'ContainerName' => [ 'shape' => 'NonZeroAnd128MaxAsciiString', ], 'DependsOn' => [ 'shape' => 'ContainerDependencyList', ], 'MountPoints' => [ 'shape' => 'ContainerMountPointList', ], 'EnvironmentOverride' => [ 'shape' => 'ContainerEnvironmentList', ], 'ImageUri' => [ 'shape' => 'ImageUriString', ], 'PortConfiguration' => [ 'shape' => 'ContainerPortConfiguration', ], 'ServerSdkVersion' => [ 'shape' => 'ServerSdkVersion', ], ], ], 'GameServerContainerGroupCounts' => [ 'type' => 'structure', 'members' => [ 'PENDING' => [ 'shape' => 'WholeNumber', ], 'ACTIVE' => [ 'shape' => 'WholeNumber', ], 'IDLE' => [ 'shape' => 'WholeNumber', ], 'TERMINATING' => [ 'shape' => 'WholeNumber', ], ], ], 'GameServerContainerGroupsPerInstance' => [ 'type' => 'integer', 'max' => 5000, 'min' => 1, ], 'GameServerData' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '.*\\S.*', ], 'GameServerGroup' => [ 'type' => 'structure', 'members' => [ 'GameServerGroupName' => [ 'shape' => 'GameServerGroupName', ], 'GameServerGroupArn' => [ 'shape' => 'GameServerGroupArn', ], 'RoleArn' => [ 'shape' => 'IamRoleArn', ], 'InstanceDefinitions' => [ 'shape' => 'InstanceDefinitions', ], 'BalancingStrategy' => [ 'shape' => 'BalancingStrategy', ], 'GameServerProtectionPolicy' => [ 'shape' => 'GameServerProtectionPolicy', ], 'AutoScalingGroupArn' => [ 'shape' => 'AutoScalingGroupArn', ], 'Status' => [ 'shape' => 'GameServerGroupStatus', ], 'StatusReason' => [ 'shape' => 'NonZeroAndMaxString', ], 'SuspendedActions' => [ 'shape' => 'GameServerGroupActions', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastUpdatedTime' => [ 'shape' => 'Timestamp', ], ], ], 'GameServerGroupAction' => [ 'type' => 'string', 'enum' => [ 'REPLACE_INSTANCE_TYPES', ], ], 'GameServerGroupActions' => [ 'type' => 'list', 'member' => [ 'shape' => 'GameServerGroupAction', ], 'max' => 1, 'min' => 1, ], 'GameServerGroupArn' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^arn:.*:gameservergroup\\/[a-zA-Z0-9-\\.]*', ], 'GameServerGroupAutoScalingPolicy' => [ 'type' => 'structure', 'required' => [ 'TargetTrackingConfiguration', ], 'members' => [ 'EstimatedInstanceWarmup' => [ 'shape' => 'PositiveInteger', ], 'TargetTrackingConfiguration' => [ 'shape' => 'TargetTrackingConfiguration', ], ], ], 'GameServerGroupDeleteOption' => [ 'type' => 'string', 'enum' => [ 'SAFE_DELETE', 'FORCE_DELETE', 'RETAIN', ], ], 'GameServerGroupInstanceType' => [ 'type' => 'string', 'enum' => [ 'c4.large', 'c4.xlarge', 'c4.2xlarge', 'c4.4xlarge', 'c4.8xlarge', 'c5.large', 'c5.xlarge', 'c5.2xlarge', 'c5.4xlarge', 'c5.9xlarge', 'c5.12xlarge', 'c5.18xlarge', 'c5.24xlarge', 'c5a.large', 'c5a.xlarge', 'c5a.2xlarge', 'c5a.4xlarge', 'c5a.8xlarge', 'c5a.12xlarge', 'c5a.16xlarge', 'c5a.24xlarge', 'c6g.medium', 'c6g.large', 'c6g.xlarge', 'c6g.2xlarge', 'c6g.4xlarge', 'c6g.8xlarge', 'c6g.12xlarge', 'c6g.16xlarge', 'r4.large', 'r4.xlarge', 'r4.2xlarge', 'r4.4xlarge', 'r4.8xlarge', 'r4.16xlarge', 'r5.large', 'r5.xlarge', 'r5.2xlarge', 'r5.4xlarge', 'r5.8xlarge', 'r5.12xlarge', 'r5.16xlarge', 'r5.24xlarge', 'r5a.large', 'r5a.xlarge', 'r5a.2xlarge', 'r5a.4xlarge', 'r5a.8xlarge', 'r5a.12xlarge', 'r5a.16xlarge', 'r5a.24xlarge', 'r6g.medium', 'r6g.large', 'r6g.xlarge', 'r6g.2xlarge', 'r6g.4xlarge', 'r6g.8xlarge', 'r6g.12xlarge', 'r6g.16xlarge', 'm4.large', 'm4.xlarge', 'm4.2xlarge', 'm4.4xlarge', 'm4.10xlarge', 'm5.large', 'm5.xlarge', 'm5.2xlarge', 'm5.4xlarge', 'm5.8xlarge', 'm5.12xlarge', 'm5.16xlarge', 'm5.24xlarge', 'm5a.large', 'm5a.xlarge', 'm5a.2xlarge', 'm5a.4xlarge', 'm5a.8xlarge', 'm5a.12xlarge', 'm5a.16xlarge', 'm5a.24xlarge', 'm6g.medium', 'm6g.large', 'm6g.xlarge', 'm6g.2xlarge', 'm6g.4xlarge', 'm6g.8xlarge', 'm6g.12xlarge', 'm6g.16xlarge', ], ], 'GameServerGroupName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9-\\.]+', ], 'GameServerGroupNameOrArn' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[a-zA-Z0-9-\\.]+|^arn:.*:gameservergroup\\/[a-zA-Z0-9-\\.]+', ], 'GameServerGroupStatus' => [ 'type' => 'string', 'enum' => [ 'NEW', 'ACTIVATING', 'ACTIVE', 'DELETE_SCHEDULED', 'DELETING', 'DELETED', 'ERROR', ], ], 'GameServerGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'GameServerGroup', ], ], 'GameServerHealthCheck' => [ 'type' => 'string', 'enum' => [ 'HEALTHY', ], ], 'GameServerId' => [ 'type' => 'string', 'max' => 128, 'min' => 3, 'pattern' => '[a-zA-Z0-9-\\.]+', ], 'GameServerInstance' => [ 'type' => 'structure', 'members' => [ 'GameServerGroupName' => [ 'shape' => 'GameServerGroupName', ], 'GameServerGroupArn' => [ 'shape' => 'GameServerGroupArn', ], 'InstanceId' => [ 'shape' => 'GameServerInstanceId', ], 'InstanceStatus' => [ 'shape' => 'GameServerInstanceStatus', ], ], ], 'GameServerInstanceId' => [ 'type' => 'string', 'max' => 19, 'min' => 19, 'pattern' => '^i-[0-9a-zA-Z]{17}$', ], 'GameServerInstanceIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'GameServerInstanceId', ], 'max' => 20, 'min' => 1, ], 'GameServerInstanceStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'DRAINING', 'SPOT_TERMINATING', ], ], 'GameServerInstances' => [ 'type' => 'list', 'member' => [ 'shape' => 'GameServerInstance', ], ], 'GameServerProtectionPolicy' => [ 'type' => 'string', 'enum' => [ 'NO_PROTECTION', 'FULL_PROTECTION', ], ], 'GameServerUtilizationStatus' => [ 'type' => 'string', 'enum' => [ 'AVAILABLE', 'UTILIZED', ], ], 'GameServers' => [ 'type' => 'list', 'member' => [ 'shape' => 'GameServer', ], ], 'GameSession' => [ 'type' => 'structure', 'members' => [ 'GameSessionId' => [ 'shape' => 'NonZeroAndMaxString', ], 'Name' => [ 'shape' => 'NonZeroAndMaxString', ], 'FleetId' => [ 'shape' => 'FleetId', ], 'FleetArn' => [ 'shape' => 'FleetArn', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'TerminationTime' => [ 'shape' => 'Timestamp', ], 'CurrentPlayerSessionCount' => [ 'shape' => 'WholeNumber', ], 'MaximumPlayerSessionCount' => [ 'shape' => 'WholeNumber', ], 'Status' => [ 'shape' => 'GameSessionStatus', ], 'StatusReason' => [ 'shape' => 'GameSessionStatusReason', ], 'GameProperties' => [ 'shape' => 'GamePropertyList', ], 'IpAddress' => [ 'shape' => 'IpAddress', ], 'DnsName' => [ 'shape' => 'DnsName', ], 'Port' => [ 'shape' => 'PortNumber', ], 'PlayerSessionCreationPolicy' => [ 'shape' => 'PlayerSessionCreationPolicy', ], 'CreatorId' => [ 'shape' => 'NonZeroAndMaxString', ], 'GameSessionData' => [ 'shape' => 'LargeGameSessionData', ], 'MatchmakerData' => [ 'shape' => 'MatchmakerData', ], 'Location' => [ 'shape' => 'LocationStringModel', ], ], ], 'GameSessionActivationTimeoutSeconds' => [ 'type' => 'integer', 'max' => 600, 'min' => 1, ], 'GameSessionConnectionInfo' => [ 'type' => 'structure', 'members' => [ 'GameSessionArn' => [ 'shape' => 'ArnStringModel', ], 'IpAddress' => [ 'shape' => 'IpAddress', ], 'DnsName' => [ 'shape' => 'DnsName', ], 'Port' => [ 'shape' => 'PositiveInteger', ], 'MatchedPlayerSessions' => [ 'shape' => 'MatchedPlayerSessionList', ], ], ], 'GameSessionCreationLimitPolicy' => [ 'type' => 'structure', 'members' => [ 'NewGameSessionsPerCreator' => [ 'shape' => 'WholeNumber', ], 'PolicyPeriodInMinutes' => [ 'shape' => 'WholeNumber', ], ], ], 'GameSessionData' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, ], 'GameSessionDetail' => [ 'type' => 'structure', 'members' => [ 'GameSession' => [ 'shape' => 'GameSession', ], 'ProtectionPolicy' => [ 'shape' => 'ProtectionPolicy', ], ], ], 'GameSessionDetailList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GameSessionDetail', ], ], 'GameSessionFullException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'NonEmptyString', ], ], 'exception' => true, ], 'GameSessionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GameSession', ], ], 'GameSessionPlacement' => [ 'type' => 'structure', 'members' => [ 'PlacementId' => [ 'shape' => 'IdStringModel', ], 'GameSessionQueueName' => [ 'shape' => 'GameSessionQueueName', ], 'Status' => [ 'shape' => 'GameSessionPlacementState', ], 'GameProperties' => [ 'shape' => 'GamePropertyList', ], 'MaximumPlayerSessionCount' => [ 'shape' => 'WholeNumber', ], 'GameSessionName' => [ 'shape' => 'NonZeroAndMaxString', ], 'GameSessionId' => [ 'shape' => 'NonZeroAndMaxString', ], 'GameSessionArn' => [ 'shape' => 'NonZeroAndMaxString', ], 'GameSessionRegion' => [ 'shape' => 'NonZeroAndMaxString', ], 'PlayerLatencies' => [ 'shape' => 'PlayerLatencyList', ], 'StartTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], 'IpAddress' => [ 'shape' => 'IpAddress', ], 'DnsName' => [ 'shape' => 'DnsName', ], 'Port' => [ 'shape' => 'PortNumber', ], 'PlacedPlayerSessions' => [ 'shape' => 'PlacedPlayerSessionList', ], 'GameSessionData' => [ 'shape' => 'LargeGameSessionData', ], 'MatchmakerData' => [ 'shape' => 'MatchmakerData', ], 'PriorityConfigurationOverride' => [ 'shape' => 'PriorityConfigurationOverride', ], ], ], 'GameSessionPlacementState' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'FULFILLED', 'CANCELLED', 'TIMED_OUT', 'FAILED', ], ], 'GameSessionQueue' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'GameSessionQueueName', ], 'GameSessionQueueArn' => [ 'shape' => 'GameSessionQueueArn', ], 'TimeoutInSeconds' => [ 'shape' => 'WholeNumber', ], 'PlayerLatencyPolicies' => [ 'shape' => 'PlayerLatencyPolicyList', ], 'Destinations' => [ 'shape' => 'GameSessionQueueDestinationList', ], 'FilterConfiguration' => [ 'shape' => 'FilterConfiguration', ], 'PriorityConfiguration' => [ 'shape' => 'PriorityConfiguration', ], 'CustomEventData' => [ 'shape' => 'QueueCustomEventData', ], 'NotificationTarget' => [ 'shape' => 'QueueSnsArnStringModel', ], ], ], 'GameSessionQueueArn' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^arn:.*:gamesessionqueue\\/[a-zA-Z0-9-]+', ], 'GameSessionQueueDestination' => [ 'type' => 'structure', 'members' => [ 'DestinationArn' => [ 'shape' => 'ArnStringModel', ], ], ], 'GameSessionQueueDestinationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GameSessionQueueDestination', ], ], 'GameSessionQueueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GameSessionQueue', ], ], 'GameSessionQueueName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9-]+', ], 'GameSessionQueueNameOrArn' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[a-zA-Z0-9-]+|^arn:.*:gamesessionqueue\\/[a-zA-Z0-9-]+', ], 'GameSessionQueueNameOrArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GameSessionQueueNameOrArn', ], ], 'GameSessionStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'ACTIVATING', 'TERMINATED', 'TERMINATING', 'ERROR', ], ], 'GameSessionStatusReason' => [ 'type' => 'string', 'enum' => [ 'INTERRUPTED', 'TRIGGERED_ON_PROCESS_TERMINATE', 'FORCE_TERMINATED', ], ], 'GetComputeAccessInput' => [ 'type' => 'structure', 'required' => [ 'FleetId', 'ComputeName', ], 'members' => [ 'FleetId' => [ 'shape' => 'FleetIdOrArn', ], 'ComputeName' => [ 'shape' => 'ComputeNameOrArn', ], ], ], 'GetComputeAccessOutput' => [ 'type' => 'structure', 'members' => [ 'FleetId' => [ 'shape' => 'FleetIdOrArn', ], 'FleetArn' => [ 'shape' => 'FleetArn', ], 'ComputeName' => [ 'shape' => 'ComputeNameOrArn', ], 'ComputeArn' => [ 'shape' => 'ComputeArn', ], 'Credentials' => [ 'shape' => 'AwsCredentials', ], 'Target' => [ 'shape' => 'SessionTarget', ], 'ContainerIdentifiers' => [ 'shape' => 'ContainerIdentifierList', ], ], ], 'GetComputeAuthTokenInput' => [ 'type' => 'structure', 'required' => [ 'FleetId', 'ComputeName', ], 'members' => [ 'FleetId' => [ 'shape' => 'FleetIdOrArn', ], 'ComputeName' => [ 'shape' => 'ComputeNameOrArn', ], ], ], 'GetComputeAuthTokenOutput' => [ 'type' => 'structure', 'members' => [ 'FleetId' => [ 'shape' => 'FleetIdOrArn', ], 'FleetArn' => [ 'shape' => 'FleetArn', ], 'ComputeName' => [ 'shape' => 'ComputeNameOrArn', ], 'ComputeArn' => [ 'shape' => 'ComputeArn', ], 'AuthToken' => [ 'shape' => 'ComputeAuthToken', ], 'ExpirationTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'GetGameSessionLogUrlInput' => [ 'type' => 'structure', 'required' => [ 'GameSessionId', ], 'members' => [ 'GameSessionId' => [ 'shape' => 'ArnStringModel', ], ], ], 'GetGameSessionLogUrlOutput' => [ 'type' => 'structure', 'members' => [ 'PreSignedUrl' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'GetInstanceAccessInput' => [ 'type' => 'structure', 'required' => [ 'FleetId', 'InstanceId', ], 'members' => [ 'FleetId' => [ 'shape' => 'FleetIdOrArn', ], 'InstanceId' => [ 'shape' => 'InstanceId', ], ], ], 'GetInstanceAccessOutput' => [ 'type' => 'structure', 'members' => [ 'InstanceAccess' => [ 'shape' => 'InstanceAccess', ], ], ], 'IamRoleArn' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^arn:.*:role\\/[\\w+=,.@-]+', ], 'IdStringModel' => [ 'type' => 'string', 'max' => 48, 'min' => 1, 'pattern' => '[a-zA-Z0-9-]+', ], 'IdempotentParameterMismatchException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'NonEmptyString', ], ], 'exception' => true, ], 'ImageUriString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9-_\\.@\\/:]+$', ], 'Instance' => [ 'type' => 'structure', 'members' => [ 'FleetId' => [ 'shape' => 'FleetId', ], 'FleetArn' => [ 'shape' => 'FleetArn', ], 'InstanceId' => [ 'shape' => 'InstanceId', ], 'IpAddress' => [ 'shape' => 'IpAddress', ], 'DnsName' => [ 'shape' => 'DnsName', ], 'OperatingSystem' => [ 'shape' => 'OperatingSystem', ], 'Type' => [ 'shape' => 'EC2InstanceType', ], 'Status' => [ 'shape' => 'InstanceStatus', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'Location' => [ 'shape' => 'LocationStringModel', ], ], ], 'InstanceAccess' => [ 'type' => 'structure', 'members' => [ 'FleetId' => [ 'shape' => 'FleetId', ], 'InstanceId' => [ 'shape' => 'InstanceId', ], 'IpAddress' => [ 'shape' => 'IpAddress', ], 'OperatingSystem' => [ 'shape' => 'OperatingSystem', ], 'Credentials' => [ 'shape' => 'InstanceCredentials', ], ], ], 'InstanceCredentials' => [ 'type' => 'structure', 'members' => [ 'UserName' => [ 'shape' => 'NonEmptyString', ], 'Secret' => [ 'shape' => 'NonEmptyString', ], ], 'sensitive' => true, ], 'InstanceDefinition' => [ 'type' => 'structure', 'required' => [ 'InstanceType', ], 'members' => [ 'InstanceType' => [ 'shape' => 'GameServerGroupInstanceType', ], 'WeightedCapacity' => [ 'shape' => 'WeightedCapacity', ], ], ], 'InstanceDefinitions' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceDefinition', ], 'max' => 20, 'min' => 2, ], 'InstanceId' => [ 'type' => 'string', 'pattern' => '[a-zA-Z0-9\\.-]+', ], 'InstanceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Instance', ], ], 'InstancePathString' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^\\/[\\s\\S]*$', ], 'InstanceRoleCredentialsProvider' => [ 'type' => 'string', 'enum' => [ 'SHARED_CREDENTIAL_FILE', ], ], 'InstanceStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'ACTIVE', 'TERMINATING', ], ], 'Integer' => [ 'type' => 'integer', ], 'InternalServiceException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'NonEmptyString', ], ], 'exception' => true, 'fault' => true, ], 'InvalidFleetStatusException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'NonEmptyString', ], ], 'exception' => true, ], 'InvalidGameSessionStatusException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'NonEmptyString', ], ], 'exception' => true, ], 'InvalidRequestException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'NonEmptyString', ], ], 'exception' => true, ], 'IpAddress' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[0-9A-Fa-f\\:\\.]+', 'sensitive' => true, ], 'IpPermission' => [ 'type' => 'structure', 'required' => [ 'FromPort', 'ToPort', 'IpRange', 'Protocol', ], 'members' => [ 'FromPort' => [ 'shape' => 'PortNumber', ], 'ToPort' => [ 'shape' => 'PortNumber', ], 'IpRange' => [ 'shape' => 'IpRange', ], 'Protocol' => [ 'shape' => 'IpProtocol', ], ], ], 'IpPermissionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IpPermission', ], 'max' => 50, ], 'IpProtocol' => [ 'type' => 'string', 'enum' => [ 'TCP', 'UDP', ], ], 'IpRange' => [ 'type' => 'string', 'pattern' => '[^\\s]+', 'sensitive' => true, ], 'LargeGameSessionData' => [ 'type' => 'string', 'max' => 262144, 'min' => 1, ], 'LatencyMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'NonEmptyString', ], 'value' => [ 'shape' => 'PositiveInteger', ], ], 'LaunchParametersStringModel' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '[A-Za-z0-9_:.+\\/\\\\\\- =@;{},?\'\\[\\]"]+', ], 'LaunchPathStringModel' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '[A-Za-z0-9_:.+\\/\\\\\\- ]+', ], 'LaunchTemplateId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]+', ], 'LaunchTemplateName' => [ 'type' => 'string', 'max' => 128, 'min' => 3, 'pattern' => '[a-zA-Z0-9\\(\\)\\.\\-/_]+', ], 'LaunchTemplateSpecification' => [ 'type' => 'structure', 'members' => [ 'LaunchTemplateId' => [ 'shape' => 'LaunchTemplateId', ], 'LaunchTemplateName' => [ 'shape' => 'LaunchTemplateName', ], 'Version' => [ 'shape' => 'LaunchTemplateVersion', ], ], ], 'LaunchTemplateVersion' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]+', ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'NonEmptyString', ], ], 'exception' => true, ], 'ListAliasesInput' => [ 'type' => 'structure', 'members' => [ 'RoutingStrategyType' => [ 'shape' => 'RoutingStrategyType', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'Limit' => [ 'shape' => 'PositiveInteger', ], 'NextToken' => [ 'shape' => 'NonEmptyString', ], ], ], 'ListAliasesOutput' => [ 'type' => 'structure', 'members' => [ 'Aliases' => [ 'shape' => 'AliasList', ], 'NextToken' => [ 'shape' => 'NonEmptyString', ], ], ], 'ListBuildsInput' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'BuildStatus', ], 'Limit' => [ 'shape' => 'PositiveInteger', ], 'NextToken' => [ 'shape' => 'NonEmptyString', ], ], ], 'ListBuildsOutput' => [ 'type' => 'structure', 'members' => [ 'Builds' => [ 'shape' => 'BuildList', ], 'NextToken' => [ 'shape' => 'NonEmptyString', ], ], ], 'ListComputeInput' => [ 'type' => 'structure', 'required' => [ 'FleetId', ], 'members' => [ 'FleetId' => [ 'shape' => 'FleetIdOrArn', ], 'Location' => [ 'shape' => 'LocationStringModel', ], 'ContainerGroupDefinitionName' => [ 'shape' => 'ContainerGroupDefinitionNameOrArn', ], 'ComputeStatus' => [ 'shape' => 'ListComputeInputStatus', ], 'Limit' => [ 'shape' => 'PositiveInteger', ], 'NextToken' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'ListComputeInputStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'IMPAIRED', ], ], 'ListComputeOutput' => [ 'type' => 'structure', 'members' => [ 'ComputeList' => [ 'shape' => 'ComputeList', ], 'NextToken' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'ListContainerFleetsInput' => [ 'type' => 'structure', 'members' => [ 'ContainerGroupDefinitionName' => [ 'shape' => 'ContainerGroupDefinitionNameOrArn', ], 'Limit' => [ 'shape' => 'PositiveInteger', ], 'NextToken' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'ListContainerFleetsOutput' => [ 'type' => 'structure', 'members' => [ 'ContainerFleets' => [ 'shape' => 'ContainerFleetList', ], 'NextToken' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'ListContainerGroupDefinitionVersionsInput' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'ContainerGroupDefinitionNameOrArn', ], 'Limit' => [ 'shape' => 'ListContainerGroupDefinitionVersionsLimit', ], 'NextToken' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'ListContainerGroupDefinitionVersionsLimit' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'ListContainerGroupDefinitionVersionsOutput' => [ 'type' => 'structure', 'members' => [ 'ContainerGroupDefinitions' => [ 'shape' => 'ContainerGroupDefinitionList', ], 'NextToken' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'ListContainerGroupDefinitionsInput' => [ 'type' => 'structure', 'members' => [ 'ContainerGroupType' => [ 'shape' => 'ContainerGroupType', ], 'Limit' => [ 'shape' => 'ListContainerGroupDefinitionsLimit', ], 'NextToken' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'ListContainerGroupDefinitionsLimit' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'ListContainerGroupDefinitionsOutput' => [ 'type' => 'structure', 'members' => [ 'ContainerGroupDefinitions' => [ 'shape' => 'ContainerGroupDefinitionList', ], 'NextToken' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'ListFleetDeploymentsInput' => [ 'type' => 'structure', 'members' => [ 'FleetId' => [ 'shape' => 'FleetIdOrArn', ], 'Limit' => [ 'shape' => 'PositiveInteger', ], 'NextToken' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'ListFleetDeploymentsOutput' => [ 'type' => 'structure', 'members' => [ 'FleetDeployments' => [ 'shape' => 'FleetDeployments', ], 'NextToken' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'ListFleetsInput' => [ 'type' => 'structure', 'members' => [ 'BuildId' => [ 'shape' => 'BuildIdOrArn', ], 'ScriptId' => [ 'shape' => 'ScriptIdOrArn', ], 'Limit' => [ 'shape' => 'PositiveInteger', ], 'NextToken' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'ListFleetsOutput' => [ 'type' => 'structure', 'members' => [ 'FleetIds' => [ 'shape' => 'FleetIdList', ], 'NextToken' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'ListGameServerGroupsInput' => [ 'type' => 'structure', 'members' => [ 'Limit' => [ 'shape' => 'PositiveInteger', ], 'NextToken' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'ListGameServerGroupsOutput' => [ 'type' => 'structure', 'members' => [ 'GameServerGroups' => [ 'shape' => 'GameServerGroups', ], 'NextToken' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'ListGameServersInput' => [ 'type' => 'structure', 'required' => [ 'GameServerGroupName', ], 'members' => [ 'GameServerGroupName' => [ 'shape' => 'GameServerGroupNameOrArn', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], 'Limit' => [ 'shape' => 'PositiveInteger', ], 'NextToken' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'ListGameServersOutput' => [ 'type' => 'structure', 'members' => [ 'GameServers' => [ 'shape' => 'GameServers', ], 'NextToken' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'ListLocationsInput' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'LocationFilterList', ], 'Limit' => [ 'shape' => 'ListLocationsLimit', ], 'NextToken' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'ListLocationsLimit' => [ 'type' => 'integer', 'max' => 200, 'min' => 1, ], 'ListLocationsOutput' => [ 'type' => 'structure', 'members' => [ 'Locations' => [ 'shape' => 'LocationModelList', ], 'NextToken' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'ListScriptsInput' => [ 'type' => 'structure', 'members' => [ 'Limit' => [ 'shape' => 'PositiveInteger', ], 'NextToken' => [ 'shape' => 'NonEmptyString', ], ], ], 'ListScriptsOutput' => [ 'type' => 'structure', 'members' => [ 'Scripts' => [ 'shape' => 'ScriptList', ], 'NextToken' => [ 'shape' => 'NonEmptyString', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'AmazonResourceName', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagList', ], ], ], 'LocationArnModel' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^arn:.*:location\\/custom-\\S+', ], 'LocationAttributes' => [ 'type' => 'structure', 'members' => [ 'LocationState' => [ 'shape' => 'LocationState', ], 'StoppedActions' => [ 'shape' => 'FleetActionList', ], 'UpdateStatus' => [ 'shape' => 'LocationUpdateStatus', ], ], ], 'LocationAttributesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LocationAttributes', ], ], 'LocationConfiguration' => [ 'type' => 'structure', 'required' => [ 'Location', ], 'members' => [ 'Location' => [ 'shape' => 'LocationStringModel', ], ], ], 'LocationConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LocationConfiguration', ], 'max' => 100, 'min' => 1, ], 'LocationFilter' => [ 'type' => 'string', 'enum' => [ 'AWS', 'CUSTOM', ], ], 'LocationFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LocationFilter', ], 'max' => 2, 'min' => 1, ], 'LocationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LocationStringModel', ], 'max' => 100, 'min' => 1, ], 'LocationModel' => [ 'type' => 'structure', 'members' => [ 'LocationName' => [ 'shape' => 'LocationStringModel', ], 'LocationArn' => [ 'shape' => 'LocationArnModel', ], 'PingBeacon' => [ 'shape' => 'PingBeacon', ], ], ], 'LocationModelList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LocationModel', ], ], 'LocationOrderOverrideList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LocationStringModel', ], 'max' => 10, 'min' => 1, ], 'LocationState' => [ 'type' => 'structure', 'members' => [ 'Location' => [ 'shape' => 'LocationStringModel', ], 'Status' => [ 'shape' => 'FleetStatus', ], ], ], 'LocationStateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LocationState', ], ], 'LocationStringModel' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[A-Za-z0-9\\-]+', ], 'LocationUpdateStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING_UPDATE', ], ], 'LocationalDeployment' => [ 'type' => 'structure', 'members' => [ 'DeploymentStatus' => [ 'shape' => 'DeploymentStatus', ], ], ], 'LocationalDeployments' => [ 'type' => 'map', 'key' => [ 'shape' => 'NonZeroAnd128MaxAsciiString', ], 'value' => [ 'shape' => 'LocationalDeployment', ], ], 'LogConfiguration' => [ 'type' => 'structure', 'members' => [ 'LogDestination' => [ 'shape' => 'LogDestination', ], 'S3BucketName' => [ 'shape' => 'NonEmptyString', ], 'LogGroupArn' => [ 'shape' => 'LogGroupArnStringModel', ], ], ], 'LogDestination' => [ 'type' => 'string', 'enum' => [ 'NONE', 'CLOUDWATCH', 'S3', ], ], 'LogGroupArnStringModel' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '[a-zA-Z0-9:/\\-\\*]+', ], 'MatchedPlayerSession' => [ 'type' => 'structure', 'members' => [ 'PlayerId' => [ 'shape' => 'PlayerId', ], 'PlayerSessionId' => [ 'shape' => 'PlayerSessionId', ], ], ], 'MatchedPlayerSessionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MatchedPlayerSession', ], ], 'MatchmakerData' => [ 'type' => 'string', 'max' => 390000, 'min' => 1, ], 'MatchmakingAcceptanceTimeoutInteger' => [ 'type' => 'integer', 'max' => 600, 'min' => 1, ], 'MatchmakingConfiguration' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'MatchmakingIdStringModel', ], 'ConfigurationArn' => [ 'shape' => 'MatchmakingConfigurationArn', ], 'Description' => [ 'shape' => 'NonZeroAndMaxString', ], 'GameSessionQueueArns' => [ 'shape' => 'QueueArnsList', ], 'RequestTimeoutSeconds' => [ 'shape' => 'MatchmakingRequestTimeoutInteger', ], 'AcceptanceTimeoutSeconds' => [ 'shape' => 'MatchmakingAcceptanceTimeoutInteger', ], 'AcceptanceRequired' => [ 'shape' => 'BooleanModel', ], 'RuleSetName' => [ 'shape' => 'MatchmakingIdStringModel', ], 'RuleSetArn' => [ 'shape' => 'MatchmakingRuleSetArn', ], 'NotificationTarget' => [ 'shape' => 'SnsArnStringModel', ], 'AdditionalPlayerCount' => [ 'shape' => 'WholeNumber', ], 'CustomEventData' => [ 'shape' => 'CustomEventData', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'GameProperties' => [ 'shape' => 'GamePropertyList', ], 'GameSessionData' => [ 'shape' => 'GameSessionData', ], 'BackfillMode' => [ 'shape' => 'BackfillMode', ], 'FlexMatchMode' => [ 'shape' => 'FlexMatchMode', ], ], ], 'MatchmakingConfigurationArn' => [ 'type' => 'string', 'pattern' => '^arn:.*:matchmakingconfiguration\\/[a-zA-Z0-9-\\.]*', ], 'MatchmakingConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MatchmakingConfiguration', ], ], 'MatchmakingConfigurationName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[a-zA-Z0-9-\\.]*|^arn:.*:matchmakingconfiguration\\/[a-zA-Z0-9-\\.]*', ], 'MatchmakingConfigurationNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MatchmakingConfigurationName', ], ], 'MatchmakingConfigurationStatus' => [ 'type' => 'string', 'enum' => [ 'CANCELLED', 'COMPLETED', 'FAILED', 'PLACING', 'QUEUED', 'REQUIRES_ACCEPTANCE', 'SEARCHING', 'TIMED_OUT', ], ], 'MatchmakingIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MatchmakingIdStringModel', ], ], 'MatchmakingIdStringModel' => [ 'type' => 'string', 'max' => 128, 'pattern' => '[a-zA-Z0-9-\\.]*', ], 'MatchmakingRequestTimeoutInteger' => [ 'type' => 'integer', 'max' => 43200, 'min' => 1, ], 'MatchmakingRuleSet' => [ 'type' => 'structure', 'required' => [ 'RuleSetBody', ], 'members' => [ 'RuleSetName' => [ 'shape' => 'MatchmakingIdStringModel', ], 'RuleSetArn' => [ 'shape' => 'MatchmakingRuleSetArn', ], 'RuleSetBody' => [ 'shape' => 'RuleSetBody', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], ], ], 'MatchmakingRuleSetArn' => [ 'type' => 'string', 'pattern' => '^arn:.*:matchmakingruleset\\/[a-zA-Z0-9-\\.]*', ], 'MatchmakingRuleSetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MatchmakingRuleSet', ], ], 'MatchmakingRuleSetName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[a-zA-Z0-9-\\.]*|^arn:.*:matchmakingruleset\\/[a-zA-Z0-9-\\.]*', ], 'MatchmakingRuleSetNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MatchmakingRuleSetName', ], 'max' => 10, 'min' => 1, ], 'MatchmakingTicket' => [ 'type' => 'structure', 'members' => [ 'TicketId' => [ 'shape' => 'MatchmakingIdStringModel', ], 'ConfigurationName' => [ 'shape' => 'MatchmakingIdStringModel', ], 'ConfigurationArn' => [ 'shape' => 'MatchmakingConfigurationArn', ], 'Status' => [ 'shape' => 'MatchmakingConfigurationStatus', ], 'StatusReason' => [ 'shape' => 'StringModel', ], 'StatusMessage' => [ 'shape' => 'StringModel', ], 'StartTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], 'Players' => [ 'shape' => 'PlayerList', ], 'GameSessionConnectionInfo' => [ 'shape' => 'GameSessionConnectionInfo', ], 'EstimatedWaitTime' => [ 'shape' => 'WholeNumber', ], ], ], 'MatchmakingTicketList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MatchmakingTicket', ], ], 'MaxConcurrentGameSessionActivations' => [ 'type' => 'integer', 'max' => 2147483647, 'min' => 1, ], 'MaximumGameServerContainerGroupsPerInstance' => [ 'type' => 'integer', 'max' => 5000, 'min' => 1, ], 'MetricGroup' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'MetricGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricGroup', ], 'max' => 1, ], 'MetricName' => [ 'type' => 'string', 'enum' => [ 'ActivatingGameSessions', 'ActiveGameSessions', 'ActiveInstances', 'AvailableGameSessions', 'AvailablePlayerSessions', 'CurrentPlayerSessions', 'IdleInstances', 'PercentAvailableGameSessions', 'PercentIdleInstances', 'QueueDepth', 'WaitTime', 'ConcurrentActivatableGameSessions', ], ], 'MinimumHealthyPercentage' => [ 'type' => 'integer', 'max' => 75, 'min' => 30, ], 'NonBlankAndLengthConstraintString' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '.*\\S.*', ], 'NonEmptyString' => [ 'type' => 'string', 'min' => 1, ], 'NonNegativeDouble' => [ 'type' => 'double', 'min' => 0, ], 'NonNegativeLimitedLengthDouble' => [ 'type' => 'string', 'max' => 11, 'min' => 1, 'pattern' => '^\\d{1,5}(?:\\.\\d{1,5})?$', ], 'NonZeroAnd128MaxAsciiString' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\-]+$', ], 'NonZeroAnd255MaxString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'NonZeroAndMaxString' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'NotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'NonEmptyString', ], ], 'exception' => true, ], 'NotReadyException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'NonEmptyString', ], ], 'exception' => true, ], 'OperatingSystem' => [ 'type' => 'string', 'enum' => [ 'WINDOWS_2012', 'AMAZON_LINUX', 'AMAZON_LINUX_2', 'WINDOWS_2016', 'AMAZON_LINUX_2023', ], ], 'OutOfCapacityException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'NonEmptyString', ], ], 'exception' => true, ], 'PingBeacon' => [ 'type' => 'structure', 'members' => [ 'UDPEndpoint' => [ 'shape' => 'UDPEndpoint', ], ], ], 'PlacedPlayerSession' => [ 'type' => 'structure', 'members' => [ 'PlayerId' => [ 'shape' => 'PlayerId', ], 'PlayerSessionId' => [ 'shape' => 'PlayerSessionId', ], ], ], 'PlacedPlayerSessionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PlacedPlayerSession', ], ], 'PlacementFallbackStrategy' => [ 'type' => 'string', 'enum' => [ 'DEFAULT_AFTER_SINGLE_PASS', 'NONE', ], ], 'Player' => [ 'type' => 'structure', 'members' => [ 'PlayerId' => [ 'shape' => 'PlayerId', ], 'PlayerAttributes' => [ 'shape' => 'PlayerAttributeMap', ], 'Team' => [ 'shape' => 'NonZeroAndMaxString', ], 'LatencyInMs' => [ 'shape' => 'LatencyMap', ], ], ], 'PlayerAttributeMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'NonZeroAndMaxString', ], 'value' => [ 'shape' => 'AttributeValue', ], ], 'PlayerAttributeString' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'PlayerAttributeStringDoubleMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'PlayerAttributeString', ], 'value' => [ 'shape' => 'DoubleObject', ], ], 'PlayerAttributeStringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PlayerAttributeString', ], ], 'PlayerData' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'PlayerDataMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'NonZeroAndMaxString', ], 'value' => [ 'shape' => 'PlayerData', ], ], 'PlayerId' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'sensitive' => true, ], 'PlayerIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PlayerId', ], 'max' => 25, 'min' => 1, 'sensitive' => true, ], 'PlayerIdsForAcceptMatch' => [ 'type' => 'list', 'member' => [ 'shape' => 'PlayerId', ], 'sensitive' => true, ], 'PlayerLatency' => [ 'type' => 'structure', 'members' => [ 'PlayerId' => [ 'shape' => 'PlayerId', ], 'RegionIdentifier' => [ 'shape' => 'NonZeroAndMaxString', ], 'LatencyInMilliseconds' => [ 'shape' => 'Float', ], ], ], 'PlayerLatencyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PlayerLatency', ], ], 'PlayerLatencyPolicy' => [ 'type' => 'structure', 'members' => [ 'MaximumIndividualPlayerLatencyMilliseconds' => [ 'shape' => 'WholeNumber', ], 'PolicyDurationSeconds' => [ 'shape' => 'WholeNumber', ], ], ], 'PlayerLatencyPolicyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PlayerLatencyPolicy', ], ], 'PlayerList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Player', ], ], 'PlayerSession' => [ 'type' => 'structure', 'members' => [ 'PlayerSessionId' => [ 'shape' => 'PlayerSessionId', ], 'PlayerId' => [ 'shape' => 'PlayerId', ], 'GameSessionId' => [ 'shape' => 'NonZeroAndMaxString', ], 'FleetId' => [ 'shape' => 'FleetId', ], 'FleetArn' => [ 'shape' => 'FleetArn', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'TerminationTime' => [ 'shape' => 'Timestamp', ], 'Status' => [ 'shape' => 'PlayerSessionStatus', ], 'IpAddress' => [ 'shape' => 'IpAddress', ], 'DnsName' => [ 'shape' => 'DnsName', ], 'Port' => [ 'shape' => 'PortNumber', ], 'PlayerData' => [ 'shape' => 'PlayerData', ], ], ], 'PlayerSessionCreationPolicy' => [ 'type' => 'string', 'enum' => [ 'ACCEPT_ALL', 'DENY_ALL', ], ], 'PlayerSessionId' => [ 'type' => 'string', 'pattern' => '^psess-\\S+', ], 'PlayerSessionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PlayerSession', ], ], 'PlayerSessionStatus' => [ 'type' => 'string', 'enum' => [ 'RESERVED', 'ACTIVE', 'COMPLETED', 'TIMEDOUT', ], ], 'PolicyType' => [ 'type' => 'string', 'enum' => [ 'RuleBased', 'TargetBased', ], ], 'PortNumber' => [ 'type' => 'integer', 'max' => 60000, 'min' => 1, 'sensitive' => true, ], 'PositiveInteger' => [ 'type' => 'integer', 'min' => 1, ], 'PositiveLong' => [ 'type' => 'long', 'min' => 1, ], 'PriorityConfiguration' => [ 'type' => 'structure', 'members' => [ 'PriorityOrder' => [ 'shape' => 'PriorityTypeList', ], 'LocationOrder' => [ 'shape' => 'LocationList', ], ], ], 'PriorityConfigurationOverride' => [ 'type' => 'structure', 'required' => [ 'LocationOrder', ], 'members' => [ 'PlacementFallbackStrategy' => [ 'shape' => 'PlacementFallbackStrategy', ], 'LocationOrder' => [ 'shape' => 'LocationOrderOverrideList', ], ], ], 'PriorityType' => [ 'type' => 'string', 'enum' => [ 'LATENCY', 'COST', 'DESTINATION', 'LOCATION', ], ], 'PriorityTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PriorityType', ], 'max' => 4, 'min' => 1, ], 'ProtectionPolicy' => [ 'type' => 'string', 'enum' => [ 'NoProtection', 'FullProtection', ], ], 'PutScalingPolicyInput' => [ 'type' => 'structure', 'required' => [ 'Name', 'FleetId', 'MetricName', ], 'members' => [ 'Name' => [ 'shape' => 'NonZeroAndMaxString', ], 'FleetId' => [ 'shape' => 'FleetIdOrArn', ], 'ScalingAdjustment' => [ 'shape' => 'Integer', ], 'ScalingAdjustmentType' => [ 'shape' => 'ScalingAdjustmentType', ], 'Threshold' => [ 'shape' => 'Double', ], 'ComparisonOperator' => [ 'shape' => 'ComparisonOperatorType', ], 'EvaluationPeriods' => [ 'shape' => 'PositiveInteger', ], 'MetricName' => [ 'shape' => 'MetricName', ], 'PolicyType' => [ 'shape' => 'PolicyType', ], 'TargetConfiguration' => [ 'shape' => 'TargetConfiguration', ], ], ], 'PutScalingPolicyOutput' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'QueueArnsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ArnStringModel', ], ], 'QueueCustomEventData' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '[\\s\\S]*', ], 'QueueSnsArnStringModel' => [ 'type' => 'string', 'max' => 300, 'min' => 0, 'pattern' => '[a-zA-Z0-9:_-]*(\\.fifo)?', ], 'RegisterComputeInput' => [ 'type' => 'structure', 'required' => [ 'FleetId', 'ComputeName', ], 'members' => [ 'FleetId' => [ 'shape' => 'FleetIdOrArn', ], 'ComputeName' => [ 'shape' => 'ComputeName', ], 'CertificatePath' => [ 'shape' => 'NonZeroAndMaxString', ], 'DnsName' => [ 'shape' => 'DnsNameInput', ], 'IpAddress' => [ 'shape' => 'IpAddress', ], 'Location' => [ 'shape' => 'LocationStringModel', ], ], ], 'RegisterComputeOutput' => [ 'type' => 'structure', 'members' => [ 'Compute' => [ 'shape' => 'Compute', ], ], ], 'RegisterGameServerInput' => [ 'type' => 'structure', 'required' => [ 'GameServerGroupName', 'GameServerId', 'InstanceId', ], 'members' => [ 'GameServerGroupName' => [ 'shape' => 'GameServerGroupNameOrArn', ], 'GameServerId' => [ 'shape' => 'GameServerId', ], 'InstanceId' => [ 'shape' => 'GameServerInstanceId', ], 'ConnectionInfo' => [ 'shape' => 'GameServerConnectionInfo', ], 'GameServerData' => [ 'shape' => 'GameServerData', ], ], ], 'RegisterGameServerOutput' => [ 'type' => 'structure', 'members' => [ 'GameServer' => [ 'shape' => 'GameServer', ], ], ], 'RequestUploadCredentialsInput' => [ 'type' => 'structure', 'required' => [ 'BuildId', ], 'members' => [ 'BuildId' => [ 'shape' => 'BuildIdOrArn', ], ], ], 'RequestUploadCredentialsOutput' => [ 'type' => 'structure', 'members' => [ 'UploadCredentials' => [ 'shape' => 'AwsCredentials', ], 'StorageLocation' => [ 'shape' => 'S3Location', ], ], ], 'ResolveAliasInput' => [ 'type' => 'structure', 'required' => [ 'AliasId', ], 'members' => [ 'AliasId' => [ 'shape' => 'AliasIdOrArn', ], ], ], 'ResolveAliasOutput' => [ 'type' => 'structure', 'members' => [ 'FleetId' => [ 'shape' => 'FleetId', ], 'FleetArn' => [ 'shape' => 'FleetArn', ], ], ], 'ResourceCreationLimitPolicy' => [ 'type' => 'structure', 'members' => [ 'NewGameSessionsPerCreator' => [ 'shape' => 'WholeNumber', ], 'PolicyPeriodInMinutes' => [ 'shape' => 'WholeNumber', ], ], ], 'ResumeGameServerGroupInput' => [ 'type' => 'structure', 'required' => [ 'GameServerGroupName', 'ResumeActions', ], 'members' => [ 'GameServerGroupName' => [ 'shape' => 'GameServerGroupNameOrArn', ], 'ResumeActions' => [ 'shape' => 'GameServerGroupActions', ], ], ], 'ResumeGameServerGroupOutput' => [ 'type' => 'structure', 'members' => [ 'GameServerGroup' => [ 'shape' => 'GameServerGroup', ], ], ], 'RoutingStrategy' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'RoutingStrategyType', ], 'FleetId' => [ 'shape' => 'FleetId', ], 'Message' => [ 'shape' => 'FreeText', ], ], ], 'RoutingStrategyType' => [ 'type' => 'string', 'enum' => [ 'SIMPLE', 'TERMINAL', ], ], 'RuleSetBody' => [ 'type' => 'string', 'max' => 65535, 'min' => 1, ], 'RuleSetLimit' => [ 'type' => 'integer', 'max' => 10, 'min' => 1, ], 'RuntimeConfiguration' => [ 'type' => 'structure', 'members' => [ 'ServerProcesses' => [ 'shape' => 'ServerProcessList', ], 'MaxConcurrentGameSessionActivations' => [ 'shape' => 'MaxConcurrentGameSessionActivations', ], 'GameSessionActivationTimeoutSeconds' => [ 'shape' => 'GameSessionActivationTimeoutSeconds', ], ], ], 'S3Location' => [ 'type' => 'structure', 'members' => [ 'Bucket' => [ 'shape' => 'NonEmptyString', ], 'Key' => [ 'shape' => 'NonEmptyString', ], 'RoleArn' => [ 'shape' => 'NonEmptyString', ], 'ObjectVersion' => [ 'shape' => 'NonEmptyString', ], ], ], 'ScalingAdjustmentType' => [ 'type' => 'string', 'enum' => [ 'ChangeInCapacity', 'ExactCapacity', 'PercentChangeInCapacity', ], ], 'ScalingPolicy' => [ 'type' => 'structure', 'members' => [ 'FleetId' => [ 'shape' => 'FleetId', ], 'FleetArn' => [ 'shape' => 'FleetArn', ], 'Name' => [ 'shape' => 'NonZeroAndMaxString', ], 'Status' => [ 'shape' => 'ScalingStatusType', ], 'ScalingAdjustment' => [ 'shape' => 'Integer', ], 'ScalingAdjustmentType' => [ 'shape' => 'ScalingAdjustmentType', ], 'ComparisonOperator' => [ 'shape' => 'ComparisonOperatorType', ], 'Threshold' => [ 'shape' => 'Double', ], 'EvaluationPeriods' => [ 'shape' => 'PositiveInteger', ], 'MetricName' => [ 'shape' => 'MetricName', ], 'PolicyType' => [ 'shape' => 'PolicyType', ], 'TargetConfiguration' => [ 'shape' => 'TargetConfiguration', ], 'UpdateStatus' => [ 'shape' => 'LocationUpdateStatus', ], 'Location' => [ 'shape' => 'LocationStringModel', ], ], ], 'ScalingPolicyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ScalingPolicy', ], ], 'ScalingStatusType' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'UPDATE_REQUESTED', 'UPDATING', 'DELETE_REQUESTED', 'DELETING', 'DELETED', 'ERROR', ], ], 'Script' => [ 'type' => 'structure', 'members' => [ 'ScriptId' => [ 'shape' => 'ScriptId', ], 'ScriptArn' => [ 'shape' => 'ScriptArn', ], 'Name' => [ 'shape' => 'NonZeroAndMaxString', ], 'Version' => [ 'shape' => 'NonZeroAndMaxString', ], 'SizeOnDisk' => [ 'shape' => 'PositiveLong', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'StorageLocation' => [ 'shape' => 'S3Location', ], ], ], 'ScriptArn' => [ 'type' => 'string', 'pattern' => '^arn:.*:script\\/script-\\S+', ], 'ScriptId' => [ 'type' => 'string', 'pattern' => '^script-\\S+', ], 'ScriptIdOrArn' => [ 'type' => 'string', 'pattern' => '^script-\\S+|^arn:.*:script\\/script-\\S+', ], 'ScriptList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Script', ], ], 'SearchGameSessionsInput' => [ 'type' => 'structure', 'members' => [ 'FleetId' => [ 'shape' => 'FleetIdOrArn', ], 'AliasId' => [ 'shape' => 'AliasIdOrArn', ], 'Location' => [ 'shape' => 'LocationStringModel', ], 'FilterExpression' => [ 'shape' => 'NonZeroAndMaxString', ], 'SortExpression' => [ 'shape' => 'NonZeroAndMaxString', ], 'Limit' => [ 'shape' => 'PositiveInteger', ], 'NextToken' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'SearchGameSessionsOutput' => [ 'type' => 'structure', 'members' => [ 'GameSessions' => [ 'shape' => 'GameSessionList', ], 'NextToken' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'ServerProcess' => [ 'type' => 'structure', 'required' => [ 'LaunchPath', 'ConcurrentExecutions', ], 'members' => [ 'LaunchPath' => [ 'shape' => 'LaunchPathStringModel', ], 'Parameters' => [ 'shape' => 'LaunchParametersStringModel', ], 'ConcurrentExecutions' => [ 'shape' => 'PositiveInteger', ], ], ], 'ServerProcessList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ServerProcess', ], 'max' => 50, 'min' => 1, ], 'ServerSdkVersion' => [ 'type' => 'string', 'max' => 128, 'pattern' => '^\\d+\\.\\d+\\.\\d+$', ], 'SessionTarget' => [ 'type' => 'string', 'max' => 400, 'min' => 1, ], 'Sha256' => [ 'type' => 'string', 'pattern' => '^sha256:[a-fA-F0-9]{64}$', ], 'SnsArnStringModel' => [ 'type' => 'string', 'max' => 300, 'min' => 0, 'pattern' => '[a-zA-Z0-9:_/-]*(.fifo)?', ], 'SortOrder' => [ 'type' => 'string', 'enum' => [ 'ASCENDING', 'DESCENDING', ], ], 'StartFleetActionsInput' => [ 'type' => 'structure', 'required' => [ 'FleetId', 'Actions', ], 'members' => [ 'FleetId' => [ 'shape' => 'FleetIdOrArn', ], 'Actions' => [ 'shape' => 'FleetActionList', ], 'Location' => [ 'shape' => 'LocationStringModel', ], ], ], 'StartFleetActionsOutput' => [ 'type' => 'structure', 'members' => [ 'FleetId' => [ 'shape' => 'FleetId', ], 'FleetArn' => [ 'shape' => 'FleetArn', ], ], ], 'StartGameSessionPlacementInput' => [ 'type' => 'structure', 'required' => [ 'PlacementId', 'GameSessionQueueName', 'MaximumPlayerSessionCount', ], 'members' => [ 'PlacementId' => [ 'shape' => 'IdStringModel', ], 'GameSessionQueueName' => [ 'shape' => 'GameSessionQueueNameOrArn', ], 'GameProperties' => [ 'shape' => 'GamePropertyList', ], 'MaximumPlayerSessionCount' => [ 'shape' => 'WholeNumber', ], 'GameSessionName' => [ 'shape' => 'NonZeroAndMaxString', ], 'PlayerLatencies' => [ 'shape' => 'PlayerLatencyList', ], 'DesiredPlayerSessions' => [ 'shape' => 'DesiredPlayerSessionList', ], 'GameSessionData' => [ 'shape' => 'LargeGameSessionData', ], 'PriorityConfigurationOverride' => [ 'shape' => 'PriorityConfigurationOverride', ], ], ], 'StartGameSessionPlacementOutput' => [ 'type' => 'structure', 'members' => [ 'GameSessionPlacement' => [ 'shape' => 'GameSessionPlacement', ], ], ], 'StartMatchBackfillInput' => [ 'type' => 'structure', 'required' => [ 'ConfigurationName', 'Players', ], 'members' => [ 'TicketId' => [ 'shape' => 'MatchmakingIdStringModel', ], 'ConfigurationName' => [ 'shape' => 'MatchmakingConfigurationName', ], 'GameSessionArn' => [ 'shape' => 'ArnStringModel', ], 'Players' => [ 'shape' => 'PlayerList', ], ], ], 'StartMatchBackfillOutput' => [ 'type' => 'structure', 'members' => [ 'MatchmakingTicket' => [ 'shape' => 'MatchmakingTicket', ], ], ], 'StartMatchmakingInput' => [ 'type' => 'structure', 'required' => [ 'ConfigurationName', 'Players', ], 'members' => [ 'TicketId' => [ 'shape' => 'MatchmakingIdStringModel', ], 'ConfigurationName' => [ 'shape' => 'MatchmakingConfigurationName', ], 'Players' => [ 'shape' => 'PlayerList', ], ], ], 'StartMatchmakingOutput' => [ 'type' => 'structure', 'members' => [ 'MatchmakingTicket' => [ 'shape' => 'MatchmakingTicket', ], ], ], 'StopFleetActionsInput' => [ 'type' => 'structure', 'required' => [ 'FleetId', 'Actions', ], 'members' => [ 'FleetId' => [ 'shape' => 'FleetIdOrArn', ], 'Actions' => [ 'shape' => 'FleetActionList', ], 'Location' => [ 'shape' => 'LocationStringModel', ], ], ], 'StopFleetActionsOutput' => [ 'type' => 'structure', 'members' => [ 'FleetId' => [ 'shape' => 'FleetId', ], 'FleetArn' => [ 'shape' => 'FleetArn', ], ], ], 'StopGameSessionPlacementInput' => [ 'type' => 'structure', 'required' => [ 'PlacementId', ], 'members' => [ 'PlacementId' => [ 'shape' => 'IdStringModel', ], ], ], 'StopGameSessionPlacementOutput' => [ 'type' => 'structure', 'members' => [ 'GameSessionPlacement' => [ 'shape' => 'GameSessionPlacement', ], ], ], 'StopMatchmakingInput' => [ 'type' => 'structure', 'required' => [ 'TicketId', ], 'members' => [ 'TicketId' => [ 'shape' => 'MatchmakingIdStringModel', ], ], ], 'StopMatchmakingOutput' => [ 'type' => 'structure', 'members' => [], ], 'StringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonZeroAndMaxString', ], ], 'StringModel' => [ 'type' => 'string', ], 'SupportContainerDefinition' => [ 'type' => 'structure', 'members' => [ 'ContainerName' => [ 'shape' => 'NonZeroAnd128MaxAsciiString', ], 'DependsOn' => [ 'shape' => 'ContainerDependencyList', ], 'MountPoints' => [ 'shape' => 'ContainerMountPointList', ], 'EnvironmentOverride' => [ 'shape' => 'ContainerEnvironmentList', ], 'Essential' => [ 'shape' => 'BooleanModel', ], 'HealthCheck' => [ 'shape' => 'ContainerHealthCheck', ], 'ImageUri' => [ 'shape' => 'ImageUriString', ], 'MemoryHardLimitMebibytes' => [ 'shape' => 'ContainerMemoryLimit', ], 'PortConfiguration' => [ 'shape' => 'ContainerPortConfiguration', ], 'ResolvedImageDigest' => [ 'shape' => 'Sha256', ], 'Vcpu' => [ 'shape' => 'ContainerVcpu', ], ], ], 'SupportContainerDefinitionInput' => [ 'type' => 'structure', 'required' => [ 'ContainerName', 'ImageUri', ], 'members' => [ 'ContainerName' => [ 'shape' => 'NonZeroAnd128MaxAsciiString', ], 'DependsOn' => [ 'shape' => 'ContainerDependencyList', ], 'MountPoints' => [ 'shape' => 'ContainerMountPointList', ], 'EnvironmentOverride' => [ 'shape' => 'ContainerEnvironmentList', ], 'Essential' => [ 'shape' => 'BooleanModel', ], 'HealthCheck' => [ 'shape' => 'ContainerHealthCheck', ], 'ImageUri' => [ 'shape' => 'ImageUriString', ], 'MemoryHardLimitMebibytes' => [ 'shape' => 'ContainerMemoryLimit', ], 'PortConfiguration' => [ 'shape' => 'ContainerPortConfiguration', ], 'Vcpu' => [ 'shape' => 'ContainerVcpu', ], ], ], 'SupportContainerDefinitionInputList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SupportContainerDefinitionInput', ], 'max' => 10, 'min' => 0, ], 'SupportContainerDefinitionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SupportContainerDefinition', ], 'max' => 10, 'min' => 1, ], 'SuspendGameServerGroupInput' => [ 'type' => 'structure', 'required' => [ 'GameServerGroupName', 'SuspendActions', ], 'members' => [ 'GameServerGroupName' => [ 'shape' => 'GameServerGroupNameOrArn', ], 'SuspendActions' => [ 'shape' => 'GameServerGroupActions', ], ], ], 'SuspendGameServerGroupOutput' => [ 'type' => 'structure', 'members' => [ 'GameServerGroup' => [ 'shape' => 'GameServerGroup', ], ], ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 200, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', 'Tags', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'AmazonResourceName', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'TaggingFailedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'NonEmptyString', ], ], 'exception' => true, ], 'TargetConfiguration' => [ 'type' => 'structure', 'required' => [ 'TargetValue', ], 'members' => [ 'TargetValue' => [ 'shape' => 'Double', ], ], ], 'TargetTrackingConfiguration' => [ 'type' => 'structure', 'required' => [ 'TargetValue', ], 'members' => [ 'TargetValue' => [ 'shape' => 'NonNegativeDouble', ], ], ], 'TerminalRoutingStrategyException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'NonEmptyString', ], ], 'exception' => true, ], 'TerminateGameSessionInput' => [ 'type' => 'structure', 'required' => [ 'GameSessionId', 'TerminationMode', ], 'members' => [ 'GameSessionId' => [ 'shape' => 'ArnStringModel', ], 'TerminationMode' => [ 'shape' => 'TerminationMode', ], ], ], 'TerminateGameSessionOutput' => [ 'type' => 'structure', 'members' => [ 'GameSession' => [ 'shape' => 'GameSession', ], ], ], 'TerminationMode' => [ 'type' => 'string', 'enum' => [ 'TRIGGER_ON_PROCESS_TERMINATE', 'FORCE_TERMINATE', ], ], 'Timestamp' => [ 'type' => 'timestamp', ], 'UDPEndpoint' => [ 'type' => 'structure', 'members' => [ 'Domain' => [ 'shape' => 'NonZeroAndMaxString', ], 'Port' => [ 'shape' => 'PositiveInteger', ], ], ], 'UnauthorizedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'NonEmptyString', ], ], 'exception' => true, ], 'UnsupportedRegionException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'NonEmptyString', ], ], 'exception' => true, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', 'TagKeys', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'AmazonResourceName', ], 'TagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateAliasInput' => [ 'type' => 'structure', 'required' => [ 'AliasId', ], 'members' => [ 'AliasId' => [ 'shape' => 'AliasIdOrArn', ], 'Name' => [ 'shape' => 'NonBlankAndLengthConstraintString', ], 'Description' => [ 'shape' => 'NonZeroAndMaxString', ], 'RoutingStrategy' => [ 'shape' => 'RoutingStrategy', ], ], ], 'UpdateAliasOutput' => [ 'type' => 'structure', 'members' => [ 'Alias' => [ 'shape' => 'Alias', ], ], ], 'UpdateBuildInput' => [ 'type' => 'structure', 'required' => [ 'BuildId', ], 'members' => [ 'BuildId' => [ 'shape' => 'BuildIdOrArn', ], 'Name' => [ 'shape' => 'NonZeroAndMaxString', ], 'Version' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'UpdateBuildOutput' => [ 'type' => 'structure', 'members' => [ 'Build' => [ 'shape' => 'Build', ], ], ], 'UpdateContainerFleetInput' => [ 'type' => 'structure', 'required' => [ 'FleetId', ], 'members' => [ 'FleetId' => [ 'shape' => 'FleetIdOrArn', ], 'GameServerContainerGroupDefinitionName' => [ 'shape' => 'ContainerGroupDefinitionNameOrArn', ], 'PerInstanceContainerGroupDefinitionName' => [ 'shape' => 'ContainerGroupDefinitionNameOrArn', ], 'GameServerContainerGroupsPerInstance' => [ 'shape' => 'GameServerContainerGroupsPerInstance', ], 'InstanceConnectionPortRange' => [ 'shape' => 'ConnectionPortRange', ], 'InstanceInboundPermissionAuthorizations' => [ 'shape' => 'IpPermissionsList', ], 'InstanceInboundPermissionRevocations' => [ 'shape' => 'IpPermissionsList', ], 'DeploymentConfiguration' => [ 'shape' => 'DeploymentConfiguration', ], 'Description' => [ 'shape' => 'NonZeroAndMaxString', ], 'MetricGroups' => [ 'shape' => 'MetricGroupList', ], 'NewGameSessionProtectionPolicy' => [ 'shape' => 'ProtectionPolicy', ], 'GameSessionCreationLimitPolicy' => [ 'shape' => 'GameSessionCreationLimitPolicy', ], 'LogConfiguration' => [ 'shape' => 'LogConfiguration', ], 'RemoveAttributes' => [ 'shape' => 'ContainerFleetRemoveAttributeList', ], ], ], 'UpdateContainerFleetOutput' => [ 'type' => 'structure', 'members' => [ 'ContainerFleet' => [ 'shape' => 'ContainerFleet', ], ], ], 'UpdateContainerGroupDefinitionInput' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'ContainerGroupDefinitionNameOrArn', ], 'GameServerContainerDefinition' => [ 'shape' => 'GameServerContainerDefinitionInput', ], 'SupportContainerDefinitions' => [ 'shape' => 'SupportContainerDefinitionInputList', ], 'TotalMemoryLimitMebibytes' => [ 'shape' => 'ContainerTotalMemoryLimit', ], 'TotalVcpuLimit' => [ 'shape' => 'ContainerTotalVcpuLimit', ], 'VersionDescription' => [ 'shape' => 'NonZeroAndMaxString', ], 'SourceVersionNumber' => [ 'shape' => 'PositiveInteger', ], 'OperatingSystem' => [ 'shape' => 'ContainerOperatingSystem', ], ], ], 'UpdateContainerGroupDefinitionOutput' => [ 'type' => 'structure', 'members' => [ 'ContainerGroupDefinition' => [ 'shape' => 'ContainerGroupDefinition', ], ], ], 'UpdateFleetAttributesInput' => [ 'type' => 'structure', 'required' => [ 'FleetId', ], 'members' => [ 'FleetId' => [ 'shape' => 'FleetIdOrArn', ], 'Name' => [ 'shape' => 'NonZeroAndMaxString', ], 'Description' => [ 'shape' => 'NonZeroAndMaxString', ], 'NewGameSessionProtectionPolicy' => [ 'shape' => 'ProtectionPolicy', ], 'ResourceCreationLimitPolicy' => [ 'shape' => 'ResourceCreationLimitPolicy', ], 'MetricGroups' => [ 'shape' => 'MetricGroupList', ], 'AnywhereConfiguration' => [ 'shape' => 'AnywhereConfiguration', ], ], ], 'UpdateFleetAttributesOutput' => [ 'type' => 'structure', 'members' => [ 'FleetId' => [ 'shape' => 'FleetId', ], 'FleetArn' => [ 'shape' => 'FleetArn', ], ], ], 'UpdateFleetCapacityInput' => [ 'type' => 'structure', 'required' => [ 'FleetId', ], 'members' => [ 'FleetId' => [ 'shape' => 'FleetIdOrArn', ], 'DesiredInstances' => [ 'shape' => 'WholeNumber', ], 'MinSize' => [ 'shape' => 'WholeNumber', ], 'MaxSize' => [ 'shape' => 'WholeNumber', ], 'Location' => [ 'shape' => 'LocationStringModel', ], ], ], 'UpdateFleetCapacityOutput' => [ 'type' => 'structure', 'members' => [ 'FleetId' => [ 'shape' => 'FleetId', ], 'FleetArn' => [ 'shape' => 'FleetArn', ], 'Location' => [ 'shape' => 'LocationStringModel', ], ], ], 'UpdateFleetPortSettingsInput' => [ 'type' => 'structure', 'required' => [ 'FleetId', ], 'members' => [ 'FleetId' => [ 'shape' => 'FleetIdOrArn', ], 'InboundPermissionAuthorizations' => [ 'shape' => 'IpPermissionsList', ], 'InboundPermissionRevocations' => [ 'shape' => 'IpPermissionsList', ], ], ], 'UpdateFleetPortSettingsOutput' => [ 'type' => 'structure', 'members' => [ 'FleetId' => [ 'shape' => 'FleetId', ], 'FleetArn' => [ 'shape' => 'FleetArn', ], ], ], 'UpdateGameServerGroupInput' => [ 'type' => 'structure', 'required' => [ 'GameServerGroupName', ], 'members' => [ 'GameServerGroupName' => [ 'shape' => 'GameServerGroupNameOrArn', ], 'RoleArn' => [ 'shape' => 'IamRoleArn', ], 'InstanceDefinitions' => [ 'shape' => 'InstanceDefinitions', ], 'GameServerProtectionPolicy' => [ 'shape' => 'GameServerProtectionPolicy', ], 'BalancingStrategy' => [ 'shape' => 'BalancingStrategy', ], ], ], 'UpdateGameServerGroupOutput' => [ 'type' => 'structure', 'members' => [ 'GameServerGroup' => [ 'shape' => 'GameServerGroup', ], ], ], 'UpdateGameServerInput' => [ 'type' => 'structure', 'required' => [ 'GameServerGroupName', 'GameServerId', ], 'members' => [ 'GameServerGroupName' => [ 'shape' => 'GameServerGroupNameOrArn', ], 'GameServerId' => [ 'shape' => 'GameServerId', ], 'GameServerData' => [ 'shape' => 'GameServerData', ], 'UtilizationStatus' => [ 'shape' => 'GameServerUtilizationStatus', ], 'HealthCheck' => [ 'shape' => 'GameServerHealthCheck', ], ], ], 'UpdateGameServerOutput' => [ 'type' => 'structure', 'members' => [ 'GameServer' => [ 'shape' => 'GameServer', ], ], ], 'UpdateGameSessionInput' => [ 'type' => 'structure', 'required' => [ 'GameSessionId', ], 'members' => [ 'GameSessionId' => [ 'shape' => 'ArnStringModel', ], 'MaximumPlayerSessionCount' => [ 'shape' => 'WholeNumber', ], 'Name' => [ 'shape' => 'NonZeroAndMaxString', ], 'PlayerSessionCreationPolicy' => [ 'shape' => 'PlayerSessionCreationPolicy', ], 'ProtectionPolicy' => [ 'shape' => 'ProtectionPolicy', ], 'GameProperties' => [ 'shape' => 'GamePropertyList', ], ], ], 'UpdateGameSessionOutput' => [ 'type' => 'structure', 'members' => [ 'GameSession' => [ 'shape' => 'GameSession', ], ], ], 'UpdateGameSessionQueueInput' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'GameSessionQueueNameOrArn', ], 'TimeoutInSeconds' => [ 'shape' => 'WholeNumber', ], 'PlayerLatencyPolicies' => [ 'shape' => 'PlayerLatencyPolicyList', ], 'Destinations' => [ 'shape' => 'GameSessionQueueDestinationList', ], 'FilterConfiguration' => [ 'shape' => 'FilterConfiguration', ], 'PriorityConfiguration' => [ 'shape' => 'PriorityConfiguration', ], 'CustomEventData' => [ 'shape' => 'QueueCustomEventData', ], 'NotificationTarget' => [ 'shape' => 'QueueSnsArnStringModel', ], ], ], 'UpdateGameSessionQueueOutput' => [ 'type' => 'structure', 'members' => [ 'GameSessionQueue' => [ 'shape' => 'GameSessionQueue', ], ], ], 'UpdateMatchmakingConfigurationInput' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'MatchmakingConfigurationName', ], 'Description' => [ 'shape' => 'NonZeroAndMaxString', ], 'GameSessionQueueArns' => [ 'shape' => 'QueueArnsList', ], 'RequestTimeoutSeconds' => [ 'shape' => 'MatchmakingRequestTimeoutInteger', ], 'AcceptanceTimeoutSeconds' => [ 'shape' => 'MatchmakingAcceptanceTimeoutInteger', ], 'AcceptanceRequired' => [ 'shape' => 'BooleanModel', ], 'RuleSetName' => [ 'shape' => 'MatchmakingRuleSetName', ], 'NotificationTarget' => [ 'shape' => 'SnsArnStringModel', ], 'AdditionalPlayerCount' => [ 'shape' => 'WholeNumber', ], 'CustomEventData' => [ 'shape' => 'CustomEventData', ], 'GameProperties' => [ 'shape' => 'GamePropertyList', ], 'GameSessionData' => [ 'shape' => 'GameSessionData', ], 'BackfillMode' => [ 'shape' => 'BackfillMode', ], 'FlexMatchMode' => [ 'shape' => 'FlexMatchMode', ], ], ], 'UpdateMatchmakingConfigurationOutput' => [ 'type' => 'structure', 'members' => [ 'Configuration' => [ 'shape' => 'MatchmakingConfiguration', ], ], ], 'UpdateRuntimeConfigurationInput' => [ 'type' => 'structure', 'required' => [ 'FleetId', 'RuntimeConfiguration', ], 'members' => [ 'FleetId' => [ 'shape' => 'FleetIdOrArn', ], 'RuntimeConfiguration' => [ 'shape' => 'RuntimeConfiguration', ], ], ], 'UpdateRuntimeConfigurationOutput' => [ 'type' => 'structure', 'members' => [ 'RuntimeConfiguration' => [ 'shape' => 'RuntimeConfiguration', ], ], ], 'UpdateScriptInput' => [ 'type' => 'structure', 'required' => [ 'ScriptId', ], 'members' => [ 'ScriptId' => [ 'shape' => 'ScriptIdOrArn', ], 'Name' => [ 'shape' => 'NonZeroAndMaxString', ], 'Version' => [ 'shape' => 'NonZeroAndMaxString', ], 'StorageLocation' => [ 'shape' => 'S3Location', ], 'ZipFile' => [ 'shape' => 'ZipBlob', ], ], ], 'UpdateScriptOutput' => [ 'type' => 'structure', 'members' => [ 'Script' => [ 'shape' => 'Script', ], ], ], 'ValidateMatchmakingRuleSetInput' => [ 'type' => 'structure', 'required' => [ 'RuleSetBody', ], 'members' => [ 'RuleSetBody' => [ 'shape' => 'RuleSetBody', ], ], ], 'ValidateMatchmakingRuleSetOutput' => [ 'type' => 'structure', 'members' => [ 'Valid' => [ 'shape' => 'BooleanModel', ], ], ], 'VpcPeeringAuthorization' => [ 'type' => 'structure', 'members' => [ 'GameLiftAwsAccountId' => [ 'shape' => 'NonZeroAndMaxString', ], 'PeerVpcAwsAccountId' => [ 'shape' => 'NonZeroAndMaxString', ], 'PeerVpcId' => [ 'shape' => 'NonZeroAndMaxString', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'ExpirationTime' => [ 'shape' => 'Timestamp', ], ], ], 'VpcPeeringAuthorizationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VpcPeeringAuthorization', ], ], 'VpcPeeringConnection' => [ 'type' => 'structure', 'members' => [ 'FleetId' => [ 'shape' => 'FleetId', ], 'FleetArn' => [ 'shape' => 'FleetArn', ], 'IpV4CidrBlock' => [ 'shape' => 'NonZeroAndMaxString', ], 'VpcPeeringConnectionId' => [ 'shape' => 'NonZeroAndMaxString', ], 'Status' => [ 'shape' => 'VpcPeeringConnectionStatus', ], 'PeerVpcId' => [ 'shape' => 'NonZeroAndMaxString', ], 'GameLiftVpcId' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'VpcPeeringConnectionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VpcPeeringConnection', ], ], 'VpcPeeringConnectionStatus' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'NonZeroAndMaxString', ], 'Message' => [ 'shape' => 'NonZeroAndMaxString', ], ], ], 'VpcSubnet' => [ 'type' => 'string', 'max' => 24, 'min' => 15, 'pattern' => '^subnet-[0-9a-z]+$', ], 'VpcSubnets' => [ 'type' => 'list', 'member' => [ 'shape' => 'VpcSubnet', ], 'max' => 20, 'min' => 1, ], 'WeightedCapacity' => [ 'type' => 'string', 'max' => 3, 'min' => 1, 'pattern' => '^[\\u0031-\\u0039][\\u0030-\\u0039]{0,2}$', ], 'WholeNumber' => [ 'type' => 'integer', 'min' => 0, ], 'ZipBlob' => [ 'type' => 'blob', 'max' => 5000000, ], ],];
