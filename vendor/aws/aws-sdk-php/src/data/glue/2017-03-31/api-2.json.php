<?php
// This file was auto-generated from sdk-root/src/data/glue/2017-03-31/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2017-03-31', 'endpointPrefix' => 'glue', 'jsonVersion' => '1.1', 'protocol' => 'json', 'protocols' => [ 'json', ], 'serviceFullName' => 'AWS Glue', 'serviceId' => 'Glue', 'signatureVersion' => 'v4', 'targetPrefix' => 'AWSGlue', 'uid' => 'glue-2017-03-31', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'BatchCreatePartition' => [ 'name' => 'BatchCreatePartition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchCreatePartitionRequest', ], 'output' => [ 'shape' => 'BatchCreatePartitionResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'BatchDeleteConnection' => [ 'name' => 'BatchDeleteConnection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchDeleteConnectionRequest', ], 'output' => [ 'shape' => 'BatchDeleteConnectionResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'BatchDeletePartition' => [ 'name' => 'BatchDeletePartition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchDeletePartitionRequest', ], 'output' => [ 'shape' => 'BatchDeletePartitionResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'BatchDeleteTable' => [ 'name' => 'BatchDeleteTable', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchDeleteTableRequest', ], 'output' => [ 'shape' => 'BatchDeleteTableResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], [ 'shape' => 'ResourceNotReadyException', ], ], ], 'BatchDeleteTableVersion' => [ 'name' => 'BatchDeleteTableVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchDeleteTableVersionRequest', ], 'output' => [ 'shape' => 'BatchDeleteTableVersionResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'BatchGetBlueprints' => [ 'name' => 'BatchGetBlueprints', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchGetBlueprintsRequest', ], 'output' => [ 'shape' => 'BatchGetBlueprintsResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'BatchGetCrawlers' => [ 'name' => 'BatchGetCrawlers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchGetCrawlersRequest', ], 'output' => [ 'shape' => 'BatchGetCrawlersResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'BatchGetCustomEntityTypes' => [ 'name' => 'BatchGetCustomEntityTypes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchGetCustomEntityTypesRequest', ], 'output' => [ 'shape' => 'BatchGetCustomEntityTypesResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'BatchGetDataQualityResult' => [ 'name' => 'BatchGetDataQualityResult', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchGetDataQualityResultRequest', ], 'output' => [ 'shape' => 'BatchGetDataQualityResultResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'BatchGetDevEndpoints' => [ 'name' => 'BatchGetDevEndpoints', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchGetDevEndpointsRequest', ], 'output' => [ 'shape' => 'BatchGetDevEndpointsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'BatchGetJobs' => [ 'name' => 'BatchGetJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchGetJobsRequest', ], 'output' => [ 'shape' => 'BatchGetJobsResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'BatchGetPartition' => [ 'name' => 'BatchGetPartition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchGetPartitionRequest', ], 'output' => [ 'shape' => 'BatchGetPartitionResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'GlueEncryptionException', ], [ 'shape' => 'InvalidStateException', ], [ 'shape' => 'FederationSourceException', ], [ 'shape' => 'FederationSourceRetryableException', ], ], ], 'BatchGetTableOptimizer' => [ 'name' => 'BatchGetTableOptimizer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchGetTableOptimizerRequest', ], 'output' => [ 'shape' => 'BatchGetTableOptimizerResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'BatchGetTriggers' => [ 'name' => 'BatchGetTriggers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchGetTriggersRequest', ], 'output' => [ 'shape' => 'BatchGetTriggersResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'BatchGetWorkflows' => [ 'name' => 'BatchGetWorkflows', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchGetWorkflowsRequest', ], 'output' => [ 'shape' => 'BatchGetWorkflowsResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'BatchPutDataQualityStatisticAnnotation' => [ 'name' => 'BatchPutDataQualityStatisticAnnotation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchPutDataQualityStatisticAnnotationRequest', ], 'output' => [ 'shape' => 'BatchPutDataQualityStatisticAnnotationResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], ], ], 'BatchStopJobRun' => [ 'name' => 'BatchStopJobRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchStopJobRunRequest', ], 'output' => [ 'shape' => 'BatchStopJobRunResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'BatchUpdatePartition' => [ 'name' => 'BatchUpdatePartition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchUpdatePartitionRequest', ], 'output' => [ 'shape' => 'BatchUpdatePartitionResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'CancelDataQualityRuleRecommendationRun' => [ 'name' => 'CancelDataQualityRuleRecommendationRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CancelDataQualityRuleRecommendationRunRequest', ], 'output' => [ 'shape' => 'CancelDataQualityRuleRecommendationRunResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'CancelDataQualityRulesetEvaluationRun' => [ 'name' => 'CancelDataQualityRulesetEvaluationRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CancelDataQualityRulesetEvaluationRunRequest', ], 'output' => [ 'shape' => 'CancelDataQualityRulesetEvaluationRunResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'CancelMLTaskRun' => [ 'name' => 'CancelMLTaskRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CancelMLTaskRunRequest', ], 'output' => [ 'shape' => 'CancelMLTaskRunResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'CancelStatement' => [ 'name' => 'CancelStatement', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CancelStatementRequest', ], 'output' => [ 'shape' => 'CancelStatementResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'IllegalSessionStateException', ], ], ], 'CheckSchemaVersionValidity' => [ 'name' => 'CheckSchemaVersionValidity', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CheckSchemaVersionValidityInput', ], 'output' => [ 'shape' => 'CheckSchemaVersionValidityResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'CreateBlueprint' => [ 'name' => 'CreateBlueprint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateBlueprintRequest', ], 'output' => [ 'shape' => 'CreateBlueprintResponse', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], ], ], 'CreateCatalog' => [ 'name' => 'CreateCatalog', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateCatalogRequest', ], 'output' => [ 'shape' => 'CreateCatalogResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'FederatedResourceAlreadyExistsException', ], [ 'shape' => 'FederationSourceException', ], ], ], 'CreateClassifier' => [ 'name' => 'CreateClassifier', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateClassifierRequest', ], 'output' => [ 'shape' => 'CreateClassifierResponse', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'CreateColumnStatisticsTaskSettings' => [ 'name' => 'CreateColumnStatisticsTaskSettings', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateColumnStatisticsTaskSettingsRequest', ], 'output' => [ 'shape' => 'CreateColumnStatisticsTaskSettingsResponse', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'ColumnStatisticsTaskRunningException', ], ], ], 'CreateConnection' => [ 'name' => 'CreateConnection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateConnectionRequest', ], 'output' => [ 'shape' => 'CreateConnectionResponse', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'CreateCrawler' => [ 'name' => 'CreateCrawler', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateCrawlerRequest', ], 'output' => [ 'shape' => 'CreateCrawlerResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], ], ], 'CreateCustomEntityType' => [ 'name' => 'CreateCustomEntityType', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateCustomEntityTypeRequest', ], 'output' => [ 'shape' => 'CreateCustomEntityTypeResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], ], ], 'CreateDataQualityRuleset' => [ 'name' => 'CreateDataQualityRuleset', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDataQualityRulesetRequest', ], 'output' => [ 'shape' => 'CreateDataQualityRulesetResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], ], 'idempotent' => true, ], 'CreateDatabase' => [ 'name' => 'CreateDatabase', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDatabaseRequest', ], 'output' => [ 'shape' => 'CreateDatabaseResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'FederatedResourceAlreadyExistsException', ], [ 'shape' => 'FederationSourceException', ], [ 'shape' => 'FederationSourceRetryableException', ], ], ], 'CreateDevEndpoint' => [ 'name' => 'CreateDevEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDevEndpointRequest', ], 'output' => [ 'shape' => 'CreateDevEndpointResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], ], ], 'CreateIntegration' => [ 'name' => 'CreateIntegration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateIntegrationRequest', ], 'output' => [ 'shape' => 'CreateIntegrationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'IntegrationConflictOperationFault', ], [ 'shape' => 'IntegrationQuotaExceededFault', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'CreateIntegrationResourceProperty' => [ 'name' => 'CreateIntegrationResourceProperty', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateIntegrationResourcePropertyRequest', ], 'output' => [ 'shape' => 'CreateIntegrationResourcePropertyResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'CreateIntegrationTableProperties' => [ 'name' => 'CreateIntegrationTableProperties', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateIntegrationTablePropertiesRequest', ], 'output' => [ 'shape' => 'CreateIntegrationTablePropertiesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'CreateJob' => [ 'name' => 'CreateJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateJobRequest', ], 'output' => [ 'shape' => 'CreateJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'CreateMLTransform' => [ 'name' => 'CreateMLTransform', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateMLTransformRequest', ], 'output' => [ 'shape' => 'CreateMLTransformResponse', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], ], ], 'CreatePartition' => [ 'name' => 'CreatePartition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreatePartitionRequest', ], 'output' => [ 'shape' => 'CreatePartitionResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'CreatePartitionIndex' => [ 'name' => 'CreatePartitionIndex', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreatePartitionIndexRequest', ], 'output' => [ 'shape' => 'CreatePartitionIndexResponse', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'CreateRegistry' => [ 'name' => 'CreateRegistry', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateRegistryInput', ], 'output' => [ 'shape' => 'CreateRegistryResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'CreateSchema' => [ 'name' => 'CreateSchema', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateSchemaInput', ], 'output' => [ 'shape' => 'CreateSchemaResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'CreateScript' => [ 'name' => 'CreateScript', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateScriptRequest', ], 'output' => [ 'shape' => 'CreateScriptResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'CreateSecurityConfiguration' => [ 'name' => 'CreateSecurityConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateSecurityConfigurationRequest', ], 'output' => [ 'shape' => 'CreateSecurityConfigurationResponse', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], ], ], 'CreateSession' => [ 'name' => 'CreateSession', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateSessionRequest', ], 'output' => [ 'shape' => 'CreateSessionResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], ], ], 'CreateTable' => [ 'name' => 'CreateTable', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateTableRequest', ], 'output' => [ 'shape' => 'CreateTableResponse', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ResourceNotReadyException', ], [ 'shape' => 'FederationSourceException', ], [ 'shape' => 'FederationSourceRetryableException', ], ], ], 'CreateTableOptimizer' => [ 'name' => 'CreateTableOptimizer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateTableOptimizerRequest', ], 'output' => [ 'shape' => 'CreateTableOptimizerResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateTrigger' => [ 'name' => 'CreateTrigger', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateTriggerRequest', ], 'output' => [ 'shape' => 'CreateTriggerResponse', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'CreateUsageProfile' => [ 'name' => 'CreateUsageProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateUsageProfileRequest', ], 'output' => [ 'shape' => 'CreateUsageProfileResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'OperationNotSupportedException', ], ], ], 'CreateUserDefinedFunction' => [ 'name' => 'CreateUserDefinedFunction', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateUserDefinedFunctionRequest', ], 'output' => [ 'shape' => 'CreateUserDefinedFunctionResponse', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'CreateWorkflow' => [ 'name' => 'CreateWorkflow', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateWorkflowRequest', ], 'output' => [ 'shape' => 'CreateWorkflowResponse', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'DeleteBlueprint' => [ 'name' => 'DeleteBlueprint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteBlueprintRequest', ], 'output' => [ 'shape' => 'DeleteBlueprintResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DeleteCatalog' => [ 'name' => 'DeleteCatalog', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteCatalogRequest', ], 'output' => [ 'shape' => 'DeleteCatalogResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'FederationSourceException', ], ], ], 'DeleteClassifier' => [ 'name' => 'DeleteClassifier', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteClassifierRequest', ], 'output' => [ 'shape' => 'DeleteClassifierResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'DeleteColumnStatisticsForPartition' => [ 'name' => 'DeleteColumnStatisticsForPartition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteColumnStatisticsForPartitionRequest', ], 'output' => [ 'shape' => 'DeleteColumnStatisticsForPartitionResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'DeleteColumnStatisticsForTable' => [ 'name' => 'DeleteColumnStatisticsForTable', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteColumnStatisticsForTableRequest', ], 'output' => [ 'shape' => 'DeleteColumnStatisticsForTableResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'DeleteColumnStatisticsTaskSettings' => [ 'name' => 'DeleteColumnStatisticsTaskSettings', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteColumnStatisticsTaskSettingsRequest', ], 'output' => [ 'shape' => 'DeleteColumnStatisticsTaskSettingsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'DeleteConnection' => [ 'name' => 'DeleteConnection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteConnectionRequest', ], 'output' => [ 'shape' => 'DeleteConnectionResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'DeleteCrawler' => [ 'name' => 'DeleteCrawler', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteCrawlerRequest', ], 'output' => [ 'shape' => 'DeleteCrawlerResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'CrawlerRunningException', ], [ 'shape' => 'SchedulerTransitioningException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'DeleteCustomEntityType' => [ 'name' => 'DeleteCustomEntityType', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteCustomEntityTypeRequest', ], 'output' => [ 'shape' => 'DeleteCustomEntityTypeResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'DeleteDataQualityRuleset' => [ 'name' => 'DeleteDataQualityRuleset', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDataQualityRulesetRequest', ], 'output' => [ 'shape' => 'DeleteDataQualityRulesetResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DeleteDatabase' => [ 'name' => 'DeleteDatabase', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDatabaseRequest', ], 'output' => [ 'shape' => 'DeleteDatabaseResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'FederationSourceException', ], [ 'shape' => 'FederationSourceRetryableException', ], ], ], 'DeleteDevEndpoint' => [ 'name' => 'DeleteDevEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDevEndpointRequest', ], 'output' => [ 'shape' => 'DeleteDevEndpointResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'DeleteIntegration' => [ 'name' => 'DeleteIntegration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteIntegrationRequest', ], 'output' => [ 'shape' => 'DeleteIntegrationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'IntegrationNotFoundFault', ], [ 'shape' => 'IntegrationConflictOperationFault', ], [ 'shape' => 'InvalidIntegrationStateFault', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InvalidStateException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'DeleteIntegrationTableProperties' => [ 'name' => 'DeleteIntegrationTableProperties', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteIntegrationTablePropertiesRequest', ], 'output' => [ 'shape' => 'DeleteIntegrationTablePropertiesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'DeleteJob' => [ 'name' => 'DeleteJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteJobRequest', ], 'output' => [ 'shape' => 'DeleteJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'DeleteMLTransform' => [ 'name' => 'DeleteMLTransform', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteMLTransformRequest', ], 'output' => [ 'shape' => 'DeleteMLTransformResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DeletePartition' => [ 'name' => 'DeletePartition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeletePartitionRequest', ], 'output' => [ 'shape' => 'DeletePartitionResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'DeletePartitionIndex' => [ 'name' => 'DeletePartitionIndex', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeletePartitionIndexRequest', ], 'output' => [ 'shape' => 'DeletePartitionIndexResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'DeleteRegistry' => [ 'name' => 'DeleteRegistry', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteRegistryInput', ], 'output' => [ 'shape' => 'DeleteRegistryResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'DeleteResourcePolicy' => [ 'name' => 'DeleteResourcePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteResourcePolicyRequest', ], 'output' => [ 'shape' => 'DeleteResourcePolicyResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ConditionCheckFailureException', ], ], ], 'DeleteSchema' => [ 'name' => 'DeleteSchema', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteSchemaInput', ], 'output' => [ 'shape' => 'DeleteSchemaResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'DeleteSchemaVersions' => [ 'name' => 'DeleteSchemaVersions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteSchemaVersionsInput', ], 'output' => [ 'shape' => 'DeleteSchemaVersionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'DeleteSecurityConfiguration' => [ 'name' => 'DeleteSecurityConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteSecurityConfigurationRequest', ], 'output' => [ 'shape' => 'DeleteSecurityConfigurationResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'DeleteSession' => [ 'name' => 'DeleteSession', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteSessionRequest', ], 'output' => [ 'shape' => 'DeleteSessionResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'IllegalSessionStateException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'DeleteTable' => [ 'name' => 'DeleteTable', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteTableRequest', ], 'output' => [ 'shape' => 'DeleteTableResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ResourceNotReadyException', ], [ 'shape' => 'FederationSourceException', ], [ 'shape' => 'FederationSourceRetryableException', ], ], ], 'DeleteTableOptimizer' => [ 'name' => 'DeleteTableOptimizer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteTableOptimizerRequest', ], 'output' => [ 'shape' => 'DeleteTableOptimizerResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeleteTableVersion' => [ 'name' => 'DeleteTableVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteTableVersionRequest', ], 'output' => [ 'shape' => 'DeleteTableVersionResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'DeleteTrigger' => [ 'name' => 'DeleteTrigger', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteTriggerRequest', ], 'output' => [ 'shape' => 'DeleteTriggerResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'DeleteUsageProfile' => [ 'name' => 'DeleteUsageProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteUsageProfileRequest', ], 'output' => [ 'shape' => 'DeleteUsageProfileResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'OperationNotSupportedException', ], ], ], 'DeleteUserDefinedFunction' => [ 'name' => 'DeleteUserDefinedFunction', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteUserDefinedFunctionRequest', ], 'output' => [ 'shape' => 'DeleteUserDefinedFunctionResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'DeleteWorkflow' => [ 'name' => 'DeleteWorkflow', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteWorkflowRequest', ], 'output' => [ 'shape' => 'DeleteWorkflowResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'DescribeConnectionType' => [ 'name' => 'DescribeConnectionType', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeConnectionTypeRequest', ], 'output' => [ 'shape' => 'DescribeConnectionTypeResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DescribeEntity' => [ 'name' => 'DescribeEntity', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEntityRequest', ], 'output' => [ 'shape' => 'DescribeEntityResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'GlueEncryptionException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'FederationSourceException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DescribeInboundIntegrations' => [ 'name' => 'DescribeInboundIntegrations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeInboundIntegrationsRequest', ], 'output' => [ 'shape' => 'DescribeInboundIntegrationsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'IntegrationNotFoundFault', ], [ 'shape' => 'TargetResourceNotFound', ], [ 'shape' => 'OperationNotSupportedException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'DescribeIntegrations' => [ 'name' => 'DescribeIntegrations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeIntegrationsRequest', ], 'output' => [ 'shape' => 'DescribeIntegrationsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'IntegrationNotFoundFault', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'GetBlueprint' => [ 'name' => 'GetBlueprint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetBlueprintRequest', ], 'output' => [ 'shape' => 'GetBlueprintResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'GetBlueprintRun' => [ 'name' => 'GetBlueprintRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetBlueprintRunRequest', ], 'output' => [ 'shape' => 'GetBlueprintRunResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetBlueprintRuns' => [ 'name' => 'GetBlueprintRuns', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetBlueprintRunsRequest', ], 'output' => [ 'shape' => 'GetBlueprintRunsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'GetCatalog' => [ 'name' => 'GetCatalog', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetCatalogRequest', ], 'output' => [ 'shape' => 'GetCatalogResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'FederationSourceException', ], [ 'shape' => 'FederationSourceRetryableException', ], ], ], 'GetCatalogImportStatus' => [ 'name' => 'GetCatalogImportStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetCatalogImportStatusRequest', ], 'output' => [ 'shape' => 'GetCatalogImportStatusResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetCatalogs' => [ 'name' => 'GetCatalogs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetCatalogsRequest', ], 'output' => [ 'shape' => 'GetCatalogsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'FederationSourceException', ], [ 'shape' => 'FederationSourceRetryableException', ], ], ], 'GetClassifier' => [ 'name' => 'GetClassifier', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetClassifierRequest', ], 'output' => [ 'shape' => 'GetClassifierResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetClassifiers' => [ 'name' => 'GetClassifiers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetClassifiersRequest', ], 'output' => [ 'shape' => 'GetClassifiersResponse', ], 'errors' => [ [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetColumnStatisticsForPartition' => [ 'name' => 'GetColumnStatisticsForPartition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetColumnStatisticsForPartitionRequest', ], 'output' => [ 'shape' => 'GetColumnStatisticsForPartitionResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'GetColumnStatisticsForTable' => [ 'name' => 'GetColumnStatisticsForTable', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetColumnStatisticsForTableRequest', ], 'output' => [ 'shape' => 'GetColumnStatisticsForTableResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'GetColumnStatisticsTaskRun' => [ 'name' => 'GetColumnStatisticsTaskRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetColumnStatisticsTaskRunRequest', ], 'output' => [ 'shape' => 'GetColumnStatisticsTaskRunResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'GetColumnStatisticsTaskRuns' => [ 'name' => 'GetColumnStatisticsTaskRuns', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetColumnStatisticsTaskRunsRequest', ], 'output' => [ 'shape' => 'GetColumnStatisticsTaskRunsResponse', ], 'errors' => [ [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetColumnStatisticsTaskSettings' => [ 'name' => 'GetColumnStatisticsTaskSettings', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetColumnStatisticsTaskSettingsRequest', ], 'output' => [ 'shape' => 'GetColumnStatisticsTaskSettingsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetConnection' => [ 'name' => 'GetConnection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetConnectionRequest', ], 'output' => [ 'shape' => 'GetConnectionResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'GetConnections' => [ 'name' => 'GetConnections', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetConnectionsRequest', ], 'output' => [ 'shape' => 'GetConnectionsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'GetCrawler' => [ 'name' => 'GetCrawler', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetCrawlerRequest', ], 'output' => [ 'shape' => 'GetCrawlerResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetCrawlerMetrics' => [ 'name' => 'GetCrawlerMetrics', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetCrawlerMetricsRequest', ], 'output' => [ 'shape' => 'GetCrawlerMetricsResponse', ], 'errors' => [ [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetCrawlers' => [ 'name' => 'GetCrawlers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetCrawlersRequest', ], 'output' => [ 'shape' => 'GetCrawlersResponse', ], 'errors' => [ [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetCustomEntityType' => [ 'name' => 'GetCustomEntityType', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetCustomEntityTypeRequest', ], 'output' => [ 'shape' => 'GetCustomEntityTypeResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetDataCatalogEncryptionSettings' => [ 'name' => 'GetDataCatalogEncryptionSettings', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDataCatalogEncryptionSettingsRequest', ], 'output' => [ 'shape' => 'GetDataCatalogEncryptionSettingsResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetDataQualityModel' => [ 'name' => 'GetDataQualityModel', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDataQualityModelRequest', ], 'output' => [ 'shape' => 'GetDataQualityModelResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'GetDataQualityModelResult' => [ 'name' => 'GetDataQualityModelResult', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDataQualityModelResultRequest', ], 'output' => [ 'shape' => 'GetDataQualityModelResultResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'GetDataQualityResult' => [ 'name' => 'GetDataQualityResult', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDataQualityResultRequest', ], 'output' => [ 'shape' => 'GetDataQualityResultResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'EntityNotFoundException', ], ], ], 'GetDataQualityRuleRecommendationRun' => [ 'name' => 'GetDataQualityRuleRecommendationRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDataQualityRuleRecommendationRunRequest', ], 'output' => [ 'shape' => 'GetDataQualityRuleRecommendationRunResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'GetDataQualityRuleset' => [ 'name' => 'GetDataQualityRuleset', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDataQualityRulesetRequest', ], 'output' => [ 'shape' => 'GetDataQualityRulesetResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'GetDataQualityRulesetEvaluationRun' => [ 'name' => 'GetDataQualityRulesetEvaluationRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDataQualityRulesetEvaluationRunRequest', ], 'output' => [ 'shape' => 'GetDataQualityRulesetEvaluationRunResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'GetDatabase' => [ 'name' => 'GetDatabase', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDatabaseRequest', ], 'output' => [ 'shape' => 'GetDatabaseResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], [ 'shape' => 'FederationSourceException', ], [ 'shape' => 'FederationSourceRetryableException', ], ], ], 'GetDatabases' => [ 'name' => 'GetDatabases', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDatabasesRequest', ], 'output' => [ 'shape' => 'GetDatabasesResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'FederationSourceException', ], [ 'shape' => 'FederationSourceRetryableException', ], ], ], 'GetDataflowGraph' => [ 'name' => 'GetDataflowGraph', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDataflowGraphRequest', ], 'output' => [ 'shape' => 'GetDataflowGraphResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetDevEndpoint' => [ 'name' => 'GetDevEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDevEndpointRequest', ], 'output' => [ 'shape' => 'GetDevEndpointResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'GetDevEndpoints' => [ 'name' => 'GetDevEndpoints', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDevEndpointsRequest', ], 'output' => [ 'shape' => 'GetDevEndpointsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'GetEntityRecords' => [ 'name' => 'GetEntityRecords', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetEntityRecordsRequest', ], 'output' => [ 'shape' => 'GetEntityRecordsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'GlueEncryptionException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'FederationSourceException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetIntegrationResourceProperty' => [ 'name' => 'GetIntegrationResourceProperty', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetIntegrationResourcePropertyRequest', ], 'output' => [ 'shape' => 'GetIntegrationResourcePropertyResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'GetIntegrationTableProperties' => [ 'name' => 'GetIntegrationTableProperties', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetIntegrationTablePropertiesRequest', ], 'output' => [ 'shape' => 'GetIntegrationTablePropertiesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'GetJob' => [ 'name' => 'GetJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetJobRequest', ], 'output' => [ 'shape' => 'GetJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetJobBookmark' => [ 'name' => 'GetJobBookmark', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetJobBookmarkRequest', ], 'output' => [ 'shape' => 'GetJobBookmarkResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetJobRun' => [ 'name' => 'GetJobRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetJobRunRequest', ], 'output' => [ 'shape' => 'GetJobRunResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetJobRuns' => [ 'name' => 'GetJobRuns', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetJobRunsRequest', ], 'output' => [ 'shape' => 'GetJobRunsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetJobs' => [ 'name' => 'GetJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetJobsRequest', ], 'output' => [ 'shape' => 'GetJobsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetMLTaskRun' => [ 'name' => 'GetMLTaskRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetMLTaskRunRequest', ], 'output' => [ 'shape' => 'GetMLTaskRunResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'GetMLTaskRuns' => [ 'name' => 'GetMLTaskRuns', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetMLTaskRunsRequest', ], 'output' => [ 'shape' => 'GetMLTaskRunsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'GetMLTransform' => [ 'name' => 'GetMLTransform', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetMLTransformRequest', ], 'output' => [ 'shape' => 'GetMLTransformResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'GetMLTransforms' => [ 'name' => 'GetMLTransforms', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetMLTransformsRequest', ], 'output' => [ 'shape' => 'GetMLTransformsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'GetMapping' => [ 'name' => 'GetMapping', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetMappingRequest', ], 'output' => [ 'shape' => 'GetMappingResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'EntityNotFoundException', ], ], ], 'GetPartition' => [ 'name' => 'GetPartition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetPartitionRequest', ], 'output' => [ 'shape' => 'GetPartitionResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], [ 'shape' => 'FederationSourceException', ], [ 'shape' => 'FederationSourceRetryableException', ], ], ], 'GetPartitionIndexes' => [ 'name' => 'GetPartitionIndexes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetPartitionIndexesRequest', ], 'output' => [ 'shape' => 'GetPartitionIndexesResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetPartitions' => [ 'name' => 'GetPartitions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetPartitionsRequest', ], 'output' => [ 'shape' => 'GetPartitionsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'GlueEncryptionException', ], [ 'shape' => 'InvalidStateException', ], [ 'shape' => 'ResourceNotReadyException', ], [ 'shape' => 'FederationSourceException', ], [ 'shape' => 'FederationSourceRetryableException', ], ], ], 'GetPlan' => [ 'name' => 'GetPlan', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetPlanRequest', ], 'output' => [ 'shape' => 'GetPlanResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetRegistry' => [ 'name' => 'GetRegistry', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRegistryInput', ], 'output' => [ 'shape' => 'GetRegistryResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'GetResourcePolicies' => [ 'name' => 'GetResourcePolicies', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetResourcePoliciesRequest', ], 'output' => [ 'shape' => 'GetResourcePoliciesResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'GetResourcePolicy' => [ 'name' => 'GetResourcePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetResourcePolicyRequest', ], 'output' => [ 'shape' => 'GetResourcePolicyResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'GetSchema' => [ 'name' => 'GetSchema', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetSchemaInput', ], 'output' => [ 'shape' => 'GetSchemaResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'GetSchemaByDefinition' => [ 'name' => 'GetSchemaByDefinition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetSchemaByDefinitionInput', ], 'output' => [ 'shape' => 'GetSchemaByDefinitionResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'GetSchemaVersion' => [ 'name' => 'GetSchemaVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetSchemaVersionInput', ], 'output' => [ 'shape' => 'GetSchemaVersionResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'GetSchemaVersionsDiff' => [ 'name' => 'GetSchemaVersionsDiff', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetSchemaVersionsDiffInput', ], 'output' => [ 'shape' => 'GetSchemaVersionsDiffResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'GetSecurityConfiguration' => [ 'name' => 'GetSecurityConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetSecurityConfigurationRequest', ], 'output' => [ 'shape' => 'GetSecurityConfigurationResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetSecurityConfigurations' => [ 'name' => 'GetSecurityConfigurations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetSecurityConfigurationsRequest', ], 'output' => [ 'shape' => 'GetSecurityConfigurationsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetSession' => [ 'name' => 'GetSession', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetSessionRequest', ], 'output' => [ 'shape' => 'GetSessionResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'GetStatement' => [ 'name' => 'GetStatement', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetStatementRequest', ], 'output' => [ 'shape' => 'GetStatementResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'IllegalSessionStateException', ], ], ], 'GetTable' => [ 'name' => 'GetTable', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetTableRequest', ], 'output' => [ 'shape' => 'GetTableResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], [ 'shape' => 'ResourceNotReadyException', ], [ 'shape' => 'FederationSourceException', ], [ 'shape' => 'FederationSourceRetryableException', ], ], ], 'GetTableOptimizer' => [ 'name' => 'GetTableOptimizer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetTableOptimizerRequest', ], 'output' => [ 'shape' => 'GetTableOptimizerResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetTableVersion' => [ 'name' => 'GetTableVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetTableVersionRequest', ], 'output' => [ 'shape' => 'GetTableVersionResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'GetTableVersions' => [ 'name' => 'GetTableVersions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetTableVersionsRequest', ], 'output' => [ 'shape' => 'GetTableVersionsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'GetTables' => [ 'name' => 'GetTables', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetTablesRequest', ], 'output' => [ 'shape' => 'GetTablesResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'GlueEncryptionException', ], [ 'shape' => 'FederationSourceException', ], [ 'shape' => 'FederationSourceRetryableException', ], ], ], 'GetTags' => [ 'name' => 'GetTags', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetTagsRequest', ], 'output' => [ 'shape' => 'GetTagsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'EntityNotFoundException', ], ], ], 'GetTrigger' => [ 'name' => 'GetTrigger', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetTriggerRequest', ], 'output' => [ 'shape' => 'GetTriggerResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetTriggers' => [ 'name' => 'GetTriggers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetTriggersRequest', ], 'output' => [ 'shape' => 'GetTriggersResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetUnfilteredPartitionMetadata' => [ 'name' => 'GetUnfilteredPartitionMetadata', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetUnfilteredPartitionMetadataRequest', ], 'output' => [ 'shape' => 'GetUnfilteredPartitionMetadataResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], [ 'shape' => 'PermissionTypeMismatchException', ], [ 'shape' => 'FederationSourceException', ], [ 'shape' => 'FederationSourceRetryableException', ], ], ], 'GetUnfilteredPartitionsMetadata' => [ 'name' => 'GetUnfilteredPartitionsMetadata', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetUnfilteredPartitionsMetadataRequest', ], 'output' => [ 'shape' => 'GetUnfilteredPartitionsMetadataResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], [ 'shape' => 'PermissionTypeMismatchException', ], [ 'shape' => 'FederationSourceException', ], [ 'shape' => 'FederationSourceRetryableException', ], ], ], 'GetUnfilteredTableMetadata' => [ 'name' => 'GetUnfilteredTableMetadata', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetUnfilteredTableMetadataRequest', ], 'output' => [ 'shape' => 'GetUnfilteredTableMetadataResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], [ 'shape' => 'PermissionTypeMismatchException', ], [ 'shape' => 'FederationSourceException', ], [ 'shape' => 'FederationSourceRetryableException', ], ], ], 'GetUsageProfile' => [ 'name' => 'GetUsageProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetUsageProfileRequest', ], 'output' => [ 'shape' => 'GetUsageProfileResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'OperationNotSupportedException', ], ], ], 'GetUserDefinedFunction' => [ 'name' => 'GetUserDefinedFunction', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetUserDefinedFunctionRequest', ], 'output' => [ 'shape' => 'GetUserDefinedFunctionResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'GetUserDefinedFunctions' => [ 'name' => 'GetUserDefinedFunctions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetUserDefinedFunctionsRequest', ], 'output' => [ 'shape' => 'GetUserDefinedFunctionsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'GetWorkflow' => [ 'name' => 'GetWorkflow', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetWorkflowRequest', ], 'output' => [ 'shape' => 'GetWorkflowResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetWorkflowRun' => [ 'name' => 'GetWorkflowRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetWorkflowRunRequest', ], 'output' => [ 'shape' => 'GetWorkflowRunResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetWorkflowRunProperties' => [ 'name' => 'GetWorkflowRunProperties', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetWorkflowRunPropertiesRequest', ], 'output' => [ 'shape' => 'GetWorkflowRunPropertiesResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetWorkflowRuns' => [ 'name' => 'GetWorkflowRuns', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetWorkflowRunsRequest', ], 'output' => [ 'shape' => 'GetWorkflowRunsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'ImportCatalogToGlue' => [ 'name' => 'ImportCatalogToGlue', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ImportCatalogToGlueRequest', ], 'output' => [ 'shape' => 'ImportCatalogToGlueResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'ListBlueprints' => [ 'name' => 'ListBlueprints', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListBlueprintsRequest', ], 'output' => [ 'shape' => 'ListBlueprintsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'ListColumnStatisticsTaskRuns' => [ 'name' => 'ListColumnStatisticsTaskRuns', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListColumnStatisticsTaskRunsRequest', ], 'output' => [ 'shape' => 'ListColumnStatisticsTaskRunsResponse', ], 'errors' => [ [ 'shape' => 'OperationTimeoutException', ], ], ], 'ListConnectionTypes' => [ 'name' => 'ListConnectionTypes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListConnectionTypesRequest', ], 'output' => [ 'shape' => 'ListConnectionTypesResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListCrawlers' => [ 'name' => 'ListCrawlers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListCrawlersRequest', ], 'output' => [ 'shape' => 'ListCrawlersResponse', ], 'errors' => [ [ 'shape' => 'OperationTimeoutException', ], ], ], 'ListCrawls' => [ 'name' => 'ListCrawls', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListCrawlsRequest', ], 'output' => [ 'shape' => 'ListCrawlsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'ListCustomEntityTypes' => [ 'name' => 'ListCustomEntityTypes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListCustomEntityTypesRequest', ], 'output' => [ 'shape' => 'ListCustomEntityTypesResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListDataQualityResults' => [ 'name' => 'ListDataQualityResults', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDataQualityResultsRequest', ], 'output' => [ 'shape' => 'ListDataQualityResultsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListDataQualityRuleRecommendationRuns' => [ 'name' => 'ListDataQualityRuleRecommendationRuns', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDataQualityRuleRecommendationRunsRequest', ], 'output' => [ 'shape' => 'ListDataQualityRuleRecommendationRunsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListDataQualityRulesetEvaluationRuns' => [ 'name' => 'ListDataQualityRulesetEvaluationRuns', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDataQualityRulesetEvaluationRunsRequest', ], 'output' => [ 'shape' => 'ListDataQualityRulesetEvaluationRunsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListDataQualityRulesets' => [ 'name' => 'ListDataQualityRulesets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDataQualityRulesetsRequest', ], 'output' => [ 'shape' => 'ListDataQualityRulesetsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListDataQualityStatisticAnnotations' => [ 'name' => 'ListDataQualityStatisticAnnotations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDataQualityStatisticAnnotationsRequest', ], 'output' => [ 'shape' => 'ListDataQualityStatisticAnnotationsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListDataQualityStatistics' => [ 'name' => 'ListDataQualityStatistics', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDataQualityStatisticsRequest', ], 'output' => [ 'shape' => 'ListDataQualityStatisticsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListDevEndpoints' => [ 'name' => 'ListDevEndpoints', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDevEndpointsRequest', ], 'output' => [ 'shape' => 'ListDevEndpointsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'ListEntities' => [ 'name' => 'ListEntities', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListEntitiesRequest', ], 'output' => [ 'shape' => 'ListEntitiesResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'GlueEncryptionException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'FederationSourceException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListJobs' => [ 'name' => 'ListJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListJobsRequest', ], 'output' => [ 'shape' => 'ListJobsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'ListMLTransforms' => [ 'name' => 'ListMLTransforms', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListMLTransformsRequest', ], 'output' => [ 'shape' => 'ListMLTransformsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListRegistries' => [ 'name' => 'ListRegistries', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListRegistriesInput', ], 'output' => [ 'shape' => 'ListRegistriesResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListSchemaVersions' => [ 'name' => 'ListSchemaVersions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListSchemaVersionsInput', ], 'output' => [ 'shape' => 'ListSchemaVersionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListSchemas' => [ 'name' => 'ListSchemas', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListSchemasInput', ], 'output' => [ 'shape' => 'ListSchemasResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListSessions' => [ 'name' => 'ListSessions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListSessionsRequest', ], 'output' => [ 'shape' => 'ListSessionsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'ListStatements' => [ 'name' => 'ListStatements', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListStatementsRequest', ], 'output' => [ 'shape' => 'ListStatementsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'IllegalSessionStateException', ], ], ], 'ListTableOptimizerRuns' => [ 'name' => 'ListTableOptimizerRuns', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTableOptimizerRunsRequest', ], 'output' => [ 'shape' => 'ListTableOptimizerRunsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListTriggers' => [ 'name' => 'ListTriggers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTriggersRequest', ], 'output' => [ 'shape' => 'ListTriggersResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'ListUsageProfiles' => [ 'name' => 'ListUsageProfiles', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListUsageProfilesRequest', ], 'output' => [ 'shape' => 'ListUsageProfilesResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationNotSupportedException', ], ], ], 'ListWorkflows' => [ 'name' => 'ListWorkflows', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListWorkflowsRequest', ], 'output' => [ 'shape' => 'ListWorkflowsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'ModifyIntegration' => [ 'name' => 'ModifyIntegration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyIntegrationRequest', ], 'output' => [ 'shape' => 'ModifyIntegrationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'IntegrationNotFoundFault', ], [ 'shape' => 'IntegrationConflictOperationFault', ], [ 'shape' => 'InvalidIntegrationStateFault', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InvalidStateException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'PutDataCatalogEncryptionSettings' => [ 'name' => 'PutDataCatalogEncryptionSettings', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutDataCatalogEncryptionSettingsRequest', ], 'output' => [ 'shape' => 'PutDataCatalogEncryptionSettingsResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'PutDataQualityProfileAnnotation' => [ 'name' => 'PutDataQualityProfileAnnotation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutDataQualityProfileAnnotationRequest', ], 'output' => [ 'shape' => 'PutDataQualityProfileAnnotationResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'PutResourcePolicy' => [ 'name' => 'PutResourcePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutResourcePolicyRequest', ], 'output' => [ 'shape' => 'PutResourcePolicyResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ConditionCheckFailureException', ], ], ], 'PutSchemaVersionMetadata' => [ 'name' => 'PutSchemaVersionMetadata', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutSchemaVersionMetadataInput', ], 'output' => [ 'shape' => 'PutSchemaVersionMetadataResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], ], ], 'PutWorkflowRunProperties' => [ 'name' => 'PutWorkflowRunProperties', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutWorkflowRunPropertiesRequest', ], 'output' => [ 'shape' => 'PutWorkflowRunPropertiesResponse', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'QuerySchemaVersionMetadata' => [ 'name' => 'QuerySchemaVersionMetadata', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'QuerySchemaVersionMetadataInput', ], 'output' => [ 'shape' => 'QuerySchemaVersionMetadataResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'EntityNotFoundException', ], ], ], 'RegisterSchemaVersion' => [ 'name' => 'RegisterSchemaVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RegisterSchemaVersionInput', ], 'output' => [ 'shape' => 'RegisterSchemaVersionResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'RemoveSchemaVersionMetadata' => [ 'name' => 'RemoveSchemaVersionMetadata', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RemoveSchemaVersionMetadataInput', ], 'output' => [ 'shape' => 'RemoveSchemaVersionMetadataResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'EntityNotFoundException', ], ], ], 'ResetJobBookmark' => [ 'name' => 'ResetJobBookmark', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ResetJobBookmarkRequest', ], 'output' => [ 'shape' => 'ResetJobBookmarkResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'ResumeWorkflowRun' => [ 'name' => 'ResumeWorkflowRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ResumeWorkflowRunRequest', ], 'output' => [ 'shape' => 'ResumeWorkflowRunResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ConcurrentRunsExceededException', ], [ 'shape' => 'IllegalWorkflowStateException', ], ], ], 'RunStatement' => [ 'name' => 'RunStatement', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RunStatementRequest', ], 'output' => [ 'shape' => 'RunStatementResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'IllegalSessionStateException', ], ], ], 'SearchTables' => [ 'name' => 'SearchTables', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SearchTablesRequest', ], 'output' => [ 'shape' => 'SearchTablesResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'StartBlueprintRun' => [ 'name' => 'StartBlueprintRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartBlueprintRunRequest', ], 'output' => [ 'shape' => 'StartBlueprintRunResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'IllegalBlueprintStateException', ], ], ], 'StartColumnStatisticsTaskRun' => [ 'name' => 'StartColumnStatisticsTaskRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartColumnStatisticsTaskRunRequest', ], 'output' => [ 'shape' => 'StartColumnStatisticsTaskRunResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'ColumnStatisticsTaskRunningException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'StartColumnStatisticsTaskRunSchedule' => [ 'name' => 'StartColumnStatisticsTaskRunSchedule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartColumnStatisticsTaskRunScheduleRequest', ], 'output' => [ 'shape' => 'StartColumnStatisticsTaskRunScheduleResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'StartCrawler' => [ 'name' => 'StartCrawler', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartCrawlerRequest', ], 'output' => [ 'shape' => 'StartCrawlerResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'CrawlerRunningException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'StartCrawlerSchedule' => [ 'name' => 'StartCrawlerSchedule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartCrawlerScheduleRequest', ], 'output' => [ 'shape' => 'StartCrawlerScheduleResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'SchedulerRunningException', ], [ 'shape' => 'SchedulerTransitioningException', ], [ 'shape' => 'NoScheduleException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'StartDataQualityRuleRecommendationRun' => [ 'name' => 'StartDataQualityRuleRecommendationRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartDataQualityRuleRecommendationRunRequest', ], 'output' => [ 'shape' => 'StartDataQualityRuleRecommendationRunResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'StartDataQualityRulesetEvaluationRun' => [ 'name' => 'StartDataQualityRulesetEvaluationRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartDataQualityRulesetEvaluationRunRequest', ], 'output' => [ 'shape' => 'StartDataQualityRulesetEvaluationRunResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'StartExportLabelsTaskRun' => [ 'name' => 'StartExportLabelsTaskRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartExportLabelsTaskRunRequest', ], 'output' => [ 'shape' => 'StartExportLabelsTaskRunResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'StartImportLabelsTaskRun' => [ 'name' => 'StartImportLabelsTaskRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartImportLabelsTaskRunRequest', ], 'output' => [ 'shape' => 'StartImportLabelsTaskRunResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'StartJobRun' => [ 'name' => 'StartJobRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartJobRunRequest', ], 'output' => [ 'shape' => 'StartJobRunResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'ConcurrentRunsExceededException', ], ], ], 'StartMLEvaluationTaskRun' => [ 'name' => 'StartMLEvaluationTaskRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartMLEvaluationTaskRunRequest', ], 'output' => [ 'shape' => 'StartMLEvaluationTaskRunResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ConcurrentRunsExceededException', ], [ 'shape' => 'MLTransformNotReadyException', ], ], ], 'StartMLLabelingSetGenerationTaskRun' => [ 'name' => 'StartMLLabelingSetGenerationTaskRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartMLLabelingSetGenerationTaskRunRequest', ], 'output' => [ 'shape' => 'StartMLLabelingSetGenerationTaskRunResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ConcurrentRunsExceededException', ], ], ], 'StartTrigger' => [ 'name' => 'StartTrigger', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartTriggerRequest', ], 'output' => [ 'shape' => 'StartTriggerResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'ConcurrentRunsExceededException', ], ], ], 'StartWorkflowRun' => [ 'name' => 'StartWorkflowRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartWorkflowRunRequest', ], 'output' => [ 'shape' => 'StartWorkflowRunResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'ConcurrentRunsExceededException', ], ], ], 'StopColumnStatisticsTaskRun' => [ 'name' => 'StopColumnStatisticsTaskRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopColumnStatisticsTaskRunRequest', ], 'output' => [ 'shape' => 'StopColumnStatisticsTaskRunResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'ColumnStatisticsTaskNotRunningException', ], [ 'shape' => 'ColumnStatisticsTaskStoppingException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'StopColumnStatisticsTaskRunSchedule' => [ 'name' => 'StopColumnStatisticsTaskRunSchedule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopColumnStatisticsTaskRunScheduleRequest', ], 'output' => [ 'shape' => 'StopColumnStatisticsTaskRunScheduleResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'StopCrawler' => [ 'name' => 'StopCrawler', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopCrawlerRequest', ], 'output' => [ 'shape' => 'StopCrawlerResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'CrawlerNotRunningException', ], [ 'shape' => 'CrawlerStoppingException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'StopCrawlerSchedule' => [ 'name' => 'StopCrawlerSchedule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopCrawlerScheduleRequest', ], 'output' => [ 'shape' => 'StopCrawlerScheduleResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'SchedulerNotRunningException', ], [ 'shape' => 'SchedulerTransitioningException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'StopSession' => [ 'name' => 'StopSession', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopSessionRequest', ], 'output' => [ 'shape' => 'StopSessionResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'IllegalSessionStateException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'StopTrigger' => [ 'name' => 'StopTrigger', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopTriggerRequest', ], 'output' => [ 'shape' => 'StopTriggerResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'StopWorkflowRun' => [ 'name' => 'StopWorkflowRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopWorkflowRunRequest', ], 'output' => [ 'shape' => 'StopWorkflowRunResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'IllegalWorkflowStateException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'EntityNotFoundException', ], ], ], 'TestConnection' => [ 'name' => 'TestConnection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TestConnectionRequest', ], 'output' => [ 'shape' => 'TestConnectionResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'GlueEncryptionException', ], [ 'shape' => 'FederationSourceException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'EntityNotFoundException', ], ], ], 'UpdateBlueprint' => [ 'name' => 'UpdateBlueprint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateBlueprintRequest', ], 'output' => [ 'shape' => 'UpdateBlueprintResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'IllegalBlueprintStateException', ], ], ], 'UpdateCatalog' => [ 'name' => 'UpdateCatalog', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateCatalogRequest', ], 'output' => [ 'shape' => 'UpdateCatalogResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'FederationSourceException', ], ], ], 'UpdateClassifier' => [ 'name' => 'UpdateClassifier', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateClassifierRequest', ], 'output' => [ 'shape' => 'UpdateClassifierResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'VersionMismatchException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'UpdateColumnStatisticsForPartition' => [ 'name' => 'UpdateColumnStatisticsForPartition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateColumnStatisticsForPartitionRequest', ], 'output' => [ 'shape' => 'UpdateColumnStatisticsForPartitionResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'UpdateColumnStatisticsForTable' => [ 'name' => 'UpdateColumnStatisticsForTable', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateColumnStatisticsForTableRequest', ], 'output' => [ 'shape' => 'UpdateColumnStatisticsForTableResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'UpdateColumnStatisticsTaskSettings' => [ 'name' => 'UpdateColumnStatisticsTaskSettings', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateColumnStatisticsTaskSettingsRequest', ], 'output' => [ 'shape' => 'UpdateColumnStatisticsTaskSettingsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'VersionMismatchException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'UpdateConnection' => [ 'name' => 'UpdateConnection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateConnectionRequest', ], 'output' => [ 'shape' => 'UpdateConnectionResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'UpdateCrawler' => [ 'name' => 'UpdateCrawler', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateCrawlerRequest', ], 'output' => [ 'shape' => 'UpdateCrawlerResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'VersionMismatchException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'CrawlerRunningException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'UpdateCrawlerSchedule' => [ 'name' => 'UpdateCrawlerSchedule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateCrawlerScheduleRequest', ], 'output' => [ 'shape' => 'UpdateCrawlerScheduleResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'VersionMismatchException', ], [ 'shape' => 'SchedulerTransitioningException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'UpdateDataQualityRuleset' => [ 'name' => 'UpdateDataQualityRuleset', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateDataQualityRulesetRequest', ], 'output' => [ 'shape' => 'UpdateDataQualityRulesetResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], ], ], 'UpdateDatabase' => [ 'name' => 'UpdateDatabase', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateDatabaseRequest', ], 'output' => [ 'shape' => 'UpdateDatabaseResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'FederationSourceException', ], [ 'shape' => 'FederationSourceRetryableException', ], [ 'shape' => 'AlreadyExistsException', ], ], ], 'UpdateDevEndpoint' => [ 'name' => 'UpdateDevEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateDevEndpointRequest', ], 'output' => [ 'shape' => 'UpdateDevEndpointResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ValidationException', ], ], ], 'UpdateIntegrationResourceProperty' => [ 'name' => 'UpdateIntegrationResourceProperty', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateIntegrationResourcePropertyRequest', ], 'output' => [ 'shape' => 'UpdateIntegrationResourcePropertyResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'UpdateIntegrationTableProperties' => [ 'name' => 'UpdateIntegrationTableProperties', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateIntegrationTablePropertiesRequest', ], 'output' => [ 'shape' => 'UpdateIntegrationTablePropertiesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'UpdateJob' => [ 'name' => 'UpdateJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateJobRequest', ], 'output' => [ 'shape' => 'UpdateJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'UpdateJobFromSourceControl' => [ 'name' => 'UpdateJobFromSourceControl', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateJobFromSourceControlRequest', ], 'output' => [ 'shape' => 'UpdateJobFromSourceControlResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'UpdateMLTransform' => [ 'name' => 'UpdateMLTransform', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateMLTransformRequest', ], 'output' => [ 'shape' => 'UpdateMLTransformResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdatePartition' => [ 'name' => 'UpdatePartition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdatePartitionRequest', ], 'output' => [ 'shape' => 'UpdatePartitionResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'UpdateRegistry' => [ 'name' => 'UpdateRegistry', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateRegistryInput', ], 'output' => [ 'shape' => 'UpdateRegistryResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateSchema' => [ 'name' => 'UpdateSchema', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateSchemaInput', ], 'output' => [ 'shape' => 'UpdateSchemaResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateSourceControlFromJob' => [ 'name' => 'UpdateSourceControlFromJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateSourceControlFromJobRequest', ], 'output' => [ 'shape' => 'UpdateSourceControlFromJobResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'UpdateTable' => [ 'name' => 'UpdateTable', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateTableRequest', ], 'output' => [ 'shape' => 'UpdateTableResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'GlueEncryptionException', ], [ 'shape' => 'ResourceNotReadyException', ], [ 'shape' => 'FederationSourceException', ], [ 'shape' => 'FederationSourceRetryableException', ], [ 'shape' => 'AlreadyExistsException', ], ], ], 'UpdateTableOptimizer' => [ 'name' => 'UpdateTableOptimizer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateTableOptimizerRequest', ], 'output' => [ 'shape' => 'UpdateTableOptimizerResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'UpdateTrigger' => [ 'name' => 'UpdateTrigger', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateTriggerRequest', ], 'output' => [ 'shape' => 'UpdateTriggerResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'UpdateUsageProfile' => [ 'name' => 'UpdateUsageProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateUsageProfileRequest', ], 'output' => [ 'shape' => 'UpdateUsageProfileResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'OperationNotSupportedException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'UpdateUserDefinedFunction' => [ 'name' => 'UpdateUserDefinedFunction', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateUserDefinedFunctionRequest', ], 'output' => [ 'shape' => 'UpdateUserDefinedFunctionResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'UpdateWorkflow' => [ 'name' => 'UpdateWorkflow', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateWorkflowRequest', ], 'output' => [ 'shape' => 'UpdateWorkflowResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], ], 'shapes' => [ 'AWSManagedClientApplicationReference' => [ 'type' => 'string', 'max' => 2048, 'pattern' => '\\S+', ], 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'AccessToken' => [ 'type' => 'string', 'max' => 4096, 'pattern' => '^[\\x20-\\x7E]*$', 'sensitive' => true, ], 'AccountId' => [ 'type' => 'string', 'max' => 12, 'min' => 0, ], 'Action' => [ 'type' => 'structure', 'members' => [ 'JobName' => [ 'shape' => 'NameString', ], 'Arguments' => [ 'shape' => 'GenericMap', ], 'Timeout' => [ 'shape' => 'Timeout', ], 'SecurityConfiguration' => [ 'shape' => 'NameString', ], 'NotificationProperty' => [ 'shape' => 'NotificationProperty', ], 'CrawlerName' => [ 'shape' => 'NameString', ], ], ], 'ActionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Action', ], ], 'AdditionalContextMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ContextKey', ], 'value' => [ 'shape' => 'ContextValue', ], ], 'AdditionalOptionKeys' => [ 'type' => 'string', 'enum' => [ 'performanceTuning.caching', 'observations.scope', ], ], 'AdditionalOptions' => [ 'type' => 'map', 'key' => [ 'shape' => 'EnclosedInStringProperty', ], 'value' => [ 'shape' => 'EnclosedInStringProperty', ], ], 'AdditionalPlanOptionsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'GenericString', ], 'value' => [ 'shape' => 'GenericString', ], ], 'AggFunction' => [ 'type' => 'string', 'enum' => [ 'avg', 'countDistinct', 'count', 'first', 'last', 'kurtosis', 'max', 'min', 'skewness', 'stddev_samp', 'stddev_pop', 'sum', 'sumDistinct', 'var_samp', 'var_pop', ], ], 'Aggregate' => [ 'type' => 'structure', 'required' => [ 'Name', 'Inputs', 'Groups', 'Aggs', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Inputs' => [ 'shape' => 'OneInput', ], 'Groups' => [ 'shape' => 'GlueStudioPathList', ], 'Aggs' => [ 'shape' => 'AggregateOperations', ], ], ], 'AggregateOperation' => [ 'type' => 'structure', 'required' => [ 'Column', 'AggFunc', ], 'members' => [ 'Column' => [ 'shape' => 'EnclosedInStringProperties', ], 'AggFunc' => [ 'shape' => 'AggFunction', ], ], ], 'AggregateOperations' => [ 'type' => 'list', 'member' => [ 'shape' => 'AggregateOperation', ], 'max' => 30, 'min' => 1, ], 'AllowFullTableExternalDataAccessEnum' => [ 'type' => 'string', 'enum' => [ 'True', 'False', ], ], 'AllowedValue' => [ 'type' => 'structure', 'required' => [ 'Value', ], 'members' => [ 'Description' => [ 'shape' => 'AllowedValueDescriptionString', ], 'Value' => [ 'shape' => 'AllowedValueValueString', ], ], ], 'AllowedValueDescriptionString' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'AllowedValueValueString' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'AllowedValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'AllowedValue', ], ], 'AllowedValuesStringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfigValueString', ], ], 'AlreadyExistsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'AmazonRedshiftAdvancedOption' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'GenericString', ], 'Value' => [ 'shape' => 'GenericString', ], ], ], 'AmazonRedshiftAdvancedOptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'AmazonRedshiftAdvancedOption', ], ], 'AmazonRedshiftNodeData' => [ 'type' => 'structure', 'members' => [ 'AccessType' => [ 'shape' => 'GenericLimitedString', ], 'SourceType' => [ 'shape' => 'GenericLimitedString', ], 'Connection' => [ 'shape' => 'Option', ], 'Schema' => [ 'shape' => 'Option', ], 'Table' => [ 'shape' => 'Option', ], 'CatalogDatabase' => [ 'shape' => 'Option', ], 'CatalogTable' => [ 'shape' => 'Option', ], 'CatalogRedshiftSchema' => [ 'shape' => 'GenericString', ], 'CatalogRedshiftTable' => [ 'shape' => 'GenericString', ], 'TempDir' => [ 'shape' => 'EnclosedInStringProperty', ], 'IamRole' => [ 'shape' => 'Option', ], 'AdvancedOptions' => [ 'shape' => 'AmazonRedshiftAdvancedOptions', ], 'SampleQuery' => [ 'shape' => 'GenericString', ], 'PreAction' => [ 'shape' => 'GenericString', ], 'PostAction' => [ 'shape' => 'GenericString', ], 'Action' => [ 'shape' => 'GenericString', ], 'TablePrefix' => [ 'shape' => 'GenericLimitedString', ], 'Upsert' => [ 'shape' => 'BooleanValue', ], 'MergeAction' => [ 'shape' => 'GenericLimitedString', ], 'MergeWhenMatched' => [ 'shape' => 'GenericLimitedString', ], 'MergeWhenNotMatched' => [ 'shape' => 'GenericLimitedString', ], 'MergeClause' => [ 'shape' => 'GenericString', ], 'CrawlerConnection' => [ 'shape' => 'GenericString', ], 'TableSchema' => [ 'shape' => 'OptionList', ], 'StagingTable' => [ 'shape' => 'GenericString', ], 'SelectedColumns' => [ 'shape' => 'OptionList', ], ], ], 'AmazonRedshiftSource' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Data' => [ 'shape' => 'AmazonRedshiftNodeData', ], ], ], 'AmazonRedshiftTarget' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Data' => [ 'shape' => 'AmazonRedshiftNodeData', ], 'Inputs' => [ 'shape' => 'OneInput', ], ], ], 'AnnotationError' => [ 'type' => 'structure', 'members' => [ 'ProfileId' => [ 'shape' => 'HashString', ], 'StatisticId' => [ 'shape' => 'HashString', ], 'FailureReason' => [ 'shape' => 'DescriptionString', ], ], ], 'AnnotationErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnnotationError', ], ], 'AnnotationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StatisticAnnotation', ], ], 'ApiVersion' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[a-zA-Z0-9.-]*', ], 'ApplyMapping' => [ 'type' => 'structure', 'required' => [ 'Name', 'Inputs', 'Mapping', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Inputs' => [ 'shape' => 'OneInput', ], 'Mapping' => [ 'shape' => 'Mappings', ], ], ], 'ArnString' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, ], 'AthenaConnectorSource' => [ 'type' => 'structure', 'required' => [ 'Name', 'ConnectionName', 'ConnectorName', 'ConnectionType', 'SchemaName', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'ConnectionName' => [ 'shape' => 'EnclosedInStringProperty', ], 'ConnectorName' => [ 'shape' => 'EnclosedInStringProperty', ], 'ConnectionType' => [ 'shape' => 'EnclosedInStringProperty', ], 'ConnectionTable' => [ 'shape' => 'EnclosedInStringPropertyWithQuote', ], 'SchemaName' => [ 'shape' => 'EnclosedInStringProperty', ], 'OutputSchemas' => [ 'shape' => 'GlueSchemas', ], ], ], 'AttemptCount' => [ 'type' => 'integer', ], 'AuditColumnNamesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColumnNameString', ], ], 'AuditContext' => [ 'type' => 'structure', 'members' => [ 'AdditionalAuditContext' => [ 'shape' => 'AuditContextString', ], 'RequestedColumns' => [ 'shape' => 'AuditColumnNamesList', ], 'AllColumnsRequested' => [ 'shape' => 'NullableBoolean', ], ], ], 'AuditContextString' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, ], 'AuthConfiguration' => [ 'type' => 'structure', 'required' => [ 'AuthenticationType', ], 'members' => [ 'AuthenticationType' => [ 'shape' => 'Property', ], 'SecretArn' => [ 'shape' => 'Property', ], 'OAuth2Properties' => [ 'shape' => 'PropertiesMap', ], 'BasicAuthenticationProperties' => [ 'shape' => 'PropertiesMap', ], 'CustomAuthenticationProperties' => [ 'shape' => 'PropertiesMap', ], ], ], 'AuthTokenString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'AuthenticationConfiguration' => [ 'type' => 'structure', 'members' => [ 'AuthenticationType' => [ 'shape' => 'AuthenticationType', ], 'SecretArn' => [ 'shape' => 'SecretArn', ], 'OAuth2Properties' => [ 'shape' => 'OAuth2Properties', ], ], ], 'AuthenticationConfigurationInput' => [ 'type' => 'structure', 'members' => [ 'AuthenticationType' => [ 'shape' => 'AuthenticationType', ], 'OAuth2Properties' => [ 'shape' => 'OAuth2PropertiesInput', ], 'SecretArn' => [ 'shape' => 'SecretArn', ], 'KmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'BasicAuthenticationCredentials' => [ 'shape' => 'BasicAuthenticationCredentials', ], 'CustomAuthenticationCredentials' => [ 'shape' => 'CredentialMap', ], ], ], 'AuthenticationType' => [ 'type' => 'string', 'enum' => [ 'BASIC', 'OAUTH2', 'CUSTOM', 'IAM', ], ], 'AuthenticationTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'AuthenticationType', ], ], 'AuthorizationCode' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, 'pattern' => '\\S+', 'sensitive' => true, ], 'AuthorizationCodeProperties' => [ 'type' => 'structure', 'members' => [ 'AuthorizationCode' => [ 'shape' => 'AuthorizationCode', ], 'RedirectUri' => [ 'shape' => 'RedirectUri', ], ], ], 'BackfillError' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'BackfillErrorCode', ], 'Partitions' => [ 'shape' => 'BackfillErroredPartitionsList', ], ], ], 'BackfillErrorCode' => [ 'type' => 'string', 'enum' => [ 'ENCRYPTED_PARTITION_ERROR', 'INTERNAL_ERROR', 'INVALID_PARTITION_TYPE_DATA_ERROR', 'MISSING_PARTITION_VALUE_ERROR', 'UNSUPPORTED_PARTITION_CHARACTER_ERROR', ], ], 'BackfillErroredPartitionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PartitionValueList', ], ], 'BackfillErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'BackfillError', ], ], 'BasicAuthenticationCredentials' => [ 'type' => 'structure', 'members' => [ 'Username' => [ 'shape' => 'Username', ], 'Password' => [ 'shape' => 'Password', ], ], ], 'BasicCatalogTarget' => [ 'type' => 'structure', 'required' => [ 'Name', 'Inputs', 'Database', 'Table', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Inputs' => [ 'shape' => 'OneInput', ], 'PartitionKeys' => [ 'shape' => 'GlueStudioPathList', ], 'Database' => [ 'shape' => 'EnclosedInStringProperty', ], 'Table' => [ 'shape' => 'EnclosedInStringProperty', ], ], ], 'BatchCreatePartitionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'PartitionInputList', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'PartitionInputList' => [ 'shape' => 'PartitionInputList', ], ], ], 'BatchCreatePartitionResponse' => [ 'type' => 'structure', 'members' => [ 'Errors' => [ 'shape' => 'PartitionErrors', ], ], ], 'BatchDeleteConnectionRequest' => [ 'type' => 'structure', 'required' => [ 'ConnectionNameList', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'ConnectionNameList' => [ 'shape' => 'DeleteConnectionNameList', ], ], ], 'BatchDeleteConnectionResponse' => [ 'type' => 'structure', 'members' => [ 'Succeeded' => [ 'shape' => 'NameStringList', ], 'Errors' => [ 'shape' => 'ErrorByName', ], ], ], 'BatchDeletePartitionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'PartitionsToDelete', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'PartitionsToDelete' => [ 'shape' => 'BatchDeletePartitionValueList', ], ], ], 'BatchDeletePartitionResponse' => [ 'type' => 'structure', 'members' => [ 'Errors' => [ 'shape' => 'PartitionErrors', ], ], ], 'BatchDeletePartitionValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PartitionValueList', ], 'max' => 25, 'min' => 0, ], 'BatchDeleteTableNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], 'max' => 100, 'min' => 0, ], 'BatchDeleteTableRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TablesToDelete', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TablesToDelete' => [ 'shape' => 'BatchDeleteTableNameList', ], 'TransactionId' => [ 'shape' => 'TransactionIdString', ], ], ], 'BatchDeleteTableResponse' => [ 'type' => 'structure', 'members' => [ 'Errors' => [ 'shape' => 'TableErrors', ], ], ], 'BatchDeleteTableVersionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VersionString', ], 'max' => 100, 'min' => 0, ], 'BatchDeleteTableVersionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'VersionIds', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'VersionIds' => [ 'shape' => 'BatchDeleteTableVersionList', ], ], ], 'BatchDeleteTableVersionResponse' => [ 'type' => 'structure', 'members' => [ 'Errors' => [ 'shape' => 'TableVersionErrors', ], ], ], 'BatchGetBlueprintNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'OrchestrationNameString', ], 'max' => 25, 'min' => 1, ], 'BatchGetBlueprintsRequest' => [ 'type' => 'structure', 'required' => [ 'Names', ], 'members' => [ 'Names' => [ 'shape' => 'BatchGetBlueprintNames', ], 'IncludeBlueprint' => [ 'shape' => 'NullableBoolean', ], 'IncludeParameterSpec' => [ 'shape' => 'NullableBoolean', ], ], ], 'BatchGetBlueprintsResponse' => [ 'type' => 'structure', 'members' => [ 'Blueprints' => [ 'shape' => 'Blueprints', ], 'MissingBlueprints' => [ 'shape' => 'BlueprintNames', ], ], ], 'BatchGetCrawlersRequest' => [ 'type' => 'structure', 'required' => [ 'CrawlerNames', ], 'members' => [ 'CrawlerNames' => [ 'shape' => 'CrawlerNameList', ], ], ], 'BatchGetCrawlersResponse' => [ 'type' => 'structure', 'members' => [ 'Crawlers' => [ 'shape' => 'CrawlerList', ], 'CrawlersNotFound' => [ 'shape' => 'CrawlerNameList', ], ], ], 'BatchGetCustomEntityTypesRequest' => [ 'type' => 'structure', 'required' => [ 'Names', ], 'members' => [ 'Names' => [ 'shape' => 'CustomEntityTypeNames', ], ], ], 'BatchGetCustomEntityTypesResponse' => [ 'type' => 'structure', 'members' => [ 'CustomEntityTypes' => [ 'shape' => 'CustomEntityTypes', ], 'CustomEntityTypesNotFound' => [ 'shape' => 'CustomEntityTypeNames', ], ], ], 'BatchGetDataQualityResultRequest' => [ 'type' => 'structure', 'required' => [ 'ResultIds', ], 'members' => [ 'ResultIds' => [ 'shape' => 'DataQualityResultIds', ], ], ], 'BatchGetDataQualityResultResponse' => [ 'type' => 'structure', 'required' => [ 'Results', ], 'members' => [ 'Results' => [ 'shape' => 'DataQualityResultsList', ], 'ResultsNotFound' => [ 'shape' => 'DataQualityResultIds', ], ], ], 'BatchGetDevEndpointsRequest' => [ 'type' => 'structure', 'required' => [ 'DevEndpointNames', ], 'members' => [ 'DevEndpointNames' => [ 'shape' => 'DevEndpointNames', ], ], ], 'BatchGetDevEndpointsResponse' => [ 'type' => 'structure', 'members' => [ 'DevEndpoints' => [ 'shape' => 'DevEndpointList', ], 'DevEndpointsNotFound' => [ 'shape' => 'DevEndpointNames', ], ], ], 'BatchGetJobsRequest' => [ 'type' => 'structure', 'required' => [ 'JobNames', ], 'members' => [ 'JobNames' => [ 'shape' => 'JobNameList', ], ], ], 'BatchGetJobsResponse' => [ 'type' => 'structure', 'members' => [ 'Jobs' => [ 'shape' => 'JobList', ], 'JobsNotFound' => [ 'shape' => 'JobNameList', ], ], ], 'BatchGetPartitionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'PartitionsToGet', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'PartitionsToGet' => [ 'shape' => 'BatchGetPartitionValueList', ], ], ], 'BatchGetPartitionResponse' => [ 'type' => 'structure', 'members' => [ 'Partitions' => [ 'shape' => 'PartitionList', ], 'UnprocessedKeys' => [ 'shape' => 'BatchGetPartitionValueList', ], ], ], 'BatchGetPartitionValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PartitionValueList', ], 'max' => 1000, 'min' => 0, ], 'BatchGetTableOptimizerEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchGetTableOptimizerEntry', ], ], 'BatchGetTableOptimizerEntry' => [ 'type' => 'structure', 'members' => [ 'catalogId' => [ 'shape' => 'CatalogIdString', ], 'databaseName' => [ 'shape' => 'databaseNameString', ], 'tableName' => [ 'shape' => 'tableNameString', ], 'type' => [ 'shape' => 'TableOptimizerType', ], ], ], 'BatchGetTableOptimizerError' => [ 'type' => 'structure', 'members' => [ 'error' => [ 'shape' => 'ErrorDetail', ], 'catalogId' => [ 'shape' => 'CatalogIdString', ], 'databaseName' => [ 'shape' => 'databaseNameString', ], 'tableName' => [ 'shape' => 'tableNameString', ], 'type' => [ 'shape' => 'TableOptimizerType', ], ], ], 'BatchGetTableOptimizerErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchGetTableOptimizerError', ], ], 'BatchGetTableOptimizerRequest' => [ 'type' => 'structure', 'required' => [ 'Entries', ], 'members' => [ 'Entries' => [ 'shape' => 'BatchGetTableOptimizerEntries', ], ], ], 'BatchGetTableOptimizerResponse' => [ 'type' => 'structure', 'members' => [ 'TableOptimizers' => [ 'shape' => 'BatchTableOptimizers', ], 'Failures' => [ 'shape' => 'BatchGetTableOptimizerErrors', ], ], ], 'BatchGetTriggersRequest' => [ 'type' => 'structure', 'required' => [ 'TriggerNames', ], 'members' => [ 'TriggerNames' => [ 'shape' => 'TriggerNameList', ], ], ], 'BatchGetTriggersResponse' => [ 'type' => 'structure', 'members' => [ 'Triggers' => [ 'shape' => 'TriggerList', ], 'TriggersNotFound' => [ 'shape' => 'TriggerNameList', ], ], ], 'BatchGetWorkflowsRequest' => [ 'type' => 'structure', 'required' => [ 'Names', ], 'members' => [ 'Names' => [ 'shape' => 'WorkflowNames', ], 'IncludeGraph' => [ 'shape' => 'NullableBoolean', ], ], ], 'BatchGetWorkflowsResponse' => [ 'type' => 'structure', 'members' => [ 'Workflows' => [ 'shape' => 'Workflows', ], 'MissingWorkflows' => [ 'shape' => 'WorkflowNames', ], ], ], 'BatchPutDataQualityStatisticAnnotationRequest' => [ 'type' => 'structure', 'required' => [ 'InclusionAnnotations', ], 'members' => [ 'InclusionAnnotations' => [ 'shape' => 'InclusionAnnotationList', ], 'ClientToken' => [ 'shape' => 'HashString', ], ], ], 'BatchPutDataQualityStatisticAnnotationResponse' => [ 'type' => 'structure', 'members' => [ 'FailedInclusionAnnotations' => [ 'shape' => 'AnnotationErrorList', ], ], ], 'BatchSize' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'BatchStopJobRunError' => [ 'type' => 'structure', 'members' => [ 'JobName' => [ 'shape' => 'NameString', ], 'JobRunId' => [ 'shape' => 'IdString', ], 'ErrorDetail' => [ 'shape' => 'ErrorDetail', ], ], ], 'BatchStopJobRunErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchStopJobRunError', ], ], 'BatchStopJobRunJobRunIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IdString', ], 'max' => 25, 'min' => 1, ], 'BatchStopJobRunRequest' => [ 'type' => 'structure', 'required' => [ 'JobName', 'JobRunIds', ], 'members' => [ 'JobName' => [ 'shape' => 'NameString', ], 'JobRunIds' => [ 'shape' => 'BatchStopJobRunJobRunIdList', ], ], ], 'BatchStopJobRunResponse' => [ 'type' => 'structure', 'members' => [ 'SuccessfulSubmissions' => [ 'shape' => 'BatchStopJobRunSuccessfulSubmissionList', ], 'Errors' => [ 'shape' => 'BatchStopJobRunErrorList', ], ], ], 'BatchStopJobRunSuccessfulSubmission' => [ 'type' => 'structure', 'members' => [ 'JobName' => [ 'shape' => 'NameString', ], 'JobRunId' => [ 'shape' => 'IdString', ], ], ], 'BatchStopJobRunSuccessfulSubmissionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchStopJobRunSuccessfulSubmission', ], ], 'BatchTableOptimizer' => [ 'type' => 'structure', 'members' => [ 'catalogId' => [ 'shape' => 'CatalogIdString', ], 'databaseName' => [ 'shape' => 'databaseNameString', ], 'tableName' => [ 'shape' => 'tableNameString', ], 'tableOptimizer' => [ 'shape' => 'TableOptimizer', ], ], ], 'BatchTableOptimizers' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchTableOptimizer', ], ], 'BatchUpdatePartitionFailureEntry' => [ 'type' => 'structure', 'members' => [ 'PartitionValueList' => [ 'shape' => 'BoundedPartitionValueList', ], 'ErrorDetail' => [ 'shape' => 'ErrorDetail', ], ], ], 'BatchUpdatePartitionFailureList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchUpdatePartitionFailureEntry', ], ], 'BatchUpdatePartitionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'Entries', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'Entries' => [ 'shape' => 'BatchUpdatePartitionRequestEntryList', ], ], ], 'BatchUpdatePartitionRequestEntry' => [ 'type' => 'structure', 'required' => [ 'PartitionValueList', 'PartitionInput', ], 'members' => [ 'PartitionValueList' => [ 'shape' => 'BoundedPartitionValueList', ], 'PartitionInput' => [ 'shape' => 'PartitionInput', ], ], ], 'BatchUpdatePartitionRequestEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchUpdatePartitionRequestEntry', ], 'max' => 100, 'min' => 1, ], 'BatchUpdatePartitionResponse' => [ 'type' => 'structure', 'members' => [ 'Errors' => [ 'shape' => 'BatchUpdatePartitionFailureList', ], ], ], 'BatchWindow' => [ 'type' => 'integer', 'box' => true, 'max' => 900, 'min' => 1, ], 'BinaryColumnStatisticsData' => [ 'type' => 'structure', 'required' => [ 'MaximumLength', 'AverageLength', 'NumberOfNulls', ], 'members' => [ 'MaximumLength' => [ 'shape' => 'NonNegativeLong', ], 'AverageLength' => [ 'shape' => 'NonNegativeDouble', ], 'NumberOfNulls' => [ 'shape' => 'NonNegativeLong', ], ], ], 'Blob' => [ 'type' => 'blob', ], 'Blueprint' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'OrchestrationNameString', ], 'Description' => [ 'shape' => 'Generic512CharString', ], 'CreatedOn' => [ 'shape' => 'TimestampValue', ], 'LastModifiedOn' => [ 'shape' => 'TimestampValue', ], 'ParameterSpec' => [ 'shape' => 'BlueprintParameterSpec', ], 'BlueprintLocation' => [ 'shape' => 'GenericString', ], 'BlueprintServiceLocation' => [ 'shape' => 'GenericString', ], 'Status' => [ 'shape' => 'BlueprintStatus', ], 'ErrorMessage' => [ 'shape' => 'ErrorString', ], 'LastActiveDefinition' => [ 'shape' => 'LastActiveDefinition', ], ], ], 'BlueprintDetails' => [ 'type' => 'structure', 'members' => [ 'BlueprintName' => [ 'shape' => 'OrchestrationNameString', ], 'RunId' => [ 'shape' => 'IdString', ], ], ], 'BlueprintNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'OrchestrationNameString', ], ], 'BlueprintParameterSpec' => [ 'type' => 'string', 'max' => 131072, 'min' => 1, ], 'BlueprintParameters' => [ 'type' => 'string', 'max' => 131072, 'min' => 1, ], 'BlueprintRun' => [ 'type' => 'structure', 'members' => [ 'BlueprintName' => [ 'shape' => 'OrchestrationNameString', ], 'RunId' => [ 'shape' => 'IdString', ], 'WorkflowName' => [ 'shape' => 'NameString', ], 'State' => [ 'shape' => 'BlueprintRunState', ], 'StartedOn' => [ 'shape' => 'TimestampValue', ], 'CompletedOn' => [ 'shape' => 'TimestampValue', ], 'ErrorMessage' => [ 'shape' => 'MessageString', ], 'RollbackErrorMessage' => [ 'shape' => 'MessageString', ], 'Parameters' => [ 'shape' => 'BlueprintParameters', ], 'RoleArn' => [ 'shape' => 'OrchestrationIAMRoleArn', ], ], ], 'BlueprintRunState' => [ 'type' => 'string', 'enum' => [ 'RUNNING', 'SUCCEEDED', 'FAILED', 'ROLLING_BACK', ], ], 'BlueprintRuns' => [ 'type' => 'list', 'member' => [ 'shape' => 'BlueprintRun', ], ], 'BlueprintStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'UPDATING', 'FAILED', ], ], 'Blueprints' => [ 'type' => 'list', 'member' => [ 'shape' => 'Blueprint', ], ], 'Bool' => [ 'type' => 'boolean', 'box' => true, ], 'Boolean' => [ 'type' => 'boolean', ], 'BooleanColumnStatisticsData' => [ 'type' => 'structure', 'required' => [ 'NumberOfTrues', 'NumberOfFalses', 'NumberOfNulls', ], 'members' => [ 'NumberOfTrues' => [ 'shape' => 'NonNegativeLong', ], 'NumberOfFalses' => [ 'shape' => 'NonNegativeLong', ], 'NumberOfNulls' => [ 'shape' => 'NonNegativeLong', ], ], ], 'BooleanNullable' => [ 'type' => 'boolean', ], 'BooleanValue' => [ 'type' => 'boolean', ], 'BoundedPartitionValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValueString', ], 'max' => 100, 'min' => 0, ], 'BoxedBoolean' => [ 'type' => 'boolean', 'box' => true, ], 'BoxedDoubleFraction' => [ 'type' => 'double', 'box' => true, 'max' => 1, 'min' => 0, ], 'BoxedLong' => [ 'type' => 'long', 'box' => true, ], 'BoxedNonNegativeInt' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'BoxedNonNegativeLong' => [ 'type' => 'long', 'box' => true, 'min' => 0, ], 'BoxedPositiveInt' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'CancelDataQualityRuleRecommendationRunRequest' => [ 'type' => 'structure', 'required' => [ 'RunId', ], 'members' => [ 'RunId' => [ 'shape' => 'HashString', ], ], ], 'CancelDataQualityRuleRecommendationRunResponse' => [ 'type' => 'structure', 'members' => [], ], 'CancelDataQualityRulesetEvaluationRunRequest' => [ 'type' => 'structure', 'required' => [ 'RunId', ], 'members' => [ 'RunId' => [ 'shape' => 'HashString', ], ], ], 'CancelDataQualityRulesetEvaluationRunResponse' => [ 'type' => 'structure', 'members' => [], ], 'CancelMLTaskRunRequest' => [ 'type' => 'structure', 'required' => [ 'TransformId', 'TaskRunId', ], 'members' => [ 'TransformId' => [ 'shape' => 'HashString', ], 'TaskRunId' => [ 'shape' => 'HashString', ], ], ], 'CancelMLTaskRunResponse' => [ 'type' => 'structure', 'members' => [ 'TransformId' => [ 'shape' => 'HashString', ], 'TaskRunId' => [ 'shape' => 'HashString', ], 'Status' => [ 'shape' => 'TaskStatusType', ], ], ], 'CancelStatementRequest' => [ 'type' => 'structure', 'required' => [ 'SessionId', 'Id', ], 'members' => [ 'SessionId' => [ 'shape' => 'NameString', ], 'Id' => [ 'shape' => 'IntegerValue', ], 'RequestOrigin' => [ 'shape' => 'OrchestrationNameString', ], ], ], 'CancelStatementResponse' => [ 'type' => 'structure', 'members' => [], ], 'Capabilities' => [ 'type' => 'structure', 'required' => [ 'SupportedAuthenticationTypes', 'SupportedDataOperations', 'SupportedComputeEnvironments', ], 'members' => [ 'SupportedAuthenticationTypes' => [ 'shape' => 'AuthenticationTypes', ], 'SupportedDataOperations' => [ 'shape' => 'DataOperations', ], 'SupportedComputeEnvironments' => [ 'shape' => 'ComputeEnvironments', ], ], ], 'Catalog' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'Name' => [ 'shape' => 'CatalogNameString', ], 'ResourceArn' => [ 'shape' => 'ResourceArnString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'Parameters' => [ 'shape' => 'ParametersMap', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], 'TargetRedshiftCatalog' => [ 'shape' => 'TargetRedshiftCatalog', ], 'FederatedCatalog' => [ 'shape' => 'FederatedCatalog', ], 'CatalogProperties' => [ 'shape' => 'CatalogPropertiesOutput', ], 'CreateTableDefaultPermissions' => [ 'shape' => 'PrincipalPermissionsList', ], 'CreateDatabaseDefaultPermissions' => [ 'shape' => 'PrincipalPermissionsList', ], 'AllowFullTableExternalDataAccess' => [ 'shape' => 'AllowFullTableExternalDataAccessEnum', ], ], ], 'CatalogDeltaSource' => [ 'type' => 'structure', 'required' => [ 'Name', 'Database', 'Table', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Database' => [ 'shape' => 'EnclosedInStringProperty', ], 'Table' => [ 'shape' => 'EnclosedInStringProperty', ], 'AdditionalDeltaOptions' => [ 'shape' => 'AdditionalOptions', ], 'OutputSchemas' => [ 'shape' => 'GlueSchemas', ], ], ], 'CatalogEncryptionMode' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'SSE-KMS', 'SSE-KMS-WITH-SERVICE-ROLE', ], ], 'CatalogEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'CatalogEntry', ], ], 'CatalogEntry' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', ], 'members' => [ 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], ], ], 'CatalogGetterPageSize' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'CatalogHudiSource' => [ 'type' => 'structure', 'required' => [ 'Name', 'Database', 'Table', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Database' => [ 'shape' => 'EnclosedInStringProperty', ], 'Table' => [ 'shape' => 'EnclosedInStringProperty', ], 'AdditionalHudiOptions' => [ 'shape' => 'AdditionalOptions', ], 'OutputSchemas' => [ 'shape' => 'GlueSchemas', ], ], ], 'CatalogIdString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'CatalogImportStatus' => [ 'type' => 'structure', 'members' => [ 'ImportCompleted' => [ 'shape' => 'Boolean', ], 'ImportTime' => [ 'shape' => 'Timestamp', ], 'ImportedBy' => [ 'shape' => 'NameString', ], ], ], 'CatalogInput' => [ 'type' => 'structure', 'members' => [ 'Description' => [ 'shape' => 'DescriptionString', ], 'FederatedCatalog' => [ 'shape' => 'FederatedCatalog', ], 'Parameters' => [ 'shape' => 'ParametersMap', ], 'TargetRedshiftCatalog' => [ 'shape' => 'TargetRedshiftCatalog', ], 'CatalogProperties' => [ 'shape' => 'CatalogProperties', ], 'CreateTableDefaultPermissions' => [ 'shape' => 'PrincipalPermissionsList', ], 'CreateDatabaseDefaultPermissions' => [ 'shape' => 'PrincipalPermissionsList', ], 'AllowFullTableExternalDataAccess' => [ 'shape' => 'AllowFullTableExternalDataAccessEnum', ], ], ], 'CatalogKafkaSource' => [ 'type' => 'structure', 'required' => [ 'Name', 'Table', 'Database', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'WindowSize' => [ 'shape' => 'BoxedPositiveInt', 'box' => true, ], 'DetectSchema' => [ 'shape' => 'BoxedBoolean', 'box' => true, ], 'Table' => [ 'shape' => 'EnclosedInStringProperty', ], 'Database' => [ 'shape' => 'EnclosedInStringProperty', ], 'StreamingOptions' => [ 'shape' => 'KafkaStreamingSourceOptions', ], 'DataPreviewOptions' => [ 'shape' => 'StreamingDataPreviewOptions', ], ], ], 'CatalogKinesisSource' => [ 'type' => 'structure', 'required' => [ 'Name', 'Table', 'Database', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'WindowSize' => [ 'shape' => 'BoxedPositiveInt', ], 'DetectSchema' => [ 'shape' => 'BoxedBoolean', 'box' => true, ], 'Table' => [ 'shape' => 'EnclosedInStringProperty', ], 'Database' => [ 'shape' => 'EnclosedInStringProperty', ], 'StreamingOptions' => [ 'shape' => 'KinesisStreamingSourceOptions', ], 'DataPreviewOptions' => [ 'shape' => 'StreamingDataPreviewOptions', ], ], ], 'CatalogList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Catalog', ], ], 'CatalogNameString' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^(?!(.*[.\\/\\\\]|aws:)).*$', ], 'CatalogProperties' => [ 'type' => 'structure', 'members' => [ 'DataLakeAccessProperties' => [ 'shape' => 'DataLakeAccessProperties', ], 'CustomProperties' => [ 'shape' => 'ParametersMap', ], ], ], 'CatalogPropertiesOutput' => [ 'type' => 'structure', 'members' => [ 'DataLakeAccessProperties' => [ 'shape' => 'DataLakeAccessPropertiesOutput', ], 'CustomProperties' => [ 'shape' => 'ParametersMap', ], ], ], 'CatalogSchemaChangePolicy' => [ 'type' => 'structure', 'members' => [ 'EnableUpdateCatalog' => [ 'shape' => 'BoxedBoolean', ], 'UpdateBehavior' => [ 'shape' => 'UpdateCatalogBehavior', ], ], ], 'CatalogSource' => [ 'type' => 'structure', 'required' => [ 'Name', 'Database', 'Table', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Database' => [ 'shape' => 'EnclosedInStringProperty', ], 'Table' => [ 'shape' => 'EnclosedInStringProperty', ], ], ], 'CatalogTablesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], 'min' => 1, ], 'CatalogTarget' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'Tables', ], 'members' => [ 'DatabaseName' => [ 'shape' => 'NameString', ], 'Tables' => [ 'shape' => 'CatalogTablesList', ], 'ConnectionName' => [ 'shape' => 'ConnectionName', ], 'EventQueueArn' => [ 'shape' => 'EventQueueArn', ], 'DlqEventQueueArn' => [ 'shape' => 'EventQueueArn', ], ], ], 'CatalogTargetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CatalogTarget', ], ], 'Category' => [ 'type' => 'string', ], 'CheckSchemaVersionValidityInput' => [ 'type' => 'structure', 'required' => [ 'DataFormat', 'SchemaDefinition', ], 'members' => [ 'DataFormat' => [ 'shape' => 'DataFormat', ], 'SchemaDefinition' => [ 'shape' => 'SchemaDefinitionString', ], ], ], 'CheckSchemaVersionValidityResponse' => [ 'type' => 'structure', 'members' => [ 'Valid' => [ 'shape' => 'IsVersionValid', ], 'Error' => [ 'shape' => 'SchemaValidationError', ], ], ], 'Classification' => [ 'type' => 'string', ], 'Classifier' => [ 'type' => 'structure', 'members' => [ 'GrokClassifier' => [ 'shape' => 'GrokClassifier', ], 'XMLClassifier' => [ 'shape' => 'XMLClassifier', ], 'JsonClassifier' => [ 'shape' => 'JsonClassifier', ], 'CsvClassifier' => [ 'shape' => 'CsvClassifier', ], ], ], 'ClassifierList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Classifier', ], ], 'ClassifierNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], ], 'CloudWatchEncryption' => [ 'type' => 'structure', 'members' => [ 'CloudWatchEncryptionMode' => [ 'shape' => 'CloudWatchEncryptionMode', ], 'KmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], ], ], 'CloudWatchEncryptionMode' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'SSE-KMS', ], ], 'CodeGenArgName' => [ 'type' => 'string', ], 'CodeGenArgValue' => [ 'type' => 'string', ], 'CodeGenConfigurationNode' => [ 'type' => 'structure', 'members' => [ 'AthenaConnectorSource' => [ 'shape' => 'AthenaConnectorSource', ], 'JDBCConnectorSource' => [ 'shape' => 'JDBCConnectorSource', ], 'SparkConnectorSource' => [ 'shape' => 'SparkConnectorSource', ], 'CatalogSource' => [ 'shape' => 'CatalogSource', ], 'RedshiftSource' => [ 'shape' => 'RedshiftSource', ], 'S3CatalogSource' => [ 'shape' => 'S3CatalogSource', ], 'S3CsvSource' => [ 'shape' => 'S3CsvSource', ], 'S3ExcelSource' => [ 'shape' => 'S3ExcelSource', ], 'S3JsonSource' => [ 'shape' => 'S3JsonSource', ], 'S3ParquetSource' => [ 'shape' => 'S3ParquetSource', ], 'RelationalCatalogSource' => [ 'shape' => 'RelationalCatalogSource', ], 'DynamoDBCatalogSource' => [ 'shape' => 'DynamoDBCatalogSource', ], 'JDBCConnectorTarget' => [ 'shape' => 'JDBCConnectorTarget', ], 'SparkConnectorTarget' => [ 'shape' => 'SparkConnectorTarget', ], 'CatalogTarget' => [ 'shape' => 'BasicCatalogTarget', ], 'RedshiftTarget' => [ 'shape' => 'RedshiftTarget', ], 'S3CatalogTarget' => [ 'shape' => 'S3CatalogTarget', ], 'S3GlueParquetTarget' => [ 'shape' => 'S3GlueParquetTarget', ], 'S3HyperDirectTarget' => [ 'shape' => 'S3HyperDirectTarget', ], 'S3DirectTarget' => [ 'shape' => 'S3DirectTarget', ], 'S3IcebergDirectTarget' => [ 'shape' => 'S3IcebergDirectTarget', ], 'ApplyMapping' => [ 'shape' => 'ApplyMapping', ], 'SelectFields' => [ 'shape' => 'SelectFields', ], 'DropFields' => [ 'shape' => 'DropFields', ], 'RenameField' => [ 'shape' => 'RenameField', ], 'Spigot' => [ 'shape' => 'Spigot', ], 'Join' => [ 'shape' => 'Join', ], 'SplitFields' => [ 'shape' => 'SplitFields', ], 'SelectFromCollection' => [ 'shape' => 'SelectFromCollection', ], 'FillMissingValues' => [ 'shape' => 'FillMissingValues', ], 'Filter' => [ 'shape' => 'Filter', ], 'CustomCode' => [ 'shape' => 'CustomCode', ], 'SparkSQL' => [ 'shape' => 'SparkSQL', ], 'DirectKinesisSource' => [ 'shape' => 'DirectKinesisSource', ], 'DirectKafkaSource' => [ 'shape' => 'DirectKafkaSource', ], 'CatalogKinesisSource' => [ 'shape' => 'CatalogKinesisSource', ], 'CatalogKafkaSource' => [ 'shape' => 'CatalogKafkaSource', ], 'DropNullFields' => [ 'shape' => 'DropNullFields', ], 'Merge' => [ 'shape' => 'Merge', ], 'Union' => [ 'shape' => 'Union', ], 'PIIDetection' => [ 'shape' => 'PIIDetection', ], 'Aggregate' => [ 'shape' => 'Aggregate', ], 'DropDuplicates' => [ 'shape' => 'DropDuplicates', ], 'GovernedCatalogTarget' => [ 'shape' => 'GovernedCatalogTarget', ], 'GovernedCatalogSource' => [ 'shape' => 'GovernedCatalogSource', ], 'MicrosoftSQLServerCatalogSource' => [ 'shape' => 'MicrosoftSQLServerCatalogSource', ], 'MySQLCatalogSource' => [ 'shape' => 'MySQLCatalogSource', ], 'OracleSQLCatalogSource' => [ 'shape' => 'OracleSQLCatalogSource', ], 'PostgreSQLCatalogSource' => [ 'shape' => 'PostgreSQLCatalogSource', ], 'MicrosoftSQLServerCatalogTarget' => [ 'shape' => 'MicrosoftSQLServerCatalogTarget', ], 'MySQLCatalogTarget' => [ 'shape' => 'MySQLCatalogTarget', ], 'OracleSQLCatalogTarget' => [ 'shape' => 'OracleSQLCatalogTarget', ], 'PostgreSQLCatalogTarget' => [ 'shape' => 'PostgreSQLCatalogTarget', ], 'DynamicTransform' => [ 'shape' => 'DynamicTransform', ], 'EvaluateDataQuality' => [ 'shape' => 'EvaluateDataQuality', ], 'S3CatalogHudiSource' => [ 'shape' => 'S3CatalogHudiSource', ], 'CatalogHudiSource' => [ 'shape' => 'CatalogHudiSource', ], 'S3HudiSource' => [ 'shape' => 'S3HudiSource', ], 'S3HudiCatalogTarget' => [ 'shape' => 'S3HudiCatalogTarget', ], 'S3HudiDirectTarget' => [ 'shape' => 'S3HudiDirectTarget', ], 'DirectJDBCSource' => [ 'shape' => 'DirectJDBCSource', ], 'S3CatalogDeltaSource' => [ 'shape' => 'S3CatalogDeltaSource', ], 'CatalogDeltaSource' => [ 'shape' => 'CatalogDeltaSource', ], 'S3DeltaSource' => [ 'shape' => 'S3DeltaSource', ], 'S3DeltaCatalogTarget' => [ 'shape' => 'S3DeltaCatalogTarget', ], 'S3DeltaDirectTarget' => [ 'shape' => 'S3DeltaDirectTarget', ], 'AmazonRedshiftSource' => [ 'shape' => 'AmazonRedshiftSource', ], 'AmazonRedshiftTarget' => [ 'shape' => 'AmazonRedshiftTarget', ], 'EvaluateDataQualityMultiFrame' => [ 'shape' => 'EvaluateDataQualityMultiFrame', ], 'Recipe' => [ 'shape' => 'Recipe', ], 'SnowflakeSource' => [ 'shape' => 'SnowflakeSource', ], 'SnowflakeTarget' => [ 'shape' => 'SnowflakeTarget', ], 'ConnectorDataSource' => [ 'shape' => 'ConnectorDataSource', ], 'ConnectorDataTarget' => [ 'shape' => 'ConnectorDataTarget', ], ], ], 'CodeGenConfigurationNodes' => [ 'type' => 'map', 'key' => [ 'shape' => 'NodeId', ], 'value' => [ 'shape' => 'CodeGenConfigurationNode', ], 'sensitive' => true, ], 'CodeGenEdge' => [ 'type' => 'structure', 'required' => [ 'Source', 'Target', ], 'members' => [ 'Source' => [ 'shape' => 'CodeGenIdentifier', ], 'Target' => [ 'shape' => 'CodeGenIdentifier', ], 'TargetParameter' => [ 'shape' => 'CodeGenArgName', ], ], ], 'CodeGenIdentifier' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[A-Za-z_][A-Za-z0-9_]*', ], 'CodeGenNode' => [ 'type' => 'structure', 'required' => [ 'Id', 'NodeType', 'Args', ], 'members' => [ 'Id' => [ 'shape' => 'CodeGenIdentifier', ], 'NodeType' => [ 'shape' => 'CodeGenNodeType', ], 'Args' => [ 'shape' => 'CodeGenNodeArgs', ], 'LineNumber' => [ 'shape' => 'Integer', ], ], ], 'CodeGenNodeArg' => [ 'type' => 'structure', 'required' => [ 'Name', 'Value', ], 'members' => [ 'Name' => [ 'shape' => 'CodeGenArgName', ], 'Value' => [ 'shape' => 'CodeGenArgValue', ], 'Param' => [ 'shape' => 'Boolean', ], ], ], 'CodeGenNodeArgs' => [ 'type' => 'list', 'member' => [ 'shape' => 'CodeGenNodeArg', ], 'max' => 50, 'min' => 0, ], 'CodeGenNodeType' => [ 'type' => 'string', ], 'Column' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Type' => [ 'shape' => 'ColumnTypeString', ], 'Comment' => [ 'shape' => 'CommentString', ], 'Parameters' => [ 'shape' => 'ParametersMap', ], ], ], 'ColumnError' => [ 'type' => 'structure', 'members' => [ 'ColumnName' => [ 'shape' => 'NameString', ], 'Error' => [ 'shape' => 'ErrorDetail', ], ], ], 'ColumnErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColumnError', ], ], 'ColumnImportance' => [ 'type' => 'structure', 'members' => [ 'ColumnName' => [ 'shape' => 'NameString', ], 'Importance' => [ 'shape' => 'GenericBoundedDouble', ], ], ], 'ColumnImportanceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColumnImportance', ], 'max' => 100, 'min' => 0, ], 'ColumnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Column', ], ], 'ColumnNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], ], 'ColumnNameString' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'ColumnRowFilter' => [ 'type' => 'structure', 'members' => [ 'ColumnName' => [ 'shape' => 'NameString', ], 'RowFilterExpression' => [ 'shape' => 'PredicateString', ], ], ], 'ColumnRowFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColumnRowFilter', ], ], 'ColumnStatistics' => [ 'type' => 'structure', 'required' => [ 'ColumnName', 'ColumnType', 'AnalyzedTime', 'StatisticsData', ], 'members' => [ 'ColumnName' => [ 'shape' => 'NameString', ], 'ColumnType' => [ 'shape' => 'TypeString', ], 'AnalyzedTime' => [ 'shape' => 'Timestamp', ], 'StatisticsData' => [ 'shape' => 'ColumnStatisticsData', ], ], ], 'ColumnStatisticsData' => [ 'type' => 'structure', 'required' => [ 'Type', ], 'members' => [ 'Type' => [ 'shape' => 'ColumnStatisticsType', ], 'BooleanColumnStatisticsData' => [ 'shape' => 'BooleanColumnStatisticsData', ], 'DateColumnStatisticsData' => [ 'shape' => 'DateColumnStatisticsData', ], 'DecimalColumnStatisticsData' => [ 'shape' => 'DecimalColumnStatisticsData', ], 'DoubleColumnStatisticsData' => [ 'shape' => 'DoubleColumnStatisticsData', ], 'LongColumnStatisticsData' => [ 'shape' => 'LongColumnStatisticsData', ], 'StringColumnStatisticsData' => [ 'shape' => 'StringColumnStatisticsData', ], 'BinaryColumnStatisticsData' => [ 'shape' => 'BinaryColumnStatisticsData', ], ], ], 'ColumnStatisticsError' => [ 'type' => 'structure', 'members' => [ 'ColumnStatistics' => [ 'shape' => 'ColumnStatistics', ], 'Error' => [ 'shape' => 'ErrorDetail', ], ], ], 'ColumnStatisticsErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColumnStatisticsError', ], ], 'ColumnStatisticsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColumnStatistics', ], ], 'ColumnStatisticsState' => [ 'type' => 'string', 'enum' => [ 'STARTING', 'RUNNING', 'SUCCEEDED', 'FAILED', 'STOPPED', ], ], 'ColumnStatisticsTaskNotRunningException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'ColumnStatisticsTaskRun' => [ 'type' => 'structure', 'members' => [ 'CustomerId' => [ 'shape' => 'AccountId', ], 'ColumnStatisticsTaskRunId' => [ 'shape' => 'HashString', ], 'DatabaseName' => [ 'shape' => 'DatabaseName', ], 'TableName' => [ 'shape' => 'TableName', ], 'ColumnNameList' => [ 'shape' => 'ColumnNameList', ], 'CatalogID' => [ 'shape' => 'CatalogIdString', ], 'Role' => [ 'shape' => 'Role', ], 'SampleSize' => [ 'shape' => 'SampleSizePercentage', ], 'SecurityConfiguration' => [ 'shape' => 'CrawlerSecurityConfiguration', ], 'NumberOfWorkers' => [ 'shape' => 'PositiveInteger', ], 'WorkerType' => [ 'shape' => 'NameString', ], 'ComputationType' => [ 'shape' => 'ComputationType', ], 'Status' => [ 'shape' => 'ColumnStatisticsState', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastUpdated' => [ 'shape' => 'Timestamp', ], 'StartTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], 'ErrorMessage' => [ 'shape' => 'DescriptionString', ], 'DPUSeconds' => [ 'shape' => 'NonNegativeDouble', ], ], ], 'ColumnStatisticsTaskRunIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'HashString', ], 'max' => 100, 'min' => 0, ], 'ColumnStatisticsTaskRunningException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'ColumnStatisticsTaskRunsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColumnStatisticsTaskRun', ], ], 'ColumnStatisticsTaskSettings' => [ 'type' => 'structure', 'members' => [ 'DatabaseName' => [ 'shape' => 'DatabaseName', ], 'TableName' => [ 'shape' => 'TableName', ], 'Schedule' => [ 'shape' => 'Schedule', ], 'ColumnNameList' => [ 'shape' => 'ColumnNameList', ], 'CatalogID' => [ 'shape' => 'CatalogIdString', ], 'Role' => [ 'shape' => 'Role', ], 'SampleSize' => [ 'shape' => 'SampleSizePercentage', ], 'SecurityConfiguration' => [ 'shape' => 'CrawlerSecurityConfiguration', ], 'ScheduleType' => [ 'shape' => 'ScheduleType', ], 'SettingSource' => [ 'shape' => 'SettingSource', ], 'LastExecutionAttempt' => [ 'shape' => 'ExecutionAttempt', ], ], ], 'ColumnStatisticsTaskStoppingException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'ColumnStatisticsType' => [ 'type' => 'string', 'enum' => [ 'BOOLEAN', 'DATE', 'DECIMAL', 'DOUBLE', 'LONG', 'STRING', 'BINARY', ], ], 'ColumnTypeString' => [ 'type' => 'string', 'max' => 131072, 'min' => 0, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'ColumnValueStringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColumnValuesString', ], ], 'ColumnValuesString' => [ 'type' => 'string', ], 'CommentString' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'CommitIdString' => [ 'type' => 'string', 'max' => 40, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'CompactionConfiguration' => [ 'type' => 'structure', 'members' => [ 'icebergConfiguration' => [ 'shape' => 'IcebergCompactionConfiguration', ], ], ], 'CompactionMetrics' => [ 'type' => 'structure', 'members' => [ 'IcebergMetrics' => [ 'shape' => 'IcebergCompactionMetrics', ], ], ], 'CompactionStrategy' => [ 'type' => 'string', 'enum' => [ 'binpack', 'sort', 'z-order', ], ], 'Comparator' => [ 'type' => 'string', 'enum' => [ 'EQUALS', 'GREATER_THAN', 'LESS_THAN', 'GREATER_THAN_EQUALS', 'LESS_THAN_EQUALS', ], ], 'Compatibility' => [ 'type' => 'string', 'enum' => [ 'NONE', 'DISABLED', 'BACKWARD', 'BACKWARD_ALL', 'FORWARD', 'FORWARD_ALL', 'FULL', 'FULL_ALL', ], ], 'CompressionType' => [ 'type' => 'string', 'enum' => [ 'gzip', 'bzip2', ], ], 'ComputationType' => [ 'type' => 'string', 'enum' => [ 'FULL', 'INCREMENTAL', ], ], 'ComputeEnvironment' => [ 'type' => 'string', 'enum' => [ 'SPARK', 'ATHENA', 'PYTHON', ], ], 'ComputeEnvironmentConfiguration' => [ 'type' => 'structure', 'required' => [ 'Name', 'Description', 'ComputeEnvironment', 'SupportedAuthenticationTypes', 'ConnectionOptions', 'ConnectionPropertyNameOverrides', 'ConnectionOptionNameOverrides', 'ConnectionPropertiesRequiredOverrides', ], 'members' => [ 'Name' => [ 'shape' => 'ComputeEnvironmentName', ], 'Description' => [ 'shape' => 'ComputeEnvironmentConfigurationDescriptionString', ], 'ComputeEnvironment' => [ 'shape' => 'ComputeEnvironment', ], 'SupportedAuthenticationTypes' => [ 'shape' => 'AuthenticationTypes', ], 'ConnectionOptions' => [ 'shape' => 'PropertiesMap', ], 'ConnectionPropertyNameOverrides' => [ 'shape' => 'PropertyNameOverrides', ], 'ConnectionOptionNameOverrides' => [ 'shape' => 'PropertyNameOverrides', ], 'ConnectionPropertiesRequiredOverrides' => [ 'shape' => 'ListOfString', ], 'PhysicalConnectionPropertiesRequired' => [ 'shape' => 'Bool', ], ], ], 'ComputeEnvironmentConfigurationDescriptionString' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'ComputeEnvironmentConfigurationMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ComputeEnvironmentName', ], 'value' => [ 'shape' => 'ComputeEnvironmentConfiguration', ], ], 'ComputeEnvironmentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ComputeEnvironment', ], ], 'ComputeEnvironmentName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'ComputeEnvironments' => [ 'type' => 'list', 'member' => [ 'shape' => 'ComputeEnvironment', ], ], 'ConcurrentModificationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'ConcurrentRunsExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'Condition' => [ 'type' => 'structure', 'members' => [ 'LogicalOperator' => [ 'shape' => 'LogicalOperator', ], 'JobName' => [ 'shape' => 'NameString', ], 'State' => [ 'shape' => 'JobRunState', ], 'CrawlerName' => [ 'shape' => 'NameString', ], 'CrawlState' => [ 'shape' => 'CrawlState', ], ], ], 'ConditionCheckFailureException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'ConditionExpression' => [ 'type' => 'structure', 'required' => [ 'Condition', 'TargetColumn', ], 'members' => [ 'Condition' => [ 'shape' => 'DatabrewCondition', ], 'Value' => [ 'shape' => 'DatabrewConditionValue', ], 'TargetColumn' => [ 'shape' => 'TargetColumn', ], ], ], 'ConditionExpressionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConditionExpression', ], ], 'ConditionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Condition', ], 'max' => 500, ], 'ConfigValueString' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9_.-]+', ], 'ConfigurationMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'NameString', ], 'value' => [ 'shape' => 'ConfigurationObject', ], ], 'ConfigurationObject' => [ 'type' => 'structure', 'members' => [ 'DefaultValue' => [ 'shape' => 'ConfigValueString', ], 'AllowedValues' => [ 'shape' => 'AllowedValuesStringList', ], 'MinValue' => [ 'shape' => 'ConfigValueString', ], 'MaxValue' => [ 'shape' => 'ConfigValueString', ], ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'ConfusionMatrix' => [ 'type' => 'structure', 'members' => [ 'NumTruePositives' => [ 'shape' => 'RecordsCount', ], 'NumFalsePositives' => [ 'shape' => 'RecordsCount', ], 'NumTrueNegatives' => [ 'shape' => 'RecordsCount', ], 'NumFalseNegatives' => [ 'shape' => 'RecordsCount', ], ], ], 'Connection' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'ConnectionType' => [ 'shape' => 'ConnectionType', ], 'MatchCriteria' => [ 'shape' => 'MatchCriteria', ], 'ConnectionProperties' => [ 'shape' => 'ConnectionProperties', ], 'SparkProperties' => [ 'shape' => 'PropertyMap', ], 'AthenaProperties' => [ 'shape' => 'PropertyMap', ], 'PythonProperties' => [ 'shape' => 'PropertyMap', ], 'PhysicalConnectionRequirements' => [ 'shape' => 'PhysicalConnectionRequirements', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastUpdatedTime' => [ 'shape' => 'Timestamp', ], 'LastUpdatedBy' => [ 'shape' => 'NameString', ], 'Status' => [ 'shape' => 'ConnectionStatus', ], 'StatusReason' => [ 'shape' => 'LongValueString', ], 'LastConnectionValidationTime' => [ 'shape' => 'Timestamp', ], 'AuthenticationConfiguration' => [ 'shape' => 'AuthenticationConfiguration', ], 'ConnectionSchemaVersion' => [ 'shape' => 'ConnectionSchemaVersion', ], 'CompatibleComputeEnvironments' => [ 'shape' => 'ComputeEnvironmentList', ], ], ], 'ConnectionInput' => [ 'type' => 'structure', 'required' => [ 'Name', 'ConnectionType', 'ConnectionProperties', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'ConnectionType' => [ 'shape' => 'ConnectionType', ], 'MatchCriteria' => [ 'shape' => 'MatchCriteria', ], 'ConnectionProperties' => [ 'shape' => 'ConnectionProperties', ], 'SparkProperties' => [ 'shape' => 'PropertyMap', ], 'AthenaProperties' => [ 'shape' => 'PropertyMap', ], 'PythonProperties' => [ 'shape' => 'PropertyMap', ], 'PhysicalConnectionRequirements' => [ 'shape' => 'PhysicalConnectionRequirements', ], 'AuthenticationConfiguration' => [ 'shape' => 'AuthenticationConfigurationInput', ], 'ValidateCredentials' => [ 'shape' => 'Boolean', ], 'ValidateForComputeEnvironments' => [ 'shape' => 'ComputeEnvironmentList', ], ], ], 'ConnectionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Connection', ], ], 'ConnectionName' => [ 'type' => 'string', ], 'ConnectionOptions' => [ 'type' => 'map', 'key' => [ 'shape' => 'OptionKey', ], 'value' => [ 'shape' => 'OptionValue', ], 'max' => 100, 'min' => 0, ], 'ConnectionPasswordEncryption' => [ 'type' => 'structure', 'required' => [ 'ReturnConnectionPasswordEncrypted', ], 'members' => [ 'ReturnConnectionPasswordEncrypted' => [ 'shape' => 'Boolean', ], 'AwsKmsKeyId' => [ 'shape' => 'NameString', ], ], ], 'ConnectionProperties' => [ 'type' => 'map', 'key' => [ 'shape' => 'ConnectionPropertyKey', ], 'value' => [ 'shape' => 'ValueString', ], 'max' => 100, 'min' => 0, ], 'ConnectionPropertyKey' => [ 'type' => 'string', 'enum' => [ 'HOST', 'PORT', 'USERNAME', 'PASSWORD', 'ENCRYPTED_PASSWORD', 'JDBC_DRIVER_JAR_URI', 'JDBC_DRIVER_CLASS_NAME', 'JDBC_ENGINE', 'JDBC_ENGINE_VERSION', 'CONFIG_FILES', 'INSTANCE_ID', 'JDBC_CONNECTION_URL', 'JDBC_ENFORCE_SSL', 'CUSTOM_JDBC_CERT', 'SKIP_CUSTOM_JDBC_CERT_VALIDATION', 'CUSTOM_JDBC_CERT_STRING', 'CONNECTION_URL', 'KAFKA_BOOTSTRAP_SERVERS', 'KAFKA_SSL_ENABLED', 'KAFKA_CUSTOM_CERT', 'KAFKA_SKIP_CUSTOM_CERT_VALIDATION', 'KAFKA_CLIENT_KEYSTORE', 'KAFKA_CLIENT_KEYSTORE_PASSWORD', 'KAFKA_CLIENT_KEY_PASSWORD', 'ENCRYPTED_KAFKA_CLIENT_KEYSTORE_PASSWORD', 'ENCRYPTED_KAFKA_CLIENT_KEY_PASSWORD', 'KAFKA_SASL_MECHANISM', 'KAFKA_SASL_PLAIN_USERNAME', 'KAFKA_SASL_PLAIN_PASSWORD', 'ENCRYPTED_KAFKA_SASL_PLAIN_PASSWORD', 'KAFKA_SASL_SCRAM_USERNAME', 'KAFKA_SASL_SCRAM_PASSWORD', 'KAFKA_SASL_SCRAM_SECRETS_ARN', 'ENCRYPTED_KAFKA_SASL_SCRAM_PASSWORD', 'KAFKA_SASL_GSSAPI_KEYTAB', 'KAFKA_SASL_GSSAPI_KRB5_CONF', 'KAFKA_SASL_GSSAPI_SERVICE', 'KAFKA_SASL_GSSAPI_PRINCIPAL', 'SECRET_ID', 'CONNECTOR_URL', 'CONNECTOR_TYPE', 'CONNECTOR_CLASS_NAME', 'ENDPOINT', 'ENDPOINT_TYPE', 'ROLE_ARN', 'REGION', 'WORKGROUP_NAME', 'CLUSTER_IDENTIFIER', 'DATABASE', ], ], 'ConnectionSchemaVersion' => [ 'type' => 'integer', 'box' => true, 'max' => 2, 'min' => 1, ], 'ConnectionStatus' => [ 'type' => 'string', 'enum' => [ 'READY', 'IN_PROGRESS', 'FAILED', ], ], 'ConnectionString' => [ 'type' => 'string', 'max' => 255, ], 'ConnectionStringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConnectionString', ], 'max' => 1000, ], 'ConnectionType' => [ 'type' => 'string', 'enum' => [ 'JDBC', 'SFTP', 'MONGODB', 'KAFKA', 'NETWORK', 'MARKETPLACE', 'CUSTOM', 'SALESFORCE', 'VIEW_VALIDATION_REDSHIFT', 'VIEW_VALIDATION_ATHENA', 'GOOGLEADS', 'GOOGLESHEETS', 'GOOGLEANALYTICS4', 'SERVICENOW', 'MARKETO', 'SAPODATA', 'ZENDESK', 'JIRACLOUD', 'NETSUITEERP', 'HUBSPOT', 'FACEBOOKADS', 'INSTAGRAMADS', 'ZOHOCRM', 'SALESFORCEPARDOT', 'SALESFORCEMARKETINGCLOUD', 'SLACK', 'STRIPE', 'INTERCOM', 'SNAPCHATADS', ], ], 'ConnectionTypeBrief' => [ 'type' => 'structure', 'members' => [ 'ConnectionType' => [ 'shape' => 'ConnectionType', ], 'DisplayName' => [ 'shape' => 'DisplayName', ], 'Vendor' => [ 'shape' => 'Vendor', ], 'Description' => [ 'shape' => 'Description', ], 'Categories' => [ 'shape' => 'ListOfString', ], 'Capabilities' => [ 'shape' => 'Capabilities', ], 'LogoUrl' => [ 'shape' => 'UrlString', ], 'ConnectionTypeVariants' => [ 'shape' => 'ConnectionTypeVariantList', ], ], ], 'ConnectionTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConnectionTypeBrief', ], ], 'ConnectionTypeVariant' => [ 'type' => 'structure', 'members' => [ 'ConnectionTypeVariantName' => [ 'shape' => 'DisplayName', ], 'DisplayName' => [ 'shape' => 'DisplayName', ], 'Description' => [ 'shape' => 'Description', ], 'LogoUrl' => [ 'shape' => 'UrlString', ], ], ], 'ConnectionTypeVariantList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConnectionTypeVariant', ], ], 'ConnectionsList' => [ 'type' => 'structure', 'members' => [ 'Connections' => [ 'shape' => 'ConnectionStringList', ], ], ], 'ConnectorDataSource' => [ 'type' => 'structure', 'required' => [ 'Name', 'ConnectionType', 'Data', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'ConnectionType' => [ 'shape' => 'EnclosedInStringProperty', ], 'Data' => [ 'shape' => 'ConnectorOptions', ], 'OutputSchemas' => [ 'shape' => 'GlueSchemas', ], ], ], 'ConnectorDataTarget' => [ 'type' => 'structure', 'required' => [ 'Name', 'ConnectionType', 'Data', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'ConnectionType' => [ 'shape' => 'EnclosedInStringProperty', ], 'Data' => [ 'shape' => 'ConnectorOptions', ], 'Inputs' => [ 'shape' => 'OneInput', ], ], ], 'ConnectorOptions' => [ 'type' => 'map', 'key' => [ 'shape' => 'GenericString', ], 'value' => [ 'shape' => 'GenericString', ], ], 'ContextKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'ContextValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'ContextWords' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], 'max' => 20, 'min' => 1, ], 'Crawl' => [ 'type' => 'structure', 'members' => [ 'State' => [ 'shape' => 'CrawlState', ], 'StartedOn' => [ 'shape' => 'TimestampValue', ], 'CompletedOn' => [ 'shape' => 'TimestampValue', ], 'ErrorMessage' => [ 'shape' => 'DescriptionString', ], 'LogGroup' => [ 'shape' => 'LogGroup', ], 'LogStream' => [ 'shape' => 'LogStream', ], ], ], 'CrawlId' => [ 'type' => 'string', ], 'CrawlList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Crawl', ], ], 'CrawlState' => [ 'type' => 'string', 'enum' => [ 'RUNNING', 'CANCELLING', 'CANCELLED', 'SUCCEEDED', 'FAILED', 'ERROR', ], ], 'Crawler' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Role' => [ 'shape' => 'Role', ], 'Targets' => [ 'shape' => 'CrawlerTargets', ], 'DatabaseName' => [ 'shape' => 'DatabaseName', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'Classifiers' => [ 'shape' => 'ClassifierNameList', ], 'RecrawlPolicy' => [ 'shape' => 'RecrawlPolicy', ], 'SchemaChangePolicy' => [ 'shape' => 'SchemaChangePolicy', ], 'LineageConfiguration' => [ 'shape' => 'LineageConfiguration', ], 'State' => [ 'shape' => 'CrawlerState', ], 'TablePrefix' => [ 'shape' => 'TablePrefix', ], 'Schedule' => [ 'shape' => 'Schedule', ], 'CrawlElapsedTime' => [ 'shape' => 'MillisecondsCount', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastUpdated' => [ 'shape' => 'Timestamp', ], 'LastCrawl' => [ 'shape' => 'LastCrawlInfo', ], 'Version' => [ 'shape' => 'VersionId', ], 'Configuration' => [ 'shape' => 'CrawlerConfiguration', ], 'CrawlerSecurityConfiguration' => [ 'shape' => 'CrawlerSecurityConfiguration', ], 'LakeFormationConfiguration' => [ 'shape' => 'LakeFormationConfiguration', ], ], ], 'CrawlerConfiguration' => [ 'type' => 'string', ], 'CrawlerHistory' => [ 'type' => 'structure', 'members' => [ 'CrawlId' => [ 'shape' => 'CrawlId', ], 'State' => [ 'shape' => 'CrawlerHistoryState', ], 'StartTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], 'Summary' => [ 'shape' => 'NameString', ], 'ErrorMessage' => [ 'shape' => 'DescriptionString', ], 'LogGroup' => [ 'shape' => 'LogGroup', ], 'LogStream' => [ 'shape' => 'LogStream', ], 'MessagePrefix' => [ 'shape' => 'MessagePrefix', ], 'DPUHour' => [ 'shape' => 'NonNegativeDouble', ], ], ], 'CrawlerHistoryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CrawlerHistory', ], ], 'CrawlerHistoryState' => [ 'type' => 'string', 'enum' => [ 'RUNNING', 'COMPLETED', 'FAILED', 'STOPPED', ], ], 'CrawlerLineageSettings' => [ 'type' => 'string', 'enum' => [ 'ENABLE', 'DISABLE', ], ], 'CrawlerList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Crawler', ], ], 'CrawlerMetrics' => [ 'type' => 'structure', 'members' => [ 'CrawlerName' => [ 'shape' => 'NameString', ], 'TimeLeftSeconds' => [ 'shape' => 'NonNegativeDouble', ], 'StillEstimating' => [ 'shape' => 'Boolean', ], 'LastRuntimeSeconds' => [ 'shape' => 'NonNegativeDouble', ], 'MedianRuntimeSeconds' => [ 'shape' => 'NonNegativeDouble', ], 'TablesCreated' => [ 'shape' => 'NonNegativeInteger', ], 'TablesUpdated' => [ 'shape' => 'NonNegativeInteger', ], 'TablesDeleted' => [ 'shape' => 'NonNegativeInteger', ], ], ], 'CrawlerMetricsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CrawlerMetrics', ], ], 'CrawlerNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], 'max' => 100, 'min' => 0, ], 'CrawlerNodeDetails' => [ 'type' => 'structure', 'members' => [ 'Crawls' => [ 'shape' => 'CrawlList', ], ], ], 'CrawlerNotRunningException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'CrawlerRunningException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'CrawlerSecurityConfiguration' => [ 'type' => 'string', 'max' => 128, 'min' => 0, ], 'CrawlerState' => [ 'type' => 'string', 'enum' => [ 'READY', 'RUNNING', 'STOPPING', ], ], 'CrawlerStoppingException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'CrawlerTargets' => [ 'type' => 'structure', 'members' => [ 'S3Targets' => [ 'shape' => 'S3TargetList', ], 'JdbcTargets' => [ 'shape' => 'JdbcTargetList', ], 'MongoDBTargets' => [ 'shape' => 'MongoDBTargetList', ], 'DynamoDBTargets' => [ 'shape' => 'DynamoDBTargetList', ], 'CatalogTargets' => [ 'shape' => 'CatalogTargetList', ], 'DeltaTargets' => [ 'shape' => 'DeltaTargetList', ], 'IcebergTargets' => [ 'shape' => 'IcebergTargetList', ], 'HudiTargets' => [ 'shape' => 'HudiTargetList', ], ], ], 'CrawlsFilter' => [ 'type' => 'structure', 'members' => [ 'FieldName' => [ 'shape' => 'FieldName', ], 'FilterOperator' => [ 'shape' => 'FilterOperator', ], 'FieldValue' => [ 'shape' => 'GenericString', ], ], ], 'CrawlsFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CrawlsFilter', ], ], 'CreateBlueprintRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'BlueprintLocation', ], 'members' => [ 'Name' => [ 'shape' => 'OrchestrationNameString', ], 'Description' => [ 'shape' => 'Generic512CharString', ], 'BlueprintLocation' => [ 'shape' => 'OrchestrationS3Location', ], 'Tags' => [ 'shape' => 'TagsMap', ], ], ], 'CreateBlueprintResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'CreateCatalogRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'CatalogInput', ], 'members' => [ 'Name' => [ 'shape' => 'CatalogNameString', ], 'CatalogInput' => [ 'shape' => 'CatalogInput', ], 'Tags' => [ 'shape' => 'TagsMap', ], ], ], 'CreateCatalogResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateClassifierRequest' => [ 'type' => 'structure', 'members' => [ 'GrokClassifier' => [ 'shape' => 'CreateGrokClassifierRequest', ], 'XMLClassifier' => [ 'shape' => 'CreateXMLClassifierRequest', ], 'JsonClassifier' => [ 'shape' => 'CreateJsonClassifierRequest', ], 'CsvClassifier' => [ 'shape' => 'CreateCsvClassifierRequest', ], ], ], 'CreateClassifierResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateColumnStatisticsTaskSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'Role', ], 'members' => [ 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'Role' => [ 'shape' => 'NameString', ], 'Schedule' => [ 'shape' => 'CronExpression', ], 'ColumnNameList' => [ 'shape' => 'ColumnNameList', ], 'SampleSize' => [ 'shape' => 'SampleSizePercentage', ], 'CatalogID' => [ 'shape' => 'NameString', ], 'SecurityConfiguration' => [ 'shape' => 'NameString', ], 'Tags' => [ 'shape' => 'TagsMap', ], ], ], 'CreateColumnStatisticsTaskSettingsResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateConnectionRequest' => [ 'type' => 'structure', 'required' => [ 'ConnectionInput', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'ConnectionInput' => [ 'shape' => 'ConnectionInput', ], 'Tags' => [ 'shape' => 'TagsMap', ], ], ], 'CreateConnectionResponse' => [ 'type' => 'structure', 'members' => [ 'CreateConnectionStatus' => [ 'shape' => 'ConnectionStatus', ], ], ], 'CreateCrawlerRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Role', 'Targets', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Role' => [ 'shape' => 'Role', ], 'DatabaseName' => [ 'shape' => 'DatabaseName', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'Targets' => [ 'shape' => 'CrawlerTargets', ], 'Schedule' => [ 'shape' => 'CronExpression', ], 'Classifiers' => [ 'shape' => 'ClassifierNameList', ], 'TablePrefix' => [ 'shape' => 'TablePrefix', ], 'SchemaChangePolicy' => [ 'shape' => 'SchemaChangePolicy', ], 'RecrawlPolicy' => [ 'shape' => 'RecrawlPolicy', ], 'LineageConfiguration' => [ 'shape' => 'LineageConfiguration', ], 'LakeFormationConfiguration' => [ 'shape' => 'LakeFormationConfiguration', ], 'Configuration' => [ 'shape' => 'CrawlerConfiguration', ], 'CrawlerSecurityConfiguration' => [ 'shape' => 'CrawlerSecurityConfiguration', ], 'Tags' => [ 'shape' => 'TagsMap', ], ], ], 'CreateCrawlerResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateCsvClassifierRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Delimiter' => [ 'shape' => 'CsvColumnDelimiter', ], 'QuoteSymbol' => [ 'shape' => 'CsvQuoteSymbol', ], 'ContainsHeader' => [ 'shape' => 'CsvHeaderOption', ], 'Header' => [ 'shape' => 'CsvHeader', ], 'DisableValueTrimming' => [ 'shape' => 'NullableBoolean', ], 'AllowSingleColumn' => [ 'shape' => 'NullableBoolean', ], 'CustomDatatypeConfigured' => [ 'shape' => 'NullableBoolean', ], 'CustomDatatypes' => [ 'shape' => 'CustomDatatypes', ], 'Serde' => [ 'shape' => 'CsvSerdeOption', ], ], ], 'CreateCustomEntityTypeRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'RegexString', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'RegexString' => [ 'shape' => 'NameString', ], 'ContextWords' => [ 'shape' => 'ContextWords', ], 'Tags' => [ 'shape' => 'TagsMap', ], ], ], 'CreateCustomEntityTypeResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'CreateDataQualityRulesetRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Ruleset', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'Ruleset' => [ 'shape' => 'DataQualityRulesetString', ], 'Tags' => [ 'shape' => 'TagsMap', ], 'TargetTable' => [ 'shape' => 'DataQualityTargetTable', ], 'DataQualitySecurityConfiguration' => [ 'shape' => 'NameString', ], 'ClientToken' => [ 'shape' => 'HashString', ], ], ], 'CreateDataQualityRulesetResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'CreateDatabaseRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseInput', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseInput' => [ 'shape' => 'DatabaseInput', ], 'Tags' => [ 'shape' => 'TagsMap', ], ], ], 'CreateDatabaseResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateDevEndpointRequest' => [ 'type' => 'structure', 'required' => [ 'EndpointName', 'RoleArn', ], 'members' => [ 'EndpointName' => [ 'shape' => 'GenericString', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'SecurityGroupIds' => [ 'shape' => 'StringList', ], 'SubnetId' => [ 'shape' => 'GenericString', ], 'PublicKey' => [ 'shape' => 'GenericString', ], 'PublicKeys' => [ 'shape' => 'PublicKeysList', ], 'NumberOfNodes' => [ 'shape' => 'IntegerValue', ], 'WorkerType' => [ 'shape' => 'WorkerType', ], 'GlueVersion' => [ 'shape' => 'GlueVersionString', ], 'NumberOfWorkers' => [ 'shape' => 'NullableInteger', ], 'ExtraPythonLibsS3Path' => [ 'shape' => 'GenericString', ], 'ExtraJarsS3Path' => [ 'shape' => 'GenericString', ], 'SecurityConfiguration' => [ 'shape' => 'NameString', ], 'Tags' => [ 'shape' => 'TagsMap', ], 'Arguments' => [ 'shape' => 'MapValue', ], ], ], 'CreateDevEndpointResponse' => [ 'type' => 'structure', 'members' => [ 'EndpointName' => [ 'shape' => 'GenericString', ], 'Status' => [ 'shape' => 'GenericString', ], 'SecurityGroupIds' => [ 'shape' => 'StringList', ], 'SubnetId' => [ 'shape' => 'GenericString', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'YarnEndpointAddress' => [ 'shape' => 'GenericString', ], 'ZeppelinRemoteSparkInterpreterPort' => [ 'shape' => 'IntegerValue', ], 'NumberOfNodes' => [ 'shape' => 'IntegerValue', ], 'WorkerType' => [ 'shape' => 'WorkerType', ], 'GlueVersion' => [ 'shape' => 'GlueVersionString', ], 'NumberOfWorkers' => [ 'shape' => 'NullableInteger', ], 'AvailabilityZone' => [ 'shape' => 'GenericString', ], 'VpcId' => [ 'shape' => 'GenericString', ], 'ExtraPythonLibsS3Path' => [ 'shape' => 'GenericString', ], 'ExtraJarsS3Path' => [ 'shape' => 'GenericString', ], 'FailureReason' => [ 'shape' => 'GenericString', ], 'SecurityConfiguration' => [ 'shape' => 'NameString', ], 'CreatedTimestamp' => [ 'shape' => 'TimestampValue', ], 'Arguments' => [ 'shape' => 'MapValue', ], ], ], 'CreateGrokClassifierRequest' => [ 'type' => 'structure', 'required' => [ 'Classification', 'Name', 'GrokPattern', ], 'members' => [ 'Classification' => [ 'shape' => 'Classification', ], 'Name' => [ 'shape' => 'NameString', ], 'GrokPattern' => [ 'shape' => 'GrokPattern', ], 'CustomPatterns' => [ 'shape' => 'CustomPatterns', ], ], ], 'CreateIntegrationRequest' => [ 'type' => 'structure', 'required' => [ 'IntegrationName', 'SourceArn', 'TargetArn', ], 'members' => [ 'IntegrationName' => [ 'shape' => 'String128', ], 'SourceArn' => [ 'shape' => 'String128', ], 'TargetArn' => [ 'shape' => 'String128', ], 'Description' => [ 'shape' => 'IntegrationDescription', ], 'DataFilter' => [ 'shape' => 'String2048', ], 'KmsKeyId' => [ 'shape' => 'String2048', ], 'AdditionalEncryptionContext' => [ 'shape' => 'IntegrationAdditionalEncryptionContextMap', ], 'Tags' => [ 'shape' => 'IntegrationTagsList', ], 'IntegrationConfig' => [ 'shape' => 'IntegrationConfig', ], ], ], 'CreateIntegrationResourcePropertyRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'String128', ], 'SourceProcessingProperties' => [ 'shape' => 'SourceProcessingProperties', ], 'TargetProcessingProperties' => [ 'shape' => 'TargetProcessingProperties', ], ], ], 'CreateIntegrationResourcePropertyResponse' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'String128', ], 'SourceProcessingProperties' => [ 'shape' => 'SourceProcessingProperties', ], 'TargetProcessingProperties' => [ 'shape' => 'TargetProcessingProperties', ], ], ], 'CreateIntegrationResponse' => [ 'type' => 'structure', 'required' => [ 'SourceArn', 'TargetArn', 'IntegrationName', 'IntegrationArn', 'Status', 'CreateTime', ], 'members' => [ 'SourceArn' => [ 'shape' => 'String128', ], 'TargetArn' => [ 'shape' => 'String128', ], 'IntegrationName' => [ 'shape' => 'String128', ], 'Description' => [ 'shape' => 'IntegrationDescription', ], 'IntegrationArn' => [ 'shape' => 'String128', ], 'KmsKeyId' => [ 'shape' => 'String2048', ], 'AdditionalEncryptionContext' => [ 'shape' => 'IntegrationAdditionalEncryptionContextMap', ], 'Tags' => [ 'shape' => 'IntegrationTagsList', ], 'Status' => [ 'shape' => 'IntegrationStatus', ], 'CreateTime' => [ 'shape' => 'IntegrationTimestamp', ], 'Errors' => [ 'shape' => 'IntegrationErrorList', ], 'DataFilter' => [ 'shape' => 'String2048', ], 'IntegrationConfig' => [ 'shape' => 'IntegrationConfig', ], ], ], 'CreateIntegrationTablePropertiesRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TableName', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'String128', ], 'TableName' => [ 'shape' => 'String128', ], 'SourceTableConfig' => [ 'shape' => 'SourceTableConfig', ], 'TargetTableConfig' => [ 'shape' => 'TargetTableConfig', ], ], ], 'CreateIntegrationTablePropertiesResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateJobRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Role', 'Command', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'JobMode' => [ 'shape' => 'JobMode', ], 'JobRunQueuingEnabled' => [ 'shape' => 'NullableBoolean', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'LogUri' => [ 'shape' => 'UriString', ], 'Role' => [ 'shape' => 'RoleString', ], 'ExecutionProperty' => [ 'shape' => 'ExecutionProperty', ], 'Command' => [ 'shape' => 'JobCommand', ], 'DefaultArguments' => [ 'shape' => 'GenericMap', ], 'NonOverridableArguments' => [ 'shape' => 'GenericMap', ], 'Connections' => [ 'shape' => 'ConnectionsList', ], 'MaxRetries' => [ 'shape' => 'MaxRetries', ], 'AllocatedCapacity' => [ 'shape' => 'IntegerValue', 'deprecated' => true, 'deprecatedMessage' => 'This property is deprecated, use MaxCapacity instead.', ], 'Timeout' => [ 'shape' => 'Timeout', ], 'MaxCapacity' => [ 'shape' => 'NullableDouble', ], 'SecurityConfiguration' => [ 'shape' => 'NameString', ], 'Tags' => [ 'shape' => 'TagsMap', ], 'NotificationProperty' => [ 'shape' => 'NotificationProperty', ], 'GlueVersion' => [ 'shape' => 'GlueVersionString', ], 'NumberOfWorkers' => [ 'shape' => 'NullableInteger', ], 'WorkerType' => [ 'shape' => 'WorkerType', ], 'CodeGenConfigurationNodes' => [ 'shape' => 'CodeGenConfigurationNodes', ], 'ExecutionClass' => [ 'shape' => 'ExecutionClass', ], 'SourceControlDetails' => [ 'shape' => 'SourceControlDetails', ], 'MaintenanceWindow' => [ 'shape' => 'MaintenanceWindow', ], ], ], 'CreateJobResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'CreateJsonClassifierRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'JsonPath', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'JsonPath' => [ 'shape' => 'JsonPath', ], ], ], 'CreateMLTransformRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'InputRecordTables', 'Parameters', 'Role', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'InputRecordTables' => [ 'shape' => 'GlueTables', ], 'Parameters' => [ 'shape' => 'TransformParameters', ], 'Role' => [ 'shape' => 'RoleString', ], 'GlueVersion' => [ 'shape' => 'GlueVersionString', ], 'MaxCapacity' => [ 'shape' => 'NullableDouble', ], 'WorkerType' => [ 'shape' => 'WorkerType', ], 'NumberOfWorkers' => [ 'shape' => 'NullableInteger', ], 'Timeout' => [ 'shape' => 'Timeout', ], 'MaxRetries' => [ 'shape' => 'NullableInteger', ], 'Tags' => [ 'shape' => 'TagsMap', ], 'TransformEncryption' => [ 'shape' => 'TransformEncryption', ], ], ], 'CreateMLTransformResponse' => [ 'type' => 'structure', 'members' => [ 'TransformId' => [ 'shape' => 'HashString', ], ], ], 'CreatePartitionIndexRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'PartitionIndex', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'PartitionIndex' => [ 'shape' => 'PartitionIndex', ], ], ], 'CreatePartitionIndexResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreatePartitionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'PartitionInput', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'PartitionInput' => [ 'shape' => 'PartitionInput', ], ], ], 'CreatePartitionResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateRegistryInput' => [ 'type' => 'structure', 'required' => [ 'RegistryName', ], 'members' => [ 'RegistryName' => [ 'shape' => 'SchemaRegistryNameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'Tags' => [ 'shape' => 'TagsMap', ], ], ], 'CreateRegistryResponse' => [ 'type' => 'structure', 'members' => [ 'RegistryArn' => [ 'shape' => 'GlueResourceArn', ], 'RegistryName' => [ 'shape' => 'SchemaRegistryNameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'Tags' => [ 'shape' => 'TagsMap', ], ], ], 'CreateSchemaInput' => [ 'type' => 'structure', 'required' => [ 'SchemaName', 'DataFormat', ], 'members' => [ 'RegistryId' => [ 'shape' => 'RegistryId', ], 'SchemaName' => [ 'shape' => 'SchemaRegistryNameString', ], 'DataFormat' => [ 'shape' => 'DataFormat', ], 'Compatibility' => [ 'shape' => 'Compatibility', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'Tags' => [ 'shape' => 'TagsMap', ], 'SchemaDefinition' => [ 'shape' => 'SchemaDefinitionString', ], ], ], 'CreateSchemaResponse' => [ 'type' => 'structure', 'members' => [ 'RegistryName' => [ 'shape' => 'SchemaRegistryNameString', ], 'RegistryArn' => [ 'shape' => 'GlueResourceArn', ], 'SchemaName' => [ 'shape' => 'SchemaRegistryNameString', ], 'SchemaArn' => [ 'shape' => 'GlueResourceArn', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'DataFormat' => [ 'shape' => 'DataFormat', ], 'Compatibility' => [ 'shape' => 'Compatibility', ], 'SchemaCheckpoint' => [ 'shape' => 'SchemaCheckpointNumber', ], 'LatestSchemaVersion' => [ 'shape' => 'VersionLongNumber', ], 'NextSchemaVersion' => [ 'shape' => 'VersionLongNumber', ], 'SchemaStatus' => [ 'shape' => 'SchemaStatus', ], 'Tags' => [ 'shape' => 'TagsMap', ], 'SchemaVersionId' => [ 'shape' => 'SchemaVersionIdString', ], 'SchemaVersionStatus' => [ 'shape' => 'SchemaVersionStatus', ], ], ], 'CreateScriptRequest' => [ 'type' => 'structure', 'members' => [ 'DagNodes' => [ 'shape' => 'DagNodes', ], 'DagEdges' => [ 'shape' => 'DagEdges', ], 'Language' => [ 'shape' => 'Language', ], ], ], 'CreateScriptResponse' => [ 'type' => 'structure', 'members' => [ 'PythonScript' => [ 'shape' => 'PythonScript', ], 'ScalaCode' => [ 'shape' => 'ScalaCode', ], ], ], 'CreateSecurityConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'EncryptionConfiguration', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'EncryptionConfiguration' => [ 'shape' => 'EncryptionConfiguration', ], ], ], 'CreateSecurityConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'CreatedTimestamp' => [ 'shape' => 'TimestampValue', ], ], ], 'CreateSessionRequest' => [ 'type' => 'structure', 'required' => [ 'Id', 'Role', 'Command', ], 'members' => [ 'Id' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'Role' => [ 'shape' => 'OrchestrationRoleArn', ], 'Command' => [ 'shape' => 'SessionCommand', ], 'Timeout' => [ 'shape' => 'Timeout', ], 'IdleTimeout' => [ 'shape' => 'Timeout', ], 'DefaultArguments' => [ 'shape' => 'OrchestrationArgumentsMap', ], 'Connections' => [ 'shape' => 'ConnectionsList', ], 'MaxCapacity' => [ 'shape' => 'NullableDouble', ], 'NumberOfWorkers' => [ 'shape' => 'NullableInteger', ], 'WorkerType' => [ 'shape' => 'WorkerType', ], 'SecurityConfiguration' => [ 'shape' => 'NameString', ], 'GlueVersion' => [ 'shape' => 'GlueVersionString', ], 'Tags' => [ 'shape' => 'TagsMap', ], 'RequestOrigin' => [ 'shape' => 'OrchestrationNameString', ], ], ], 'CreateSessionResponse' => [ 'type' => 'structure', 'members' => [ 'Session' => [ 'shape' => 'Session', ], ], ], 'CreateTableOptimizerRequest' => [ 'type' => 'structure', 'required' => [ 'CatalogId', 'DatabaseName', 'TableName', 'Type', 'TableOptimizerConfiguration', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'Type' => [ 'shape' => 'TableOptimizerType', ], 'TableOptimizerConfiguration' => [ 'shape' => 'TableOptimizerConfiguration', ], ], ], 'CreateTableOptimizerResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateTableRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableInput', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableInput' => [ 'shape' => 'TableInput', ], 'PartitionIndexes' => [ 'shape' => 'PartitionIndexList', ], 'TransactionId' => [ 'shape' => 'TransactionIdString', ], 'OpenTableFormatInput' => [ 'shape' => 'OpenTableFormatInput', ], ], ], 'CreateTableResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateTriggerRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Type', 'Actions', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'WorkflowName' => [ 'shape' => 'NameString', ], 'Type' => [ 'shape' => 'TriggerType', ], 'Schedule' => [ 'shape' => 'GenericString', ], 'Predicate' => [ 'shape' => 'Predicate', ], 'Actions' => [ 'shape' => 'ActionList', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'StartOnCreation' => [ 'shape' => 'BooleanValue', ], 'Tags' => [ 'shape' => 'TagsMap', ], 'EventBatchingCondition' => [ 'shape' => 'EventBatchingCondition', ], ], ], 'CreateTriggerResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'CreateUsageProfileRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Configuration', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'Configuration' => [ 'shape' => 'ProfileConfiguration', ], 'Tags' => [ 'shape' => 'TagsMap', ], ], ], 'CreateUsageProfileResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'CreateUserDefinedFunctionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'FunctionInput', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'FunctionInput' => [ 'shape' => 'UserDefinedFunctionInput', ], ], ], 'CreateUserDefinedFunctionResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateWorkflowRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'WorkflowDescriptionString', ], 'DefaultRunProperties' => [ 'shape' => 'WorkflowRunProperties', ], 'Tags' => [ 'shape' => 'TagsMap', ], 'MaxConcurrentRuns' => [ 'shape' => 'NullableInteger', ], ], ], 'CreateWorkflowResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'CreateXMLClassifierRequest' => [ 'type' => 'structure', 'required' => [ 'Classification', 'Name', ], 'members' => [ 'Classification' => [ 'shape' => 'Classification', ], 'Name' => [ 'shape' => 'NameString', ], 'RowTag' => [ 'shape' => 'RowTag', ], ], ], 'CreatedTimestamp' => [ 'type' => 'string', ], 'CredentialKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'CredentialMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'CredentialKey', ], 'value' => [ 'shape' => 'CredentialValue', ], 'sensitive' => true, ], 'CredentialValue' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'CronExpression' => [ 'type' => 'string', ], 'CsvClassifier' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastUpdated' => [ 'shape' => 'Timestamp', ], 'Version' => [ 'shape' => 'VersionId', ], 'Delimiter' => [ 'shape' => 'CsvColumnDelimiter', ], 'QuoteSymbol' => [ 'shape' => 'CsvQuoteSymbol', ], 'ContainsHeader' => [ 'shape' => 'CsvHeaderOption', ], 'Header' => [ 'shape' => 'CsvHeader', ], 'DisableValueTrimming' => [ 'shape' => 'NullableBoolean', ], 'AllowSingleColumn' => [ 'shape' => 'NullableBoolean', ], 'CustomDatatypeConfigured' => [ 'shape' => 'NullableBoolean', ], 'CustomDatatypes' => [ 'shape' => 'CustomDatatypes', ], 'Serde' => [ 'shape' => 'CsvSerdeOption', ], ], ], 'CsvColumnDelimiter' => [ 'type' => 'string', 'max' => 1, 'min' => 1, 'pattern' => '[^\\r\\n]', ], 'CsvHeader' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], ], 'CsvHeaderOption' => [ 'type' => 'string', 'enum' => [ 'UNKNOWN', 'PRESENT', 'ABSENT', ], ], 'CsvQuoteSymbol' => [ 'type' => 'string', 'max' => 1, 'min' => 1, 'pattern' => '[^\\r\\n]', ], 'CsvSerdeOption' => [ 'type' => 'string', 'enum' => [ 'OpenCSVSerDe', 'LazySimpleSerDe', 'None', ], ], 'CustomCode' => [ 'type' => 'structure', 'required' => [ 'Name', 'Inputs', 'Code', 'ClassName', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Inputs' => [ 'shape' => 'ManyInputs', ], 'Code' => [ 'shape' => 'ExtendedString', ], 'ClassName' => [ 'shape' => 'EnclosedInStringProperty', ], 'OutputSchemas' => [ 'shape' => 'GlueSchemas', ], ], ], 'CustomDatatypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], ], 'CustomEntityType' => [ 'type' => 'structure', 'required' => [ 'Name', 'RegexString', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'RegexString' => [ 'shape' => 'NameString', ], 'ContextWords' => [ 'shape' => 'ContextWords', ], ], ], 'CustomEntityTypeNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], 'max' => 50, 'min' => 1, ], 'CustomEntityTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'CustomEntityType', ], ], 'CustomPatterns' => [ 'type' => 'string', 'max' => 16000, 'min' => 0, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]*', ], 'CustomProperties' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'DQAdditionalOptions' => [ 'type' => 'map', 'key' => [ 'shape' => 'AdditionalOptionKeys', ], 'value' => [ 'shape' => 'GenericString', ], ], 'DQCompositeRuleEvaluationMethod' => [ 'type' => 'string', 'enum' => [ 'COLUMN', 'ROW', ], ], 'DQDLAliases' => [ 'type' => 'map', 'key' => [ 'shape' => 'NodeName', ], 'value' => [ 'shape' => 'EnclosedInStringProperty', ], ], 'DQDLString' => [ 'type' => 'string', 'max' => 65536, 'min' => 1, 'pattern' => '([\\u0020-\\u007E\\r\\s\\n])*', ], 'DQResultsPublishingOptions' => [ 'type' => 'structure', 'members' => [ 'EvaluationContext' => [ 'shape' => 'GenericLimitedString', ], 'ResultsS3Prefix' => [ 'shape' => 'EnclosedInStringProperty', ], 'CloudWatchMetricsEnabled' => [ 'shape' => 'BoxedBoolean', ], 'ResultsPublishingEnabled' => [ 'shape' => 'BoxedBoolean', ], ], ], 'DQStopJobOnFailureOptions' => [ 'type' => 'structure', 'members' => [ 'StopJobOnFailureTiming' => [ 'shape' => 'DQStopJobOnFailureTiming', ], ], ], 'DQStopJobOnFailureTiming' => [ 'type' => 'string', 'enum' => [ 'Immediate', 'AfterDataLoad', ], ], 'DQTransformOutput' => [ 'type' => 'string', 'enum' => [ 'PrimaryInput', 'EvaluationResults', ], ], 'DagEdges' => [ 'type' => 'list', 'member' => [ 'shape' => 'CodeGenEdge', ], ], 'DagNodes' => [ 'type' => 'list', 'member' => [ 'shape' => 'CodeGenNode', ], ], 'DataCatalogEncryptionSettings' => [ 'type' => 'structure', 'members' => [ 'EncryptionAtRest' => [ 'shape' => 'EncryptionAtRest', ], 'ConnectionPasswordEncryption' => [ 'shape' => 'ConnectionPasswordEncryption', ], ], ], 'DataFormat' => [ 'type' => 'string', 'enum' => [ 'AVRO', 'JSON', 'PROTOBUF', ], ], 'DataLakeAccessProperties' => [ 'type' => 'structure', 'members' => [ 'DataLakeAccess' => [ 'shape' => 'Boolean', ], 'DataTransferRole' => [ 'shape' => 'IAMRoleArn', ], 'KmsKey' => [ 'shape' => 'ResourceArnString', ], 'CatalogType' => [ 'shape' => 'NameString', ], ], ], 'DataLakeAccessPropertiesOutput' => [ 'type' => 'structure', 'members' => [ 'DataLakeAccess' => [ 'shape' => 'Boolean', ], 'DataTransferRole' => [ 'shape' => 'IAMRoleArn', ], 'KmsKey' => [ 'shape' => 'ResourceArnString', ], 'ManagedWorkgroupName' => [ 'shape' => 'NameString', ], 'ManagedWorkgroupStatus' => [ 'shape' => 'NameString', ], 'RedshiftDatabaseName' => [ 'shape' => 'NameString', ], 'StatusMessage' => [ 'shape' => 'NameString', ], 'CatalogType' => [ 'shape' => 'NameString', ], ], ], 'DataLakePrincipal' => [ 'type' => 'structure', 'members' => [ 'DataLakePrincipalIdentifier' => [ 'shape' => 'DataLakePrincipalString', ], ], ], 'DataLakePrincipalString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'DataOperation' => [ 'type' => 'string', 'enum' => [ 'READ', 'WRITE', ], ], 'DataOperations' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataOperation', ], ], 'DataQualityAggregatedMetrics' => [ 'type' => 'structure', 'members' => [ 'TotalRowsProcessed' => [ 'shape' => 'NullableDouble', ], 'TotalRowsPassed' => [ 'shape' => 'NullableDouble', ], 'TotalRowsFailed' => [ 'shape' => 'NullableDouble', ], 'TotalRulesProcessed' => [ 'shape' => 'NullableDouble', ], 'TotalRulesPassed' => [ 'shape' => 'NullableDouble', ], 'TotalRulesFailed' => [ 'shape' => 'NullableDouble', ], ], ], 'DataQualityAnalyzerResult' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DataQualityRuleResultDescription', ], 'EvaluationMessage' => [ 'shape' => 'DataQualityRuleResultDescription', ], 'EvaluatedMetrics' => [ 'shape' => 'EvaluatedMetricsMap', ], ], ], 'DataQualityAnalyzerResults' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataQualityAnalyzerResult', ], 'max' => 2000, 'min' => 0, ], 'DataQualityEncryption' => [ 'type' => 'structure', 'members' => [ 'DataQualityEncryptionMode' => [ 'shape' => 'DataQualityEncryptionMode', ], 'KmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], ], ], 'DataQualityEncryptionMode' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'SSE-KMS', ], ], 'DataQualityEvaluationRunAdditionalRunOptions' => [ 'type' => 'structure', 'members' => [ 'CloudWatchMetricsEnabled' => [ 'shape' => 'NullableBoolean', ], 'ResultsS3Prefix' => [ 'shape' => 'UriString', ], 'CompositeRuleEvaluationMethod' => [ 'shape' => 'DQCompositeRuleEvaluationMethod', ], ], ], 'DataQualityMetricValues' => [ 'type' => 'structure', 'members' => [ 'ActualValue' => [ 'shape' => 'NullableDouble', ], 'ExpectedValue' => [ 'shape' => 'NullableDouble', ], 'LowerLimit' => [ 'shape' => 'NullableDouble', ], 'UpperLimit' => [ 'shape' => 'NullableDouble', ], ], ], 'DataQualityModelStatus' => [ 'type' => 'string', 'enum' => [ 'RUNNING', 'SUCCEEDED', 'FAILED', ], ], 'DataQualityObservation' => [ 'type' => 'structure', 'members' => [ 'Description' => [ 'shape' => 'DataQualityObservationDescription', ], 'MetricBasedObservation' => [ 'shape' => 'MetricBasedObservation', ], ], ], 'DataQualityObservationDescription' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]*', 'sensitive' => true, ], 'DataQualityObservations' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataQualityObservation', ], 'max' => 50, 'min' => 0, ], 'DataQualityResult' => [ 'type' => 'structure', 'members' => [ 'ResultId' => [ 'shape' => 'HashString', ], 'ProfileId' => [ 'shape' => 'HashString', ], 'Score' => [ 'shape' => 'GenericBoundedDouble', ], 'DataSource' => [ 'shape' => 'DataSource', ], 'RulesetName' => [ 'shape' => 'NameString', ], 'EvaluationContext' => [ 'shape' => 'GenericString', ], 'StartedOn' => [ 'shape' => 'Timestamp', ], 'CompletedOn' => [ 'shape' => 'Timestamp', ], 'JobName' => [ 'shape' => 'NameString', ], 'JobRunId' => [ 'shape' => 'HashString', ], 'RulesetEvaluationRunId' => [ 'shape' => 'HashString', ], 'RuleResults' => [ 'shape' => 'DataQualityRuleResults', ], 'AnalyzerResults' => [ 'shape' => 'DataQualityAnalyzerResults', ], 'Observations' => [ 'shape' => 'DataQualityObservations', ], 'AggregatedMetrics' => [ 'shape' => 'DataQualityAggregatedMetrics', ], ], ], 'DataQualityResultDescription' => [ 'type' => 'structure', 'members' => [ 'ResultId' => [ 'shape' => 'HashString', ], 'DataSource' => [ 'shape' => 'DataSource', ], 'JobName' => [ 'shape' => 'NameString', ], 'JobRunId' => [ 'shape' => 'HashString', ], 'StartedOn' => [ 'shape' => 'Timestamp', ], ], ], 'DataQualityResultDescriptionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataQualityResultDescription', ], ], 'DataQualityResultFilterCriteria' => [ 'type' => 'structure', 'members' => [ 'DataSource' => [ 'shape' => 'DataSource', ], 'JobName' => [ 'shape' => 'NameString', ], 'JobRunId' => [ 'shape' => 'HashString', ], 'StartedAfter' => [ 'shape' => 'Timestamp', ], 'StartedBefore' => [ 'shape' => 'Timestamp', ], ], ], 'DataQualityResultIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'HashString', ], 'max' => 10, 'min' => 1, ], 'DataQualityResultIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'HashString', ], 'max' => 100, 'min' => 1, ], 'DataQualityResultsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataQualityResult', ], ], 'DataQualityRuleRecommendationRunDescription' => [ 'type' => 'structure', 'members' => [ 'RunId' => [ 'shape' => 'HashString', ], 'Status' => [ 'shape' => 'TaskStatusType', ], 'StartedOn' => [ 'shape' => 'Timestamp', ], 'DataSource' => [ 'shape' => 'DataSource', ], ], ], 'DataQualityRuleRecommendationRunFilter' => [ 'type' => 'structure', 'required' => [ 'DataSource', ], 'members' => [ 'DataSource' => [ 'shape' => 'DataSource', ], 'StartedBefore' => [ 'shape' => 'Timestamp', ], 'StartedAfter' => [ 'shape' => 'Timestamp', ], ], ], 'DataQualityRuleRecommendationRunList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataQualityRuleRecommendationRunDescription', ], ], 'DataQualityRuleResult' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DataQualityRuleResultDescription', ], 'EvaluationMessage' => [ 'shape' => 'DataQualityRuleResultDescription', ], 'Result' => [ 'shape' => 'DataQualityRuleResultStatus', ], 'EvaluatedMetrics' => [ 'shape' => 'EvaluatedMetricsMap', ], 'EvaluatedRule' => [ 'shape' => 'DataQualityRuleResultDescription', ], 'RuleMetrics' => [ 'shape' => 'RuleMetricsMap', ], ], ], 'DataQualityRuleResultDescription' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]*', 'sensitive' => true, ], 'DataQualityRuleResultStatus' => [ 'type' => 'string', 'enum' => [ 'PASS', 'FAIL', 'ERROR', ], ], 'DataQualityRuleResults' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataQualityRuleResult', ], 'max' => 2000, 'min' => 0, ], 'DataQualityRulesetEvaluationRunDescription' => [ 'type' => 'structure', 'members' => [ 'RunId' => [ 'shape' => 'HashString', ], 'Status' => [ 'shape' => 'TaskStatusType', ], 'StartedOn' => [ 'shape' => 'Timestamp', ], 'DataSource' => [ 'shape' => 'DataSource', ], ], ], 'DataQualityRulesetEvaluationRunFilter' => [ 'type' => 'structure', 'required' => [ 'DataSource', ], 'members' => [ 'DataSource' => [ 'shape' => 'DataSource', ], 'StartedBefore' => [ 'shape' => 'Timestamp', ], 'StartedAfter' => [ 'shape' => 'Timestamp', ], ], ], 'DataQualityRulesetEvaluationRunList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataQualityRulesetEvaluationRunDescription', ], ], 'DataQualityRulesetFilterCriteria' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'CreatedBefore' => [ 'shape' => 'Timestamp', ], 'CreatedAfter' => [ 'shape' => 'Timestamp', ], 'LastModifiedBefore' => [ 'shape' => 'Timestamp', ], 'LastModifiedAfter' => [ 'shape' => 'Timestamp', ], 'TargetTable' => [ 'shape' => 'DataQualityTargetTable', ], ], ], 'DataQualityRulesetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataQualityRulesetListDetails', ], ], 'DataQualityRulesetListDetails' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'CreatedOn' => [ 'shape' => 'Timestamp', ], 'LastModifiedOn' => [ 'shape' => 'Timestamp', ], 'TargetTable' => [ 'shape' => 'DataQualityTargetTable', ], 'RecommendationRunId' => [ 'shape' => 'HashString', ], 'RuleCount' => [ 'shape' => 'NullableInteger', ], ], ], 'DataQualityRulesetString' => [ 'type' => 'string', 'max' => 65536, 'min' => 1, ], 'DataQualityTargetTable' => [ 'type' => 'structure', 'required' => [ 'TableName', 'DatabaseName', ], 'members' => [ 'TableName' => [ 'shape' => 'NameString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'CatalogId' => [ 'shape' => 'NameString', ], ], ], 'DataSource' => [ 'type' => 'structure', 'required' => [ 'GlueTable', ], 'members' => [ 'GlueTable' => [ 'shape' => 'GlueTable', ], ], ], 'DataSourceMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'NameString', ], 'value' => [ 'shape' => 'DataSource', ], ], 'Database' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'LocationUri' => [ 'shape' => 'URI', ], 'Parameters' => [ 'shape' => 'ParametersMap', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], 'CreateTableDefaultPermissions' => [ 'shape' => 'PrincipalPermissionsList', ], 'TargetDatabase' => [ 'shape' => 'DatabaseIdentifier', ], 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'FederatedDatabase' => [ 'shape' => 'FederatedDatabase', ], ], ], 'DatabaseAttributes' => [ 'type' => 'string', 'enum' => [ 'NAME', ], ], 'DatabaseAttributesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DatabaseAttributes', ], ], 'DatabaseIdentifier' => [ 'type' => 'structure', 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'Region' => [ 'shape' => 'NameString', ], ], ], 'DatabaseInput' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'LocationUri' => [ 'shape' => 'URI', ], 'Parameters' => [ 'shape' => 'ParametersMap', ], 'CreateTableDefaultPermissions' => [ 'shape' => 'PrincipalPermissionsList', ], 'TargetDatabase' => [ 'shape' => 'DatabaseIdentifier', ], 'FederatedDatabase' => [ 'shape' => 'FederatedDatabase', ], ], ], 'DatabaseList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Database', ], ], 'DatabaseName' => [ 'type' => 'string', ], 'DatabrewCondition' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[A-Z\\_]+$', ], 'DatabrewConditionValue' => [ 'type' => 'string', 'max' => 1024, ], 'DatapointInclusionAnnotation' => [ 'type' => 'structure', 'members' => [ 'ProfileId' => [ 'shape' => 'HashString', ], 'StatisticId' => [ 'shape' => 'HashString', ], 'InclusionAnnotation' => [ 'shape' => 'InclusionAnnotationValue', ], ], ], 'Datatype' => [ 'type' => 'structure', 'required' => [ 'Id', 'Label', ], 'members' => [ 'Id' => [ 'shape' => 'GenericLimitedString', ], 'Label' => [ 'shape' => 'GenericLimitedString', ], ], ], 'DateColumnStatisticsData' => [ 'type' => 'structure', 'required' => [ 'NumberOfNulls', 'NumberOfDistinctValues', ], 'members' => [ 'MinimumValue' => [ 'shape' => 'Timestamp', ], 'MaximumValue' => [ 'shape' => 'Timestamp', ], 'NumberOfNulls' => [ 'shape' => 'NonNegativeLong', ], 'NumberOfDistinctValues' => [ 'shape' => 'NonNegativeLong', ], ], ], 'DecimalColumnStatisticsData' => [ 'type' => 'structure', 'required' => [ 'NumberOfNulls', 'NumberOfDistinctValues', ], 'members' => [ 'MinimumValue' => [ 'shape' => 'DecimalNumber', ], 'MaximumValue' => [ 'shape' => 'DecimalNumber', ], 'NumberOfNulls' => [ 'shape' => 'NonNegativeLong', ], 'NumberOfDistinctValues' => [ 'shape' => 'NonNegativeLong', ], ], ], 'DecimalNumber' => [ 'type' => 'structure', 'required' => [ 'UnscaledValue', 'Scale', ], 'members' => [ 'UnscaledValue' => [ 'shape' => 'Blob', ], 'Scale' => [ 'shape' => 'Integer', ], ], ], 'DeleteBehavior' => [ 'type' => 'string', 'enum' => [ 'LOG', 'DELETE_FROM_DATABASE', 'DEPRECATE_IN_DATABASE', ], ], 'DeleteBlueprintRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'DeleteBlueprintResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'DeleteCatalogRequest' => [ 'type' => 'structure', 'required' => [ 'CatalogId', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], ], ], 'DeleteCatalogResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteClassifierRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'DeleteClassifierResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteColumnStatisticsForPartitionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'PartitionValues', 'ColumnName', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'PartitionValues' => [ 'shape' => 'ValueStringList', ], 'ColumnName' => [ 'shape' => 'NameString', ], ], ], 'DeleteColumnStatisticsForPartitionResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteColumnStatisticsForTableRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'ColumnName', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'ColumnName' => [ 'shape' => 'NameString', ], ], ], 'DeleteColumnStatisticsForTableResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteColumnStatisticsTaskSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', ], 'members' => [ 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], ], ], 'DeleteColumnStatisticsTaskSettingsResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteConnectionNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], 'max' => 25, 'min' => 0, ], 'DeleteConnectionRequest' => [ 'type' => 'structure', 'required' => [ 'ConnectionName', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'ConnectionName' => [ 'shape' => 'NameString', ], ], ], 'DeleteConnectionResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteCrawlerRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'DeleteCrawlerResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteCustomEntityTypeRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'DeleteCustomEntityTypeResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'DeleteDataQualityRulesetRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'DeleteDataQualityRulesetResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteDatabaseRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'Name' => [ 'shape' => 'NameString', ], ], ], 'DeleteDatabaseResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteDevEndpointRequest' => [ 'type' => 'structure', 'required' => [ 'EndpointName', ], 'members' => [ 'EndpointName' => [ 'shape' => 'GenericString', ], ], ], 'DeleteDevEndpointResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteIntegrationRequest' => [ 'type' => 'structure', 'required' => [ 'IntegrationIdentifier', ], 'members' => [ 'IntegrationIdentifier' => [ 'shape' => 'String128', ], ], ], 'DeleteIntegrationResponse' => [ 'type' => 'structure', 'required' => [ 'SourceArn', 'TargetArn', 'IntegrationName', 'IntegrationArn', 'Status', 'CreateTime', ], 'members' => [ 'SourceArn' => [ 'shape' => 'String128', ], 'TargetArn' => [ 'shape' => 'String128', ], 'IntegrationName' => [ 'shape' => 'String128', ], 'Description' => [ 'shape' => 'IntegrationDescription', ], 'IntegrationArn' => [ 'shape' => 'String128', ], 'KmsKeyId' => [ 'shape' => 'String2048', ], 'AdditionalEncryptionContext' => [ 'shape' => 'IntegrationAdditionalEncryptionContextMap', ], 'Tags' => [ 'shape' => 'IntegrationTagsList', ], 'Status' => [ 'shape' => 'IntegrationStatus', ], 'CreateTime' => [ 'shape' => 'IntegrationTimestamp', ], 'Errors' => [ 'shape' => 'IntegrationErrorList', ], 'DataFilter' => [ 'shape' => 'String2048', ], ], ], 'DeleteIntegrationTablePropertiesRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TableName', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'String128', ], 'TableName' => [ 'shape' => 'String128', ], ], ], 'DeleteIntegrationTablePropertiesResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteJobRequest' => [ 'type' => 'structure', 'required' => [ 'JobName', ], 'members' => [ 'JobName' => [ 'shape' => 'NameString', ], ], ], 'DeleteJobResponse' => [ 'type' => 'structure', 'members' => [ 'JobName' => [ 'shape' => 'NameString', ], ], ], 'DeleteMLTransformRequest' => [ 'type' => 'structure', 'required' => [ 'TransformId', ], 'members' => [ 'TransformId' => [ 'shape' => 'HashString', ], ], ], 'DeleteMLTransformResponse' => [ 'type' => 'structure', 'members' => [ 'TransformId' => [ 'shape' => 'HashString', ], ], ], 'DeletePartitionIndexRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'IndexName', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'IndexName' => [ 'shape' => 'NameString', ], ], ], 'DeletePartitionIndexResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeletePartitionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'PartitionValues', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'PartitionValues' => [ 'shape' => 'ValueStringList', ], ], ], 'DeletePartitionResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteRegistryInput' => [ 'type' => 'structure', 'required' => [ 'RegistryId', ], 'members' => [ 'RegistryId' => [ 'shape' => 'RegistryId', ], ], ], 'DeleteRegistryResponse' => [ 'type' => 'structure', 'members' => [ 'RegistryName' => [ 'shape' => 'SchemaRegistryNameString', ], 'RegistryArn' => [ 'shape' => 'GlueResourceArn', ], 'Status' => [ 'shape' => 'RegistryStatus', ], ], ], 'DeleteResourcePolicyRequest' => [ 'type' => 'structure', 'members' => [ 'PolicyHashCondition' => [ 'shape' => 'HashString', ], 'ResourceArn' => [ 'shape' => 'GlueResourceArn', ], ], ], 'DeleteResourcePolicyResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteSchemaInput' => [ 'type' => 'structure', 'required' => [ 'SchemaId', ], 'members' => [ 'SchemaId' => [ 'shape' => 'SchemaId', ], ], ], 'DeleteSchemaResponse' => [ 'type' => 'structure', 'members' => [ 'SchemaArn' => [ 'shape' => 'GlueResourceArn', ], 'SchemaName' => [ 'shape' => 'SchemaRegistryNameString', ], 'Status' => [ 'shape' => 'SchemaStatus', ], ], ], 'DeleteSchemaVersionsInput' => [ 'type' => 'structure', 'required' => [ 'SchemaId', 'Versions', ], 'members' => [ 'SchemaId' => [ 'shape' => 'SchemaId', ], 'Versions' => [ 'shape' => 'VersionsString', ], ], ], 'DeleteSchemaVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'SchemaVersionErrors' => [ 'shape' => 'SchemaVersionErrorList', ], ], ], 'DeleteSecurityConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'DeleteSecurityConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteSessionRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'NameString', ], 'RequestOrigin' => [ 'shape' => 'OrchestrationNameString', ], ], ], 'DeleteSessionResponse' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'NameString', ], ], ], 'DeleteTableOptimizerRequest' => [ 'type' => 'structure', 'required' => [ 'CatalogId', 'DatabaseName', 'TableName', 'Type', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'Type' => [ 'shape' => 'TableOptimizerType', ], ], ], 'DeleteTableOptimizerResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteTableRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'Name', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'Name' => [ 'shape' => 'NameString', ], 'TransactionId' => [ 'shape' => 'TransactionIdString', ], ], ], 'DeleteTableResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteTableVersionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'VersionId', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'VersionId' => [ 'shape' => 'VersionString', ], ], ], 'DeleteTableVersionResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteTriggerRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'DeleteTriggerResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'DeleteUsageProfileRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'DeleteUsageProfileResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteUserDefinedFunctionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'FunctionName', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'FunctionName' => [ 'shape' => 'NameString', ], ], ], 'DeleteUserDefinedFunctionResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteWorkflowRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'DeleteWorkflowResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'DeltaTarget' => [ 'type' => 'structure', 'members' => [ 'DeltaTables' => [ 'shape' => 'PathList', ], 'ConnectionName' => [ 'shape' => 'ConnectionName', ], 'WriteManifest' => [ 'shape' => 'NullableBoolean', ], 'CreateNativeDeltaTable' => [ 'shape' => 'NullableBoolean', ], ], ], 'DeltaTargetCompressionType' => [ 'type' => 'string', 'enum' => [ 'uncompressed', 'snappy', ], ], 'DeltaTargetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeltaTarget', ], ], 'DescribeConnectionTypeRequest' => [ 'type' => 'structure', 'required' => [ 'ConnectionType', ], 'members' => [ 'ConnectionType' => [ 'shape' => 'NameString', ], ], ], 'DescribeConnectionTypeResponse' => [ 'type' => 'structure', 'members' => [ 'ConnectionType' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'Description', ], 'Capabilities' => [ 'shape' => 'Capabilities', ], 'ConnectionProperties' => [ 'shape' => 'PropertiesMap', ], 'ConnectionOptions' => [ 'shape' => 'PropertiesMap', ], 'AuthenticationConfiguration' => [ 'shape' => 'AuthConfiguration', ], 'ComputeEnvironmentConfigurations' => [ 'shape' => 'ComputeEnvironmentConfigurationMap', ], 'PhysicalConnectionRequirements' => [ 'shape' => 'PropertiesMap', ], 'AthenaConnectionProperties' => [ 'shape' => 'PropertiesMap', ], 'PythonConnectionProperties' => [ 'shape' => 'PropertiesMap', ], 'SparkConnectionProperties' => [ 'shape' => 'PropertiesMap', ], ], ], 'DescribeEntityRequest' => [ 'type' => 'structure', 'required' => [ 'ConnectionName', 'EntityName', ], 'members' => [ 'ConnectionName' => [ 'shape' => 'NameString', ], 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'EntityName' => [ 'shape' => 'EntityName', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'DataStoreApiVersion' => [ 'shape' => 'ApiVersion', ], ], ], 'DescribeEntityResponse' => [ 'type' => 'structure', 'members' => [ 'Fields' => [ 'shape' => 'FieldsList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeInboundIntegrationsRequest' => [ 'type' => 'structure', 'members' => [ 'IntegrationArn' => [ 'shape' => 'String128', ], 'Marker' => [ 'shape' => 'String128', ], 'MaxRecords' => [ 'shape' => 'IntegrationInteger', ], 'TargetArn' => [ 'shape' => 'String128', ], ], ], 'DescribeInboundIntegrationsResponse' => [ 'type' => 'structure', 'members' => [ 'InboundIntegrations' => [ 'shape' => 'InboundIntegrationsList', ], 'Marker' => [ 'shape' => 'String128', ], ], ], 'DescribeIntegrationsRequest' => [ 'type' => 'structure', 'members' => [ 'IntegrationIdentifier' => [ 'shape' => 'String128', ], 'Marker' => [ 'shape' => 'String128', ], 'MaxRecords' => [ 'shape' => 'IntegrationInteger', ], 'Filters' => [ 'shape' => 'IntegrationFilterList', ], ], ], 'DescribeIntegrationsResponse' => [ 'type' => 'structure', 'members' => [ 'Integrations' => [ 'shape' => 'IntegrationsList', ], 'Marker' => [ 'shape' => 'String128', ], ], ], 'Description' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'DescriptionString' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]*', ], 'DescriptionStringRemovable' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]*', ], 'DevEndpoint' => [ 'type' => 'structure', 'members' => [ 'EndpointName' => [ 'shape' => 'GenericString', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'SecurityGroupIds' => [ 'shape' => 'StringList', ], 'SubnetId' => [ 'shape' => 'GenericString', ], 'YarnEndpointAddress' => [ 'shape' => 'GenericString', ], 'PrivateAddress' => [ 'shape' => 'GenericString', ], 'ZeppelinRemoteSparkInterpreterPort' => [ 'shape' => 'IntegerValue', ], 'PublicAddress' => [ 'shape' => 'GenericString', ], 'Status' => [ 'shape' => 'GenericString', ], 'WorkerType' => [ 'shape' => 'WorkerType', ], 'GlueVersion' => [ 'shape' => 'GlueVersionString', ], 'NumberOfWorkers' => [ 'shape' => 'NullableInteger', ], 'NumberOfNodes' => [ 'shape' => 'IntegerValue', ], 'AvailabilityZone' => [ 'shape' => 'GenericString', ], 'VpcId' => [ 'shape' => 'GenericString', ], 'ExtraPythonLibsS3Path' => [ 'shape' => 'GenericString', ], 'ExtraJarsS3Path' => [ 'shape' => 'GenericString', ], 'FailureReason' => [ 'shape' => 'GenericString', ], 'LastUpdateStatus' => [ 'shape' => 'GenericString', ], 'CreatedTimestamp' => [ 'shape' => 'TimestampValue', ], 'LastModifiedTimestamp' => [ 'shape' => 'TimestampValue', ], 'PublicKey' => [ 'shape' => 'GenericString', ], 'PublicKeys' => [ 'shape' => 'PublicKeysList', ], 'SecurityConfiguration' => [ 'shape' => 'NameString', ], 'Arguments' => [ 'shape' => 'MapValue', ], ], ], 'DevEndpointCustomLibraries' => [ 'type' => 'structure', 'members' => [ 'ExtraPythonLibsS3Path' => [ 'shape' => 'GenericString', ], 'ExtraJarsS3Path' => [ 'shape' => 'GenericString', ], ], ], 'DevEndpointList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DevEndpoint', ], ], 'DevEndpointNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], ], 'DevEndpointNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'GenericString', ], 'max' => 25, 'min' => 1, ], 'DirectJDBCSource' => [ 'type' => 'structure', 'required' => [ 'Name', 'Database', 'Table', 'ConnectionName', 'ConnectionType', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Database' => [ 'shape' => 'EnclosedInStringProperty', ], 'Table' => [ 'shape' => 'EnclosedInStringProperty', ], 'ConnectionName' => [ 'shape' => 'EnclosedInStringProperty', ], 'ConnectionType' => [ 'shape' => 'JDBCConnectionType', ], 'RedshiftTmpDir' => [ 'shape' => 'EnclosedInStringProperty', ], ], ], 'DirectKafkaSource' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'StreamingOptions' => [ 'shape' => 'KafkaStreamingSourceOptions', ], 'WindowSize' => [ 'shape' => 'BoxedPositiveInt', 'box' => true, ], 'DetectSchema' => [ 'shape' => 'BoxedBoolean', 'box' => true, ], 'DataPreviewOptions' => [ 'shape' => 'StreamingDataPreviewOptions', ], ], ], 'DirectKinesisSource' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'WindowSize' => [ 'shape' => 'BoxedPositiveInt', 'box' => true, ], 'DetectSchema' => [ 'shape' => 'BoxedBoolean', 'box' => true, ], 'StreamingOptions' => [ 'shape' => 'KinesisStreamingSourceOptions', ], 'DataPreviewOptions' => [ 'shape' => 'StreamingDataPreviewOptions', ], ], ], 'DirectSchemaChangePolicy' => [ 'type' => 'structure', 'members' => [ 'EnableUpdateCatalog' => [ 'shape' => 'BoxedBoolean', ], 'UpdateBehavior' => [ 'shape' => 'UpdateCatalogBehavior', ], 'Table' => [ 'shape' => 'EnclosedInStringProperty', ], 'Database' => [ 'shape' => 'EnclosedInStringProperty', ], ], ], 'DisplayName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'Double' => [ 'type' => 'double', ], 'DoubleColumnStatisticsData' => [ 'type' => 'structure', 'required' => [ 'NumberOfNulls', 'NumberOfDistinctValues', ], 'members' => [ 'MinimumValue' => [ 'shape' => 'Double', ], 'MaximumValue' => [ 'shape' => 'Double', ], 'NumberOfNulls' => [ 'shape' => 'NonNegativeLong', ], 'NumberOfDistinctValues' => [ 'shape' => 'NonNegativeLong', ], ], ], 'DoubleValue' => [ 'type' => 'double', ], 'DropDuplicates' => [ 'type' => 'structure', 'required' => [ 'Name', 'Inputs', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Inputs' => [ 'shape' => 'OneInput', ], 'Columns' => [ 'shape' => 'LimitedPathList', ], ], ], 'DropFields' => [ 'type' => 'structure', 'required' => [ 'Name', 'Inputs', 'Paths', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Inputs' => [ 'shape' => 'OneInput', ], 'Paths' => [ 'shape' => 'GlueStudioPathList', ], ], ], 'DropNullFields' => [ 'type' => 'structure', 'required' => [ 'Name', 'Inputs', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Inputs' => [ 'shape' => 'OneInput', ], 'NullCheckBoxList' => [ 'shape' => 'NullCheckBoxList', ], 'NullTextList' => [ 'shape' => 'NullValueFields', ], ], ], 'DynamicTransform' => [ 'type' => 'structure', 'required' => [ 'Name', 'TransformName', 'Inputs', 'FunctionName', 'Path', ], 'members' => [ 'Name' => [ 'shape' => 'EnclosedInStringProperty', ], 'TransformName' => [ 'shape' => 'EnclosedInStringProperty', ], 'Inputs' => [ 'shape' => 'OneInput', ], 'Parameters' => [ 'shape' => 'TransformConfigParameterList', ], 'FunctionName' => [ 'shape' => 'EnclosedInStringProperty', ], 'Path' => [ 'shape' => 'EnclosedInStringProperty', ], 'Version' => [ 'shape' => 'EnclosedInStringProperty', ], 'OutputSchemas' => [ 'shape' => 'GlueSchemas', ], ], ], 'DynamoDBCatalogSource' => [ 'type' => 'structure', 'required' => [ 'Name', 'Database', 'Table', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Database' => [ 'shape' => 'EnclosedInStringProperty', ], 'Table' => [ 'shape' => 'EnclosedInStringProperty', ], ], ], 'DynamoDBTarget' => [ 'type' => 'structure', 'members' => [ 'Path' => [ 'shape' => 'Path', ], 'scanAll' => [ 'shape' => 'NullableBoolean', ], 'scanRate' => [ 'shape' => 'NullableDouble', ], ], ], 'DynamoDBTargetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DynamoDBTarget', ], ], 'Edge' => [ 'type' => 'structure', 'members' => [ 'SourceId' => [ 'shape' => 'NameString', ], 'DestinationId' => [ 'shape' => 'NameString', ], ], ], 'EdgeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Edge', ], ], 'EnableAdditionalMetadata' => [ 'type' => 'list', 'member' => [ 'shape' => 'JdbcMetadataEntry', ], ], 'EnableHybridValues' => [ 'type' => 'string', 'enum' => [ 'TRUE', 'FALSE', ], ], 'EnclosedInStringProperties' => [ 'type' => 'list', 'member' => [ 'shape' => 'EnclosedInStringProperty', ], ], 'EnclosedInStringPropertiesMinOne' => [ 'type' => 'list', 'member' => [ 'shape' => 'EnclosedInStringProperty', ], ], 'EnclosedInStringProperty' => [ 'type' => 'string', 'pattern' => '([\\u0009\\u000B\\u000C\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF])*', ], 'EnclosedInStringPropertyWithQuote' => [ 'type' => 'string', 'pattern' => '([\\u0009\\u000B\\u000C\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF])*', ], 'EncryptionAtRest' => [ 'type' => 'structure', 'required' => [ 'CatalogEncryptionMode', ], 'members' => [ 'CatalogEncryptionMode' => [ 'shape' => 'CatalogEncryptionMode', ], 'SseAwsKmsKeyId' => [ 'shape' => 'NameString', ], 'CatalogEncryptionServiceRole' => [ 'shape' => 'IAMRoleArn', ], ], ], 'EncryptionConfiguration' => [ 'type' => 'structure', 'members' => [ 'S3Encryption' => [ 'shape' => 'S3EncryptionList', ], 'CloudWatchEncryption' => [ 'shape' => 'CloudWatchEncryption', ], 'JobBookmarksEncryption' => [ 'shape' => 'JobBookmarksEncryption', ], 'DataQualityEncryption' => [ 'shape' => 'DataQualityEncryption', ], ], ], 'Entity' => [ 'type' => 'structure', 'members' => [ 'EntityName' => [ 'shape' => 'EntityName', ], 'Label' => [ 'shape' => 'EntityLabel', ], 'IsParentEntity' => [ 'shape' => 'IsParentEntity', ], 'Description' => [ 'shape' => 'EntityDescription', ], 'Category' => [ 'shape' => 'Category', ], 'CustomProperties' => [ 'shape' => 'CustomProperties', ], ], ], 'EntityDescription' => [ 'type' => 'string', ], 'EntityFieldName' => [ 'type' => 'string', ], 'EntityLabel' => [ 'type' => 'string', ], 'EntityList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Entity', ], ], 'EntityName' => [ 'type' => 'string', ], 'EntityNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], 'FromFederationSource' => [ 'shape' => 'NullableBoolean', ], ], 'exception' => true, ], 'ErrorByName' => [ 'type' => 'map', 'key' => [ 'shape' => 'NameString', ], 'value' => [ 'shape' => 'ErrorDetail', ], ], 'ErrorCodeString' => [ 'type' => 'string', ], 'ErrorDetail' => [ 'type' => 'structure', 'members' => [ 'ErrorCode' => [ 'shape' => 'NameString', ], 'ErrorMessage' => [ 'shape' => 'DescriptionString', ], ], ], 'ErrorDetails' => [ 'type' => 'structure', 'members' => [ 'ErrorCode' => [ 'shape' => 'ErrorCodeString', ], 'ErrorMessage' => [ 'shape' => 'ErrorMessageString', ], ], ], 'ErrorMessageString' => [ 'type' => 'string', ], 'ErrorString' => [ 'type' => 'string', ], 'EvaluateDataQuality' => [ 'type' => 'structure', 'required' => [ 'Name', 'Inputs', 'Ruleset', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Inputs' => [ 'shape' => 'OneInput', ], 'Ruleset' => [ 'shape' => 'DQDLString', ], 'Output' => [ 'shape' => 'DQTransformOutput', ], 'PublishingOptions' => [ 'shape' => 'DQResultsPublishingOptions', ], 'StopJobOnFailureOptions' => [ 'shape' => 'DQStopJobOnFailureOptions', ], ], ], 'EvaluateDataQualityMultiFrame' => [ 'type' => 'structure', 'required' => [ 'Name', 'Inputs', 'Ruleset', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Inputs' => [ 'shape' => 'ManyInputs', ], 'AdditionalDataSources' => [ 'shape' => 'DQDLAliases', ], 'Ruleset' => [ 'shape' => 'DQDLString', ], 'PublishingOptions' => [ 'shape' => 'DQResultsPublishingOptions', ], 'AdditionalOptions' => [ 'shape' => 'DQAdditionalOptions', ], 'StopJobOnFailureOptions' => [ 'shape' => 'DQStopJobOnFailureOptions', ], ], ], 'EvaluatedMetricsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'NameString', ], 'value' => [ 'shape' => 'NullableDouble', ], 'sensitive' => true, ], 'EvaluationMetrics' => [ 'type' => 'structure', 'required' => [ 'TransformType', ], 'members' => [ 'TransformType' => [ 'shape' => 'TransformType', ], 'FindMatchesMetrics' => [ 'shape' => 'FindMatchesMetrics', ], ], ], 'EventBatchingCondition' => [ 'type' => 'structure', 'required' => [ 'BatchSize', ], 'members' => [ 'BatchSize' => [ 'shape' => 'BatchSize', ], 'BatchWindow' => [ 'shape' => 'BatchWindow', ], ], ], 'EventQueueArn' => [ 'type' => 'string', ], 'ExecutionAttempt' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'ExecutionStatus', ], 'ColumnStatisticsTaskRunId' => [ 'shape' => 'HashString', ], 'ExecutionTimestamp' => [ 'shape' => 'Timestamp', ], 'ErrorMessage' => [ 'shape' => 'DescriptionString', ], ], ], 'ExecutionClass' => [ 'type' => 'string', 'enum' => [ 'FLEX', 'STANDARD', ], 'max' => 16, ], 'ExecutionProperty' => [ 'type' => 'structure', 'members' => [ 'MaxConcurrentRuns' => [ 'shape' => 'MaxConcurrentRuns', ], ], ], 'ExecutionStatus' => [ 'type' => 'string', 'enum' => [ 'FAILED', 'STARTED', ], ], 'ExecutionTime' => [ 'type' => 'integer', ], 'ExistCondition' => [ 'type' => 'string', 'enum' => [ 'MUST_EXIST', 'NOT_EXIST', 'NONE', ], ], 'ExportLabelsTaskRunProperties' => [ 'type' => 'structure', 'members' => [ 'OutputS3Path' => [ 'shape' => 'UriString', ], ], ], 'ExtendedString' => [ 'type' => 'string', 'pattern' => '[\\s\\S]*', ], 'FederatedCatalog' => [ 'type' => 'structure', 'members' => [ 'Identifier' => [ 'shape' => 'FederationIdentifier', ], 'ConnectionName' => [ 'shape' => 'NameString', ], ], ], 'FederatedDatabase' => [ 'type' => 'structure', 'members' => [ 'Identifier' => [ 'shape' => 'FederationIdentifier', ], 'ConnectionName' => [ 'shape' => 'NameString', ], ], ], 'FederatedResourceAlreadyExistsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], 'AssociatedGlueResource' => [ 'shape' => 'GlueResourceArn', ], ], 'exception' => true, ], 'FederatedTable' => [ 'type' => 'structure', 'members' => [ 'Identifier' => [ 'shape' => 'FederationIdentifier', ], 'DatabaseIdentifier' => [ 'shape' => 'FederationIdentifier', ], 'ConnectionName' => [ 'shape' => 'NameString', ], ], ], 'FederationIdentifier' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'FederationSourceErrorCode' => [ 'type' => 'string', 'enum' => [ 'AccessDeniedException', 'EntityNotFoundException', 'InvalidCredentialsException', 'InvalidInputException', 'InvalidResponseException', 'OperationTimeoutException', 'OperationNotSupportedException', 'InternalServiceException', 'PartialFailureException', 'ThrottlingException', ], ], 'FederationSourceException' => [ 'type' => 'structure', 'members' => [ 'FederationSourceErrorCode' => [ 'shape' => 'FederationSourceErrorCode', ], 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'FederationSourceRetryableException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'Field' => [ 'type' => 'structure', 'members' => [ 'FieldName' => [ 'shape' => 'EntityFieldName', ], 'Label' => [ 'shape' => 'FieldLabel', ], 'Description' => [ 'shape' => 'FieldDescription', ], 'FieldType' => [ 'shape' => 'FieldDataType', ], 'IsPrimaryKey' => [ 'shape' => 'Bool', ], 'IsNullable' => [ 'shape' => 'Bool', ], 'IsRetrievable' => [ 'shape' => 'Bool', ], 'IsFilterable' => [ 'shape' => 'Bool', ], 'IsPartitionable' => [ 'shape' => 'Bool', ], 'IsCreateable' => [ 'shape' => 'Bool', ], 'IsUpdateable' => [ 'shape' => 'Bool', ], 'IsUpsertable' => [ 'shape' => 'Bool', ], 'IsDefaultOnCreate' => [ 'shape' => 'Bool', ], 'SupportedValues' => [ 'shape' => 'ListOfString', ], 'SupportedFilterOperators' => [ 'shape' => 'FieldFilterOperatorsList', ], 'ParentField' => [ 'shape' => 'String', ], 'NativeDataType' => [ 'shape' => 'String', ], 'CustomProperties' => [ 'shape' => 'CustomProperties', ], ], ], 'FieldDataType' => [ 'type' => 'string', 'enum' => [ 'INT', 'SMALLINT', 'BIGINT', 'FLOAT', 'LONG', 'DATE', 'BOOLEAN', 'MAP', 'ARRAY', 'STRING', 'TIMESTAMP', 'DECIMAL', 'BYTE', 'SHORT', 'DOUBLE', 'STRUCT', ], ], 'FieldDescription' => [ 'type' => 'string', ], 'FieldFilterOperator' => [ 'type' => 'string', 'enum' => [ 'LESS_THAN', 'GREATER_THAN', 'BETWEEN', 'EQUAL_TO', 'NOT_EQUAL_TO', 'GREATER_THAN_OR_EQUAL_TO', 'LESS_THAN_OR_EQUAL_TO', 'CONTAINS', 'ORDER_BY', ], ], 'FieldFilterOperatorsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FieldFilterOperator', ], ], 'FieldLabel' => [ 'type' => 'string', ], 'FieldName' => [ 'type' => 'string', 'enum' => [ 'CRAWL_ID', 'STATE', 'START_TIME', 'END_TIME', 'DPU_HOUR', ], ], 'FieldType' => [ 'type' => 'string', ], 'FieldsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Field', ], ], 'FillMissingValues' => [ 'type' => 'structure', 'required' => [ 'Name', 'Inputs', 'ImputedPath', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Inputs' => [ 'shape' => 'OneInput', ], 'ImputedPath' => [ 'shape' => 'EnclosedInStringProperty', ], 'FilledPath' => [ 'shape' => 'EnclosedInStringProperty', ], ], ], 'Filter' => [ 'type' => 'structure', 'required' => [ 'Name', 'Inputs', 'LogicalOperator', 'Filters', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Inputs' => [ 'shape' => 'OneInput', ], 'LogicalOperator' => [ 'shape' => 'FilterLogicalOperator', ], 'Filters' => [ 'shape' => 'FilterExpressions', ], ], ], 'FilterExpression' => [ 'type' => 'structure', 'required' => [ 'Operation', 'Values', ], 'members' => [ 'Operation' => [ 'shape' => 'FilterOperation', ], 'Negated' => [ 'shape' => 'BoxedBoolean', ], 'Values' => [ 'shape' => 'FilterValues', ], ], ], 'FilterExpressions' => [ 'type' => 'list', 'member' => [ 'shape' => 'FilterExpression', ], ], 'FilterLogicalOperator' => [ 'type' => 'string', 'enum' => [ 'AND', 'OR', ], ], 'FilterOperation' => [ 'type' => 'string', 'enum' => [ 'EQ', 'LT', 'GT', 'LTE', 'GTE', 'REGEX', 'ISNULL', ], ], 'FilterOperator' => [ 'type' => 'string', 'enum' => [ 'GT', 'GE', 'LT', 'LE', 'EQ', 'NE', ], ], 'FilterPredicate' => [ 'type' => 'string', 'max' => 100000, 'min' => 1, ], 'FilterString' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'FilterValue' => [ 'type' => 'structure', 'required' => [ 'Type', 'Value', ], 'members' => [ 'Type' => [ 'shape' => 'FilterValueType', ], 'Value' => [ 'shape' => 'EnclosedInStringProperties', ], ], ], 'FilterValueType' => [ 'type' => 'string', 'enum' => [ 'COLUMNEXTRACTED', 'CONSTANT', ], ], 'FilterValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'FilterValue', ], ], 'FindMatchesMetrics' => [ 'type' => 'structure', 'members' => [ 'AreaUnderPRCurve' => [ 'shape' => 'GenericBoundedDouble', ], 'Precision' => [ 'shape' => 'GenericBoundedDouble', ], 'Recall' => [ 'shape' => 'GenericBoundedDouble', ], 'F1' => [ 'shape' => 'GenericBoundedDouble', ], 'ConfusionMatrix' => [ 'shape' => 'ConfusionMatrix', ], 'ColumnImportances' => [ 'shape' => 'ColumnImportanceList', ], ], ], 'FindMatchesParameters' => [ 'type' => 'structure', 'members' => [ 'PrimaryKeyColumnName' => [ 'shape' => 'ColumnNameString', ], 'PrecisionRecallTradeoff' => [ 'shape' => 'GenericBoundedDouble', ], 'AccuracyCostTradeoff' => [ 'shape' => 'GenericBoundedDouble', ], 'EnforceProvidedLabels' => [ 'shape' => 'NullableBoolean', ], ], ], 'FindMatchesTaskRunProperties' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'HashString', ], 'JobName' => [ 'shape' => 'NameString', ], 'JobRunId' => [ 'shape' => 'HashString', ], ], ], 'FormatString' => [ 'type' => 'string', 'max' => 128, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'Generic512CharString' => [ 'type' => 'string', 'max' => 512, 'min' => 1, ], 'GenericBoundedDouble' => [ 'type' => 'double', 'box' => true, 'max' => 1.0, 'min' => 0.0, ], 'GenericLimitedString' => [ 'type' => 'string', 'pattern' => '[A-Za-z0-9_-]*', ], 'GenericMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'GenericString', ], 'value' => [ 'shape' => 'GenericString', ], ], 'GenericString' => [ 'type' => 'string', ], 'GetBlueprintRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'IncludeBlueprint' => [ 'shape' => 'NullableBoolean', ], 'IncludeParameterSpec' => [ 'shape' => 'NullableBoolean', ], ], ], 'GetBlueprintResponse' => [ 'type' => 'structure', 'members' => [ 'Blueprint' => [ 'shape' => 'Blueprint', ], ], ], 'GetBlueprintRunRequest' => [ 'type' => 'structure', 'required' => [ 'BlueprintName', 'RunId', ], 'members' => [ 'BlueprintName' => [ 'shape' => 'OrchestrationNameString', ], 'RunId' => [ 'shape' => 'IdString', ], ], ], 'GetBlueprintRunResponse' => [ 'type' => 'structure', 'members' => [ 'BlueprintRun' => [ 'shape' => 'BlueprintRun', ], ], ], 'GetBlueprintRunsRequest' => [ 'type' => 'structure', 'required' => [ 'BlueprintName', ], 'members' => [ 'BlueprintName' => [ 'shape' => 'NameString', ], 'NextToken' => [ 'shape' => 'GenericString', ], 'MaxResults' => [ 'shape' => 'PageSize', ], ], ], 'GetBlueprintRunsResponse' => [ 'type' => 'structure', 'members' => [ 'BlueprintRuns' => [ 'shape' => 'BlueprintRuns', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'GetCatalogImportStatusRequest' => [ 'type' => 'structure', 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], ], ], 'GetCatalogImportStatusResponse' => [ 'type' => 'structure', 'members' => [ 'ImportStatus' => [ 'shape' => 'CatalogImportStatus', ], ], ], 'GetCatalogRequest' => [ 'type' => 'structure', 'required' => [ 'CatalogId', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], ], ], 'GetCatalogResponse' => [ 'type' => 'structure', 'members' => [ 'Catalog' => [ 'shape' => 'Catalog', ], ], ], 'GetCatalogsRequest' => [ 'type' => 'structure', 'members' => [ 'ParentCatalogId' => [ 'shape' => 'CatalogIdString', ], 'NextToken' => [ 'shape' => 'Token', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'Recursive' => [ 'shape' => 'Boolean', ], 'IncludeRoot' => [ 'shape' => 'NullableBoolean', ], ], ], 'GetCatalogsResponse' => [ 'type' => 'structure', 'required' => [ 'CatalogList', ], 'members' => [ 'CatalogList' => [ 'shape' => 'CatalogList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetClassifierRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'GetClassifierResponse' => [ 'type' => 'structure', 'members' => [ 'Classifier' => [ 'shape' => 'Classifier', ], ], ], 'GetClassifiersRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'PageSize', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetClassifiersResponse' => [ 'type' => 'structure', 'members' => [ 'Classifiers' => [ 'shape' => 'ClassifierList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetColumnNamesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], 'max' => 100, 'min' => 0, ], 'GetColumnStatisticsForPartitionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'PartitionValues', 'ColumnNames', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'PartitionValues' => [ 'shape' => 'ValueStringList', ], 'ColumnNames' => [ 'shape' => 'GetColumnNamesList', ], ], ], 'GetColumnStatisticsForPartitionResponse' => [ 'type' => 'structure', 'members' => [ 'ColumnStatisticsList' => [ 'shape' => 'ColumnStatisticsList', ], 'Errors' => [ 'shape' => 'ColumnErrors', ], ], ], 'GetColumnStatisticsForTableRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'ColumnNames', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'ColumnNames' => [ 'shape' => 'GetColumnNamesList', ], ], ], 'GetColumnStatisticsForTableResponse' => [ 'type' => 'structure', 'members' => [ 'ColumnStatisticsList' => [ 'shape' => 'ColumnStatisticsList', ], 'Errors' => [ 'shape' => 'ColumnErrors', ], ], ], 'GetColumnStatisticsTaskRunRequest' => [ 'type' => 'structure', 'required' => [ 'ColumnStatisticsTaskRunId', ], 'members' => [ 'ColumnStatisticsTaskRunId' => [ 'shape' => 'HashString', ], ], ], 'GetColumnStatisticsTaskRunResponse' => [ 'type' => 'structure', 'members' => [ 'ColumnStatisticsTaskRun' => [ 'shape' => 'ColumnStatisticsTaskRun', ], ], ], 'GetColumnStatisticsTaskRunsRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', ], 'members' => [ 'DatabaseName' => [ 'shape' => 'DatabaseName', ], 'TableName' => [ 'shape' => 'NameString', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetColumnStatisticsTaskRunsResponse' => [ 'type' => 'structure', 'members' => [ 'ColumnStatisticsTaskRuns' => [ 'shape' => 'ColumnStatisticsTaskRunsList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetColumnStatisticsTaskSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', ], 'members' => [ 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], ], ], 'GetColumnStatisticsTaskSettingsResponse' => [ 'type' => 'structure', 'members' => [ 'ColumnStatisticsTaskSettings' => [ 'shape' => 'ColumnStatisticsTaskSettings', ], ], ], 'GetConnectionRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'Name' => [ 'shape' => 'NameString', ], 'HidePassword' => [ 'shape' => 'Boolean', ], 'ApplyOverrideForComputeEnvironment' => [ 'shape' => 'ComputeEnvironment', ], ], ], 'GetConnectionResponse' => [ 'type' => 'structure', 'members' => [ 'Connection' => [ 'shape' => 'Connection', ], ], ], 'GetConnectionsFilter' => [ 'type' => 'structure', 'members' => [ 'MatchCriteria' => [ 'shape' => 'MatchCriteria', ], 'ConnectionType' => [ 'shape' => 'ConnectionType', ], 'ConnectionSchemaVersion' => [ 'shape' => 'ConnectionSchemaVersion', ], ], ], 'GetConnectionsRequest' => [ 'type' => 'structure', 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'Filter' => [ 'shape' => 'GetConnectionsFilter', ], 'HidePassword' => [ 'shape' => 'Boolean', ], 'NextToken' => [ 'shape' => 'Token', ], 'MaxResults' => [ 'shape' => 'PageSize', ], ], ], 'GetConnectionsResponse' => [ 'type' => 'structure', 'members' => [ 'ConnectionList' => [ 'shape' => 'ConnectionList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetCrawlerMetricsRequest' => [ 'type' => 'structure', 'members' => [ 'CrawlerNameList' => [ 'shape' => 'CrawlerNameList', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetCrawlerMetricsResponse' => [ 'type' => 'structure', 'members' => [ 'CrawlerMetricsList' => [ 'shape' => 'CrawlerMetricsList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetCrawlerRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'GetCrawlerResponse' => [ 'type' => 'structure', 'members' => [ 'Crawler' => [ 'shape' => 'Crawler', ], ], ], 'GetCrawlersRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'PageSize', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetCrawlersResponse' => [ 'type' => 'structure', 'members' => [ 'Crawlers' => [ 'shape' => 'CrawlerList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetCustomEntityTypeRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'GetCustomEntityTypeResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'RegexString' => [ 'shape' => 'NameString', ], 'ContextWords' => [ 'shape' => 'ContextWords', ], ], ], 'GetDataCatalogEncryptionSettingsRequest' => [ 'type' => 'structure', 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], ], ], 'GetDataCatalogEncryptionSettingsResponse' => [ 'type' => 'structure', 'members' => [ 'DataCatalogEncryptionSettings' => [ 'shape' => 'DataCatalogEncryptionSettings', ], ], ], 'GetDataQualityModelRequest' => [ 'type' => 'structure', 'required' => [ 'ProfileId', ], 'members' => [ 'StatisticId' => [ 'shape' => 'HashString', ], 'ProfileId' => [ 'shape' => 'HashString', ], ], ], 'GetDataQualityModelResponse' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'DataQualityModelStatus', ], 'StartedOn' => [ 'shape' => 'Timestamp', ], 'CompletedOn' => [ 'shape' => 'Timestamp', ], 'FailureReason' => [ 'shape' => 'HashString', ], ], ], 'GetDataQualityModelResultRequest' => [ 'type' => 'structure', 'required' => [ 'StatisticId', 'ProfileId', ], 'members' => [ 'StatisticId' => [ 'shape' => 'HashString', ], 'ProfileId' => [ 'shape' => 'HashString', ], ], ], 'GetDataQualityModelResultResponse' => [ 'type' => 'structure', 'members' => [ 'CompletedOn' => [ 'shape' => 'Timestamp', ], 'Model' => [ 'shape' => 'StatisticModelResults', ], ], ], 'GetDataQualityResultRequest' => [ 'type' => 'structure', 'required' => [ 'ResultId', ], 'members' => [ 'ResultId' => [ 'shape' => 'HashString', ], ], ], 'GetDataQualityResultResponse' => [ 'type' => 'structure', 'members' => [ 'ResultId' => [ 'shape' => 'HashString', ], 'ProfileId' => [ 'shape' => 'HashString', ], 'Score' => [ 'shape' => 'GenericBoundedDouble', ], 'DataSource' => [ 'shape' => 'DataSource', ], 'RulesetName' => [ 'shape' => 'NameString', ], 'EvaluationContext' => [ 'shape' => 'GenericString', ], 'StartedOn' => [ 'shape' => 'Timestamp', ], 'CompletedOn' => [ 'shape' => 'Timestamp', ], 'JobName' => [ 'shape' => 'NameString', ], 'JobRunId' => [ 'shape' => 'HashString', ], 'RulesetEvaluationRunId' => [ 'shape' => 'HashString', ], 'RuleResults' => [ 'shape' => 'DataQualityRuleResults', ], 'AnalyzerResults' => [ 'shape' => 'DataQualityAnalyzerResults', ], 'Observations' => [ 'shape' => 'DataQualityObservations', ], 'AggregatedMetrics' => [ 'shape' => 'DataQualityAggregatedMetrics', ], ], ], 'GetDataQualityRuleRecommendationRunRequest' => [ 'type' => 'structure', 'required' => [ 'RunId', ], 'members' => [ 'RunId' => [ 'shape' => 'HashString', ], ], ], 'GetDataQualityRuleRecommendationRunResponse' => [ 'type' => 'structure', 'members' => [ 'RunId' => [ 'shape' => 'HashString', ], 'DataSource' => [ 'shape' => 'DataSource', ], 'Role' => [ 'shape' => 'RoleString', ], 'NumberOfWorkers' => [ 'shape' => 'NullableInteger', ], 'Timeout' => [ 'shape' => 'Timeout', ], 'Status' => [ 'shape' => 'TaskStatusType', ], 'ErrorString' => [ 'shape' => 'GenericString', ], 'StartedOn' => [ 'shape' => 'Timestamp', ], 'LastModifiedOn' => [ 'shape' => 'Timestamp', ], 'CompletedOn' => [ 'shape' => 'Timestamp', ], 'ExecutionTime' => [ 'shape' => 'ExecutionTime', ], 'RecommendedRuleset' => [ 'shape' => 'DataQualityRulesetString', ], 'CreatedRulesetName' => [ 'shape' => 'NameString', ], 'DataQualitySecurityConfiguration' => [ 'shape' => 'NameString', ], ], ], 'GetDataQualityRulesetEvaluationRunRequest' => [ 'type' => 'structure', 'required' => [ 'RunId', ], 'members' => [ 'RunId' => [ 'shape' => 'HashString', ], ], ], 'GetDataQualityRulesetEvaluationRunResponse' => [ 'type' => 'structure', 'members' => [ 'RunId' => [ 'shape' => 'HashString', ], 'DataSource' => [ 'shape' => 'DataSource', ], 'Role' => [ 'shape' => 'RoleString', ], 'NumberOfWorkers' => [ 'shape' => 'NullableInteger', ], 'Timeout' => [ 'shape' => 'Timeout', ], 'AdditionalRunOptions' => [ 'shape' => 'DataQualityEvaluationRunAdditionalRunOptions', ], 'Status' => [ 'shape' => 'TaskStatusType', ], 'ErrorString' => [ 'shape' => 'GenericString', ], 'StartedOn' => [ 'shape' => 'Timestamp', ], 'LastModifiedOn' => [ 'shape' => 'Timestamp', ], 'CompletedOn' => [ 'shape' => 'Timestamp', ], 'ExecutionTime' => [ 'shape' => 'ExecutionTime', ], 'RulesetNames' => [ 'shape' => 'RulesetNames', ], 'ResultIds' => [ 'shape' => 'DataQualityResultIdList', ], 'AdditionalDataSources' => [ 'shape' => 'DataSourceMap', ], ], ], 'GetDataQualityRulesetRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'GetDataQualityRulesetResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'Ruleset' => [ 'shape' => 'DataQualityRulesetString', ], 'TargetTable' => [ 'shape' => 'DataQualityTargetTable', ], 'CreatedOn' => [ 'shape' => 'Timestamp', ], 'LastModifiedOn' => [ 'shape' => 'Timestamp', ], 'RecommendationRunId' => [ 'shape' => 'HashString', ], 'DataQualitySecurityConfiguration' => [ 'shape' => 'NameString', ], ], ], 'GetDatabaseRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'Name' => [ 'shape' => 'NameString', ], ], ], 'GetDatabaseResponse' => [ 'type' => 'structure', 'members' => [ 'Database' => [ 'shape' => 'Database', ], ], ], 'GetDatabasesRequest' => [ 'type' => 'structure', 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'NextToken' => [ 'shape' => 'Token', ], 'MaxResults' => [ 'shape' => 'CatalogGetterPageSize', ], 'ResourceShareType' => [ 'shape' => 'ResourceShareType', ], 'AttributesToGet' => [ 'shape' => 'DatabaseAttributesList', ], ], ], 'GetDatabasesResponse' => [ 'type' => 'structure', 'required' => [ 'DatabaseList', ], 'members' => [ 'DatabaseList' => [ 'shape' => 'DatabaseList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetDataflowGraphRequest' => [ 'type' => 'structure', 'members' => [ 'PythonScript' => [ 'shape' => 'PythonScript', ], ], ], 'GetDataflowGraphResponse' => [ 'type' => 'structure', 'members' => [ 'DagNodes' => [ 'shape' => 'DagNodes', ], 'DagEdges' => [ 'shape' => 'DagEdges', ], ], ], 'GetDevEndpointRequest' => [ 'type' => 'structure', 'required' => [ 'EndpointName', ], 'members' => [ 'EndpointName' => [ 'shape' => 'GenericString', ], ], ], 'GetDevEndpointResponse' => [ 'type' => 'structure', 'members' => [ 'DevEndpoint' => [ 'shape' => 'DevEndpoint', ], ], ], 'GetDevEndpointsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'PageSize', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'GetDevEndpointsResponse' => [ 'type' => 'structure', 'members' => [ 'DevEndpoints' => [ 'shape' => 'DevEndpointList', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'GetEntityRecordsRequest' => [ 'type' => 'structure', 'required' => [ 'EntityName', 'Limit', ], 'members' => [ 'ConnectionName' => [ 'shape' => 'NameString', ], 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'EntityName' => [ 'shape' => 'EntityName', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'DataStoreApiVersion' => [ 'shape' => 'ApiVersion', ], 'ConnectionOptions' => [ 'shape' => 'ConnectionOptions', ], 'FilterPredicate' => [ 'shape' => 'FilterPredicate', ], 'Limit' => [ 'shape' => 'Limit', ], 'OrderBy' => [ 'shape' => 'String', ], 'SelectedFields' => [ 'shape' => 'SelectedFields', ], ], ], 'GetEntityRecordsResponse' => [ 'type' => 'structure', 'members' => [ 'Records' => [ 'shape' => 'Records', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetIntegrationResourcePropertyRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'String128', ], ], ], 'GetIntegrationResourcePropertyResponse' => [ 'type' => 'structure', 'members' => [ 'ResourceArn' => [ 'shape' => 'String128', ], 'SourceProcessingProperties' => [ 'shape' => 'SourceProcessingProperties', ], 'TargetProcessingProperties' => [ 'shape' => 'TargetProcessingProperties', ], ], ], 'GetIntegrationTablePropertiesRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TableName', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'String128', ], 'TableName' => [ 'shape' => 'String128', ], ], ], 'GetIntegrationTablePropertiesResponse' => [ 'type' => 'structure', 'members' => [ 'ResourceArn' => [ 'shape' => 'String128', ], 'TableName' => [ 'shape' => 'String128', ], 'SourceTableConfig' => [ 'shape' => 'SourceTableConfig', ], 'TargetTableConfig' => [ 'shape' => 'TargetTableConfig', ], ], ], 'GetJobBookmarkRequest' => [ 'type' => 'structure', 'required' => [ 'JobName', ], 'members' => [ 'JobName' => [ 'shape' => 'JobName', ], 'RunId' => [ 'shape' => 'RunId', ], ], ], 'GetJobBookmarkResponse' => [ 'type' => 'structure', 'members' => [ 'JobBookmarkEntry' => [ 'shape' => 'JobBookmarkEntry', ], ], ], 'GetJobRequest' => [ 'type' => 'structure', 'required' => [ 'JobName', ], 'members' => [ 'JobName' => [ 'shape' => 'NameString', ], ], ], 'GetJobResponse' => [ 'type' => 'structure', 'members' => [ 'Job' => [ 'shape' => 'Job', ], ], ], 'GetJobRunRequest' => [ 'type' => 'structure', 'required' => [ 'JobName', 'RunId', ], 'members' => [ 'JobName' => [ 'shape' => 'NameString', ], 'RunId' => [ 'shape' => 'IdString', ], 'PredecessorsIncluded' => [ 'shape' => 'BooleanValue', ], ], ], 'GetJobRunResponse' => [ 'type' => 'structure', 'members' => [ 'JobRun' => [ 'shape' => 'JobRun', ], ], ], 'GetJobRunsRequest' => [ 'type' => 'structure', 'required' => [ 'JobName', ], 'members' => [ 'JobName' => [ 'shape' => 'NameString', ], 'NextToken' => [ 'shape' => 'GenericString', ], 'MaxResults' => [ 'shape' => 'OrchestrationPageSize200', ], ], ], 'GetJobRunsResponse' => [ 'type' => 'structure', 'members' => [ 'JobRuns' => [ 'shape' => 'JobRunList', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'GetJobsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'GenericString', ], 'MaxResults' => [ 'shape' => 'PageSize', ], ], ], 'GetJobsResponse' => [ 'type' => 'structure', 'members' => [ 'Jobs' => [ 'shape' => 'JobList', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'GetMLTaskRunRequest' => [ 'type' => 'structure', 'required' => [ 'TransformId', 'TaskRunId', ], 'members' => [ 'TransformId' => [ 'shape' => 'HashString', ], 'TaskRunId' => [ 'shape' => 'HashString', ], ], ], 'GetMLTaskRunResponse' => [ 'type' => 'structure', 'members' => [ 'TransformId' => [ 'shape' => 'HashString', ], 'TaskRunId' => [ 'shape' => 'HashString', ], 'Status' => [ 'shape' => 'TaskStatusType', ], 'LogGroupName' => [ 'shape' => 'GenericString', ], 'Properties' => [ 'shape' => 'TaskRunProperties', ], 'ErrorString' => [ 'shape' => 'GenericString', ], 'StartedOn' => [ 'shape' => 'Timestamp', ], 'LastModifiedOn' => [ 'shape' => 'Timestamp', ], 'CompletedOn' => [ 'shape' => 'Timestamp', ], 'ExecutionTime' => [ 'shape' => 'ExecutionTime', ], ], ], 'GetMLTaskRunsRequest' => [ 'type' => 'structure', 'required' => [ 'TransformId', ], 'members' => [ 'TransformId' => [ 'shape' => 'HashString', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'Filter' => [ 'shape' => 'TaskRunFilterCriteria', ], 'Sort' => [ 'shape' => 'TaskRunSortCriteria', ], ], ], 'GetMLTaskRunsResponse' => [ 'type' => 'structure', 'members' => [ 'TaskRuns' => [ 'shape' => 'TaskRunList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'GetMLTransformRequest' => [ 'type' => 'structure', 'required' => [ 'TransformId', ], 'members' => [ 'TransformId' => [ 'shape' => 'HashString', ], ], ], 'GetMLTransformResponse' => [ 'type' => 'structure', 'members' => [ 'TransformId' => [ 'shape' => 'HashString', ], 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'Status' => [ 'shape' => 'TransformStatusType', ], 'CreatedOn' => [ 'shape' => 'Timestamp', ], 'LastModifiedOn' => [ 'shape' => 'Timestamp', ], 'InputRecordTables' => [ 'shape' => 'GlueTables', ], 'Parameters' => [ 'shape' => 'TransformParameters', ], 'EvaluationMetrics' => [ 'shape' => 'EvaluationMetrics', ], 'LabelCount' => [ 'shape' => 'LabelCount', ], 'Schema' => [ 'shape' => 'TransformSchema', ], 'Role' => [ 'shape' => 'RoleString', ], 'GlueVersion' => [ 'shape' => 'GlueVersionString', ], 'MaxCapacity' => [ 'shape' => 'NullableDouble', ], 'WorkerType' => [ 'shape' => 'WorkerType', ], 'NumberOfWorkers' => [ 'shape' => 'NullableInteger', ], 'Timeout' => [ 'shape' => 'Timeout', ], 'MaxRetries' => [ 'shape' => 'NullableInteger', ], 'TransformEncryption' => [ 'shape' => 'TransformEncryption', ], ], ], 'GetMLTransformsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'PaginationToken', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'Filter' => [ 'shape' => 'TransformFilterCriteria', ], 'Sort' => [ 'shape' => 'TransformSortCriteria', ], ], ], 'GetMLTransformsResponse' => [ 'type' => 'structure', 'required' => [ 'Transforms', ], 'members' => [ 'Transforms' => [ 'shape' => 'TransformList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'GetMappingRequest' => [ 'type' => 'structure', 'required' => [ 'Source', ], 'members' => [ 'Source' => [ 'shape' => 'CatalogEntry', ], 'Sinks' => [ 'shape' => 'CatalogEntries', ], 'Location' => [ 'shape' => 'Location', ], ], ], 'GetMappingResponse' => [ 'type' => 'structure', 'required' => [ 'Mapping', ], 'members' => [ 'Mapping' => [ 'shape' => 'MappingList', ], ], ], 'GetPartitionIndexesRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetPartitionIndexesResponse' => [ 'type' => 'structure', 'members' => [ 'PartitionIndexDescriptorList' => [ 'shape' => 'PartitionIndexDescriptorList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetPartitionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'PartitionValues', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'PartitionValues' => [ 'shape' => 'ValueStringList', ], ], ], 'GetPartitionResponse' => [ 'type' => 'structure', 'members' => [ 'Partition' => [ 'shape' => 'Partition', ], ], ], 'GetPartitionsRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'Expression' => [ 'shape' => 'PredicateString', ], 'NextToken' => [ 'shape' => 'Token', ], 'Segment' => [ 'shape' => 'Segment', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'ExcludeColumnSchema' => [ 'shape' => 'BooleanNullable', ], 'TransactionId' => [ 'shape' => 'TransactionIdString', ], 'QueryAsOfTime' => [ 'shape' => 'Timestamp', ], ], ], 'GetPartitionsResponse' => [ 'type' => 'structure', 'members' => [ 'Partitions' => [ 'shape' => 'PartitionList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetPlanRequest' => [ 'type' => 'structure', 'required' => [ 'Mapping', 'Source', ], 'members' => [ 'Mapping' => [ 'shape' => 'MappingList', ], 'Source' => [ 'shape' => 'CatalogEntry', ], 'Sinks' => [ 'shape' => 'CatalogEntries', ], 'Location' => [ 'shape' => 'Location', ], 'Language' => [ 'shape' => 'Language', ], 'AdditionalPlanOptionsMap' => [ 'shape' => 'AdditionalPlanOptionsMap', ], ], ], 'GetPlanResponse' => [ 'type' => 'structure', 'members' => [ 'PythonScript' => [ 'shape' => 'PythonScript', ], 'ScalaCode' => [ 'shape' => 'ScalaCode', ], ], ], 'GetRegistryInput' => [ 'type' => 'structure', 'required' => [ 'RegistryId', ], 'members' => [ 'RegistryId' => [ 'shape' => 'RegistryId', ], ], ], 'GetRegistryResponse' => [ 'type' => 'structure', 'members' => [ 'RegistryName' => [ 'shape' => 'SchemaRegistryNameString', ], 'RegistryArn' => [ 'shape' => 'GlueResourceArn', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'Status' => [ 'shape' => 'RegistryStatus', ], 'CreatedTime' => [ 'shape' => 'CreatedTimestamp', ], 'UpdatedTime' => [ 'shape' => 'UpdatedTimestamp', ], ], ], 'GetResourcePoliciesRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'Token', ], 'MaxResults' => [ 'shape' => 'PageSize', ], ], ], 'GetResourcePoliciesResponse' => [ 'type' => 'structure', 'members' => [ 'GetResourcePoliciesResponseList' => [ 'shape' => 'GetResourcePoliciesResponseList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetResourcePoliciesResponseList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GluePolicy', ], ], 'GetResourcePolicyRequest' => [ 'type' => 'structure', 'members' => [ 'ResourceArn' => [ 'shape' => 'GlueResourceArn', ], ], ], 'GetResourcePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'PolicyInJson' => [ 'shape' => 'PolicyJsonString', ], 'PolicyHash' => [ 'shape' => 'HashString', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'GetSchemaByDefinitionInput' => [ 'type' => 'structure', 'required' => [ 'SchemaId', 'SchemaDefinition', ], 'members' => [ 'SchemaId' => [ 'shape' => 'SchemaId', ], 'SchemaDefinition' => [ 'shape' => 'SchemaDefinitionString', ], ], ], 'GetSchemaByDefinitionResponse' => [ 'type' => 'structure', 'members' => [ 'SchemaVersionId' => [ 'shape' => 'SchemaVersionIdString', ], 'SchemaArn' => [ 'shape' => 'GlueResourceArn', ], 'DataFormat' => [ 'shape' => 'DataFormat', ], 'Status' => [ 'shape' => 'SchemaVersionStatus', ], 'CreatedTime' => [ 'shape' => 'CreatedTimestamp', ], ], ], 'GetSchemaInput' => [ 'type' => 'structure', 'required' => [ 'SchemaId', ], 'members' => [ 'SchemaId' => [ 'shape' => 'SchemaId', ], ], ], 'GetSchemaResponse' => [ 'type' => 'structure', 'members' => [ 'RegistryName' => [ 'shape' => 'SchemaRegistryNameString', ], 'RegistryArn' => [ 'shape' => 'GlueResourceArn', ], 'SchemaName' => [ 'shape' => 'SchemaRegistryNameString', ], 'SchemaArn' => [ 'shape' => 'GlueResourceArn', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'DataFormat' => [ 'shape' => 'DataFormat', ], 'Compatibility' => [ 'shape' => 'Compatibility', ], 'SchemaCheckpoint' => [ 'shape' => 'SchemaCheckpointNumber', ], 'LatestSchemaVersion' => [ 'shape' => 'VersionLongNumber', ], 'NextSchemaVersion' => [ 'shape' => 'VersionLongNumber', ], 'SchemaStatus' => [ 'shape' => 'SchemaStatus', ], 'CreatedTime' => [ 'shape' => 'CreatedTimestamp', ], 'UpdatedTime' => [ 'shape' => 'UpdatedTimestamp', ], ], ], 'GetSchemaVersionInput' => [ 'type' => 'structure', 'members' => [ 'SchemaId' => [ 'shape' => 'SchemaId', ], 'SchemaVersionId' => [ 'shape' => 'SchemaVersionIdString', ], 'SchemaVersionNumber' => [ 'shape' => 'SchemaVersionNumber', ], ], ], 'GetSchemaVersionResponse' => [ 'type' => 'structure', 'members' => [ 'SchemaVersionId' => [ 'shape' => 'SchemaVersionIdString', ], 'SchemaDefinition' => [ 'shape' => 'SchemaDefinitionString', ], 'DataFormat' => [ 'shape' => 'DataFormat', ], 'SchemaArn' => [ 'shape' => 'GlueResourceArn', ], 'VersionNumber' => [ 'shape' => 'VersionLongNumber', ], 'Status' => [ 'shape' => 'SchemaVersionStatus', ], 'CreatedTime' => [ 'shape' => 'CreatedTimestamp', ], ], ], 'GetSchemaVersionsDiffInput' => [ 'type' => 'structure', 'required' => [ 'SchemaId', 'FirstSchemaVersionNumber', 'SecondSchemaVersionNumber', 'SchemaDiffType', ], 'members' => [ 'SchemaId' => [ 'shape' => 'SchemaId', ], 'FirstSchemaVersionNumber' => [ 'shape' => 'SchemaVersionNumber', ], 'SecondSchemaVersionNumber' => [ 'shape' => 'SchemaVersionNumber', ], 'SchemaDiffType' => [ 'shape' => 'SchemaDiffType', ], ], ], 'GetSchemaVersionsDiffResponse' => [ 'type' => 'structure', 'members' => [ 'Diff' => [ 'shape' => 'SchemaDefinitionDiff', ], ], ], 'GetSecurityConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'GetSecurityConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'SecurityConfiguration' => [ 'shape' => 'SecurityConfiguration', ], ], ], 'GetSecurityConfigurationsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'PageSize', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'GetSecurityConfigurationsResponse' => [ 'type' => 'structure', 'members' => [ 'SecurityConfigurations' => [ 'shape' => 'SecurityConfigurationList', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'GetSessionRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'NameString', ], 'RequestOrigin' => [ 'shape' => 'OrchestrationNameString', ], ], ], 'GetSessionResponse' => [ 'type' => 'structure', 'members' => [ 'Session' => [ 'shape' => 'Session', ], ], ], 'GetStatementRequest' => [ 'type' => 'structure', 'required' => [ 'SessionId', 'Id', ], 'members' => [ 'SessionId' => [ 'shape' => 'NameString', ], 'Id' => [ 'shape' => 'IntegerValue', ], 'RequestOrigin' => [ 'shape' => 'OrchestrationNameString', ], ], ], 'GetStatementResponse' => [ 'type' => 'structure', 'members' => [ 'Statement' => [ 'shape' => 'Statement', ], ], ], 'GetTableOptimizerRequest' => [ 'type' => 'structure', 'required' => [ 'CatalogId', 'DatabaseName', 'TableName', 'Type', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'Type' => [ 'shape' => 'TableOptimizerType', ], ], ], 'GetTableOptimizerResponse' => [ 'type' => 'structure', 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'TableOptimizer' => [ 'shape' => 'TableOptimizer', ], ], ], 'GetTableRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'Name', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'Name' => [ 'shape' => 'NameString', ], 'TransactionId' => [ 'shape' => 'TransactionIdString', ], 'QueryAsOfTime' => [ 'shape' => 'Timestamp', ], 'IncludeStatusDetails' => [ 'shape' => 'BooleanNullable', ], ], ], 'GetTableResponse' => [ 'type' => 'structure', 'members' => [ 'Table' => [ 'shape' => 'Table', ], ], ], 'GetTableVersionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'VersionId' => [ 'shape' => 'VersionString', ], ], ], 'GetTableVersionResponse' => [ 'type' => 'structure', 'members' => [ 'TableVersion' => [ 'shape' => 'TableVersion', ], ], ], 'GetTableVersionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TableVersion', ], ], 'GetTableVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'NextToken' => [ 'shape' => 'Token', ], 'MaxResults' => [ 'shape' => 'CatalogGetterPageSize', ], ], ], 'GetTableVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'TableVersions' => [ 'shape' => 'GetTableVersionsList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetTablesRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'Expression' => [ 'shape' => 'FilterString', ], 'NextToken' => [ 'shape' => 'Token', ], 'MaxResults' => [ 'shape' => 'CatalogGetterPageSize', ], 'TransactionId' => [ 'shape' => 'TransactionIdString', ], 'QueryAsOfTime' => [ 'shape' => 'Timestamp', ], 'IncludeStatusDetails' => [ 'shape' => 'BooleanNullable', ], 'AttributesToGet' => [ 'shape' => 'TableAttributesList', ], ], ], 'GetTablesResponse' => [ 'type' => 'structure', 'members' => [ 'TableList' => [ 'shape' => 'TableList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetTagsRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'GlueResourceArn', ], ], ], 'GetTagsResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagsMap', ], ], ], 'GetTriggerRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'GetTriggerResponse' => [ 'type' => 'structure', 'members' => [ 'Trigger' => [ 'shape' => 'Trigger', ], ], ], 'GetTriggersRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'GenericString', ], 'DependentJobName' => [ 'shape' => 'NameString', ], 'MaxResults' => [ 'shape' => 'OrchestrationPageSize200', ], ], ], 'GetTriggersResponse' => [ 'type' => 'structure', 'members' => [ 'Triggers' => [ 'shape' => 'TriggerList', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'GetUnfilteredPartitionMetadataRequest' => [ 'type' => 'structure', 'required' => [ 'CatalogId', 'DatabaseName', 'TableName', 'PartitionValues', 'SupportedPermissionTypes', ], 'members' => [ 'Region' => [ 'shape' => 'ValueString', ], 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'PartitionValues' => [ 'shape' => 'ValueStringList', ], 'AuditContext' => [ 'shape' => 'AuditContext', ], 'SupportedPermissionTypes' => [ 'shape' => 'PermissionTypeList', ], 'QuerySessionContext' => [ 'shape' => 'QuerySessionContext', ], ], ], 'GetUnfilteredPartitionMetadataResponse' => [ 'type' => 'structure', 'members' => [ 'Partition' => [ 'shape' => 'Partition', ], 'AuthorizedColumns' => [ 'shape' => 'NameStringList', ], 'IsRegisteredWithLakeFormation' => [ 'shape' => 'Boolean', ], ], ], 'GetUnfilteredPartitionsMetadataRequest' => [ 'type' => 'structure', 'required' => [ 'CatalogId', 'DatabaseName', 'TableName', 'SupportedPermissionTypes', ], 'members' => [ 'Region' => [ 'shape' => 'ValueString', ], 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'Expression' => [ 'shape' => 'PredicateString', ], 'AuditContext' => [ 'shape' => 'AuditContext', ], 'SupportedPermissionTypes' => [ 'shape' => 'PermissionTypeList', ], 'NextToken' => [ 'shape' => 'Token', ], 'Segment' => [ 'shape' => 'Segment', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'QuerySessionContext' => [ 'shape' => 'QuerySessionContext', ], ], ], 'GetUnfilteredPartitionsMetadataResponse' => [ 'type' => 'structure', 'members' => [ 'UnfilteredPartitions' => [ 'shape' => 'UnfilteredPartitionList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetUnfilteredTableMetadataRequest' => [ 'type' => 'structure', 'required' => [ 'CatalogId', 'DatabaseName', 'Name', 'SupportedPermissionTypes', ], 'members' => [ 'Region' => [ 'shape' => 'ValueString', ], 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'Name' => [ 'shape' => 'NameString', ], 'AuditContext' => [ 'shape' => 'AuditContext', ], 'SupportedPermissionTypes' => [ 'shape' => 'PermissionTypeList', ], 'ParentResourceArn' => [ 'shape' => 'ArnString', ], 'RootResourceArn' => [ 'shape' => 'ArnString', ], 'SupportedDialect' => [ 'shape' => 'SupportedDialect', ], 'Permissions' => [ 'shape' => 'PermissionList', ], 'QuerySessionContext' => [ 'shape' => 'QuerySessionContext', ], ], ], 'GetUnfilteredTableMetadataResponse' => [ 'type' => 'structure', 'members' => [ 'Table' => [ 'shape' => 'Table', ], 'AuthorizedColumns' => [ 'shape' => 'NameStringList', ], 'IsRegisteredWithLakeFormation' => [ 'shape' => 'Boolean', ], 'CellFilters' => [ 'shape' => 'ColumnRowFilterList', ], 'QueryAuthorizationId' => [ 'shape' => 'HashString', ], 'IsMultiDialectView' => [ 'shape' => 'Boolean', ], 'ResourceArn' => [ 'shape' => 'ArnString', ], 'IsProtected' => [ 'shape' => 'Boolean', ], 'Permissions' => [ 'shape' => 'PermissionList', ], 'RowFilter' => [ 'shape' => 'PredicateString', ], ], ], 'GetUsageProfileRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'GetUsageProfileResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'Configuration' => [ 'shape' => 'ProfileConfiguration', ], 'CreatedOn' => [ 'shape' => 'TimestampValue', ], 'LastModifiedOn' => [ 'shape' => 'TimestampValue', ], ], ], 'GetUserDefinedFunctionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'FunctionName', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'FunctionName' => [ 'shape' => 'NameString', ], ], ], 'GetUserDefinedFunctionResponse' => [ 'type' => 'structure', 'members' => [ 'UserDefinedFunction' => [ 'shape' => 'UserDefinedFunction', ], ], ], 'GetUserDefinedFunctionsRequest' => [ 'type' => 'structure', 'required' => [ 'Pattern', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'Pattern' => [ 'shape' => 'NameString', ], 'NextToken' => [ 'shape' => 'Token', ], 'MaxResults' => [ 'shape' => 'CatalogGetterPageSize', ], ], ], 'GetUserDefinedFunctionsResponse' => [ 'type' => 'structure', 'members' => [ 'UserDefinedFunctions' => [ 'shape' => 'UserDefinedFunctionList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetWorkflowRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'IncludeGraph' => [ 'shape' => 'NullableBoolean', ], ], ], 'GetWorkflowResponse' => [ 'type' => 'structure', 'members' => [ 'Workflow' => [ 'shape' => 'Workflow', ], ], ], 'GetWorkflowRunPropertiesRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'RunId', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'RunId' => [ 'shape' => 'IdString', ], ], ], 'GetWorkflowRunPropertiesResponse' => [ 'type' => 'structure', 'members' => [ 'RunProperties' => [ 'shape' => 'WorkflowRunProperties', ], ], ], 'GetWorkflowRunRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'RunId', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'RunId' => [ 'shape' => 'IdString', ], 'IncludeGraph' => [ 'shape' => 'NullableBoolean', ], ], ], 'GetWorkflowRunResponse' => [ 'type' => 'structure', 'members' => [ 'Run' => [ 'shape' => 'WorkflowRun', ], ], ], 'GetWorkflowRunsRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'IncludeGraph' => [ 'shape' => 'NullableBoolean', ], 'NextToken' => [ 'shape' => 'GenericString', ], 'MaxResults' => [ 'shape' => 'PageSize', ], ], ], 'GetWorkflowRunsResponse' => [ 'type' => 'structure', 'members' => [ 'Runs' => [ 'shape' => 'WorkflowRuns', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'GlueEncryptionException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'GluePolicy' => [ 'type' => 'structure', 'members' => [ 'PolicyInJson' => [ 'shape' => 'PolicyJsonString', ], 'PolicyHash' => [ 'shape' => 'HashString', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'GlueRecordType' => [ 'type' => 'string', 'enum' => [ 'DATE', 'STRING', 'TIMESTAMP', 'INT', 'FLOAT', 'LONG', 'BIGDECIMAL', 'BYTE', 'SHORT', 'DOUBLE', ], ], 'GlueResourceArn' => [ 'type' => 'string', 'max' => 10240, 'min' => 1, 'pattern' => 'arn:aws(-(cn|us-gov|iso(-[bef])?))?:glue:.*', ], 'GlueSchema' => [ 'type' => 'structure', 'members' => [ 'Columns' => [ 'shape' => 'GlueStudioSchemaColumnList', ], ], ], 'GlueSchemas' => [ 'type' => 'list', 'member' => [ 'shape' => 'GlueSchema', ], ], 'GlueStudioColumnNameString' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'GlueStudioPathList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EnclosedInStringProperties', ], ], 'GlueStudioSchemaColumn' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'GlueStudioColumnNameString', ], 'Type' => [ 'shape' => 'ColumnTypeString', ], ], ], 'GlueStudioSchemaColumnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GlueStudioSchemaColumn', ], ], 'GlueTable' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', ], 'members' => [ 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'CatalogId' => [ 'shape' => 'NameString', ], 'ConnectionName' => [ 'shape' => 'NameString', ], 'AdditionalOptions' => [ 'shape' => 'GlueTableAdditionalOptions', ], ], ], 'GlueTableAdditionalOptions' => [ 'type' => 'map', 'key' => [ 'shape' => 'NameString', ], 'value' => [ 'shape' => 'DescriptionString', ], 'max' => 10, 'min' => 1, ], 'GlueTables' => [ 'type' => 'list', 'member' => [ 'shape' => 'GlueTable', ], 'max' => 10, 'min' => 0, ], 'GlueVersionString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^(\\w+\\.)+\\w+$', ], 'GovernedCatalogSource' => [ 'type' => 'structure', 'required' => [ 'Name', 'Database', 'Table', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Database' => [ 'shape' => 'EnclosedInStringProperty', ], 'Table' => [ 'shape' => 'EnclosedInStringProperty', ], 'PartitionPredicate' => [ 'shape' => 'EnclosedInStringProperty', ], 'AdditionalOptions' => [ 'shape' => 'S3SourceAdditionalOptions', ], ], ], 'GovernedCatalogTarget' => [ 'type' => 'structure', 'required' => [ 'Name', 'Inputs', 'Table', 'Database', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Inputs' => [ 'shape' => 'OneInput', ], 'PartitionKeys' => [ 'shape' => 'GlueStudioPathList', ], 'Table' => [ 'shape' => 'EnclosedInStringProperty', ], 'Database' => [ 'shape' => 'EnclosedInStringProperty', ], 'SchemaChangePolicy' => [ 'shape' => 'CatalogSchemaChangePolicy', ], ], ], 'GrokClassifier' => [ 'type' => 'structure', 'required' => [ 'Name', 'Classification', 'GrokPattern', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Classification' => [ 'shape' => 'Classification', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastUpdated' => [ 'shape' => 'Timestamp', ], 'Version' => [ 'shape' => 'VersionId', ], 'GrokPattern' => [ 'shape' => 'GrokPattern', ], 'CustomPatterns' => [ 'shape' => 'CustomPatterns', ], ], ], 'GrokPattern' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\t]*', ], 'HashString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'HudiTarget' => [ 'type' => 'structure', 'members' => [ 'Paths' => [ 'shape' => 'PathList', ], 'ConnectionName' => [ 'shape' => 'ConnectionName', ], 'Exclusions' => [ 'shape' => 'PathList', ], 'MaximumTraversalDepth' => [ 'shape' => 'NullableInteger', ], ], ], 'HudiTargetCompressionType' => [ 'type' => 'string', 'enum' => [ 'gzip', 'lzo', 'uncompressed', 'snappy', ], ], 'HudiTargetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'HudiTarget', ], ], 'HyperTargetCompressionType' => [ 'type' => 'string', 'enum' => [ 'uncompressed', ], ], 'IAMRoleArn' => [ 'type' => 'string', 'pattern' => '^arn:aws(-(cn|us-gov|iso(-[bef])?))?:iam::[0-9]{12}:role/.+', ], 'IcebergCompactionConfiguration' => [ 'type' => 'structure', 'members' => [ 'strategy' => [ 'shape' => 'CompactionStrategy', ], ], ], 'IcebergCompactionMetrics' => [ 'type' => 'structure', 'members' => [ 'NumberOfBytesCompacted' => [ 'shape' => 'metricCounts', ], 'NumberOfFilesCompacted' => [ 'shape' => 'metricCounts', ], 'DpuHours' => [ 'shape' => 'dpuHours', ], 'NumberOfDpus' => [ 'shape' => 'dpuCounts', ], 'JobDurationInHour' => [ 'shape' => 'dpuDurationInHour', ], ], ], 'IcebergInput' => [ 'type' => 'structure', 'required' => [ 'MetadataOperation', ], 'members' => [ 'MetadataOperation' => [ 'shape' => 'MetadataOperation', ], 'Version' => [ 'shape' => 'VersionString', ], ], ], 'IcebergOrphanFileDeletionConfiguration' => [ 'type' => 'structure', 'members' => [ 'orphanFileRetentionPeriodInDays' => [ 'shape' => 'NullableInteger', ], 'location' => [ 'shape' => 'MessageString', ], ], ], 'IcebergOrphanFileDeletionMetrics' => [ 'type' => 'structure', 'members' => [ 'NumberOfOrphanFilesDeleted' => [ 'shape' => 'metricCounts', ], 'DpuHours' => [ 'shape' => 'dpuHours', ], 'NumberOfDpus' => [ 'shape' => 'dpuCounts', ], 'JobDurationInHour' => [ 'shape' => 'dpuDurationInHour', ], ], ], 'IcebergRetentionConfiguration' => [ 'type' => 'structure', 'members' => [ 'snapshotRetentionPeriodInDays' => [ 'shape' => 'NullableInteger', ], 'numberOfSnapshotsToRetain' => [ 'shape' => 'NullableInteger', ], 'cleanExpiredFiles' => [ 'shape' => 'NullableBoolean', ], ], ], 'IcebergRetentionMetrics' => [ 'type' => 'structure', 'members' => [ 'NumberOfDataFilesDeleted' => [ 'shape' => 'metricCounts', ], 'NumberOfManifestFilesDeleted' => [ 'shape' => 'metricCounts', ], 'NumberOfManifestListsDeleted' => [ 'shape' => 'metricCounts', ], 'DpuHours' => [ 'shape' => 'dpuHours', ], 'NumberOfDpus' => [ 'shape' => 'dpuCounts', ], 'JobDurationInHour' => [ 'shape' => 'dpuDurationInHour', ], ], ], 'IcebergTarget' => [ 'type' => 'structure', 'members' => [ 'Paths' => [ 'shape' => 'PathList', ], 'ConnectionName' => [ 'shape' => 'ConnectionName', ], 'Exclusions' => [ 'shape' => 'PathList', ], 'MaximumTraversalDepth' => [ 'shape' => 'NullableInteger', ], ], ], 'IcebergTargetCompressionType' => [ 'type' => 'string', 'enum' => [ 'gzip', 'lzo', 'uncompressed', 'snappy', ], ], 'IcebergTargetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IcebergTarget', ], ], 'IdString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'IdempotentParameterMismatchException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'IdleTimeout' => [ 'type' => 'integer', 'box' => true, ], 'IllegalBlueprintStateException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'IllegalSessionStateException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'IllegalWorkflowStateException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'ImportCatalogToGlueRequest' => [ 'type' => 'structure', 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], ], ], 'ImportCatalogToGlueResponse' => [ 'type' => 'structure', 'members' => [], ], 'ImportLabelsTaskRunProperties' => [ 'type' => 'structure', 'members' => [ 'InputS3Path' => [ 'shape' => 'UriString', ], 'Replace' => [ 'shape' => 'ReplaceBoolean', ], ], ], 'InboundIntegration' => [ 'type' => 'structure', 'required' => [ 'SourceArn', 'TargetArn', 'IntegrationArn', 'Status', 'CreateTime', ], 'members' => [ 'SourceArn' => [ 'shape' => 'String128', ], 'TargetArn' => [ 'shape' => 'String128', ], 'IntegrationArn' => [ 'shape' => 'String128', ], 'Status' => [ 'shape' => 'IntegrationStatus', ], 'CreateTime' => [ 'shape' => 'IntegrationTimestamp', ], 'IntegrationConfig' => [ 'shape' => 'IntegrationConfig', ], 'Errors' => [ 'shape' => 'IntegrationErrorList', ], ], ], 'InboundIntegrationsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InboundIntegration', ], ], 'InclusionAnnotationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DatapointInclusionAnnotation', ], ], 'InclusionAnnotationValue' => [ 'type' => 'string', 'enum' => [ 'INCLUDE', 'EXCLUDE', ], ], 'Integer' => [ 'type' => 'integer', ], 'IntegerFlag' => [ 'type' => 'integer', 'max' => 1, 'min' => 0, ], 'IntegerValue' => [ 'type' => 'integer', ], 'Integration' => [ 'type' => 'structure', 'required' => [ 'SourceArn', 'TargetArn', 'IntegrationName', 'IntegrationArn', 'Status', 'CreateTime', ], 'members' => [ 'SourceArn' => [ 'shape' => 'String128', ], 'TargetArn' => [ 'shape' => 'String128', ], 'Description' => [ 'shape' => 'IntegrationDescription', ], 'IntegrationName' => [ 'shape' => 'String128', ], 'IntegrationArn' => [ 'shape' => 'String128', ], 'KmsKeyId' => [ 'shape' => 'String2048', ], 'AdditionalEncryptionContext' => [ 'shape' => 'IntegrationAdditionalEncryptionContextMap', ], 'Tags' => [ 'shape' => 'IntegrationTagsList', ], 'Status' => [ 'shape' => 'IntegrationStatus', ], 'CreateTime' => [ 'shape' => 'IntegrationTimestamp', ], 'IntegrationConfig' => [ 'shape' => 'IntegrationConfig', ], 'Errors' => [ 'shape' => 'IntegrationErrorList', ], 'DataFilter' => [ 'shape' => 'String2048', ], ], ], 'IntegrationAdditionalEncryptionContextMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'IntegrationString', ], 'value' => [ 'shape' => 'IntegrationString', ], ], 'IntegrationConfig' => [ 'type' => 'structure', 'members' => [ 'RefreshInterval' => [ 'shape' => 'String128', ], ], ], 'IntegrationConflictOperationFault' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'IntegrationErrorMessage', ], ], 'exception' => true, ], 'IntegrationDescription' => [ 'type' => 'string', 'max' => 1000, 'pattern' => '[\\S\\s]*', ], 'IntegrationError' => [ 'type' => 'structure', 'members' => [ 'ErrorCode' => [ 'shape' => 'String128', ], 'ErrorMessage' => [ 'shape' => 'String2048', ], ], ], 'IntegrationErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IntegrationError', ], ], 'IntegrationErrorMessage' => [ 'type' => 'string', ], 'IntegrationFilter' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String128', ], 'Values' => [ 'shape' => 'IntegrationFilterValues', ], ], ], 'IntegrationFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IntegrationFilter', ], ], 'IntegrationFilterValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'String128', ], ], 'IntegrationInteger' => [ 'type' => 'integer', ], 'IntegrationNotFoundFault' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'IntegrationErrorMessage', ], ], 'exception' => true, ], 'IntegrationPartition' => [ 'type' => 'structure', 'members' => [ 'FieldName' => [ 'shape' => 'String128', ], 'FunctionSpec' => [ 'shape' => 'String128', ], 'ConversionSpec' => [ 'shape' => 'String128', ], ], ], 'IntegrationPartitionSpecList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IntegrationPartition', ], ], 'IntegrationQuotaExceededFault' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'IntegrationErrorMessage', ], ], 'exception' => true, ], 'IntegrationStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'MODIFYING', 'FAILED', 'DELETING', 'SYNCING', 'NEEDS_ATTENTION', ], ], 'IntegrationString' => [ 'type' => 'string', ], 'IntegrationTagsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], ], 'IntegrationTimestamp' => [ 'type' => 'timestamp', ], 'IntegrationsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Integration', ], ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'IntegrationErrorMessage', ], ], 'exception' => true, ], 'InternalServiceException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, 'fault' => true, ], 'InvalidInputException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], 'FromFederationSource' => [ 'shape' => 'NullableBoolean', ], ], 'exception' => true, ], 'InvalidIntegrationStateFault' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'IntegrationErrorMessage', ], ], 'exception' => true, ], 'InvalidStateException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'IsParentEntity' => [ 'type' => 'boolean', 'box' => true, ], 'IsVersionValid' => [ 'type' => 'boolean', ], 'Iso8601DateTime' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'JDBCConnectionType' => [ 'type' => 'string', 'enum' => [ 'sqlserver', 'mysql', 'oracle', 'postgresql', 'redshift', ], ], 'JDBCConnectorOptions' => [ 'type' => 'structure', 'members' => [ 'FilterPredicate' => [ 'shape' => 'EnclosedInStringProperty', ], 'PartitionColumn' => [ 'shape' => 'EnclosedInStringProperty', ], 'LowerBound' => [ 'shape' => 'BoxedNonNegativeLong', ], 'UpperBound' => [ 'shape' => 'BoxedNonNegativeLong', ], 'NumPartitions' => [ 'shape' => 'BoxedNonNegativeLong', ], 'JobBookmarkKeys' => [ 'shape' => 'EnclosedInStringProperties', ], 'JobBookmarkKeysSortOrder' => [ 'shape' => 'EnclosedInStringProperty', ], 'DataTypeMapping' => [ 'shape' => 'JDBCDataTypeMapping', ], ], ], 'JDBCConnectorSource' => [ 'type' => 'structure', 'required' => [ 'Name', 'ConnectionName', 'ConnectorName', 'ConnectionType', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'ConnectionName' => [ 'shape' => 'EnclosedInStringProperty', ], 'ConnectorName' => [ 'shape' => 'EnclosedInStringProperty', ], 'ConnectionType' => [ 'shape' => 'EnclosedInStringProperty', ], 'AdditionalOptions' => [ 'shape' => 'JDBCConnectorOptions', ], 'ConnectionTable' => [ 'shape' => 'EnclosedInStringPropertyWithQuote', ], 'Query' => [ 'shape' => 'SqlQuery', ], 'OutputSchemas' => [ 'shape' => 'GlueSchemas', ], ], ], 'JDBCConnectorTarget' => [ 'type' => 'structure', 'required' => [ 'Name', 'Inputs', 'ConnectionName', 'ConnectionTable', 'ConnectorName', 'ConnectionType', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Inputs' => [ 'shape' => 'OneInput', ], 'ConnectionName' => [ 'shape' => 'EnclosedInStringProperty', ], 'ConnectionTable' => [ 'shape' => 'EnclosedInStringPropertyWithQuote', ], 'ConnectorName' => [ 'shape' => 'EnclosedInStringProperty', ], 'ConnectionType' => [ 'shape' => 'EnclosedInStringProperty', ], 'AdditionalOptions' => [ 'shape' => 'AdditionalOptions', ], 'OutputSchemas' => [ 'shape' => 'GlueSchemas', ], ], ], 'JDBCDataType' => [ 'type' => 'string', 'enum' => [ 'ARRAY', 'BIGINT', 'BINARY', 'BIT', 'BLOB', 'BOOLEAN', 'CHAR', 'CLOB', 'DATALINK', 'DATE', 'DECIMAL', 'DISTINCT', 'DOUBLE', 'FLOAT', 'INTEGER', 'JAVA_OBJECT', 'LONGNVARCHAR', 'LONGVARBINARY', 'LONGVARCHAR', 'NCHAR', 'NCLOB', 'NULL', 'NUMERIC', 'NVARCHAR', 'OTHER', 'REAL', 'REF', 'REF_CURSOR', 'ROWID', 'SMALLINT', 'SQLXML', 'STRUCT', 'TIME', 'TIME_WITH_TIMEZONE', 'TIMESTAMP', 'TIMESTAMP_WITH_TIMEZONE', 'TINYINT', 'VARBINARY', 'VARCHAR', ], ], 'JDBCDataTypeMapping' => [ 'type' => 'map', 'key' => [ 'shape' => 'JDBCDataType', ], 'value' => [ 'shape' => 'GlueRecordType', ], ], 'JdbcMetadataEntry' => [ 'type' => 'string', 'enum' => [ 'COMMENTS', 'RAWTYPES', ], ], 'JdbcTarget' => [ 'type' => 'structure', 'members' => [ 'ConnectionName' => [ 'shape' => 'ConnectionName', ], 'Path' => [ 'shape' => 'Path', ], 'Exclusions' => [ 'shape' => 'PathList', ], 'EnableAdditionalMetadata' => [ 'shape' => 'EnableAdditionalMetadata', ], ], ], 'JdbcTargetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'JdbcTarget', ], ], 'Job' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'JobMode' => [ 'shape' => 'JobMode', ], 'JobRunQueuingEnabled' => [ 'shape' => 'NullableBoolean', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'LogUri' => [ 'shape' => 'UriString', ], 'Role' => [ 'shape' => 'RoleString', ], 'CreatedOn' => [ 'shape' => 'TimestampValue', ], 'LastModifiedOn' => [ 'shape' => 'TimestampValue', ], 'ExecutionProperty' => [ 'shape' => 'ExecutionProperty', ], 'Command' => [ 'shape' => 'JobCommand', ], 'DefaultArguments' => [ 'shape' => 'GenericMap', ], 'NonOverridableArguments' => [ 'shape' => 'GenericMap', ], 'Connections' => [ 'shape' => 'ConnectionsList', ], 'MaxRetries' => [ 'shape' => 'MaxRetries', ], 'AllocatedCapacity' => [ 'shape' => 'IntegerValue', 'deprecated' => true, 'deprecatedMessage' => 'This property is deprecated, use MaxCapacity instead.', ], 'Timeout' => [ 'shape' => 'Timeout', ], 'MaxCapacity' => [ 'shape' => 'NullableDouble', ], 'WorkerType' => [ 'shape' => 'WorkerType', ], 'NumberOfWorkers' => [ 'shape' => 'NullableInteger', ], 'SecurityConfiguration' => [ 'shape' => 'NameString', ], 'NotificationProperty' => [ 'shape' => 'NotificationProperty', ], 'GlueVersion' => [ 'shape' => 'GlueVersionString', ], 'CodeGenConfigurationNodes' => [ 'shape' => 'CodeGenConfigurationNodes', ], 'ExecutionClass' => [ 'shape' => 'ExecutionClass', ], 'SourceControlDetails' => [ 'shape' => 'SourceControlDetails', ], 'MaintenanceWindow' => [ 'shape' => 'MaintenanceWindow', ], 'ProfileName' => [ 'shape' => 'NameString', ], ], ], 'JobBookmarkEntry' => [ 'type' => 'structure', 'members' => [ 'JobName' => [ 'shape' => 'JobName', ], 'Version' => [ 'shape' => 'IntegerValue', ], 'Run' => [ 'shape' => 'IntegerValue', ], 'Attempt' => [ 'shape' => 'IntegerValue', ], 'PreviousRunId' => [ 'shape' => 'RunId', ], 'RunId' => [ 'shape' => 'RunId', ], 'JobBookmark' => [ 'shape' => 'JsonValue', ], ], ], 'JobBookmarksEncryption' => [ 'type' => 'structure', 'members' => [ 'JobBookmarksEncryptionMode' => [ 'shape' => 'JobBookmarksEncryptionMode', ], 'KmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], ], ], 'JobBookmarksEncryptionMode' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'CSE-KMS', ], ], 'JobCommand' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'GenericString', ], 'ScriptLocation' => [ 'shape' => 'ScriptLocationString', ], 'PythonVersion' => [ 'shape' => 'PythonVersionString', ], 'Runtime' => [ 'shape' => 'RuntimeNameString', ], ], ], 'JobList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Job', ], ], 'JobMode' => [ 'type' => 'string', 'enum' => [ 'SCRIPT', 'VISUAL', 'NOTEBOOK', ], ], 'JobName' => [ 'type' => 'string', ], 'JobNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], ], 'JobNodeDetails' => [ 'type' => 'structure', 'members' => [ 'JobRuns' => [ 'shape' => 'JobRunList', ], ], ], 'JobRun' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'IdString', ], 'Attempt' => [ 'shape' => 'AttemptCount', ], 'PreviousRunId' => [ 'shape' => 'IdString', ], 'TriggerName' => [ 'shape' => 'NameString', ], 'JobName' => [ 'shape' => 'NameString', ], 'JobMode' => [ 'shape' => 'JobMode', ], 'JobRunQueuingEnabled' => [ 'shape' => 'NullableBoolean', ], 'StartedOn' => [ 'shape' => 'TimestampValue', ], 'LastModifiedOn' => [ 'shape' => 'TimestampValue', ], 'CompletedOn' => [ 'shape' => 'TimestampValue', ], 'JobRunState' => [ 'shape' => 'JobRunState', ], 'Arguments' => [ 'shape' => 'GenericMap', ], 'ErrorMessage' => [ 'shape' => 'ErrorString', ], 'PredecessorRuns' => [ 'shape' => 'PredecessorList', ], 'AllocatedCapacity' => [ 'shape' => 'IntegerValue', 'deprecated' => true, 'deprecatedMessage' => 'This property is deprecated, use MaxCapacity instead.', ], 'ExecutionTime' => [ 'shape' => 'ExecutionTime', ], 'Timeout' => [ 'shape' => 'Timeout', ], 'MaxCapacity' => [ 'shape' => 'NullableDouble', ], 'WorkerType' => [ 'shape' => 'WorkerType', ], 'NumberOfWorkers' => [ 'shape' => 'NullableInteger', ], 'SecurityConfiguration' => [ 'shape' => 'NameString', ], 'LogGroupName' => [ 'shape' => 'GenericString', ], 'NotificationProperty' => [ 'shape' => 'NotificationProperty', ], 'GlueVersion' => [ 'shape' => 'GlueVersionString', ], 'DPUSeconds' => [ 'shape' => 'NullableDouble', ], 'ExecutionClass' => [ 'shape' => 'ExecutionClass', ], 'MaintenanceWindow' => [ 'shape' => 'MaintenanceWindow', ], 'ProfileName' => [ 'shape' => 'NameString', ], 'StateDetail' => [ 'shape' => 'OrchestrationMessageString', ], ], ], 'JobRunList' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobRun', ], ], 'JobRunState' => [ 'type' => 'string', 'enum' => [ 'STARTING', 'RUNNING', 'STOPPING', 'STOPPED', 'SUCCEEDED', 'FAILED', 'TIMEOUT', 'ERROR', 'WAITING', 'EXPIRED', ], ], 'JobUpdate' => [ 'type' => 'structure', 'members' => [ 'JobMode' => [ 'shape' => 'JobMode', ], 'JobRunQueuingEnabled' => [ 'shape' => 'NullableBoolean', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'LogUri' => [ 'shape' => 'UriString', ], 'Role' => [ 'shape' => 'RoleString', ], 'ExecutionProperty' => [ 'shape' => 'ExecutionProperty', ], 'Command' => [ 'shape' => 'JobCommand', ], 'DefaultArguments' => [ 'shape' => 'GenericMap', ], 'NonOverridableArguments' => [ 'shape' => 'GenericMap', ], 'Connections' => [ 'shape' => 'ConnectionsList', ], 'MaxRetries' => [ 'shape' => 'MaxRetries', ], 'AllocatedCapacity' => [ 'shape' => 'IntegerValue', 'deprecated' => true, 'deprecatedMessage' => 'This property is deprecated, use MaxCapacity instead.', ], 'Timeout' => [ 'shape' => 'Timeout', ], 'MaxCapacity' => [ 'shape' => 'NullableDouble', ], 'WorkerType' => [ 'shape' => 'WorkerType', ], 'NumberOfWorkers' => [ 'shape' => 'NullableInteger', ], 'SecurityConfiguration' => [ 'shape' => 'NameString', ], 'NotificationProperty' => [ 'shape' => 'NotificationProperty', ], 'GlueVersion' => [ 'shape' => 'GlueVersionString', ], 'CodeGenConfigurationNodes' => [ 'shape' => 'CodeGenConfigurationNodes', ], 'ExecutionClass' => [ 'shape' => 'ExecutionClass', ], 'SourceControlDetails' => [ 'shape' => 'SourceControlDetails', ], 'MaintenanceWindow' => [ 'shape' => 'MaintenanceWindow', ], ], ], 'Join' => [ 'type' => 'structure', 'required' => [ 'Name', 'Inputs', 'JoinType', 'Columns', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Inputs' => [ 'shape' => 'TwoInputs', ], 'JoinType' => [ 'shape' => 'JoinType', ], 'Columns' => [ 'shape' => 'JoinColumns', ], ], ], 'JoinColumn' => [ 'type' => 'structure', 'required' => [ 'From', 'Keys', ], 'members' => [ 'From' => [ 'shape' => 'EnclosedInStringProperty', ], 'Keys' => [ 'shape' => 'GlueStudioPathList', ], ], ], 'JoinColumns' => [ 'type' => 'list', 'member' => [ 'shape' => 'JoinColumn', ], 'max' => 2, 'min' => 2, ], 'JoinType' => [ 'type' => 'string', 'enum' => [ 'equijoin', 'left', 'right', 'outer', 'leftsemi', 'leftanti', ], ], 'JsonClassifier' => [ 'type' => 'structure', 'required' => [ 'Name', 'JsonPath', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastUpdated' => [ 'shape' => 'Timestamp', ], 'Version' => [ 'shape' => 'VersionId', ], 'JsonPath' => [ 'shape' => 'JsonPath', ], ], ], 'JsonPath' => [ 'type' => 'string', ], 'JsonValue' => [ 'type' => 'string', ], 'JwtToken' => [ 'type' => 'string', 'max' => 8000, 'pattern' => '^([a-zA-Z0-9_=]+)\\.([a-zA-Z0-9_=]+)\\.([a-zA-Z0-9_\\-\\+\\/=]*)', 'sensitive' => true, ], 'KMSKeyNotAccessibleFault' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'IntegrationErrorMessage', ], ], 'exception' => true, ], 'KafkaStreamingSourceOptions' => [ 'type' => 'structure', 'members' => [ 'BootstrapServers' => [ 'shape' => 'EnclosedInStringProperty', ], 'SecurityProtocol' => [ 'shape' => 'EnclosedInStringProperty', ], 'ConnectionName' => [ 'shape' => 'EnclosedInStringProperty', ], 'TopicName' => [ 'shape' => 'EnclosedInStringProperty', ], 'Assign' => [ 'shape' => 'EnclosedInStringProperty', ], 'SubscribePattern' => [ 'shape' => 'EnclosedInStringProperty', ], 'Classification' => [ 'shape' => 'EnclosedInStringProperty', ], 'Delimiter' => [ 'shape' => 'EnclosedInStringProperty', ], 'StartingOffsets' => [ 'shape' => 'EnclosedInStringProperty', ], 'EndingOffsets' => [ 'shape' => 'EnclosedInStringProperty', ], 'PollTimeoutMs' => [ 'shape' => 'BoxedNonNegativeLong', ], 'NumRetries' => [ 'shape' => 'BoxedNonNegativeInt', ], 'RetryIntervalMs' => [ 'shape' => 'BoxedNonNegativeLong', ], 'MaxOffsetsPerTrigger' => [ 'shape' => 'BoxedNonNegativeLong', ], 'MinPartitions' => [ 'shape' => 'BoxedNonNegativeInt', ], 'IncludeHeaders' => [ 'shape' => 'BoxedBoolean', ], 'AddRecordTimestamp' => [ 'shape' => 'EnclosedInStringProperty', ], 'EmitConsumerLagMetrics' => [ 'shape' => 'EnclosedInStringProperty', ], 'StartingTimestamp' => [ 'shape' => 'Iso8601DateTime', ], ], ], 'KeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], 'min' => 1, ], 'KeySchemaElement' => [ 'type' => 'structure', 'required' => [ 'Name', 'Type', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Type' => [ 'shape' => 'ColumnTypeString', ], ], ], 'KeySchemaElementList' => [ 'type' => 'list', 'member' => [ 'shape' => 'KeySchemaElement', ], 'min' => 1, ], 'KeyString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'KinesisStreamingSourceOptions' => [ 'type' => 'structure', 'members' => [ 'EndpointUrl' => [ 'shape' => 'EnclosedInStringProperty', ], 'StreamName' => [ 'shape' => 'EnclosedInStringProperty', ], 'Classification' => [ 'shape' => 'EnclosedInStringProperty', ], 'Delimiter' => [ 'shape' => 'EnclosedInStringProperty', ], 'StartingPosition' => [ 'shape' => 'StartingPosition', ], 'MaxFetchTimeInMs' => [ 'shape' => 'BoxedNonNegativeLong', ], 'MaxFetchRecordsPerShard' => [ 'shape' => 'BoxedNonNegativeLong', ], 'MaxRecordPerRead' => [ 'shape' => 'BoxedNonNegativeLong', ], 'AddIdleTimeBetweenReads' => [ 'shape' => 'BoxedBoolean', ], 'IdleTimeBetweenReadsInMs' => [ 'shape' => 'BoxedNonNegativeLong', ], 'DescribeShardInterval' => [ 'shape' => 'BoxedNonNegativeLong', ], 'NumRetries' => [ 'shape' => 'BoxedNonNegativeInt', ], 'RetryIntervalMs' => [ 'shape' => 'BoxedNonNegativeLong', ], 'MaxRetryIntervalMs' => [ 'shape' => 'BoxedNonNegativeLong', ], 'AvoidEmptyBatches' => [ 'shape' => 'BoxedBoolean', ], 'StreamArn' => [ 'shape' => 'EnclosedInStringProperty', ], 'RoleArn' => [ 'shape' => 'EnclosedInStringProperty', ], 'RoleSessionName' => [ 'shape' => 'EnclosedInStringProperty', ], 'AddRecordTimestamp' => [ 'shape' => 'EnclosedInStringProperty', ], 'EmitConsumerLagMetrics' => [ 'shape' => 'EnclosedInStringProperty', ], 'StartingTimestamp' => [ 'shape' => 'Iso8601DateTime', ], ], ], 'KmsKeyArn' => [ 'type' => 'string', 'pattern' => 'arn:aws:kms:.*', ], 'LabelCount' => [ 'type' => 'integer', ], 'LabelingSetGenerationTaskRunProperties' => [ 'type' => 'structure', 'members' => [ 'OutputS3Path' => [ 'shape' => 'UriString', ], ], ], 'LakeFormationConfiguration' => [ 'type' => 'structure', 'members' => [ 'UseLakeFormationCredentials' => [ 'shape' => 'NullableBoolean', ], 'AccountId' => [ 'shape' => 'AccountId', ], ], ], 'Language' => [ 'type' => 'string', 'enum' => [ 'PYTHON', 'SCALA', ], ], 'LastActiveDefinition' => [ 'type' => 'structure', 'members' => [ 'Description' => [ 'shape' => 'Generic512CharString', ], 'LastModifiedOn' => [ 'shape' => 'TimestampValue', ], 'ParameterSpec' => [ 'shape' => 'BlueprintParameterSpec', ], 'BlueprintLocation' => [ 'shape' => 'GenericString', ], 'BlueprintServiceLocation' => [ 'shape' => 'GenericString', ], ], ], 'LastCrawlInfo' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'LastCrawlStatus', ], 'ErrorMessage' => [ 'shape' => 'DescriptionString', ], 'LogGroup' => [ 'shape' => 'LogGroup', ], 'LogStream' => [ 'shape' => 'LogStream', ], 'MessagePrefix' => [ 'shape' => 'MessagePrefix', ], 'StartTime' => [ 'shape' => 'Timestamp', ], ], ], 'LastCrawlStatus' => [ 'type' => 'string', 'enum' => [ 'SUCCEEDED', 'CANCELLED', 'FAILED', ], ], 'LatestSchemaVersionBoolean' => [ 'type' => 'boolean', ], 'Limit' => [ 'type' => 'long', 'box' => true, 'max' => 1000, 'min' => 1, ], 'LimitedPathList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LimitedStringList', ], ], 'LimitedStringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GenericLimitedString', ], ], 'LineageConfiguration' => [ 'type' => 'structure', 'members' => [ 'CrawlerLineageSettings' => [ 'shape' => 'CrawlerLineageSettings', ], ], ], 'ListBlueprintsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'GenericString', ], 'MaxResults' => [ 'shape' => 'OrchestrationPageSize25', ], 'Tags' => [ 'shape' => 'TagsMap', ], ], ], 'ListBlueprintsResponse' => [ 'type' => 'structure', 'members' => [ 'Blueprints' => [ 'shape' => 'BlueprintNames', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'ListColumnStatisticsTaskRunsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'PageSize', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListColumnStatisticsTaskRunsResponse' => [ 'type' => 'structure', 'members' => [ 'ColumnStatisticsTaskRunIds' => [ 'shape' => 'ColumnStatisticsTaskRunIdList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListConnectionTypesRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListConnectionTypesResponse' => [ 'type' => 'structure', 'members' => [ 'ConnectionTypes' => [ 'shape' => 'ConnectionTypeList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListCrawlersRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'PageSize', ], 'NextToken' => [ 'shape' => 'Token', ], 'Tags' => [ 'shape' => 'TagsMap', ], ], ], 'ListCrawlersResponse' => [ 'type' => 'structure', 'members' => [ 'CrawlerNames' => [ 'shape' => 'CrawlerNameList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListCrawlsRequest' => [ 'type' => 'structure', 'required' => [ 'CrawlerName', ], 'members' => [ 'CrawlerName' => [ 'shape' => 'NameString', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'Filters' => [ 'shape' => 'CrawlsFilterList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListCrawlsResponse' => [ 'type' => 'structure', 'members' => [ 'Crawls' => [ 'shape' => 'CrawlerHistoryList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListCustomEntityTypesRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'PaginationToken', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'Tags' => [ 'shape' => 'TagsMap', ], ], ], 'ListCustomEntityTypesResponse' => [ 'type' => 'structure', 'members' => [ 'CustomEntityTypes' => [ 'shape' => 'CustomEntityTypes', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListDataQualityResultsRequest' => [ 'type' => 'structure', 'members' => [ 'Filter' => [ 'shape' => 'DataQualityResultFilterCriteria', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'MaxResults' => [ 'shape' => 'PageSize', ], ], ], 'ListDataQualityResultsResponse' => [ 'type' => 'structure', 'required' => [ 'Results', ], 'members' => [ 'Results' => [ 'shape' => 'DataQualityResultDescriptionList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListDataQualityRuleRecommendationRunsRequest' => [ 'type' => 'structure', 'members' => [ 'Filter' => [ 'shape' => 'DataQualityRuleRecommendationRunFilter', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'MaxResults' => [ 'shape' => 'PageSize', ], ], ], 'ListDataQualityRuleRecommendationRunsResponse' => [ 'type' => 'structure', 'members' => [ 'Runs' => [ 'shape' => 'DataQualityRuleRecommendationRunList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListDataQualityRulesetEvaluationRunsRequest' => [ 'type' => 'structure', 'members' => [ 'Filter' => [ 'shape' => 'DataQualityRulesetEvaluationRunFilter', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'MaxResults' => [ 'shape' => 'PageSize', ], ], ], 'ListDataQualityRulesetEvaluationRunsResponse' => [ 'type' => 'structure', 'members' => [ 'Runs' => [ 'shape' => 'DataQualityRulesetEvaluationRunList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListDataQualityRulesetsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'PaginationToken', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'Filter' => [ 'shape' => 'DataQualityRulesetFilterCriteria', ], 'Tags' => [ 'shape' => 'TagsMap', ], ], ], 'ListDataQualityRulesetsResponse' => [ 'type' => 'structure', 'members' => [ 'Rulesets' => [ 'shape' => 'DataQualityRulesetList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListDataQualityStatisticAnnotationsRequest' => [ 'type' => 'structure', 'members' => [ 'StatisticId' => [ 'shape' => 'HashString', ], 'ProfileId' => [ 'shape' => 'HashString', ], 'TimestampFilter' => [ 'shape' => 'TimestampFilter', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListDataQualityStatisticAnnotationsResponse' => [ 'type' => 'structure', 'members' => [ 'Annotations' => [ 'shape' => 'AnnotationList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListDataQualityStatisticsRequest' => [ 'type' => 'structure', 'members' => [ 'StatisticId' => [ 'shape' => 'HashString', ], 'ProfileId' => [ 'shape' => 'HashString', ], 'TimestampFilter' => [ 'shape' => 'TimestampFilter', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListDataQualityStatisticsResponse' => [ 'type' => 'structure', 'members' => [ 'Statistics' => [ 'shape' => 'StatisticSummaryList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListDevEndpointsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'GenericString', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'Tags' => [ 'shape' => 'TagsMap', ], ], ], 'ListDevEndpointsResponse' => [ 'type' => 'structure', 'members' => [ 'DevEndpointNames' => [ 'shape' => 'DevEndpointNameList', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'ListEntitiesRequest' => [ 'type' => 'structure', 'members' => [ 'ConnectionName' => [ 'shape' => 'NameString', ], 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'ParentEntityName' => [ 'shape' => 'EntityName', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'DataStoreApiVersion' => [ 'shape' => 'ApiVersion', ], ], ], 'ListEntitiesResponse' => [ 'type' => 'structure', 'members' => [ 'Entities' => [ 'shape' => 'EntityList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListJobsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'GenericString', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'Tags' => [ 'shape' => 'TagsMap', ], ], ], 'ListJobsResponse' => [ 'type' => 'structure', 'members' => [ 'JobNames' => [ 'shape' => 'JobNameList', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'ListMLTransformsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'PaginationToken', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'Filter' => [ 'shape' => 'TransformFilterCriteria', ], 'Sort' => [ 'shape' => 'TransformSortCriteria', ], 'Tags' => [ 'shape' => 'TagsMap', ], ], ], 'ListMLTransformsResponse' => [ 'type' => 'structure', 'required' => [ 'TransformIds', ], 'members' => [ 'TransformIds' => [ 'shape' => 'TransformIdList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListOfString' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'ListRegistriesInput' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResultsNumber', ], 'NextToken' => [ 'shape' => 'SchemaRegistryTokenString', ], ], ], 'ListRegistriesResponse' => [ 'type' => 'structure', 'members' => [ 'Registries' => [ 'shape' => 'RegistryListDefinition', ], 'NextToken' => [ 'shape' => 'SchemaRegistryTokenString', ], ], ], 'ListSchemaVersionsInput' => [ 'type' => 'structure', 'required' => [ 'SchemaId', ], 'members' => [ 'SchemaId' => [ 'shape' => 'SchemaId', ], 'MaxResults' => [ 'shape' => 'MaxResultsNumber', ], 'NextToken' => [ 'shape' => 'SchemaRegistryTokenString', ], ], ], 'ListSchemaVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'Schemas' => [ 'shape' => 'SchemaVersionList', ], 'NextToken' => [ 'shape' => 'SchemaRegistryTokenString', ], ], ], 'ListSchemasInput' => [ 'type' => 'structure', 'members' => [ 'RegistryId' => [ 'shape' => 'RegistryId', ], 'MaxResults' => [ 'shape' => 'MaxResultsNumber', ], 'NextToken' => [ 'shape' => 'SchemaRegistryTokenString', ], ], ], 'ListSchemasResponse' => [ 'type' => 'structure', 'members' => [ 'Schemas' => [ 'shape' => 'SchemaListDefinition', ], 'NextToken' => [ 'shape' => 'SchemaRegistryTokenString', ], ], ], 'ListSessionsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'OrchestrationToken', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'Tags' => [ 'shape' => 'TagsMap', ], 'RequestOrigin' => [ 'shape' => 'OrchestrationNameString', ], ], ], 'ListSessionsResponse' => [ 'type' => 'structure', 'members' => [ 'Ids' => [ 'shape' => 'SessionIdList', ], 'Sessions' => [ 'shape' => 'SessionList', ], 'NextToken' => [ 'shape' => 'OrchestrationToken', ], ], ], 'ListStatementsRequest' => [ 'type' => 'structure', 'required' => [ 'SessionId', ], 'members' => [ 'SessionId' => [ 'shape' => 'NameString', ], 'RequestOrigin' => [ 'shape' => 'OrchestrationNameString', ], 'NextToken' => [ 'shape' => 'OrchestrationToken', ], ], ], 'ListStatementsResponse' => [ 'type' => 'structure', 'members' => [ 'Statements' => [ 'shape' => 'StatementList', ], 'NextToken' => [ 'shape' => 'OrchestrationToken', ], ], ], 'ListTableOptimizerRunsRequest' => [ 'type' => 'structure', 'required' => [ 'CatalogId', 'DatabaseName', 'TableName', 'Type', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'Type' => [ 'shape' => 'TableOptimizerType', ], 'MaxResults' => [ 'shape' => 'MaxListTableOptimizerRunsTokenResults', ], 'NextToken' => [ 'shape' => 'ListTableOptimizerRunsToken', ], ], ], 'ListTableOptimizerRunsResponse' => [ 'type' => 'structure', 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'NextToken' => [ 'shape' => 'ListTableOptimizerRunsToken', ], 'TableOptimizerRuns' => [ 'shape' => 'TableOptimizerRuns', ], ], ], 'ListTableOptimizerRunsToken' => [ 'type' => 'string', ], 'ListTriggersRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'GenericString', ], 'DependentJobName' => [ 'shape' => 'NameString', ], 'MaxResults' => [ 'shape' => 'OrchestrationPageSize200', ], 'Tags' => [ 'shape' => 'TagsMap', ], ], ], 'ListTriggersResponse' => [ 'type' => 'structure', 'members' => [ 'TriggerNames' => [ 'shape' => 'TriggerNameList', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'ListUsageProfilesRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'OrchestrationToken', ], 'MaxResults' => [ 'shape' => 'OrchestrationPageSize200', ], ], ], 'ListUsageProfilesResponse' => [ 'type' => 'structure', 'members' => [ 'Profiles' => [ 'shape' => 'UsageProfileDefinitionList', ], 'NextToken' => [ 'shape' => 'OrchestrationToken', ], ], ], 'ListWorkflowsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'GenericString', ], 'MaxResults' => [ 'shape' => 'OrchestrationPageSize25', ], ], ], 'ListWorkflowsResponse' => [ 'type' => 'structure', 'members' => [ 'Workflows' => [ 'shape' => 'WorkflowNames', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'Location' => [ 'type' => 'structure', 'members' => [ 'Jdbc' => [ 'shape' => 'CodeGenNodeArgs', ], 'S3' => [ 'shape' => 'CodeGenNodeArgs', ], 'DynamoDB' => [ 'shape' => 'CodeGenNodeArgs', ], ], ], 'LocationMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ColumnValuesString', ], 'value' => [ 'shape' => 'ColumnValuesString', ], ], 'LocationString' => [ 'type' => 'string', 'max' => 2056, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]*', ], 'LocationStringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LocationString', ], ], 'LogGroup' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '[\\.\\-_/#A-Za-z0-9]+', ], 'LogStream' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '[^:*]*', ], 'Logical' => [ 'type' => 'string', 'enum' => [ 'AND', 'ANY', ], ], 'LogicalOperator' => [ 'type' => 'string', 'enum' => [ 'EQUALS', ], ], 'Long' => [ 'type' => 'long', ], 'LongColumnStatisticsData' => [ 'type' => 'structure', 'required' => [ 'NumberOfNulls', 'NumberOfDistinctValues', ], 'members' => [ 'MinimumValue' => [ 'shape' => 'Long', ], 'MaximumValue' => [ 'shape' => 'Long', ], 'NumberOfNulls' => [ 'shape' => 'NonNegativeLong', ], 'NumberOfDistinctValues' => [ 'shape' => 'NonNegativeLong', ], ], ], 'LongValue' => [ 'type' => 'long', ], 'LongValueString' => [ 'type' => 'string', 'max' => 16384, 'min' => 1, ], 'MLTransform' => [ 'type' => 'structure', 'members' => [ 'TransformId' => [ 'shape' => 'HashString', ], 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'Status' => [ 'shape' => 'TransformStatusType', ], 'CreatedOn' => [ 'shape' => 'Timestamp', ], 'LastModifiedOn' => [ 'shape' => 'Timestamp', ], 'InputRecordTables' => [ 'shape' => 'GlueTables', ], 'Parameters' => [ 'shape' => 'TransformParameters', ], 'EvaluationMetrics' => [ 'shape' => 'EvaluationMetrics', ], 'LabelCount' => [ 'shape' => 'LabelCount', ], 'Schema' => [ 'shape' => 'TransformSchema', ], 'Role' => [ 'shape' => 'RoleString', ], 'GlueVersion' => [ 'shape' => 'GlueVersionString', ], 'MaxCapacity' => [ 'shape' => 'NullableDouble', ], 'WorkerType' => [ 'shape' => 'WorkerType', ], 'NumberOfWorkers' => [ 'shape' => 'NullableInteger', ], 'Timeout' => [ 'shape' => 'Timeout', ], 'MaxRetries' => [ 'shape' => 'NullableInteger', ], 'TransformEncryption' => [ 'shape' => 'TransformEncryption', ], ], ], 'MLTransformNotReadyException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'MLUserDataEncryption' => [ 'type' => 'structure', 'required' => [ 'MlUserDataEncryptionMode', ], 'members' => [ 'MlUserDataEncryptionMode' => [ 'shape' => 'MLUserDataEncryptionModeString', ], 'KmsKeyId' => [ 'shape' => 'NameString', ], ], ], 'MLUserDataEncryptionModeString' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'SSE-KMS', ], ], 'MaintenanceWindow' => [ 'type' => 'string', 'pattern' => '^(Sun|Mon|Tue|Wed|Thu|Fri|Sat):([01]?[0-9]|2[0-3])$', ], 'ManyInputs' => [ 'type' => 'list', 'member' => [ 'shape' => 'NodeId', ], 'min' => 1, ], 'MapValue' => [ 'type' => 'map', 'key' => [ 'shape' => 'GenericString', ], 'value' => [ 'shape' => 'GenericString', ], 'max' => 100, 'min' => 0, ], 'Mapping' => [ 'type' => 'structure', 'members' => [ 'ToKey' => [ 'shape' => 'EnclosedInStringProperty', ], 'FromPath' => [ 'shape' => 'EnclosedInStringProperties', ], 'FromType' => [ 'shape' => 'EnclosedInStringProperty', ], 'ToType' => [ 'shape' => 'EnclosedInStringProperty', ], 'Dropped' => [ 'shape' => 'BoxedBoolean', ], 'Children' => [ 'shape' => 'Mappings', ], ], ], 'MappingEntry' => [ 'type' => 'structure', 'members' => [ 'SourceTable' => [ 'shape' => 'TableName', ], 'SourcePath' => [ 'shape' => 'SchemaPathString', ], 'SourceType' => [ 'shape' => 'FieldType', ], 'TargetTable' => [ 'shape' => 'TableName', ], 'TargetPath' => [ 'shape' => 'SchemaPathString', ], 'TargetType' => [ 'shape' => 'FieldType', ], ], ], 'MappingList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MappingEntry', ], ], 'Mappings' => [ 'type' => 'list', 'member' => [ 'shape' => 'Mapping', ], ], 'MaskValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '[*A-Za-z0-9_-]*', ], 'MatchCriteria' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], 'max' => 10, 'min' => 0, ], 'MaxConcurrentRuns' => [ 'type' => 'integer', ], 'MaxListTableOptimizerRunsTokenResults' => [ 'type' => 'integer', ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MaxResultsNumber' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MaxRetries' => [ 'type' => 'integer', ], 'Merge' => [ 'type' => 'structure', 'required' => [ 'Name', 'Inputs', 'Source', 'PrimaryKeys', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Inputs' => [ 'shape' => 'TwoInputs', ], 'Source' => [ 'shape' => 'NodeId', ], 'PrimaryKeys' => [ 'shape' => 'GlueStudioPathList', ], ], ], 'MessagePrefix' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'MessageString' => [ 'type' => 'string', ], 'MetadataInfo' => [ 'type' => 'structure', 'members' => [ 'MetadataValue' => [ 'shape' => 'MetadataValueString', ], 'CreatedTime' => [ 'shape' => 'CreatedTimestamp', ], 'OtherMetadataValueList' => [ 'shape' => 'OtherMetadataValueList', ], ], ], 'MetadataInfoMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'MetadataKeyString', ], 'value' => [ 'shape' => 'MetadataInfo', ], ], 'MetadataKeyString' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9+-=._./@]+', ], 'MetadataKeyValuePair' => [ 'type' => 'structure', 'members' => [ 'MetadataKey' => [ 'shape' => 'MetadataKeyString', ], 'MetadataValue' => [ 'shape' => 'MetadataValueString', ], ], ], 'MetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetadataKeyValuePair', ], ], 'MetadataOperation' => [ 'type' => 'string', 'enum' => [ 'CREATE', ], ], 'MetadataValueString' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[a-zA-Z0-9+-=._./@]+', ], 'MetricBasedObservation' => [ 'type' => 'structure', 'members' => [ 'MetricName' => [ 'shape' => 'NameString', ], 'StatisticId' => [ 'shape' => 'HashString', ], 'MetricValues' => [ 'shape' => 'DataQualityMetricValues', ], 'NewRules' => [ 'shape' => 'NewRules', ], ], ], 'MicrosoftSQLServerCatalogSource' => [ 'type' => 'structure', 'required' => [ 'Name', 'Database', 'Table', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Database' => [ 'shape' => 'EnclosedInStringProperty', ], 'Table' => [ 'shape' => 'EnclosedInStringProperty', ], ], ], 'MicrosoftSQLServerCatalogTarget' => [ 'type' => 'structure', 'required' => [ 'Name', 'Inputs', 'Database', 'Table', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Inputs' => [ 'shape' => 'OneInput', ], 'Database' => [ 'shape' => 'EnclosedInStringProperty', ], 'Table' => [ 'shape' => 'EnclosedInStringProperty', ], ], ], 'MillisecondsCount' => [ 'type' => 'long', ], 'ModifyIntegrationRequest' => [ 'type' => 'structure', 'required' => [ 'IntegrationIdentifier', ], 'members' => [ 'IntegrationIdentifier' => [ 'shape' => 'String128', ], 'Description' => [ 'shape' => 'IntegrationDescription', ], 'DataFilter' => [ 'shape' => 'String2048', ], 'IntegrationName' => [ 'shape' => 'String128', ], ], ], 'ModifyIntegrationResponse' => [ 'type' => 'structure', 'required' => [ 'SourceArn', 'TargetArn', 'IntegrationName', 'IntegrationArn', 'Status', 'CreateTime', ], 'members' => [ 'SourceArn' => [ 'shape' => 'String128', ], 'TargetArn' => [ 'shape' => 'String128', ], 'IntegrationName' => [ 'shape' => 'String128', ], 'Description' => [ 'shape' => 'IntegrationDescription', ], 'IntegrationArn' => [ 'shape' => 'String128', ], 'KmsKeyId' => [ 'shape' => 'String2048', ], 'AdditionalEncryptionContext' => [ 'shape' => 'IntegrationAdditionalEncryptionContextMap', ], 'Tags' => [ 'shape' => 'IntegrationTagsList', ], 'Status' => [ 'shape' => 'IntegrationStatus', ], 'CreateTime' => [ 'shape' => 'IntegrationTimestamp', ], 'Errors' => [ 'shape' => 'IntegrationErrorList', ], 'DataFilter' => [ 'shape' => 'String2048', ], ], ], 'MongoDBTarget' => [ 'type' => 'structure', 'members' => [ 'ConnectionName' => [ 'shape' => 'ConnectionName', ], 'Path' => [ 'shape' => 'Path', ], 'ScanAll' => [ 'shape' => 'NullableBoolean', ], ], ], 'MongoDBTargetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MongoDBTarget', ], ], 'MySQLCatalogSource' => [ 'type' => 'structure', 'required' => [ 'Name', 'Database', 'Table', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Database' => [ 'shape' => 'EnclosedInStringProperty', ], 'Table' => [ 'shape' => 'EnclosedInStringProperty', ], ], ], 'MySQLCatalogTarget' => [ 'type' => 'structure', 'required' => [ 'Name', 'Inputs', 'Database', 'Table', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Inputs' => [ 'shape' => 'OneInput', ], 'Database' => [ 'shape' => 'EnclosedInStringProperty', ], 'Table' => [ 'shape' => 'EnclosedInStringProperty', ], ], ], 'NameString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'NameStringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], ], 'NewRules' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], ], 'NextToken' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '[-a-zA-Z0-9+=/:_]*', ], 'NoScheduleException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'Node' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'NodeType', ], 'Name' => [ 'shape' => 'NameString', ], 'UniqueId' => [ 'shape' => 'NameString', ], 'TriggerDetails' => [ 'shape' => 'TriggerNodeDetails', ], 'JobDetails' => [ 'shape' => 'JobNodeDetails', ], 'CrawlerDetails' => [ 'shape' => 'CrawlerNodeDetails', ], ], ], 'NodeId' => [ 'type' => 'string', 'pattern' => '[A-Za-z0-9_-]*', ], 'NodeIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], ], 'NodeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Node', ], ], 'NodeName' => [ 'type' => 'string', 'pattern' => '([^\\r\\n])*', ], 'NodeType' => [ 'type' => 'string', 'enum' => [ 'CRAWLER', 'JOB', 'TRIGGER', ], ], 'NonNegativeDouble' => [ 'type' => 'double', 'min' => 0.0, ], 'NonNegativeInt' => [ 'type' => 'integer', 'min' => 0, ], 'NonNegativeInteger' => [ 'type' => 'integer', 'min' => 0, ], 'NonNegativeLong' => [ 'type' => 'long', 'min' => 0, ], 'NotificationProperty' => [ 'type' => 'structure', 'members' => [ 'NotifyDelayAfter' => [ 'shape' => 'NotifyDelayAfter', ], ], ], 'NotifyDelayAfter' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'NullCheckBoxList' => [ 'type' => 'structure', 'members' => [ 'IsEmpty' => [ 'shape' => 'BoxedBoolean', ], 'IsNullString' => [ 'shape' => 'BoxedBoolean', ], 'IsNegOne' => [ 'shape' => 'BoxedBoolean', ], ], ], 'NullValueField' => [ 'type' => 'structure', 'required' => [ 'Value', 'Datatype', ], 'members' => [ 'Value' => [ 'shape' => 'EnclosedInStringProperty', ], 'Datatype' => [ 'shape' => 'Datatype', ], ], ], 'NullValueFields' => [ 'type' => 'list', 'member' => [ 'shape' => 'NullValueField', ], 'max' => 50, 'min' => 0, ], 'NullableBoolean' => [ 'type' => 'boolean', 'box' => true, ], 'NullableDouble' => [ 'type' => 'double', 'box' => true, ], 'NullableInteger' => [ 'type' => 'integer', 'box' => true, ], 'NullableString' => [ 'type' => 'string', 'box' => true, ], 'NumberTargetPartitionsString' => [ 'type' => 'string', ], 'OAuth2ClientApplication' => [ 'type' => 'structure', 'members' => [ 'UserManagedClientApplicationClientId' => [ 'shape' => 'UserManagedClientApplicationClientId', ], 'AWSManagedClientApplicationReference' => [ 'shape' => 'AWSManagedClientApplicationReference', ], ], ], 'OAuth2Credentials' => [ 'type' => 'structure', 'members' => [ 'UserManagedClientApplicationClientSecret' => [ 'shape' => 'UserManagedClientApplicationClientSecret', ], 'AccessToken' => [ 'shape' => 'AccessToken', ], 'RefreshToken' => [ 'shape' => 'RefreshToken', ], 'JwtToken' => [ 'shape' => 'JwtToken', ], ], ], 'OAuth2GrantType' => [ 'type' => 'string', 'enum' => [ 'AUTHORIZATION_CODE', 'CLIENT_CREDENTIALS', 'JWT_BEARER', ], ], 'OAuth2Properties' => [ 'type' => 'structure', 'members' => [ 'OAuth2GrantType' => [ 'shape' => 'OAuth2GrantType', ], 'OAuth2ClientApplication' => [ 'shape' => 'OAuth2ClientApplication', ], 'TokenUrl' => [ 'shape' => 'TokenUrl', ], 'TokenUrlParametersMap' => [ 'shape' => 'TokenUrlParametersMap', ], ], ], 'OAuth2PropertiesInput' => [ 'type' => 'structure', 'members' => [ 'OAuth2GrantType' => [ 'shape' => 'OAuth2GrantType', ], 'OAuth2ClientApplication' => [ 'shape' => 'OAuth2ClientApplication', ], 'TokenUrl' => [ 'shape' => 'TokenUrl', ], 'TokenUrlParametersMap' => [ 'shape' => 'TokenUrlParametersMap', ], 'AuthorizationCodeProperties' => [ 'shape' => 'AuthorizationCodeProperties', ], 'OAuth2Credentials' => [ 'shape' => 'OAuth2Credentials', ], ], ], 'OneInput' => [ 'type' => 'list', 'member' => [ 'shape' => 'NodeId', ], 'max' => 1, 'min' => 1, ], 'OpenTableFormatInput' => [ 'type' => 'structure', 'members' => [ 'IcebergInput' => [ 'shape' => 'IcebergInput', ], ], ], 'Operation' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[A-Z\\_]+$', ], 'OperationNotSupportedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'OperationTimeoutException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'Option' => [ 'type' => 'structure', 'members' => [ 'Value' => [ 'shape' => 'EnclosedInStringProperty', ], 'Label' => [ 'shape' => 'EnclosedInStringProperty', ], 'Description' => [ 'shape' => 'EnclosedInStringProperty', ], ], ], 'OptionKey' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[\\w]*', ], 'OptionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Option', ], ], 'OptionValue' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[\\S]*', ], 'OracleSQLCatalogSource' => [ 'type' => 'structure', 'required' => [ 'Name', 'Database', 'Table', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Database' => [ 'shape' => 'EnclosedInStringProperty', ], 'Table' => [ 'shape' => 'EnclosedInStringProperty', ], ], ], 'OracleSQLCatalogTarget' => [ 'type' => 'structure', 'required' => [ 'Name', 'Inputs', 'Database', 'Table', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Inputs' => [ 'shape' => 'OneInput', ], 'Database' => [ 'shape' => 'EnclosedInStringProperty', ], 'Table' => [ 'shape' => 'EnclosedInStringProperty', ], ], ], 'OrchestrationArgumentsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'OrchestrationNameString', ], 'value' => [ 'shape' => 'OrchestrationArgumentsValue', ], 'max' => 75, 'min' => 0, ], 'OrchestrationArgumentsValue' => [ 'type' => 'string', 'max' => 4096, 'min' => 0, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]*', ], 'OrchestrationIAMRoleArn' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => 'arn:aws[^:]*:iam::[0-9]*:role/.+', ], 'OrchestrationMessageString' => [ 'type' => 'string', 'max' => 400000, ], 'OrchestrationNameString' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\.\\-_A-Za-z0-9]+', ], 'OrchestrationPageSize200' => [ 'type' => 'integer', 'box' => true, 'max' => 200, 'min' => 1, ], 'OrchestrationPageSize25' => [ 'type' => 'integer', 'box' => true, 'max' => 25, 'min' => 1, ], 'OrchestrationRoleArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws[^:]*:iam::[0-9]*:role/.+', ], 'OrchestrationS3Location' => [ 'type' => 'string', 'max' => 8192, 'min' => 1, 'pattern' => '^s3://([^/]+)/([^/]+/)*([^/]+)$', ], 'OrchestrationStatementCodeString' => [ 'type' => 'string', 'max' => 68000, ], 'OrchestrationStringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GenericString', ], ], 'OrchestrationToken' => [ 'type' => 'string', 'max' => 400000, ], 'Order' => [ 'type' => 'structure', 'required' => [ 'Column', 'SortOrder', ], 'members' => [ 'Column' => [ 'shape' => 'NameString', ], 'SortOrder' => [ 'shape' => 'IntegerFlag', ], ], ], 'OrderList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Order', ], ], 'OrphanFileDeletionConfiguration' => [ 'type' => 'structure', 'members' => [ 'icebergConfiguration' => [ 'shape' => 'IcebergOrphanFileDeletionConfiguration', ], ], ], 'OrphanFileDeletionMetrics' => [ 'type' => 'structure', 'members' => [ 'IcebergMetrics' => [ 'shape' => 'IcebergOrphanFileDeletionMetrics', ], ], ], 'OtherMetadataValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OtherMetadataValueListItem', ], ], 'OtherMetadataValueListItem' => [ 'type' => 'structure', 'members' => [ 'MetadataValue' => [ 'shape' => 'MetadataValueString', ], 'CreatedTime' => [ 'shape' => 'CreatedTimestamp', ], ], ], 'PIIDetection' => [ 'type' => 'structure', 'required' => [ 'Name', 'Inputs', 'PiiType', 'EntityTypesToDetect', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Inputs' => [ 'shape' => 'OneInput', ], 'PiiType' => [ 'shape' => 'PiiType', ], 'EntityTypesToDetect' => [ 'shape' => 'EnclosedInStringProperties', ], 'OutputColumnName' => [ 'shape' => 'EnclosedInStringProperty', ], 'SampleFraction' => [ 'shape' => 'BoxedDoubleFraction', ], 'ThresholdFraction' => [ 'shape' => 'BoxedDoubleFraction', ], 'MaskValue' => [ 'shape' => 'MaskValue', ], ], ], 'PageSize' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 1, ], 'PaginationToken' => [ 'type' => 'string', ], 'ParamType' => [ 'type' => 'string', 'enum' => [ 'str', 'int', 'float', 'complex', 'bool', 'list', 'null', ], ], 'ParameterMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ParameterName', ], 'value' => [ 'shape' => 'ParameterValue', ], ], 'ParameterName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[A-Za-z0-9]+$', ], 'ParameterValue' => [ 'type' => 'string', 'max' => 32768, 'min' => 1, ], 'ParametersMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'KeyString', ], 'value' => [ 'shape' => 'ParametersMapValue', ], ], 'ParametersMapValue' => [ 'type' => 'string', 'max' => 512000, ], 'ParquetCompressionType' => [ 'type' => 'string', 'enum' => [ 'snappy', 'lzo', 'gzip', 'brotli', 'lz4', 'uncompressed', 'none', ], ], 'Partition' => [ 'type' => 'structure', 'members' => [ 'Values' => [ 'shape' => 'ValueStringList', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastAccessTime' => [ 'shape' => 'Timestamp', ], 'StorageDescriptor' => [ 'shape' => 'StorageDescriptor', ], 'Parameters' => [ 'shape' => 'ParametersMap', ], 'LastAnalyzedTime' => [ 'shape' => 'Timestamp', ], 'CatalogId' => [ 'shape' => 'CatalogIdString', ], ], ], 'PartitionError' => [ 'type' => 'structure', 'members' => [ 'PartitionValues' => [ 'shape' => 'ValueStringList', ], 'ErrorDetail' => [ 'shape' => 'ErrorDetail', ], ], ], 'PartitionErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'PartitionError', ], ], 'PartitionIndex' => [ 'type' => 'structure', 'required' => [ 'Keys', 'IndexName', ], 'members' => [ 'Keys' => [ 'shape' => 'KeyList', ], 'IndexName' => [ 'shape' => 'NameString', ], ], ], 'PartitionIndexDescriptor' => [ 'type' => 'structure', 'required' => [ 'IndexName', 'Keys', 'IndexStatus', ], 'members' => [ 'IndexName' => [ 'shape' => 'NameString', ], 'Keys' => [ 'shape' => 'KeySchemaElementList', ], 'IndexStatus' => [ 'shape' => 'PartitionIndexStatus', ], 'BackfillErrors' => [ 'shape' => 'BackfillErrors', ], ], ], 'PartitionIndexDescriptorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PartitionIndexDescriptor', ], ], 'PartitionIndexList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PartitionIndex', ], 'max' => 3, ], 'PartitionIndexStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'DELETING', 'FAILED', ], ], 'PartitionInput' => [ 'type' => 'structure', 'members' => [ 'Values' => [ 'shape' => 'ValueStringList', ], 'LastAccessTime' => [ 'shape' => 'Timestamp', ], 'StorageDescriptor' => [ 'shape' => 'StorageDescriptor', ], 'Parameters' => [ 'shape' => 'ParametersMap', ], 'LastAnalyzedTime' => [ 'shape' => 'Timestamp', ], ], ], 'PartitionInputList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PartitionInput', ], 'max' => 100, 'min' => 0, ], 'PartitionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Partition', ], ], 'PartitionValueList' => [ 'type' => 'structure', 'required' => [ 'Values', ], 'members' => [ 'Values' => [ 'shape' => 'ValueStringList', ], ], ], 'Password' => [ 'type' => 'string', 'max' => 512, 'pattern' => '.*', 'sensitive' => true, ], 'Path' => [ 'type' => 'string', ], 'PathList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Path', ], ], 'Permission' => [ 'type' => 'string', 'enum' => [ 'ALL', 'SELECT', 'ALTER', 'DROP', 'DELETE', 'INSERT', 'CREATE_DATABASE', 'CREATE_TABLE', 'DATA_LOCATION_ACCESS', ], ], 'PermissionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Permission', ], ], 'PermissionType' => [ 'type' => 'string', 'enum' => [ 'COLUMN_PERMISSION', 'CELL_FILTER_PERMISSION', 'NESTED_PERMISSION', 'NESTED_CELL_PERMISSION', ], ], 'PermissionTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PermissionType', ], 'max' => 255, 'min' => 1, ], 'PermissionTypeMismatchException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'PhysicalConnectionRequirements' => [ 'type' => 'structure', 'members' => [ 'SubnetId' => [ 'shape' => 'NameString', ], 'SecurityGroupIdList' => [ 'shape' => 'SecurityGroupIdList', ], 'AvailabilityZone' => [ 'shape' => 'NameString', ], ], ], 'PiiType' => [ 'type' => 'string', 'enum' => [ 'RowAudit', 'RowMasking', 'ColumnAudit', 'ColumnMasking', ], ], 'PolicyJsonString' => [ 'type' => 'string', 'min' => 2, ], 'PollingTime' => [ 'type' => 'long', 'box' => true, 'min' => 10, ], 'PositiveInteger' => [ 'type' => 'integer', 'min' => 1, ], 'PositiveLong' => [ 'type' => 'long', 'box' => true, 'min' => 1, ], 'PostgreSQLCatalogSource' => [ 'type' => 'structure', 'required' => [ 'Name', 'Database', 'Table', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Database' => [ 'shape' => 'EnclosedInStringProperty', ], 'Table' => [ 'shape' => 'EnclosedInStringProperty', ], ], ], 'PostgreSQLCatalogTarget' => [ 'type' => 'structure', 'required' => [ 'Name', 'Inputs', 'Database', 'Table', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Inputs' => [ 'shape' => 'OneInput', ], 'Database' => [ 'shape' => 'EnclosedInStringProperty', ], 'Table' => [ 'shape' => 'EnclosedInStringProperty', ], ], ], 'Predecessor' => [ 'type' => 'structure', 'members' => [ 'JobName' => [ 'shape' => 'NameString', ], 'RunId' => [ 'shape' => 'IdString', ], ], ], 'PredecessorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Predecessor', ], ], 'Predicate' => [ 'type' => 'structure', 'members' => [ 'Logical' => [ 'shape' => 'Logical', ], 'Conditions' => [ 'shape' => 'ConditionList', ], ], ], 'PredicateString' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]*', ], 'PrimaryKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String128', ], ], 'PrincipalPermissions' => [ 'type' => 'structure', 'members' => [ 'Principal' => [ 'shape' => 'DataLakePrincipal', ], 'Permissions' => [ 'shape' => 'PermissionList', ], ], ], 'PrincipalPermissionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PrincipalPermissions', ], ], 'PrincipalType' => [ 'type' => 'string', 'enum' => [ 'USER', 'ROLE', 'GROUP', ], ], 'Prob' => [ 'type' => 'double', 'box' => true, 'max' => 1, 'min' => 0, ], 'ProfileConfiguration' => [ 'type' => 'structure', 'members' => [ 'SessionConfiguration' => [ 'shape' => 'ConfigurationMap', ], 'JobConfiguration' => [ 'shape' => 'ConfigurationMap', ], ], ], 'PropertiesMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'PropertyName', ], 'value' => [ 'shape' => 'Property', ], ], 'Property' => [ 'type' => 'structure', 'required' => [ 'Name', 'Description', 'Required', 'PropertyTypes', ], 'members' => [ 'Name' => [ 'shape' => 'PropertyName', ], 'Description' => [ 'shape' => 'PropertyDescriptionString', ], 'Required' => [ 'shape' => 'Bool', ], 'DefaultValue' => [ 'shape' => 'String', ], 'PropertyTypes' => [ 'shape' => 'PropertyTypes', ], 'AllowedValues' => [ 'shape' => 'AllowedValues', ], 'DataOperationScopes' => [ 'shape' => 'DataOperations', ], ], ], 'PropertyDescriptionString' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'PropertyKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'PropertyMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'PropertyKey', ], 'value' => [ 'shape' => 'PropertyValue', ], ], 'PropertyName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'PropertyNameOverrides' => [ 'type' => 'map', 'key' => [ 'shape' => 'PropertyName', ], 'value' => [ 'shape' => 'PropertyName', ], ], 'PropertyPredicate' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'ValueString', ], 'Value' => [ 'shape' => 'ValueString', ], 'Comparator' => [ 'shape' => 'Comparator', ], ], ], 'PropertyType' => [ 'type' => 'string', 'enum' => [ 'USER_INPUT', 'SECRET', 'READ_ONLY', 'UNUSED', 'SECRET_OR_USER_INPUT', ], ], 'PropertyTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'PropertyType', ], ], 'PropertyValue' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'PublicKeysList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GenericString', ], 'max' => 5, ], 'PutDataCatalogEncryptionSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'DataCatalogEncryptionSettings', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DataCatalogEncryptionSettings' => [ 'shape' => 'DataCatalogEncryptionSettings', ], ], ], 'PutDataCatalogEncryptionSettingsResponse' => [ 'type' => 'structure', 'members' => [], ], 'PutDataQualityProfileAnnotationRequest' => [ 'type' => 'structure', 'required' => [ 'ProfileId', 'InclusionAnnotation', ], 'members' => [ 'ProfileId' => [ 'shape' => 'HashString', ], 'InclusionAnnotation' => [ 'shape' => 'InclusionAnnotationValue', ], ], ], 'PutDataQualityProfileAnnotationResponse' => [ 'type' => 'structure', 'members' => [], ], 'PutResourcePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'PolicyInJson', ], 'members' => [ 'PolicyInJson' => [ 'shape' => 'PolicyJsonString', ], 'ResourceArn' => [ 'shape' => 'GlueResourceArn', ], 'PolicyHashCondition' => [ 'shape' => 'HashString', ], 'PolicyExistsCondition' => [ 'shape' => 'ExistCondition', ], 'EnableHybrid' => [ 'shape' => 'EnableHybridValues', ], ], ], 'PutResourcePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'PolicyHash' => [ 'shape' => 'HashString', ], ], ], 'PutSchemaVersionMetadataInput' => [ 'type' => 'structure', 'required' => [ 'MetadataKeyValue', ], 'members' => [ 'SchemaId' => [ 'shape' => 'SchemaId', ], 'SchemaVersionNumber' => [ 'shape' => 'SchemaVersionNumber', ], 'SchemaVersionId' => [ 'shape' => 'SchemaVersionIdString', ], 'MetadataKeyValue' => [ 'shape' => 'MetadataKeyValuePair', ], ], ], 'PutSchemaVersionMetadataResponse' => [ 'type' => 'structure', 'members' => [ 'SchemaArn' => [ 'shape' => 'GlueResourceArn', ], 'SchemaName' => [ 'shape' => 'SchemaRegistryNameString', ], 'RegistryName' => [ 'shape' => 'SchemaRegistryNameString', ], 'LatestVersion' => [ 'shape' => 'LatestSchemaVersionBoolean', ], 'VersionNumber' => [ 'shape' => 'VersionLongNumber', ], 'SchemaVersionId' => [ 'shape' => 'SchemaVersionIdString', ], 'MetadataKey' => [ 'shape' => 'MetadataKeyString', ], 'MetadataValue' => [ 'shape' => 'MetadataValueString', ], ], ], 'PutWorkflowRunPropertiesRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'RunId', 'RunProperties', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'RunId' => [ 'shape' => 'IdString', ], 'RunProperties' => [ 'shape' => 'WorkflowRunProperties', ], ], ], 'PutWorkflowRunPropertiesResponse' => [ 'type' => 'structure', 'members' => [], ], 'PythonScript' => [ 'type' => 'string', ], 'PythonVersionString' => [ 'type' => 'string', 'pattern' => '^([2-3]|3[.]9)$', ], 'QuerySchemaVersionMetadataInput' => [ 'type' => 'structure', 'members' => [ 'SchemaId' => [ 'shape' => 'SchemaId', ], 'SchemaVersionNumber' => [ 'shape' => 'SchemaVersionNumber', ], 'SchemaVersionId' => [ 'shape' => 'SchemaVersionIdString', ], 'MetadataList' => [ 'shape' => 'MetadataList', ], 'MaxResults' => [ 'shape' => 'QuerySchemaVersionMetadataMaxResults', ], 'NextToken' => [ 'shape' => 'SchemaRegistryTokenString', ], ], ], 'QuerySchemaVersionMetadataMaxResults' => [ 'type' => 'integer', 'max' => 50, 'min' => 1, ], 'QuerySchemaVersionMetadataResponse' => [ 'type' => 'structure', 'members' => [ 'MetadataInfoMap' => [ 'shape' => 'MetadataInfoMap', ], 'SchemaVersionId' => [ 'shape' => 'SchemaVersionIdString', ], 'NextToken' => [ 'shape' => 'SchemaRegistryTokenString', ], ], ], 'QuerySessionContext' => [ 'type' => 'structure', 'members' => [ 'QueryId' => [ 'shape' => 'HashString', ], 'QueryStartTime' => [ 'shape' => 'Timestamp', ], 'ClusterId' => [ 'shape' => 'NullableString', ], 'QueryAuthorizationId' => [ 'shape' => 'HashString', ], 'AdditionalContext' => [ 'shape' => 'AdditionalContextMap', ], ], ], 'QuoteChar' => [ 'type' => 'string', 'enum' => [ 'quote', 'quillemet', 'single_quote', 'disabled', ], ], 'Recipe' => [ 'type' => 'structure', 'required' => [ 'Name', 'Inputs', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Inputs' => [ 'shape' => 'OneInput', ], 'RecipeReference' => [ 'shape' => 'RecipeReference', ], 'RecipeSteps' => [ 'shape' => 'RecipeSteps', ], ], ], 'RecipeAction' => [ 'type' => 'structure', 'required' => [ 'Operation', ], 'members' => [ 'Operation' => [ 'shape' => 'Operation', ], 'Parameters' => [ 'shape' => 'ParameterMap', ], ], ], 'RecipeReference' => [ 'type' => 'structure', 'required' => [ 'RecipeArn', 'RecipeVersion', ], 'members' => [ 'RecipeArn' => [ 'shape' => 'EnclosedInStringProperty', ], 'RecipeVersion' => [ 'shape' => 'RecipeVersion', ], ], ], 'RecipeStep' => [ 'type' => 'structure', 'required' => [ 'Action', ], 'members' => [ 'Action' => [ 'shape' => 'RecipeAction', ], 'ConditionExpressions' => [ 'shape' => 'ConditionExpressionList', ], ], ], 'RecipeSteps' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecipeStep', ], ], 'RecipeVersion' => [ 'type' => 'string', 'max' => 16, 'min' => 1, ], 'Record' => [ 'type' => 'structure', 'members' => [], 'document' => true, 'sensitive' => true, ], 'Records' => [ 'type' => 'list', 'member' => [ 'shape' => 'Record', ], 'sensitive' => true, ], 'RecordsCount' => [ 'type' => 'long', 'box' => true, ], 'RecrawlBehavior' => [ 'type' => 'string', 'enum' => [ 'CRAWL_EVERYTHING', 'CRAWL_NEW_FOLDERS_ONLY', 'CRAWL_EVENT_MODE', ], ], 'RecrawlPolicy' => [ 'type' => 'structure', 'members' => [ 'RecrawlBehavior' => [ 'shape' => 'RecrawlBehavior', ], ], ], 'RedirectUri' => [ 'type' => 'string', 'max' => 512, 'pattern' => '^(https?):\\/\\/[^\\s/$.?#].[^\\s]*$', ], 'RedshiftSource' => [ 'type' => 'structure', 'required' => [ 'Name', 'Database', 'Table', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Database' => [ 'shape' => 'EnclosedInStringProperty', ], 'Table' => [ 'shape' => 'EnclosedInStringProperty', ], 'RedshiftTmpDir' => [ 'shape' => 'EnclosedInStringProperty', ], 'TmpDirIAMRole' => [ 'shape' => 'EnclosedInStringProperty', ], ], ], 'RedshiftTarget' => [ 'type' => 'structure', 'required' => [ 'Name', 'Inputs', 'Database', 'Table', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Inputs' => [ 'shape' => 'OneInput', ], 'Database' => [ 'shape' => 'EnclosedInStringProperty', ], 'Table' => [ 'shape' => 'EnclosedInStringProperty', ], 'RedshiftTmpDir' => [ 'shape' => 'EnclosedInStringProperty', ], 'TmpDirIAMRole' => [ 'shape' => 'EnclosedInStringProperty', ], 'UpsertRedshiftOptions' => [ 'shape' => 'UpsertRedshiftTargetOptions', ], ], ], 'ReferenceDatasetsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], ], 'RefreshToken' => [ 'type' => 'string', 'max' => 4096, 'pattern' => '^[\\x20-\\x7E]*$', 'sensitive' => true, ], 'RegisterSchemaVersionInput' => [ 'type' => 'structure', 'required' => [ 'SchemaId', 'SchemaDefinition', ], 'members' => [ 'SchemaId' => [ 'shape' => 'SchemaId', ], 'SchemaDefinition' => [ 'shape' => 'SchemaDefinitionString', ], ], ], 'RegisterSchemaVersionResponse' => [ 'type' => 'structure', 'members' => [ 'SchemaVersionId' => [ 'shape' => 'SchemaVersionIdString', ], 'VersionNumber' => [ 'shape' => 'VersionLongNumber', ], 'Status' => [ 'shape' => 'SchemaVersionStatus', ], ], ], 'RegistryId' => [ 'type' => 'structure', 'members' => [ 'RegistryName' => [ 'shape' => 'SchemaRegistryNameString', ], 'RegistryArn' => [ 'shape' => 'GlueResourceArn', ], ], ], 'RegistryListDefinition' => [ 'type' => 'list', 'member' => [ 'shape' => 'RegistryListItem', ], ], 'RegistryListItem' => [ 'type' => 'structure', 'members' => [ 'RegistryName' => [ 'shape' => 'SchemaRegistryNameString', ], 'RegistryArn' => [ 'shape' => 'GlueResourceArn', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'Status' => [ 'shape' => 'RegistryStatus', ], 'CreatedTime' => [ 'shape' => 'CreatedTimestamp', ], 'UpdatedTime' => [ 'shape' => 'UpdatedTimestamp', ], ], ], 'RegistryStatus' => [ 'type' => 'string', 'enum' => [ 'AVAILABLE', 'DELETING', ], ], 'RelationalCatalogSource' => [ 'type' => 'structure', 'required' => [ 'Name', 'Database', 'Table', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Database' => [ 'shape' => 'EnclosedInStringProperty', ], 'Table' => [ 'shape' => 'EnclosedInStringProperty', ], ], ], 'RemoveSchemaVersionMetadataInput' => [ 'type' => 'structure', 'required' => [ 'MetadataKeyValue', ], 'members' => [ 'SchemaId' => [ 'shape' => 'SchemaId', ], 'SchemaVersionNumber' => [ 'shape' => 'SchemaVersionNumber', ], 'SchemaVersionId' => [ 'shape' => 'SchemaVersionIdString', ], 'MetadataKeyValue' => [ 'shape' => 'MetadataKeyValuePair', ], ], ], 'RemoveSchemaVersionMetadataResponse' => [ 'type' => 'structure', 'members' => [ 'SchemaArn' => [ 'shape' => 'GlueResourceArn', ], 'SchemaName' => [ 'shape' => 'SchemaRegistryNameString', ], 'RegistryName' => [ 'shape' => 'SchemaRegistryNameString', ], 'LatestVersion' => [ 'shape' => 'LatestSchemaVersionBoolean', ], 'VersionNumber' => [ 'shape' => 'VersionLongNumber', ], 'SchemaVersionId' => [ 'shape' => 'SchemaVersionIdString', ], 'MetadataKey' => [ 'shape' => 'MetadataKeyString', ], 'MetadataValue' => [ 'shape' => 'MetadataValueString', ], ], ], 'RenameField' => [ 'type' => 'structure', 'required' => [ 'Name', 'Inputs', 'SourcePath', 'TargetPath', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Inputs' => [ 'shape' => 'OneInput', ], 'SourcePath' => [ 'shape' => 'EnclosedInStringProperties', ], 'TargetPath' => [ 'shape' => 'EnclosedInStringProperties', ], ], ], 'ReplaceBoolean' => [ 'type' => 'boolean', ], 'ResetJobBookmarkRequest' => [ 'type' => 'structure', 'required' => [ 'JobName', ], 'members' => [ 'JobName' => [ 'shape' => 'JobName', ], 'RunId' => [ 'shape' => 'RunId', ], ], ], 'ResetJobBookmarkResponse' => [ 'type' => 'structure', 'members' => [ 'JobBookmarkEntry' => [ 'shape' => 'JobBookmarkEntry', ], ], ], 'ResourceAction' => [ 'type' => 'string', 'enum' => [ 'UPDATE', 'CREATE', ], ], 'ResourceArnString' => [ 'type' => 'string', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'IntegrationErrorMessage', ], ], 'exception' => true, ], 'ResourceNotReadyException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'ResourceNumberLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'ResourceShareType' => [ 'type' => 'string', 'enum' => [ 'FOREIGN', 'ALL', 'FEDERATED', ], ], 'ResourceState' => [ 'type' => 'string', 'enum' => [ 'QUEUED', 'IN_PROGRESS', 'SUCCESS', 'STOPPED', 'FAILED', ], ], 'ResourceType' => [ 'type' => 'string', 'enum' => [ 'JAR', 'FILE', 'ARCHIVE', ], ], 'ResourceUri' => [ 'type' => 'structure', 'members' => [ 'ResourceType' => [ 'shape' => 'ResourceType', ], 'Uri' => [ 'shape' => 'URI', ], ], ], 'ResourceUriList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceUri', ], 'max' => 1000, 'min' => 0, ], 'ResumeWorkflowRunRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'RunId', 'NodeIds', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'RunId' => [ 'shape' => 'IdString', ], 'NodeIds' => [ 'shape' => 'NodeIdList', ], ], ], 'ResumeWorkflowRunResponse' => [ 'type' => 'structure', 'members' => [ 'RunId' => [ 'shape' => 'IdString', ], 'NodeIds' => [ 'shape' => 'NodeIdList', ], ], ], 'RetentionConfiguration' => [ 'type' => 'structure', 'members' => [ 'icebergConfiguration' => [ 'shape' => 'IcebergRetentionConfiguration', ], ], ], 'RetentionMetrics' => [ 'type' => 'structure', 'members' => [ 'IcebergMetrics' => [ 'shape' => 'IcebergRetentionMetrics', ], ], ], 'Role' => [ 'type' => 'string', ], 'RoleArn' => [ 'type' => 'string', 'pattern' => 'arn:aws:iam::\\d{12}:role/.*', ], 'RoleString' => [ 'type' => 'string', ], 'RowTag' => [ 'type' => 'string', ], 'RuleMetricsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'NameString', ], 'value' => [ 'shape' => 'NullableDouble', ], 'sensitive' => true, ], 'RulesetNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], 'max' => 10, 'min' => 1, ], 'RunId' => [ 'type' => 'string', ], 'RunIdentifier' => [ 'type' => 'structure', 'members' => [ 'RunId' => [ 'shape' => 'HashString', ], 'JobRunId' => [ 'shape' => 'HashString', ], ], ], 'RunMetrics' => [ 'type' => 'structure', 'members' => [ 'NumberOfBytesCompacted' => [ 'shape' => 'MessageString', ], 'NumberOfFilesCompacted' => [ 'shape' => 'MessageString', ], 'NumberOfDpus' => [ 'shape' => 'MessageString', ], 'JobDurationInHour' => [ 'shape' => 'MessageString', ], ], ], 'RunStatementRequest' => [ 'type' => 'structure', 'required' => [ 'SessionId', 'Code', ], 'members' => [ 'SessionId' => [ 'shape' => 'NameString', ], 'Code' => [ 'shape' => 'OrchestrationStatementCodeString', ], 'RequestOrigin' => [ 'shape' => 'OrchestrationNameString', ], ], ], 'RunStatementResponse' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'IntegerValue', ], ], ], 'RuntimeNameString' => [ 'type' => 'string', 'max' => 64, 'pattern' => '.*', ], 'S3CatalogDeltaSource' => [ 'type' => 'structure', 'required' => [ 'Name', 'Database', 'Table', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Database' => [ 'shape' => 'EnclosedInStringProperty', ], 'Table' => [ 'shape' => 'EnclosedInStringProperty', ], 'AdditionalDeltaOptions' => [ 'shape' => 'AdditionalOptions', ], 'OutputSchemas' => [ 'shape' => 'GlueSchemas', ], ], ], 'S3CatalogHudiSource' => [ 'type' => 'structure', 'required' => [ 'Name', 'Database', 'Table', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Database' => [ 'shape' => 'EnclosedInStringProperty', ], 'Table' => [ 'shape' => 'EnclosedInStringProperty', ], 'AdditionalHudiOptions' => [ 'shape' => 'AdditionalOptions', ], 'OutputSchemas' => [ 'shape' => 'GlueSchemas', ], ], ], 'S3CatalogSource' => [ 'type' => 'structure', 'required' => [ 'Name', 'Database', 'Table', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Database' => [ 'shape' => 'EnclosedInStringProperty', ], 'Table' => [ 'shape' => 'EnclosedInStringProperty', ], 'PartitionPredicate' => [ 'shape' => 'EnclosedInStringProperty', ], 'AdditionalOptions' => [ 'shape' => 'S3SourceAdditionalOptions', ], ], ], 'S3CatalogTarget' => [ 'type' => 'structure', 'required' => [ 'Name', 'Inputs', 'Table', 'Database', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Inputs' => [ 'shape' => 'OneInput', ], 'PartitionKeys' => [ 'shape' => 'GlueStudioPathList', ], 'Table' => [ 'shape' => 'EnclosedInStringProperty', ], 'Database' => [ 'shape' => 'EnclosedInStringProperty', ], 'SchemaChangePolicy' => [ 'shape' => 'CatalogSchemaChangePolicy', ], ], ], 'S3CsvSource' => [ 'type' => 'structure', 'required' => [ 'Name', 'Paths', 'Separator', 'QuoteChar', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Paths' => [ 'shape' => 'EnclosedInStringProperties', ], 'CompressionType' => [ 'shape' => 'CompressionType', ], 'Exclusions' => [ 'shape' => 'EnclosedInStringProperties', ], 'GroupSize' => [ 'shape' => 'EnclosedInStringProperty', ], 'GroupFiles' => [ 'shape' => 'EnclosedInStringProperty', ], 'Recurse' => [ 'shape' => 'BoxedBoolean', ], 'MaxBand' => [ 'shape' => 'BoxedNonNegativeInt', ], 'MaxFilesInBand' => [ 'shape' => 'BoxedNonNegativeInt', ], 'AdditionalOptions' => [ 'shape' => 'S3DirectSourceAdditionalOptions', ], 'Separator' => [ 'shape' => 'Separator', ], 'Escaper' => [ 'shape' => 'EnclosedInStringPropertyWithQuote', ], 'QuoteChar' => [ 'shape' => 'QuoteChar', ], 'Multiline' => [ 'shape' => 'BoxedBoolean', ], 'WithHeader' => [ 'shape' => 'BoxedBoolean', ], 'WriteHeader' => [ 'shape' => 'BoxedBoolean', ], 'SkipFirst' => [ 'shape' => 'BoxedBoolean', ], 'OptimizePerformance' => [ 'shape' => 'BooleanValue', ], 'OutputSchemas' => [ 'shape' => 'GlueSchemas', ], ], ], 'S3DeltaCatalogTarget' => [ 'type' => 'structure', 'required' => [ 'Name', 'Inputs', 'Table', 'Database', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Inputs' => [ 'shape' => 'OneInput', ], 'PartitionKeys' => [ 'shape' => 'GlueStudioPathList', ], 'Table' => [ 'shape' => 'EnclosedInStringProperty', ], 'Database' => [ 'shape' => 'EnclosedInStringProperty', ], 'AdditionalOptions' => [ 'shape' => 'AdditionalOptions', ], 'SchemaChangePolicy' => [ 'shape' => 'CatalogSchemaChangePolicy', ], ], ], 'S3DeltaDirectTarget' => [ 'type' => 'structure', 'required' => [ 'Name', 'Inputs', 'Path', 'Compression', 'Format', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Inputs' => [ 'shape' => 'OneInput', ], 'PartitionKeys' => [ 'shape' => 'GlueStudioPathList', ], 'Path' => [ 'shape' => 'EnclosedInStringProperty', ], 'Compression' => [ 'shape' => 'DeltaTargetCompressionType', ], 'NumberTargetPartitions' => [ 'shape' => 'NumberTargetPartitionsString', ], 'Format' => [ 'shape' => 'TargetFormat', ], 'AdditionalOptions' => [ 'shape' => 'AdditionalOptions', ], 'SchemaChangePolicy' => [ 'shape' => 'DirectSchemaChangePolicy', ], ], ], 'S3DeltaSource' => [ 'type' => 'structure', 'required' => [ 'Name', 'Paths', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Paths' => [ 'shape' => 'EnclosedInStringProperties', ], 'AdditionalDeltaOptions' => [ 'shape' => 'AdditionalOptions', ], 'AdditionalOptions' => [ 'shape' => 'S3DirectSourceAdditionalOptions', ], 'OutputSchemas' => [ 'shape' => 'GlueSchemas', ], ], ], 'S3DirectSourceAdditionalOptions' => [ 'type' => 'structure', 'members' => [ 'BoundedSize' => [ 'shape' => 'BoxedLong', ], 'BoundedFiles' => [ 'shape' => 'BoxedLong', ], 'EnableSamplePath' => [ 'shape' => 'BoxedBoolean', ], 'SamplePath' => [ 'shape' => 'EnclosedInStringProperty', ], ], ], 'S3DirectTarget' => [ 'type' => 'structure', 'required' => [ 'Name', 'Inputs', 'Path', 'Format', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Inputs' => [ 'shape' => 'OneInput', ], 'PartitionKeys' => [ 'shape' => 'GlueStudioPathList', ], 'Path' => [ 'shape' => 'EnclosedInStringProperty', ], 'Compression' => [ 'shape' => 'EnclosedInStringProperty', ], 'NumberTargetPartitions' => [ 'shape' => 'NumberTargetPartitionsString', ], 'Format' => [ 'shape' => 'TargetFormat', ], 'SchemaChangePolicy' => [ 'shape' => 'DirectSchemaChangePolicy', ], ], ], 'S3Encryption' => [ 'type' => 'structure', 'members' => [ 'S3EncryptionMode' => [ 'shape' => 'S3EncryptionMode', ], 'KmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], ], ], 'S3EncryptionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'S3Encryption', ], ], 'S3EncryptionMode' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'SSE-KMS', 'SSE-S3', ], ], 'S3ExcelSource' => [ 'type' => 'structure', 'required' => [ 'Name', 'Paths', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Paths' => [ 'shape' => 'EnclosedInStringProperties', ], 'CompressionType' => [ 'shape' => 'ParquetCompressionType', ], 'Exclusions' => [ 'shape' => 'EnclosedInStringProperties', ], 'GroupSize' => [ 'shape' => 'EnclosedInStringProperty', ], 'GroupFiles' => [ 'shape' => 'EnclosedInStringProperty', ], 'Recurse' => [ 'shape' => 'BoxedBoolean', ], 'MaxBand' => [ 'shape' => 'BoxedNonNegativeInt', ], 'MaxFilesInBand' => [ 'shape' => 'BoxedNonNegativeInt', ], 'AdditionalOptions' => [ 'shape' => 'S3DirectSourceAdditionalOptions', ], 'NumberRows' => [ 'shape' => 'BoxedLong', ], 'SkipFooter' => [ 'shape' => 'BoxedNonNegativeInt', ], 'OutputSchemas' => [ 'shape' => 'GlueSchemas', ], ], ], 'S3GlueParquetTarget' => [ 'type' => 'structure', 'required' => [ 'Name', 'Inputs', 'Path', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Inputs' => [ 'shape' => 'OneInput', ], 'PartitionKeys' => [ 'shape' => 'GlueStudioPathList', ], 'Path' => [ 'shape' => 'EnclosedInStringProperty', ], 'Compression' => [ 'shape' => 'ParquetCompressionType', ], 'NumberTargetPartitions' => [ 'shape' => 'NumberTargetPartitionsString', ], 'SchemaChangePolicy' => [ 'shape' => 'DirectSchemaChangePolicy', ], ], ], 'S3HudiCatalogTarget' => [ 'type' => 'structure', 'required' => [ 'Name', 'Inputs', 'Table', 'Database', 'AdditionalOptions', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Inputs' => [ 'shape' => 'OneInput', ], 'PartitionKeys' => [ 'shape' => 'GlueStudioPathList', ], 'Table' => [ 'shape' => 'EnclosedInStringProperty', ], 'Database' => [ 'shape' => 'EnclosedInStringProperty', ], 'AdditionalOptions' => [ 'shape' => 'AdditionalOptions', ], 'SchemaChangePolicy' => [ 'shape' => 'CatalogSchemaChangePolicy', ], ], ], 'S3HudiDirectTarget' => [ 'type' => 'structure', 'required' => [ 'Name', 'Inputs', 'Path', 'Compression', 'Format', 'AdditionalOptions', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Inputs' => [ 'shape' => 'OneInput', ], 'Path' => [ 'shape' => 'EnclosedInStringProperty', ], 'Compression' => [ 'shape' => 'HudiTargetCompressionType', ], 'NumberTargetPartitions' => [ 'shape' => 'NumberTargetPartitionsString', ], 'PartitionKeys' => [ 'shape' => 'GlueStudioPathList', ], 'Format' => [ 'shape' => 'TargetFormat', ], 'AdditionalOptions' => [ 'shape' => 'AdditionalOptions', ], 'SchemaChangePolicy' => [ 'shape' => 'DirectSchemaChangePolicy', ], ], ], 'S3HudiSource' => [ 'type' => 'structure', 'required' => [ 'Name', 'Paths', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Paths' => [ 'shape' => 'EnclosedInStringProperties', ], 'AdditionalHudiOptions' => [ 'shape' => 'AdditionalOptions', ], 'AdditionalOptions' => [ 'shape' => 'S3DirectSourceAdditionalOptions', ], 'OutputSchemas' => [ 'shape' => 'GlueSchemas', ], ], ], 'S3HyperDirectTarget' => [ 'type' => 'structure', 'required' => [ 'Name', 'Inputs', 'Path', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Inputs' => [ 'shape' => 'OneInput', ], 'PartitionKeys' => [ 'shape' => 'GlueStudioPathList', ], 'Path' => [ 'shape' => 'EnclosedInStringProperty', ], 'Compression' => [ 'shape' => 'HyperTargetCompressionType', ], 'SchemaChangePolicy' => [ 'shape' => 'DirectSchemaChangePolicy', ], ], ], 'S3IcebergDirectTarget' => [ 'type' => 'structure', 'required' => [ 'Name', 'Inputs', 'Path', 'Format', 'Compression', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Inputs' => [ 'shape' => 'OneInput', ], 'PartitionKeys' => [ 'shape' => 'GlueStudioPathList', ], 'Path' => [ 'shape' => 'EnclosedInStringProperty', ], 'Format' => [ 'shape' => 'TargetFormat', ], 'AdditionalOptions' => [ 'shape' => 'AdditionalOptions', ], 'SchemaChangePolicy' => [ 'shape' => 'DirectSchemaChangePolicy', ], 'Compression' => [ 'shape' => 'IcebergTargetCompressionType', ], 'NumberTargetPartitions' => [ 'shape' => 'NumberTargetPartitionsString', ], ], ], 'S3JsonSource' => [ 'type' => 'structure', 'required' => [ 'Name', 'Paths', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Paths' => [ 'shape' => 'EnclosedInStringProperties', ], 'CompressionType' => [ 'shape' => 'CompressionType', ], 'Exclusions' => [ 'shape' => 'EnclosedInStringProperties', ], 'GroupSize' => [ 'shape' => 'EnclosedInStringProperty', ], 'GroupFiles' => [ 'shape' => 'EnclosedInStringProperty', ], 'Recurse' => [ 'shape' => 'BoxedBoolean', ], 'MaxBand' => [ 'shape' => 'BoxedNonNegativeInt', ], 'MaxFilesInBand' => [ 'shape' => 'BoxedNonNegativeInt', ], 'AdditionalOptions' => [ 'shape' => 'S3DirectSourceAdditionalOptions', ], 'JsonPath' => [ 'shape' => 'EnclosedInStringProperty', ], 'Multiline' => [ 'shape' => 'BoxedBoolean', ], 'OutputSchemas' => [ 'shape' => 'GlueSchemas', ], ], ], 'S3ParquetSource' => [ 'type' => 'structure', 'required' => [ 'Name', 'Paths', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Paths' => [ 'shape' => 'EnclosedInStringProperties', ], 'CompressionType' => [ 'shape' => 'ParquetCompressionType', ], 'Exclusions' => [ 'shape' => 'EnclosedInStringProperties', ], 'GroupSize' => [ 'shape' => 'EnclosedInStringProperty', ], 'GroupFiles' => [ 'shape' => 'EnclosedInStringProperty', ], 'Recurse' => [ 'shape' => 'BoxedBoolean', ], 'MaxBand' => [ 'shape' => 'BoxedNonNegativeInt', ], 'MaxFilesInBand' => [ 'shape' => 'BoxedNonNegativeInt', ], 'AdditionalOptions' => [ 'shape' => 'S3DirectSourceAdditionalOptions', ], 'OutputSchemas' => [ 'shape' => 'GlueSchemas', ], ], ], 'S3SourceAdditionalOptions' => [ 'type' => 'structure', 'members' => [ 'BoundedSize' => [ 'shape' => 'BoxedLong', ], 'BoundedFiles' => [ 'shape' => 'BoxedLong', ], ], ], 'S3Target' => [ 'type' => 'structure', 'members' => [ 'Path' => [ 'shape' => 'Path', ], 'Exclusions' => [ 'shape' => 'PathList', ], 'ConnectionName' => [ 'shape' => 'ConnectionName', ], 'SampleSize' => [ 'shape' => 'NullableInteger', ], 'EventQueueArn' => [ 'shape' => 'EventQueueArn', ], 'DlqEventQueueArn' => [ 'shape' => 'EventQueueArn', ], ], ], 'S3TargetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'S3Target', ], ], 'SampleSizePercentage' => [ 'type' => 'double', 'max' => 100, 'min' => 0, ], 'ScalaCode' => [ 'type' => 'string', ], 'Schedule' => [ 'type' => 'structure', 'members' => [ 'ScheduleExpression' => [ 'shape' => 'CronExpression', ], 'State' => [ 'shape' => 'ScheduleState', ], ], ], 'ScheduleState' => [ 'type' => 'string', 'enum' => [ 'SCHEDULED', 'NOT_SCHEDULED', 'TRANSITIONING', ], ], 'ScheduleType' => [ 'type' => 'string', 'enum' => [ 'CRON', 'AUTO', ], ], 'SchedulerNotRunningException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'SchedulerRunningException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'SchedulerTransitioningException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'SchemaChangePolicy' => [ 'type' => 'structure', 'members' => [ 'UpdateBehavior' => [ 'shape' => 'UpdateBehavior', ], 'DeleteBehavior' => [ 'shape' => 'DeleteBehavior', ], ], ], 'SchemaCheckpointNumber' => [ 'type' => 'long', 'max' => 100000, 'min' => 1, ], 'SchemaColumn' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'ColumnNameString', ], 'DataType' => [ 'shape' => 'ColumnTypeString', ], ], ], 'SchemaDefinitionDiff' => [ 'type' => 'string', 'max' => 340000, 'min' => 1, 'pattern' => '.*\\S.*', ], 'SchemaDefinitionString' => [ 'type' => 'string', 'max' => 170000, 'min' => 1, 'pattern' => '.*\\S.*', ], 'SchemaDiffType' => [ 'type' => 'string', 'enum' => [ 'SYNTAX_DIFF', ], ], 'SchemaId' => [ 'type' => 'structure', 'members' => [ 'SchemaArn' => [ 'shape' => 'GlueResourceArn', ], 'SchemaName' => [ 'shape' => 'SchemaRegistryNameString', ], 'RegistryName' => [ 'shape' => 'SchemaRegistryNameString', ], ], ], 'SchemaListDefinition' => [ 'type' => 'list', 'member' => [ 'shape' => 'SchemaListItem', ], ], 'SchemaListItem' => [ 'type' => 'structure', 'members' => [ 'RegistryName' => [ 'shape' => 'SchemaRegistryNameString', ], 'SchemaName' => [ 'shape' => 'SchemaRegistryNameString', ], 'SchemaArn' => [ 'shape' => 'GlueResourceArn', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'SchemaStatus' => [ 'shape' => 'SchemaStatus', ], 'CreatedTime' => [ 'shape' => 'CreatedTimestamp', ], 'UpdatedTime' => [ 'shape' => 'UpdatedTimestamp', ], ], ], 'SchemaPathString' => [ 'type' => 'string', ], 'SchemaReference' => [ 'type' => 'structure', 'members' => [ 'SchemaId' => [ 'shape' => 'SchemaId', ], 'SchemaVersionId' => [ 'shape' => 'SchemaVersionIdString', ], 'SchemaVersionNumber' => [ 'shape' => 'VersionLongNumber', 'box' => true, ], ], ], 'SchemaRegistryNameString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[a-zA-Z0-9-_$#.]+', ], 'SchemaRegistryTokenString' => [ 'type' => 'string', ], 'SchemaStatus' => [ 'type' => 'string', 'enum' => [ 'AVAILABLE', 'PENDING', 'DELETING', ], ], 'SchemaValidationError' => [ 'type' => 'string', 'max' => 5000, 'min' => 1, ], 'SchemaVersionErrorItem' => [ 'type' => 'structure', 'members' => [ 'VersionNumber' => [ 'shape' => 'VersionLongNumber', ], 'ErrorDetails' => [ 'shape' => 'ErrorDetails', ], ], ], 'SchemaVersionErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SchemaVersionErrorItem', ], ], 'SchemaVersionIdString' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}', ], 'SchemaVersionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SchemaVersionListItem', ], ], 'SchemaVersionListItem' => [ 'type' => 'structure', 'members' => [ 'SchemaArn' => [ 'shape' => 'GlueResourceArn', ], 'SchemaVersionId' => [ 'shape' => 'SchemaVersionIdString', ], 'VersionNumber' => [ 'shape' => 'VersionLongNumber', ], 'Status' => [ 'shape' => 'SchemaVersionStatus', ], 'CreatedTime' => [ 'shape' => 'CreatedTimestamp', ], ], ], 'SchemaVersionNumber' => [ 'type' => 'structure', 'members' => [ 'LatestVersion' => [ 'shape' => 'LatestSchemaVersionBoolean', ], 'VersionNumber' => [ 'shape' => 'VersionLongNumber', ], ], ], 'SchemaVersionStatus' => [ 'type' => 'string', 'enum' => [ 'AVAILABLE', 'PENDING', 'FAILURE', 'DELETING', ], ], 'ScriptLocationString' => [ 'type' => 'string', 'max' => 400000, ], 'SearchPropertyPredicates' => [ 'type' => 'list', 'member' => [ 'shape' => 'PropertyPredicate', ], ], 'SearchTablesRequest' => [ 'type' => 'structure', 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'NextToken' => [ 'shape' => 'Token', ], 'Filters' => [ 'shape' => 'SearchPropertyPredicates', ], 'SearchText' => [ 'shape' => 'ValueString', ], 'SortCriteria' => [ 'shape' => 'SortCriteria', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'ResourceShareType' => [ 'shape' => 'ResourceShareType', ], 'IncludeStatusDetails' => [ 'shape' => 'BooleanNullable', ], ], ], 'SearchTablesResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'Token', ], 'TableList' => [ 'shape' => 'TableList', ], ], ], 'SecretArn' => [ 'type' => 'string', 'pattern' => '^arn:aws(-(cn|us-gov|iso(-[bef])?))?:secretsmanager:.*$', ], 'SecurityConfiguration' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'CreatedTimeStamp' => [ 'shape' => 'TimestampValue', ], 'EncryptionConfiguration' => [ 'shape' => 'EncryptionConfiguration', ], ], ], 'SecurityConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityConfiguration', ], ], 'SecurityGroupIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], 'max' => 50, 'min' => 0, ], 'Segment' => [ 'type' => 'structure', 'required' => [ 'SegmentNumber', 'TotalSegments', ], 'members' => [ 'SegmentNumber' => [ 'shape' => 'NonNegativeInteger', ], 'TotalSegments' => [ 'shape' => 'TotalSegmentsInteger', ], ], ], 'SelectFields' => [ 'type' => 'structure', 'required' => [ 'Name', 'Inputs', 'Paths', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Inputs' => [ 'shape' => 'OneInput', ], 'Paths' => [ 'shape' => 'GlueStudioPathList', ], ], ], 'SelectFromCollection' => [ 'type' => 'structure', 'required' => [ 'Name', 'Inputs', 'Index', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Inputs' => [ 'shape' => 'OneInput', ], 'Index' => [ 'shape' => 'NonNegativeInt', ], ], ], 'SelectedFields' => [ 'type' => 'list', 'member' => [ 'shape' => 'EntityFieldName', ], 'max' => 1000, 'min' => 1, ], 'Separator' => [ 'type' => 'string', 'enum' => [ 'comma', 'ctrla', 'pipe', 'semicolon', 'tab', ], ], 'SerDeInfo' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'SerializationLibrary' => [ 'shape' => 'NameString', ], 'Parameters' => [ 'shape' => 'ParametersMap', ], ], ], 'Session' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'NameString', ], 'CreatedOn' => [ 'shape' => 'TimestampValue', ], 'Status' => [ 'shape' => 'SessionStatus', ], 'ErrorMessage' => [ 'shape' => 'DescriptionString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'Role' => [ 'shape' => 'OrchestrationRoleArn', ], 'Command' => [ 'shape' => 'SessionCommand', ], 'DefaultArguments' => [ 'shape' => 'OrchestrationArgumentsMap', ], 'Connections' => [ 'shape' => 'ConnectionsList', ], 'Progress' => [ 'shape' => 'DoubleValue', ], 'MaxCapacity' => [ 'shape' => 'NullableDouble', ], 'SecurityConfiguration' => [ 'shape' => 'NameString', ], 'GlueVersion' => [ 'shape' => 'GlueVersionString', ], 'NumberOfWorkers' => [ 'shape' => 'NullableInteger', ], 'WorkerType' => [ 'shape' => 'WorkerType', ], 'CompletedOn' => [ 'shape' => 'TimestampValue', ], 'ExecutionTime' => [ 'shape' => 'NullableDouble', ], 'DPUSeconds' => [ 'shape' => 'NullableDouble', ], 'IdleTimeout' => [ 'shape' => 'IdleTimeout', ], 'ProfileName' => [ 'shape' => 'NameString', ], ], ], 'SessionCommand' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'PythonVersion' => [ 'shape' => 'PythonVersionString', ], ], ], 'SessionIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], ], 'SessionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Session', ], ], 'SessionStatus' => [ 'type' => 'string', 'enum' => [ 'PROVISIONING', 'READY', 'FAILED', 'TIMEOUT', 'STOPPING', 'STOPPED', ], ], 'SettingSource' => [ 'type' => 'string', 'enum' => [ 'CATALOG', 'TABLE', ], ], 'SkewedInfo' => [ 'type' => 'structure', 'members' => [ 'SkewedColumnNames' => [ 'shape' => 'NameStringList', ], 'SkewedColumnValues' => [ 'shape' => 'ColumnValueStringList', ], 'SkewedColumnValueLocationMaps' => [ 'shape' => 'LocationMap', ], ], ], 'SnowflakeNodeData' => [ 'type' => 'structure', 'members' => [ 'SourceType' => [ 'shape' => 'GenericLimitedString', ], 'Connection' => [ 'shape' => 'Option', ], 'Schema' => [ 'shape' => 'GenericString', ], 'Table' => [ 'shape' => 'GenericString', ], 'Database' => [ 'shape' => 'GenericString', ], 'TempDir' => [ 'shape' => 'EnclosedInStringProperty', ], 'IamRole' => [ 'shape' => 'Option', ], 'AdditionalOptions' => [ 'shape' => 'AdditionalOptions', ], 'SampleQuery' => [ 'shape' => 'GenericString', ], 'PreAction' => [ 'shape' => 'GenericString', ], 'PostAction' => [ 'shape' => 'GenericString', ], 'Action' => [ 'shape' => 'GenericString', ], 'Upsert' => [ 'shape' => 'BooleanValue', ], 'MergeAction' => [ 'shape' => 'GenericLimitedString', ], 'MergeWhenMatched' => [ 'shape' => 'GenericLimitedString', ], 'MergeWhenNotMatched' => [ 'shape' => 'GenericLimitedString', ], 'MergeClause' => [ 'shape' => 'GenericString', ], 'StagingTable' => [ 'shape' => 'GenericString', ], 'SelectedColumns' => [ 'shape' => 'OptionList', ], 'AutoPushdown' => [ 'shape' => 'BooleanValue', ], 'TableSchema' => [ 'shape' => 'OptionList', ], ], ], 'SnowflakeSource' => [ 'type' => 'structure', 'required' => [ 'Name', 'Data', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Data' => [ 'shape' => 'SnowflakeNodeData', ], 'OutputSchemas' => [ 'shape' => 'GlueSchemas', ], ], ], 'SnowflakeTarget' => [ 'type' => 'structure', 'required' => [ 'Name', 'Data', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Data' => [ 'shape' => 'SnowflakeNodeData', ], 'Inputs' => [ 'shape' => 'OneInput', ], ], ], 'Sort' => [ 'type' => 'string', 'enum' => [ 'ASC', 'DESC', ], ], 'SortCriteria' => [ 'type' => 'list', 'member' => [ 'shape' => 'SortCriterion', ], 'max' => 1, 'min' => 0, ], 'SortCriterion' => [ 'type' => 'structure', 'members' => [ 'FieldName' => [ 'shape' => 'ValueString', ], 'Sort' => [ 'shape' => 'Sort', ], ], ], 'SortDirectionType' => [ 'type' => 'string', 'enum' => [ 'DESCENDING', 'ASCENDING', ], ], 'SourceControlAuthStrategy' => [ 'type' => 'string', 'enum' => [ 'PERSONAL_ACCESS_TOKEN', 'AWS_SECRETS_MANAGER', ], ], 'SourceControlDetails' => [ 'type' => 'structure', 'members' => [ 'Provider' => [ 'shape' => 'SourceControlProvider', ], 'Repository' => [ 'shape' => 'Generic512CharString', ], 'Owner' => [ 'shape' => 'Generic512CharString', ], 'Branch' => [ 'shape' => 'Generic512CharString', ], 'Folder' => [ 'shape' => 'Generic512CharString', ], 'LastCommitId' => [ 'shape' => 'Generic512CharString', ], 'AuthStrategy' => [ 'shape' => 'SourceControlAuthStrategy', ], 'AuthToken' => [ 'shape' => 'Generic512CharString', ], ], ], 'SourceControlProvider' => [ 'type' => 'string', 'enum' => [ 'GITHUB', 'GITLAB', 'BITBUCKET', 'AWS_CODE_COMMIT', ], ], 'SourceProcessingProperties' => [ 'type' => 'structure', 'members' => [ 'RoleArn' => [ 'shape' => 'String128', ], ], ], 'SourceTableConfig' => [ 'type' => 'structure', 'members' => [ 'Fields' => [ 'shape' => 'SourceTableFieldsList', ], 'FilterPredicate' => [ 'shape' => 'String128', ], 'PrimaryKey' => [ 'shape' => 'PrimaryKeyList', ], 'RecordUpdateField' => [ 'shape' => 'String128', ], ], ], 'SourceTableFieldsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String128', ], ], 'SparkConnectorSource' => [ 'type' => 'structure', 'required' => [ 'Name', 'ConnectionName', 'ConnectorName', 'ConnectionType', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'ConnectionName' => [ 'shape' => 'EnclosedInStringProperty', ], 'ConnectorName' => [ 'shape' => 'EnclosedInStringProperty', ], 'ConnectionType' => [ 'shape' => 'EnclosedInStringProperty', ], 'AdditionalOptions' => [ 'shape' => 'AdditionalOptions', ], 'OutputSchemas' => [ 'shape' => 'GlueSchemas', ], ], ], 'SparkConnectorTarget' => [ 'type' => 'structure', 'required' => [ 'Name', 'Inputs', 'ConnectionName', 'ConnectorName', 'ConnectionType', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Inputs' => [ 'shape' => 'OneInput', ], 'ConnectionName' => [ 'shape' => 'EnclosedInStringProperty', ], 'ConnectorName' => [ 'shape' => 'EnclosedInStringProperty', ], 'ConnectionType' => [ 'shape' => 'EnclosedInStringProperty', ], 'AdditionalOptions' => [ 'shape' => 'AdditionalOptions', ], 'OutputSchemas' => [ 'shape' => 'GlueSchemas', ], ], ], 'SparkSQL' => [ 'type' => 'structure', 'required' => [ 'Name', 'Inputs', 'SqlQuery', 'SqlAliases', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Inputs' => [ 'shape' => 'ManyInputs', ], 'SqlQuery' => [ 'shape' => 'SqlQuery', ], 'SqlAliases' => [ 'shape' => 'SqlAliases', ], 'OutputSchemas' => [ 'shape' => 'GlueSchemas', ], ], ], 'Spigot' => [ 'type' => 'structure', 'required' => [ 'Name', 'Inputs', 'Path', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Inputs' => [ 'shape' => 'OneInput', ], 'Path' => [ 'shape' => 'EnclosedInStringProperty', ], 'Topk' => [ 'shape' => 'Topk', ], 'Prob' => [ 'shape' => 'Prob', ], ], ], 'SplitFields' => [ 'type' => 'structure', 'required' => [ 'Name', 'Inputs', 'Paths', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Inputs' => [ 'shape' => 'OneInput', ], 'Paths' => [ 'shape' => 'GlueStudioPathList', ], ], ], 'SqlAlias' => [ 'type' => 'structure', 'required' => [ 'From', 'Alias', ], 'members' => [ 'From' => [ 'shape' => 'NodeId', ], 'Alias' => [ 'shape' => 'EnclosedInStringPropertyWithQuote', ], ], ], 'SqlAliases' => [ 'type' => 'list', 'member' => [ 'shape' => 'SqlAlias', ], ], 'SqlQuery' => [ 'type' => 'string', 'pattern' => '([\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\s])*', ], 'StartBlueprintRunRequest' => [ 'type' => 'structure', 'required' => [ 'BlueprintName', 'RoleArn', ], 'members' => [ 'BlueprintName' => [ 'shape' => 'OrchestrationNameString', ], 'Parameters' => [ 'shape' => 'BlueprintParameters', ], 'RoleArn' => [ 'shape' => 'OrchestrationIAMRoleArn', ], ], ], 'StartBlueprintRunResponse' => [ 'type' => 'structure', 'members' => [ 'RunId' => [ 'shape' => 'IdString', ], ], ], 'StartColumnStatisticsTaskRunRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'Role', ], 'members' => [ 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'ColumnNameList' => [ 'shape' => 'ColumnNameList', ], 'Role' => [ 'shape' => 'NameString', ], 'SampleSize' => [ 'shape' => 'SampleSizePercentage', ], 'CatalogID' => [ 'shape' => 'NameString', ], 'SecurityConfiguration' => [ 'shape' => 'NameString', ], ], ], 'StartColumnStatisticsTaskRunResponse' => [ 'type' => 'structure', 'members' => [ 'ColumnStatisticsTaskRunId' => [ 'shape' => 'HashString', ], ], ], 'StartColumnStatisticsTaskRunScheduleRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', ], 'members' => [ 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], ], ], 'StartColumnStatisticsTaskRunScheduleResponse' => [ 'type' => 'structure', 'members' => [], ], 'StartCrawlerRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'StartCrawlerResponse' => [ 'type' => 'structure', 'members' => [], ], 'StartCrawlerScheduleRequest' => [ 'type' => 'structure', 'required' => [ 'CrawlerName', ], 'members' => [ 'CrawlerName' => [ 'shape' => 'NameString', ], ], ], 'StartCrawlerScheduleResponse' => [ 'type' => 'structure', 'members' => [], ], 'StartDataQualityRuleRecommendationRunRequest' => [ 'type' => 'structure', 'required' => [ 'DataSource', 'Role', ], 'members' => [ 'DataSource' => [ 'shape' => 'DataSource', ], 'Role' => [ 'shape' => 'RoleString', ], 'NumberOfWorkers' => [ 'shape' => 'NullableInteger', ], 'Timeout' => [ 'shape' => 'Timeout', ], 'CreatedRulesetName' => [ 'shape' => 'NameString', ], 'DataQualitySecurityConfiguration' => [ 'shape' => 'NameString', ], 'ClientToken' => [ 'shape' => 'HashString', ], ], ], 'StartDataQualityRuleRecommendationRunResponse' => [ 'type' => 'structure', 'members' => [ 'RunId' => [ 'shape' => 'HashString', ], ], ], 'StartDataQualityRulesetEvaluationRunRequest' => [ 'type' => 'structure', 'required' => [ 'DataSource', 'Role', 'RulesetNames', ], 'members' => [ 'DataSource' => [ 'shape' => 'DataSource', ], 'Role' => [ 'shape' => 'RoleString', ], 'NumberOfWorkers' => [ 'shape' => 'NullableInteger', ], 'Timeout' => [ 'shape' => 'Timeout', ], 'ClientToken' => [ 'shape' => 'HashString', ], 'AdditionalRunOptions' => [ 'shape' => 'DataQualityEvaluationRunAdditionalRunOptions', ], 'RulesetNames' => [ 'shape' => 'RulesetNames', ], 'AdditionalDataSources' => [ 'shape' => 'DataSourceMap', ], ], ], 'StartDataQualityRulesetEvaluationRunResponse' => [ 'type' => 'structure', 'members' => [ 'RunId' => [ 'shape' => 'HashString', ], ], ], 'StartExportLabelsTaskRunRequest' => [ 'type' => 'structure', 'required' => [ 'TransformId', 'OutputS3Path', ], 'members' => [ 'TransformId' => [ 'shape' => 'HashString', ], 'OutputS3Path' => [ 'shape' => 'UriString', ], ], ], 'StartExportLabelsTaskRunResponse' => [ 'type' => 'structure', 'members' => [ 'TaskRunId' => [ 'shape' => 'HashString', ], ], ], 'StartImportLabelsTaskRunRequest' => [ 'type' => 'structure', 'required' => [ 'TransformId', 'InputS3Path', ], 'members' => [ 'TransformId' => [ 'shape' => 'HashString', ], 'InputS3Path' => [ 'shape' => 'UriString', ], 'ReplaceAllLabels' => [ 'shape' => 'ReplaceBoolean', ], ], ], 'StartImportLabelsTaskRunResponse' => [ 'type' => 'structure', 'members' => [ 'TaskRunId' => [ 'shape' => 'HashString', ], ], ], 'StartJobRunRequest' => [ 'type' => 'structure', 'required' => [ 'JobName', ], 'members' => [ 'JobName' => [ 'shape' => 'NameString', ], 'JobRunQueuingEnabled' => [ 'shape' => 'NullableBoolean', ], 'JobRunId' => [ 'shape' => 'IdString', ], 'Arguments' => [ 'shape' => 'GenericMap', ], 'AllocatedCapacity' => [ 'shape' => 'IntegerValue', 'deprecated' => true, 'deprecatedMessage' => 'This property is deprecated, use MaxCapacity instead.', ], 'Timeout' => [ 'shape' => 'Timeout', ], 'MaxCapacity' => [ 'shape' => 'NullableDouble', ], 'SecurityConfiguration' => [ 'shape' => 'NameString', ], 'NotificationProperty' => [ 'shape' => 'NotificationProperty', ], 'WorkerType' => [ 'shape' => 'WorkerType', ], 'NumberOfWorkers' => [ 'shape' => 'NullableInteger', ], 'ExecutionClass' => [ 'shape' => 'ExecutionClass', ], ], ], 'StartJobRunResponse' => [ 'type' => 'structure', 'members' => [ 'JobRunId' => [ 'shape' => 'IdString', ], ], ], 'StartMLEvaluationTaskRunRequest' => [ 'type' => 'structure', 'required' => [ 'TransformId', ], 'members' => [ 'TransformId' => [ 'shape' => 'HashString', ], ], ], 'StartMLEvaluationTaskRunResponse' => [ 'type' => 'structure', 'members' => [ 'TaskRunId' => [ 'shape' => 'HashString', ], ], ], 'StartMLLabelingSetGenerationTaskRunRequest' => [ 'type' => 'structure', 'required' => [ 'TransformId', 'OutputS3Path', ], 'members' => [ 'TransformId' => [ 'shape' => 'HashString', ], 'OutputS3Path' => [ 'shape' => 'UriString', ], ], ], 'StartMLLabelingSetGenerationTaskRunResponse' => [ 'type' => 'structure', 'members' => [ 'TaskRunId' => [ 'shape' => 'HashString', ], ], ], 'StartTriggerRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'StartTriggerResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'StartWorkflowRunRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'RunProperties' => [ 'shape' => 'WorkflowRunProperties', ], ], ], 'StartWorkflowRunResponse' => [ 'type' => 'structure', 'members' => [ 'RunId' => [ 'shape' => 'IdString', ], ], ], 'StartingEventBatchCondition' => [ 'type' => 'structure', 'members' => [ 'BatchSize' => [ 'shape' => 'NullableInteger', ], 'BatchWindow' => [ 'shape' => 'NullableInteger', ], ], ], 'StartingPosition' => [ 'type' => 'string', 'enum' => [ 'latest', 'trim_horizon', 'earliest', 'timestamp', ], ], 'Statement' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'IntegerValue', ], 'Code' => [ 'shape' => 'GenericString', ], 'State' => [ 'shape' => 'StatementState', ], 'Output' => [ 'shape' => 'StatementOutput', ], 'Progress' => [ 'shape' => 'DoubleValue', ], 'StartedOn' => [ 'shape' => 'LongValue', ], 'CompletedOn' => [ 'shape' => 'LongValue', ], ], ], 'StatementList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Statement', ], ], 'StatementOutput' => [ 'type' => 'structure', 'members' => [ 'Data' => [ 'shape' => 'StatementOutputData', ], 'ExecutionCount' => [ 'shape' => 'IntegerValue', ], 'Status' => [ 'shape' => 'StatementState', ], 'ErrorName' => [ 'shape' => 'GenericString', ], 'ErrorValue' => [ 'shape' => 'GenericString', ], 'Traceback' => [ 'shape' => 'OrchestrationStringList', ], ], ], 'StatementOutputData' => [ 'type' => 'structure', 'members' => [ 'TextPlain' => [ 'shape' => 'GenericString', ], ], ], 'StatementState' => [ 'type' => 'string', 'enum' => [ 'WAITING', 'RUNNING', 'AVAILABLE', 'CANCELLING', 'CANCELLED', 'ERROR', ], ], 'StatisticAnnotation' => [ 'type' => 'structure', 'members' => [ 'ProfileId' => [ 'shape' => 'HashString', ], 'StatisticId' => [ 'shape' => 'HashString', ], 'StatisticRecordedOn' => [ 'shape' => 'Timestamp', ], 'InclusionAnnotation' => [ 'shape' => 'TimestampedInclusionAnnotation', ], ], ], 'StatisticEvaluationLevel' => [ 'type' => 'string', 'enum' => [ 'Dataset', 'Column', 'Multicolumn', ], ], 'StatisticModelResult' => [ 'type' => 'structure', 'members' => [ 'LowerBound' => [ 'shape' => 'NullableDouble', ], 'UpperBound' => [ 'shape' => 'NullableDouble', ], 'PredictedValue' => [ 'shape' => 'NullableDouble', ], 'ActualValue' => [ 'shape' => 'NullableDouble', ], 'Date' => [ 'shape' => 'Timestamp', ], 'InclusionAnnotation' => [ 'shape' => 'InclusionAnnotationValue', ], ], ], 'StatisticModelResults' => [ 'type' => 'list', 'member' => [ 'shape' => 'StatisticModelResult', ], ], 'StatisticNameString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[A-Z][A-Za-z\\.]+', ], 'StatisticPropertiesMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'NameString', ], 'value' => [ 'shape' => 'DescriptionString', ], 'sensitive' => true, ], 'StatisticSummary' => [ 'type' => 'structure', 'members' => [ 'StatisticId' => [ 'shape' => 'HashString', ], 'ProfileId' => [ 'shape' => 'HashString', ], 'RunIdentifier' => [ 'shape' => 'RunIdentifier', ], 'StatisticName' => [ 'shape' => 'StatisticNameString', ], 'DoubleValue' => [ 'shape' => 'double', ], 'EvaluationLevel' => [ 'shape' => 'StatisticEvaluationLevel', ], 'ColumnsReferenced' => [ 'shape' => 'ColumnNameList', ], 'ReferencedDatasets' => [ 'shape' => 'ReferenceDatasetsList', ], 'StatisticProperties' => [ 'shape' => 'StatisticPropertiesMap', ], 'RecordedOn' => [ 'shape' => 'Timestamp', ], 'InclusionAnnotation' => [ 'shape' => 'TimestampedInclusionAnnotation', ], ], ], 'StatisticSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StatisticSummary', ], ], 'StatusDetails' => [ 'type' => 'structure', 'members' => [ 'RequestedChange' => [ 'shape' => 'Table', ], 'ViewValidations' => [ 'shape' => 'ViewValidationList', ], ], ], 'StopColumnStatisticsTaskRunRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', ], 'members' => [ 'DatabaseName' => [ 'shape' => 'DatabaseName', ], 'TableName' => [ 'shape' => 'NameString', ], ], ], 'StopColumnStatisticsTaskRunResponse' => [ 'type' => 'structure', 'members' => [], ], 'StopColumnStatisticsTaskRunScheduleRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', ], 'members' => [ 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], ], ], 'StopColumnStatisticsTaskRunScheduleResponse' => [ 'type' => 'structure', 'members' => [], ], 'StopCrawlerRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'StopCrawlerResponse' => [ 'type' => 'structure', 'members' => [], ], 'StopCrawlerScheduleRequest' => [ 'type' => 'structure', 'required' => [ 'CrawlerName', ], 'members' => [ 'CrawlerName' => [ 'shape' => 'NameString', ], ], ], 'StopCrawlerScheduleResponse' => [ 'type' => 'structure', 'members' => [], ], 'StopSessionRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'NameString', ], 'RequestOrigin' => [ 'shape' => 'OrchestrationNameString', ], ], ], 'StopSessionResponse' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'NameString', ], ], ], 'StopTriggerRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'StopTriggerResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'StopWorkflowRunRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'RunId', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'RunId' => [ 'shape' => 'IdString', ], ], ], 'StopWorkflowRunResponse' => [ 'type' => 'structure', 'members' => [], ], 'StorageDescriptor' => [ 'type' => 'structure', 'members' => [ 'Columns' => [ 'shape' => 'ColumnList', ], 'Location' => [ 'shape' => 'LocationString', ], 'AdditionalLocations' => [ 'shape' => 'LocationStringList', ], 'InputFormat' => [ 'shape' => 'FormatString', ], 'OutputFormat' => [ 'shape' => 'FormatString', ], 'Compressed' => [ 'shape' => 'Boolean', ], 'NumberOfBuckets' => [ 'shape' => 'Integer', ], 'SerdeInfo' => [ 'shape' => 'SerDeInfo', ], 'BucketColumns' => [ 'shape' => 'NameStringList', ], 'SortColumns' => [ 'shape' => 'OrderList', ], 'Parameters' => [ 'shape' => 'ParametersMap', ], 'SkewedInfo' => [ 'shape' => 'SkewedInfo', ], 'StoredAsSubDirectories' => [ 'shape' => 'Boolean', ], 'SchemaReference' => [ 'shape' => 'SchemaReference', ], ], ], 'StreamingDataPreviewOptions' => [ 'type' => 'structure', 'members' => [ 'PollingTime' => [ 'shape' => 'PollingTime', ], 'RecordPollingLimit' => [ 'shape' => 'PositiveLong', ], ], ], 'String' => [ 'type' => 'string', ], 'String128' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'String2048' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'StringColumnStatisticsData' => [ 'type' => 'structure', 'required' => [ 'MaximumLength', 'AverageLength', 'NumberOfNulls', 'NumberOfDistinctValues', ], 'members' => [ 'MaximumLength' => [ 'shape' => 'NonNegativeLong', ], 'AverageLength' => [ 'shape' => 'NonNegativeDouble', ], 'NumberOfNulls' => [ 'shape' => 'NonNegativeLong', ], 'NumberOfDistinctValues' => [ 'shape' => 'NonNegativeLong', ], ], ], 'StringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GenericString', ], ], 'SupportedDialect' => [ 'type' => 'structure', 'members' => [ 'Dialect' => [ 'shape' => 'ViewDialect', ], 'DialectVersion' => [ 'shape' => 'ViewDialectVersionString', ], ], ], 'Table' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'Owner' => [ 'shape' => 'NameString', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], 'LastAccessTime' => [ 'shape' => 'Timestamp', ], 'LastAnalyzedTime' => [ 'shape' => 'Timestamp', ], 'Retention' => [ 'shape' => 'NonNegativeInteger', ], 'StorageDescriptor' => [ 'shape' => 'StorageDescriptor', ], 'PartitionKeys' => [ 'shape' => 'ColumnList', ], 'ViewOriginalText' => [ 'shape' => 'ViewTextString', ], 'ViewExpandedText' => [ 'shape' => 'ViewTextString', ], 'TableType' => [ 'shape' => 'TableTypeString', ], 'Parameters' => [ 'shape' => 'ParametersMap', ], 'CreatedBy' => [ 'shape' => 'NameString', ], 'IsRegisteredWithLakeFormation' => [ 'shape' => 'Boolean', ], 'TargetTable' => [ 'shape' => 'TableIdentifier', ], 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'VersionId' => [ 'shape' => 'VersionString', ], 'FederatedTable' => [ 'shape' => 'FederatedTable', ], 'ViewDefinition' => [ 'shape' => 'ViewDefinition', ], 'IsMultiDialectView' => [ 'shape' => 'NullableBoolean', ], 'Status' => [ 'shape' => 'TableStatus', ], ], ], 'TableAttributes' => [ 'type' => 'string', 'enum' => [ 'NAME', 'TABLE_TYPE', ], ], 'TableAttributesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TableAttributes', ], ], 'TableError' => [ 'type' => 'structure', 'members' => [ 'TableName' => [ 'shape' => 'NameString', ], 'ErrorDetail' => [ 'shape' => 'ErrorDetail', ], ], ], 'TableErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'TableError', ], ], 'TableIdentifier' => [ 'type' => 'structure', 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'Name' => [ 'shape' => 'NameString', ], 'Region' => [ 'shape' => 'NameString', ], ], ], 'TableInput' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'Owner' => [ 'shape' => 'NameString', ], 'LastAccessTime' => [ 'shape' => 'Timestamp', ], 'LastAnalyzedTime' => [ 'shape' => 'Timestamp', ], 'Retention' => [ 'shape' => 'NonNegativeInteger', ], 'StorageDescriptor' => [ 'shape' => 'StorageDescriptor', ], 'PartitionKeys' => [ 'shape' => 'ColumnList', ], 'ViewOriginalText' => [ 'shape' => 'ViewTextString', ], 'ViewExpandedText' => [ 'shape' => 'ViewTextString', ], 'TableType' => [ 'shape' => 'TableTypeString', ], 'Parameters' => [ 'shape' => 'ParametersMap', ], 'TargetTable' => [ 'shape' => 'TableIdentifier', ], 'ViewDefinition' => [ 'shape' => 'ViewDefinitionInput', ], ], ], 'TableList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Table', ], ], 'TableName' => [ 'type' => 'string', ], 'TableOptimizer' => [ 'type' => 'structure', 'members' => [ 'type' => [ 'shape' => 'TableOptimizerType', ], 'configuration' => [ 'shape' => 'TableOptimizerConfiguration', ], 'lastRun' => [ 'shape' => 'TableOptimizerRun', ], ], ], 'TableOptimizerConfiguration' => [ 'type' => 'structure', 'members' => [ 'roleArn' => [ 'shape' => 'ArnString', ], 'enabled' => [ 'shape' => 'NullableBoolean', ], 'vpcConfiguration' => [ 'shape' => 'TableOptimizerVpcConfiguration', ], 'compactionConfiguration' => [ 'shape' => 'CompactionConfiguration', ], 'retentionConfiguration' => [ 'shape' => 'RetentionConfiguration', ], 'orphanFileDeletionConfiguration' => [ 'shape' => 'OrphanFileDeletionConfiguration', ], ], ], 'TableOptimizerEventType' => [ 'type' => 'string', 'enum' => [ 'starting', 'completed', 'failed', 'in_progress', ], ], 'TableOptimizerRun' => [ 'type' => 'structure', 'members' => [ 'eventType' => [ 'shape' => 'TableOptimizerEventType', ], 'startTimestamp' => [ 'shape' => 'TableOptimizerRunTimestamp', ], 'endTimestamp' => [ 'shape' => 'TableOptimizerRunTimestamp', ], 'metrics' => [ 'shape' => 'RunMetrics', 'deprecated' => true, 'deprecatedMessage' => 'Metrics has been replaced by optimizer type specific metrics such as IcebergCompactionMetrics', ], 'error' => [ 'shape' => 'MessageString', ], 'compactionMetrics' => [ 'shape' => 'CompactionMetrics', ], 'compactionStrategy' => [ 'shape' => 'CompactionStrategy', ], 'retentionMetrics' => [ 'shape' => 'RetentionMetrics', ], 'orphanFileDeletionMetrics' => [ 'shape' => 'OrphanFileDeletionMetrics', ], ], ], 'TableOptimizerRunTimestamp' => [ 'type' => 'timestamp', ], 'TableOptimizerRuns' => [ 'type' => 'list', 'member' => [ 'shape' => 'TableOptimizerRun', ], ], 'TableOptimizerType' => [ 'type' => 'string', 'enum' => [ 'compaction', 'retention', 'orphan_file_deletion', ], ], 'TableOptimizerVpcConfiguration' => [ 'type' => 'structure', 'members' => [ 'glueConnectionName' => [ 'shape' => 'glueConnectionNameString', ], ], 'union' => true, ], 'TablePrefix' => [ 'type' => 'string', 'max' => 128, 'min' => 0, ], 'TableStatus' => [ 'type' => 'structure', 'members' => [ 'RequestedBy' => [ 'shape' => 'NameString', ], 'UpdatedBy' => [ 'shape' => 'NameString', ], 'RequestTime' => [ 'shape' => 'Timestamp', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], 'Action' => [ 'shape' => 'ResourceAction', ], 'State' => [ 'shape' => 'ResourceState', ], 'Error' => [ 'shape' => 'ErrorDetail', ], 'Details' => [ 'shape' => 'StatusDetails', ], ], ], 'TableTypeString' => [ 'type' => 'string', 'max' => 255, ], 'TableVersion' => [ 'type' => 'structure', 'members' => [ 'Table' => [ 'shape' => 'Table', ], 'VersionId' => [ 'shape' => 'VersionString', ], ], ], 'TableVersionError' => [ 'type' => 'structure', 'members' => [ 'TableName' => [ 'shape' => 'NameString', ], 'VersionId' => [ 'shape' => 'VersionString', ], 'ErrorDetail' => [ 'shape' => 'ErrorDetail', ], ], ], 'TableVersionErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'TableVersionError', ], ], 'Tag' => [ 'type' => 'structure', 'members' => [ 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeysList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagsToAdd', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'GlueResourceArn', ], 'TagsToAdd' => [ 'shape' => 'TagsMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'TagsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 50, 'min' => 0, ], 'TargetColumn' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'TargetFormat' => [ 'type' => 'string', 'enum' => [ 'json', 'csv', 'avro', 'orc', 'parquet', 'hudi', 'delta', 'iceberg', 'hyper', 'xml', ], ], 'TargetProcessingProperties' => [ 'type' => 'structure', 'members' => [ 'RoleArn' => [ 'shape' => 'String128', ], 'KmsArn' => [ 'shape' => 'String2048', ], 'ConnectionName' => [ 'shape' => 'String128', ], 'EventBusArn' => [ 'shape' => 'String2048', ], ], ], 'TargetRedshiftCatalog' => [ 'type' => 'structure', 'required' => [ 'CatalogArn', ], 'members' => [ 'CatalogArn' => [ 'shape' => 'ResourceArnString', ], ], ], 'TargetResourceNotFound' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'IntegrationErrorMessage', ], ], 'exception' => true, ], 'TargetTableConfig' => [ 'type' => 'structure', 'members' => [ 'UnnestSpec' => [ 'shape' => 'UnnestSpec', ], 'PartitionSpec' => [ 'shape' => 'IntegrationPartitionSpecList', ], 'TargetTableName' => [ 'shape' => 'String128', ], ], ], 'TaskRun' => [ 'type' => 'structure', 'members' => [ 'TransformId' => [ 'shape' => 'HashString', ], 'TaskRunId' => [ 'shape' => 'HashString', ], 'Status' => [ 'shape' => 'TaskStatusType', ], 'LogGroupName' => [ 'shape' => 'GenericString', ], 'Properties' => [ 'shape' => 'TaskRunProperties', ], 'ErrorString' => [ 'shape' => 'GenericString', ], 'StartedOn' => [ 'shape' => 'Timestamp', ], 'LastModifiedOn' => [ 'shape' => 'Timestamp', ], 'CompletedOn' => [ 'shape' => 'Timestamp', ], 'ExecutionTime' => [ 'shape' => 'ExecutionTime', ], ], ], 'TaskRunFilterCriteria' => [ 'type' => 'structure', 'members' => [ 'TaskRunType' => [ 'shape' => 'TaskType', ], 'Status' => [ 'shape' => 'TaskStatusType', ], 'StartedBefore' => [ 'shape' => 'Timestamp', ], 'StartedAfter' => [ 'shape' => 'Timestamp', ], ], ], 'TaskRunList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TaskRun', ], ], 'TaskRunProperties' => [ 'type' => 'structure', 'members' => [ 'TaskType' => [ 'shape' => 'TaskType', ], 'ImportLabelsTaskRunProperties' => [ 'shape' => 'ImportLabelsTaskRunProperties', ], 'ExportLabelsTaskRunProperties' => [ 'shape' => 'ExportLabelsTaskRunProperties', ], 'LabelingSetGenerationTaskRunProperties' => [ 'shape' => 'LabelingSetGenerationTaskRunProperties', ], 'FindMatchesTaskRunProperties' => [ 'shape' => 'FindMatchesTaskRunProperties', ], ], ], 'TaskRunSortColumnType' => [ 'type' => 'string', 'enum' => [ 'TASK_RUN_TYPE', 'STATUS', 'STARTED', ], ], 'TaskRunSortCriteria' => [ 'type' => 'structure', 'required' => [ 'Column', 'SortDirection', ], 'members' => [ 'Column' => [ 'shape' => 'TaskRunSortColumnType', ], 'SortDirection' => [ 'shape' => 'SortDirectionType', ], ], ], 'TaskStatusType' => [ 'type' => 'string', 'enum' => [ 'STARTING', 'RUNNING', 'STOPPING', 'STOPPED', 'SUCCEEDED', 'FAILED', 'TIMEOUT', ], ], 'TaskType' => [ 'type' => 'string', 'enum' => [ 'EVALUATION', 'LABELING_SET_GENERATION', 'IMPORT_LABELS', 'EXPORT_LABELS', 'FIND_MATCHES', ], ], 'TestConnectionInput' => [ 'type' => 'structure', 'required' => [ 'ConnectionType', 'ConnectionProperties', ], 'members' => [ 'ConnectionType' => [ 'shape' => 'ConnectionType', ], 'ConnectionProperties' => [ 'shape' => 'ConnectionProperties', ], 'AuthenticationConfiguration' => [ 'shape' => 'AuthenticationConfigurationInput', ], ], ], 'TestConnectionRequest' => [ 'type' => 'structure', 'members' => [ 'ConnectionName' => [ 'shape' => 'NameString', ], 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'TestConnectionInput' => [ 'shape' => 'TestConnectionInput', ], ], ], 'TestConnectionResponse' => [ 'type' => 'structure', 'members' => [], ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'Timeout' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TimestampFilter' => [ 'type' => 'structure', 'members' => [ 'RecordedBefore' => [ 'shape' => 'Timestamp', ], 'RecordedAfter' => [ 'shape' => 'Timestamp', ], ], ], 'TimestampValue' => [ 'type' => 'timestamp', ], 'TimestampedInclusionAnnotation' => [ 'type' => 'structure', 'members' => [ 'Value' => [ 'shape' => 'InclusionAnnotationValue', ], 'LastModifiedOn' => [ 'shape' => 'Timestamp', ], ], ], 'Token' => [ 'type' => 'string', ], 'TokenUrl' => [ 'type' => 'string', 'max' => 256, 'pattern' => '^(https?)://[-a-zA-Z0-9+&@#/%?=~_|!:,.;]*[-a-zA-Z0-9+&@#/%=~_|]', ], 'TokenUrlParameterKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TokenUrlParameterValue' => [ 'type' => 'string', 'max' => 512, 'min' => 1, ], 'TokenUrlParametersMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TokenUrlParameterKey', ], 'value' => [ 'shape' => 'TokenUrlParameterValue', ], ], 'Topk' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 0, ], 'TotalSegmentsInteger' => [ 'type' => 'integer', 'max' => 10, 'min' => 1, ], 'TransactionIdString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\p{L}\\p{N}\\p{P}]*', ], 'TransformConfigParameter' => [ 'type' => 'structure', 'required' => [ 'Name', 'Type', ], 'members' => [ 'Name' => [ 'shape' => 'EnclosedInStringProperty', ], 'Type' => [ 'shape' => 'ParamType', ], 'ValidationRule' => [ 'shape' => 'EnclosedInStringProperty', ], 'ValidationMessage' => [ 'shape' => 'EnclosedInStringProperty', ], 'Value' => [ 'shape' => 'EnclosedInStringProperties', ], 'ListType' => [ 'shape' => 'ParamType', ], 'IsOptional' => [ 'shape' => 'BoxedBoolean', ], ], ], 'TransformConfigParameterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TransformConfigParameter', ], ], 'TransformEncryption' => [ 'type' => 'structure', 'members' => [ 'MlUserDataEncryption' => [ 'shape' => 'MLUserDataEncryption', ], 'TaskRunSecurityConfigurationName' => [ 'shape' => 'NameString', ], ], ], 'TransformFilterCriteria' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'TransformType' => [ 'shape' => 'TransformType', ], 'Status' => [ 'shape' => 'TransformStatusType', ], 'GlueVersion' => [ 'shape' => 'GlueVersionString', ], 'CreatedBefore' => [ 'shape' => 'Timestamp', ], 'CreatedAfter' => [ 'shape' => 'Timestamp', ], 'LastModifiedBefore' => [ 'shape' => 'Timestamp', ], 'LastModifiedAfter' => [ 'shape' => 'Timestamp', ], 'Schema' => [ 'shape' => 'TransformSchema', ], ], ], 'TransformIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'HashString', ], ], 'TransformList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MLTransform', ], ], 'TransformParameters' => [ 'type' => 'structure', 'required' => [ 'TransformType', ], 'members' => [ 'TransformType' => [ 'shape' => 'TransformType', ], 'FindMatchesParameters' => [ 'shape' => 'FindMatchesParameters', ], ], ], 'TransformSchema' => [ 'type' => 'list', 'member' => [ 'shape' => 'SchemaColumn', ], 'max' => 100, ], 'TransformSortColumnType' => [ 'type' => 'string', 'enum' => [ 'NAME', 'TRANSFORM_TYPE', 'STATUS', 'CREATED', 'LAST_MODIFIED', ], ], 'TransformSortCriteria' => [ 'type' => 'structure', 'required' => [ 'Column', 'SortDirection', ], 'members' => [ 'Column' => [ 'shape' => 'TransformSortColumnType', ], 'SortDirection' => [ 'shape' => 'SortDirectionType', ], ], ], 'TransformStatusType' => [ 'type' => 'string', 'enum' => [ 'NOT_READY', 'READY', 'DELETING', ], ], 'TransformType' => [ 'type' => 'string', 'enum' => [ 'FIND_MATCHES', ], ], 'Trigger' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'WorkflowName' => [ 'shape' => 'NameString', ], 'Id' => [ 'shape' => 'IdString', ], 'Type' => [ 'shape' => 'TriggerType', ], 'State' => [ 'shape' => 'TriggerState', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'Schedule' => [ 'shape' => 'GenericString', ], 'Actions' => [ 'shape' => 'ActionList', ], 'Predicate' => [ 'shape' => 'Predicate', ], 'EventBatchingCondition' => [ 'shape' => 'EventBatchingCondition', ], ], ], 'TriggerList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Trigger', ], ], 'TriggerNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], ], 'TriggerNodeDetails' => [ 'type' => 'structure', 'members' => [ 'Trigger' => [ 'shape' => 'Trigger', ], ], ], 'TriggerState' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'CREATED', 'ACTIVATING', 'ACTIVATED', 'DEACTIVATING', 'DEACTIVATED', 'DELETING', 'UPDATING', ], ], 'TriggerType' => [ 'type' => 'string', 'enum' => [ 'SCHEDULED', 'CONDITIONAL', 'ON_DEMAND', 'EVENT', ], ], 'TriggerUpdate' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'Schedule' => [ 'shape' => 'GenericString', ], 'Actions' => [ 'shape' => 'ActionList', ], 'Predicate' => [ 'shape' => 'Predicate', ], 'EventBatchingCondition' => [ 'shape' => 'EventBatchingCondition', ], ], ], 'TwoInputs' => [ 'type' => 'list', 'member' => [ 'shape' => 'NodeId', ], 'max' => 2, 'min' => 2, ], 'TypeString' => [ 'type' => 'string', 'max' => 20000, 'min' => 0, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'URI' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]*', ], 'UnfilteredPartition' => [ 'type' => 'structure', 'members' => [ 'Partition' => [ 'shape' => 'Partition', ], 'AuthorizedColumns' => [ 'shape' => 'NameStringList', ], 'IsRegisteredWithLakeFormation' => [ 'shape' => 'Boolean', ], ], ], 'UnfilteredPartitionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UnfilteredPartition', ], ], 'Union' => [ 'type' => 'structure', 'required' => [ 'Name', 'Inputs', 'UnionType', ], 'members' => [ 'Name' => [ 'shape' => 'NodeName', ], 'Inputs' => [ 'shape' => 'TwoInputs', ], 'UnionType' => [ 'shape' => 'UnionType', ], ], ], 'UnionType' => [ 'type' => 'string', 'enum' => [ 'ALL', 'DISTINCT', ], ], 'UnnestSpec' => [ 'type' => 'string', 'enum' => [ 'TOPLEVEL', 'FULL', 'NOUNNEST', ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagsToRemove', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'GlueResourceArn', ], 'TagsToRemove' => [ 'shape' => 'TagKeysList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateBehavior' => [ 'type' => 'string', 'enum' => [ 'LOG', 'UPDATE_IN_DATABASE', ], ], 'UpdateBlueprintRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'BlueprintLocation', ], 'members' => [ 'Name' => [ 'shape' => 'OrchestrationNameString', ], 'Description' => [ 'shape' => 'Generic512CharString', ], 'BlueprintLocation' => [ 'shape' => 'OrchestrationS3Location', ], ], ], 'UpdateBlueprintResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'UpdateCatalogBehavior' => [ 'type' => 'string', 'enum' => [ 'UPDATE_IN_DATABASE', 'LOG', ], ], 'UpdateCatalogRequest' => [ 'type' => 'structure', 'required' => [ 'CatalogId', 'CatalogInput', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'CatalogInput' => [ 'shape' => 'CatalogInput', ], ], ], 'UpdateCatalogResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateClassifierRequest' => [ 'type' => 'structure', 'members' => [ 'GrokClassifier' => [ 'shape' => 'UpdateGrokClassifierRequest', ], 'XMLClassifier' => [ 'shape' => 'UpdateXMLClassifierRequest', ], 'JsonClassifier' => [ 'shape' => 'UpdateJsonClassifierRequest', ], 'CsvClassifier' => [ 'shape' => 'UpdateCsvClassifierRequest', ], ], ], 'UpdateClassifierResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateColumnStatisticsForPartitionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'PartitionValues', 'ColumnStatisticsList', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'PartitionValues' => [ 'shape' => 'ValueStringList', ], 'ColumnStatisticsList' => [ 'shape' => 'UpdateColumnStatisticsList', ], ], ], 'UpdateColumnStatisticsForPartitionResponse' => [ 'type' => 'structure', 'members' => [ 'Errors' => [ 'shape' => 'ColumnStatisticsErrors', ], ], ], 'UpdateColumnStatisticsForTableRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'ColumnStatisticsList', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'ColumnStatisticsList' => [ 'shape' => 'UpdateColumnStatisticsList', ], ], ], 'UpdateColumnStatisticsForTableResponse' => [ 'type' => 'structure', 'members' => [ 'Errors' => [ 'shape' => 'ColumnStatisticsErrors', ], ], ], 'UpdateColumnStatisticsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColumnStatistics', ], 'max' => 25, 'min' => 0, ], 'UpdateColumnStatisticsTaskSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', ], 'members' => [ 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'Role' => [ 'shape' => 'NameString', ], 'Schedule' => [ 'shape' => 'CronExpression', ], 'ColumnNameList' => [ 'shape' => 'ColumnNameList', ], 'SampleSize' => [ 'shape' => 'SampleSizePercentage', ], 'CatalogID' => [ 'shape' => 'NameString', ], 'SecurityConfiguration' => [ 'shape' => 'NameString', ], ], ], 'UpdateColumnStatisticsTaskSettingsResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateConnectionRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'ConnectionInput', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'Name' => [ 'shape' => 'NameString', ], 'ConnectionInput' => [ 'shape' => 'ConnectionInput', ], ], ], 'UpdateConnectionResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateCrawlerRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Role' => [ 'shape' => 'Role', ], 'DatabaseName' => [ 'shape' => 'DatabaseName', ], 'Description' => [ 'shape' => 'DescriptionStringRemovable', ], 'Targets' => [ 'shape' => 'CrawlerTargets', ], 'Schedule' => [ 'shape' => 'CronExpression', ], 'Classifiers' => [ 'shape' => 'ClassifierNameList', ], 'TablePrefix' => [ 'shape' => 'TablePrefix', ], 'SchemaChangePolicy' => [ 'shape' => 'SchemaChangePolicy', ], 'RecrawlPolicy' => [ 'shape' => 'RecrawlPolicy', ], 'LineageConfiguration' => [ 'shape' => 'LineageConfiguration', ], 'LakeFormationConfiguration' => [ 'shape' => 'LakeFormationConfiguration', ], 'Configuration' => [ 'shape' => 'CrawlerConfiguration', ], 'CrawlerSecurityConfiguration' => [ 'shape' => 'CrawlerSecurityConfiguration', ], ], ], 'UpdateCrawlerResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateCrawlerScheduleRequest' => [ 'type' => 'structure', 'required' => [ 'CrawlerName', ], 'members' => [ 'CrawlerName' => [ 'shape' => 'NameString', ], 'Schedule' => [ 'shape' => 'CronExpression', ], ], ], 'UpdateCrawlerScheduleResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateCsvClassifierRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Delimiter' => [ 'shape' => 'CsvColumnDelimiter', ], 'QuoteSymbol' => [ 'shape' => 'CsvQuoteSymbol', ], 'ContainsHeader' => [ 'shape' => 'CsvHeaderOption', ], 'Header' => [ 'shape' => 'CsvHeader', ], 'DisableValueTrimming' => [ 'shape' => 'NullableBoolean', ], 'AllowSingleColumn' => [ 'shape' => 'NullableBoolean', ], 'CustomDatatypeConfigured' => [ 'shape' => 'NullableBoolean', ], 'CustomDatatypes' => [ 'shape' => 'CustomDatatypes', ], 'Serde' => [ 'shape' => 'CsvSerdeOption', ], ], ], 'UpdateDataQualityRulesetRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'Ruleset' => [ 'shape' => 'DataQualityRulesetString', ], ], ], 'UpdateDataQualityRulesetResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'Ruleset' => [ 'shape' => 'DataQualityRulesetString', ], ], ], 'UpdateDatabaseRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'DatabaseInput', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'Name' => [ 'shape' => 'NameString', ], 'DatabaseInput' => [ 'shape' => 'DatabaseInput', ], ], ], 'UpdateDatabaseResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateDevEndpointRequest' => [ 'type' => 'structure', 'required' => [ 'EndpointName', ], 'members' => [ 'EndpointName' => [ 'shape' => 'GenericString', ], 'PublicKey' => [ 'shape' => 'GenericString', ], 'AddPublicKeys' => [ 'shape' => 'PublicKeysList', ], 'DeletePublicKeys' => [ 'shape' => 'PublicKeysList', ], 'CustomLibraries' => [ 'shape' => 'DevEndpointCustomLibraries', ], 'UpdateEtlLibraries' => [ 'shape' => 'BooleanValue', ], 'DeleteArguments' => [ 'shape' => 'StringList', ], 'AddArguments' => [ 'shape' => 'MapValue', ], ], ], 'UpdateDevEndpointResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateGrokClassifierRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Classification' => [ 'shape' => 'Classification', ], 'GrokPattern' => [ 'shape' => 'GrokPattern', ], 'CustomPatterns' => [ 'shape' => 'CustomPatterns', ], ], ], 'UpdateIntegrationResourcePropertyRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'String128', ], 'SourceProcessingProperties' => [ 'shape' => 'SourceProcessingProperties', ], 'TargetProcessingProperties' => [ 'shape' => 'TargetProcessingProperties', ], ], ], 'UpdateIntegrationResourcePropertyResponse' => [ 'type' => 'structure', 'members' => [ 'ResourceArn' => [ 'shape' => 'String128', ], 'SourceProcessingProperties' => [ 'shape' => 'SourceProcessingProperties', ], 'TargetProcessingProperties' => [ 'shape' => 'TargetProcessingProperties', ], ], ], 'UpdateIntegrationTablePropertiesRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TableName', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'String128', ], 'TableName' => [ 'shape' => 'String128', ], 'SourceTableConfig' => [ 'shape' => 'SourceTableConfig', ], 'TargetTableConfig' => [ 'shape' => 'TargetTableConfig', ], ], ], 'UpdateIntegrationTablePropertiesResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateJobFromSourceControlRequest' => [ 'type' => 'structure', 'members' => [ 'JobName' => [ 'shape' => 'NameString', ], 'Provider' => [ 'shape' => 'SourceControlProvider', ], 'RepositoryName' => [ 'shape' => 'NameString', ], 'RepositoryOwner' => [ 'shape' => 'NameString', ], 'BranchName' => [ 'shape' => 'NameString', ], 'Folder' => [ 'shape' => 'NameString', ], 'CommitId' => [ 'shape' => 'CommitIdString', ], 'AuthStrategy' => [ 'shape' => 'SourceControlAuthStrategy', ], 'AuthToken' => [ 'shape' => 'AuthTokenString', ], ], ], 'UpdateJobFromSourceControlResponse' => [ 'type' => 'structure', 'members' => [ 'JobName' => [ 'shape' => 'NameString', ], ], ], 'UpdateJobRequest' => [ 'type' => 'structure', 'required' => [ 'JobName', 'JobUpdate', ], 'members' => [ 'JobName' => [ 'shape' => 'NameString', ], 'JobUpdate' => [ 'shape' => 'JobUpdate', ], ], ], 'UpdateJobResponse' => [ 'type' => 'structure', 'members' => [ 'JobName' => [ 'shape' => 'NameString', ], ], ], 'UpdateJsonClassifierRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'JsonPath' => [ 'shape' => 'JsonPath', ], ], ], 'UpdateMLTransformRequest' => [ 'type' => 'structure', 'required' => [ 'TransformId', ], 'members' => [ 'TransformId' => [ 'shape' => 'HashString', ], 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'Parameters' => [ 'shape' => 'TransformParameters', ], 'Role' => [ 'shape' => 'RoleString', ], 'GlueVersion' => [ 'shape' => 'GlueVersionString', ], 'MaxCapacity' => [ 'shape' => 'NullableDouble', ], 'WorkerType' => [ 'shape' => 'WorkerType', ], 'NumberOfWorkers' => [ 'shape' => 'NullableInteger', ], 'Timeout' => [ 'shape' => 'Timeout', ], 'MaxRetries' => [ 'shape' => 'NullableInteger', ], ], ], 'UpdateMLTransformResponse' => [ 'type' => 'structure', 'members' => [ 'TransformId' => [ 'shape' => 'HashString', ], ], ], 'UpdatePartitionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'PartitionValueList', 'PartitionInput', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'PartitionValueList' => [ 'shape' => 'BoundedPartitionValueList', ], 'PartitionInput' => [ 'shape' => 'PartitionInput', ], ], ], 'UpdatePartitionResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateRegistryInput' => [ 'type' => 'structure', 'required' => [ 'RegistryId', 'Description', ], 'members' => [ 'RegistryId' => [ 'shape' => 'RegistryId', ], 'Description' => [ 'shape' => 'DescriptionString', ], ], ], 'UpdateRegistryResponse' => [ 'type' => 'structure', 'members' => [ 'RegistryName' => [ 'shape' => 'SchemaRegistryNameString', ], 'RegistryArn' => [ 'shape' => 'GlueResourceArn', ], ], ], 'UpdateSchemaInput' => [ 'type' => 'structure', 'required' => [ 'SchemaId', ], 'members' => [ 'SchemaId' => [ 'shape' => 'SchemaId', ], 'SchemaVersionNumber' => [ 'shape' => 'SchemaVersionNumber', ], 'Compatibility' => [ 'shape' => 'Compatibility', ], 'Description' => [ 'shape' => 'DescriptionString', ], ], ], 'UpdateSchemaResponse' => [ 'type' => 'structure', 'members' => [ 'SchemaArn' => [ 'shape' => 'GlueResourceArn', ], 'SchemaName' => [ 'shape' => 'SchemaRegistryNameString', ], 'RegistryName' => [ 'shape' => 'SchemaRegistryNameString', ], ], ], 'UpdateSourceControlFromJobRequest' => [ 'type' => 'structure', 'members' => [ 'JobName' => [ 'shape' => 'NameString', ], 'Provider' => [ 'shape' => 'SourceControlProvider', ], 'RepositoryName' => [ 'shape' => 'NameString', ], 'RepositoryOwner' => [ 'shape' => 'NameString', ], 'BranchName' => [ 'shape' => 'NameString', ], 'Folder' => [ 'shape' => 'NameString', ], 'CommitId' => [ 'shape' => 'CommitIdString', ], 'AuthStrategy' => [ 'shape' => 'SourceControlAuthStrategy', ], 'AuthToken' => [ 'shape' => 'AuthTokenString', ], ], ], 'UpdateSourceControlFromJobResponse' => [ 'type' => 'structure', 'members' => [ 'JobName' => [ 'shape' => 'NameString', ], ], ], 'UpdateTableOptimizerRequest' => [ 'type' => 'structure', 'required' => [ 'CatalogId', 'DatabaseName', 'TableName', 'Type', 'TableOptimizerConfiguration', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'Type' => [ 'shape' => 'TableOptimizerType', ], 'TableOptimizerConfiguration' => [ 'shape' => 'TableOptimizerConfiguration', ], ], ], 'UpdateTableOptimizerResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateTableRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableInput', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableInput' => [ 'shape' => 'TableInput', ], 'SkipArchive' => [ 'shape' => 'BooleanNullable', ], 'TransactionId' => [ 'shape' => 'TransactionIdString', ], 'VersionId' => [ 'shape' => 'VersionString', ], 'ViewUpdateAction' => [ 'shape' => 'ViewUpdateAction', ], 'Force' => [ 'shape' => 'Boolean', ], ], ], 'UpdateTableResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateTriggerRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'TriggerUpdate', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'TriggerUpdate' => [ 'shape' => 'TriggerUpdate', ], ], ], 'UpdateTriggerResponse' => [ 'type' => 'structure', 'members' => [ 'Trigger' => [ 'shape' => 'Trigger', ], ], ], 'UpdateUsageProfileRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Configuration', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'Configuration' => [ 'shape' => 'ProfileConfiguration', ], ], ], 'UpdateUsageProfileResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'UpdateUserDefinedFunctionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'FunctionName', 'FunctionInput', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'FunctionName' => [ 'shape' => 'NameString', ], 'FunctionInput' => [ 'shape' => 'UserDefinedFunctionInput', ], ], ], 'UpdateUserDefinedFunctionResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateWorkflowRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'WorkflowDescriptionString', ], 'DefaultRunProperties' => [ 'shape' => 'WorkflowRunProperties', ], 'MaxConcurrentRuns' => [ 'shape' => 'NullableInteger', ], ], ], 'UpdateWorkflowResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'UpdateXMLClassifierRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Classification' => [ 'shape' => 'Classification', ], 'RowTag' => [ 'shape' => 'RowTag', ], ], ], 'UpdatedTimestamp' => [ 'type' => 'string', ], 'UpsertRedshiftTargetOptions' => [ 'type' => 'structure', 'members' => [ 'TableLocation' => [ 'shape' => 'EnclosedInStringProperty', ], 'ConnectionName' => [ 'shape' => 'EnclosedInStringProperty', ], 'UpsertKeys' => [ 'shape' => 'EnclosedInStringPropertiesMinOne', ], ], ], 'UriString' => [ 'type' => 'string', ], 'UrlString' => [ 'type' => 'string', ], 'UsageProfileDefinition' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'CreatedOn' => [ 'shape' => 'TimestampValue', ], 'LastModifiedOn' => [ 'shape' => 'TimestampValue', ], ], ], 'UsageProfileDefinitionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UsageProfileDefinition', ], ], 'UserDefinedFunction' => [ 'type' => 'structure', 'members' => [ 'FunctionName' => [ 'shape' => 'NameString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'ClassName' => [ 'shape' => 'NameString', ], 'OwnerName' => [ 'shape' => 'NameString', ], 'OwnerType' => [ 'shape' => 'PrincipalType', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], 'ResourceUris' => [ 'shape' => 'ResourceUriList', ], 'CatalogId' => [ 'shape' => 'CatalogIdString', ], ], ], 'UserDefinedFunctionInput' => [ 'type' => 'structure', 'members' => [ 'FunctionName' => [ 'shape' => 'NameString', ], 'ClassName' => [ 'shape' => 'NameString', ], 'OwnerName' => [ 'shape' => 'NameString', ], 'OwnerType' => [ 'shape' => 'PrincipalType', ], 'ResourceUris' => [ 'shape' => 'ResourceUriList', ], ], ], 'UserDefinedFunctionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserDefinedFunction', ], ], 'UserManagedClientApplicationClientId' => [ 'type' => 'string', 'max' => 2048, 'pattern' => '\\S+', ], 'UserManagedClientApplicationClientSecret' => [ 'type' => 'string', 'max' => 512, 'pattern' => '^[\\x20-\\x7E]*$', 'sensitive' => true, ], 'Username' => [ 'type' => 'string', 'max' => 512, 'pattern' => '\\S+', ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'ValueString' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'ValueStringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValueString', ], ], 'Vendor' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'VersionId' => [ 'type' => 'long', ], 'VersionLongNumber' => [ 'type' => 'long', 'max' => 100000, 'min' => 1, ], 'VersionMismatchException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'VersionString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'VersionsString' => [ 'type' => 'string', 'max' => 100000, 'min' => 1, 'pattern' => '[1-9][0-9]*|[1-9][0-9]*-[1-9][0-9]*', ], 'ViewDefinition' => [ 'type' => 'structure', 'members' => [ 'IsProtected' => [ 'shape' => 'NullableBoolean', ], 'Definer' => [ 'shape' => 'ArnString', ], 'SubObjects' => [ 'shape' => 'ViewSubObjectsList', ], 'Representations' => [ 'shape' => 'ViewRepresentationList', ], ], ], 'ViewDefinitionInput' => [ 'type' => 'structure', 'members' => [ 'IsProtected' => [ 'shape' => 'NullableBoolean', ], 'Definer' => [ 'shape' => 'ArnString', ], 'Representations' => [ 'shape' => 'ViewRepresentationInputList', ], 'SubObjects' => [ 'shape' => 'ViewSubObjectsList', ], ], ], 'ViewDialect' => [ 'type' => 'string', 'enum' => [ 'REDSHIFT', 'ATHENA', 'SPARK', ], ], 'ViewDialectVersionString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'ViewRepresentation' => [ 'type' => 'structure', 'members' => [ 'Dialect' => [ 'shape' => 'ViewDialect', ], 'DialectVersion' => [ 'shape' => 'ViewDialectVersionString', ], 'ViewOriginalText' => [ 'shape' => 'ViewTextString', ], 'ViewExpandedText' => [ 'shape' => 'ViewTextString', ], 'ValidationConnection' => [ 'shape' => 'NameString', ], 'IsStale' => [ 'shape' => 'NullableBoolean', ], ], ], 'ViewRepresentationInput' => [ 'type' => 'structure', 'members' => [ 'Dialect' => [ 'shape' => 'ViewDialect', ], 'DialectVersion' => [ 'shape' => 'ViewDialectVersionString', ], 'ViewOriginalText' => [ 'shape' => 'ViewTextString', ], 'ValidationConnection' => [ 'shape' => 'NameString', ], 'ViewExpandedText' => [ 'shape' => 'ViewTextString', ], ], ], 'ViewRepresentationInputList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ViewRepresentationInput', ], 'max' => 10, 'min' => 1, ], 'ViewRepresentationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ViewRepresentation', ], 'max' => 1000, 'min' => 1, ], 'ViewSubObjectsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ArnString', ], 'max' => 10, 'min' => 0, ], 'ViewTextString' => [ 'type' => 'string', 'max' => 409600, ], 'ViewUpdateAction' => [ 'type' => 'string', 'enum' => [ 'ADD', 'REPLACE', 'ADD_OR_REPLACE', 'DROP', ], ], 'ViewValidation' => [ 'type' => 'structure', 'members' => [ 'Dialect' => [ 'shape' => 'ViewDialect', ], 'DialectVersion' => [ 'shape' => 'ViewDialectVersionString', ], 'ViewValidationText' => [ 'shape' => 'ViewTextString', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], 'State' => [ 'shape' => 'ResourceState', ], 'Error' => [ 'shape' => 'ErrorDetail', ], ], ], 'ViewValidationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ViewValidation', ], ], 'WorkerType' => [ 'type' => 'string', 'enum' => [ 'Standard', 'G.1X', 'G.2X', 'G.025X', 'G.4X', 'G.8X', 'Z.2X', ], ], 'Workflow' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'GenericString', ], 'DefaultRunProperties' => [ 'shape' => 'WorkflowRunProperties', ], 'CreatedOn' => [ 'shape' => 'TimestampValue', ], 'LastModifiedOn' => [ 'shape' => 'TimestampValue', ], 'LastRun' => [ 'shape' => 'WorkflowRun', ], 'Graph' => [ 'shape' => 'WorkflowGraph', ], 'MaxConcurrentRuns' => [ 'shape' => 'NullableInteger', ], 'BlueprintDetails' => [ 'shape' => 'BlueprintDetails', ], ], ], 'WorkflowDescriptionString' => [ 'type' => 'string', 'max' => 120000, ], 'WorkflowGraph' => [ 'type' => 'structure', 'members' => [ 'Nodes' => [ 'shape' => 'NodeList', ], 'Edges' => [ 'shape' => 'EdgeList', ], ], ], 'WorkflowNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], 'max' => 25, 'min' => 1, ], 'WorkflowRun' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'WorkflowRunId' => [ 'shape' => 'IdString', ], 'PreviousRunId' => [ 'shape' => 'IdString', ], 'WorkflowRunProperties' => [ 'shape' => 'WorkflowRunProperties', ], 'StartedOn' => [ 'shape' => 'TimestampValue', ], 'CompletedOn' => [ 'shape' => 'TimestampValue', ], 'Status' => [ 'shape' => 'WorkflowRunStatus', ], 'ErrorMessage' => [ 'shape' => 'ErrorString', ], 'Statistics' => [ 'shape' => 'WorkflowRunStatistics', ], 'Graph' => [ 'shape' => 'WorkflowGraph', ], 'StartingEventBatchCondition' => [ 'shape' => 'StartingEventBatchCondition', ], ], ], 'WorkflowRunProperties' => [ 'type' => 'map', 'key' => [ 'shape' => 'IdString', ], 'value' => [ 'shape' => 'GenericString', ], ], 'WorkflowRunStatistics' => [ 'type' => 'structure', 'members' => [ 'TotalActions' => [ 'shape' => 'IntegerValue', ], 'TimeoutActions' => [ 'shape' => 'IntegerValue', ], 'FailedActions' => [ 'shape' => 'IntegerValue', ], 'StoppedActions' => [ 'shape' => 'IntegerValue', ], 'SucceededActions' => [ 'shape' => 'IntegerValue', ], 'RunningActions' => [ 'shape' => 'IntegerValue', ], 'ErroredActions' => [ 'shape' => 'IntegerValue', ], 'WaitingActions' => [ 'shape' => 'IntegerValue', ], ], ], 'WorkflowRunStatus' => [ 'type' => 'string', 'enum' => [ 'RUNNING', 'COMPLETED', 'STOPPING', 'STOPPED', 'ERROR', ], ], 'WorkflowRuns' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkflowRun', ], 'max' => 1000, 'min' => 1, ], 'Workflows' => [ 'type' => 'list', 'member' => [ 'shape' => 'Workflow', ], 'max' => 25, 'min' => 1, ], 'XMLClassifier' => [ 'type' => 'structure', 'required' => [ 'Name', 'Classification', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Classification' => [ 'shape' => 'Classification', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastUpdated' => [ 'shape' => 'Timestamp', ], 'Version' => [ 'shape' => 'VersionId', ], 'RowTag' => [ 'shape' => 'RowTag', ], ], ], 'databaseNameString' => [ 'type' => 'string', 'min' => 1, ], 'double' => [ 'type' => 'double', ], 'dpuCounts' => [ 'type' => 'integer', ], 'dpuDurationInHour' => [ 'type' => 'double', ], 'dpuHours' => [ 'type' => 'double', ], 'glueConnectionNameString' => [ 'type' => 'string', 'min' => 1, ], 'metricCounts' => [ 'type' => 'long', ], 'tableNameString' => [ 'type' => 'string', 'min' => 1, ], ],];
