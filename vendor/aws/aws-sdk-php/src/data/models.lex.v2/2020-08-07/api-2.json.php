<?php
// This file was auto-generated from sdk-root/src/data/models.lex.v2/2020-08-07/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2020-08-07', 'endpointPrefix' => 'models-v2-lex', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceAbbreviation' => 'Lex Models V2', 'serviceFullName' => 'Amazon Lex Model Building V2', 'serviceId' => 'Lex Models V2', 'signatureVersion' => 'v4', 'signingName' => 'lex', 'uid' => 'models.lex.v2-2020-08-07', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'BatchCreateCustomVocabularyItem' => [ 'name' => 'BatchCreateCustomVocabularyItem', 'http' => [ 'method' => 'PUT', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/customvocabulary/DEFAULT/batchcreate', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchCreateCustomVocabularyItemRequest', ], 'output' => [ 'shape' => 'BatchCreateCustomVocabularyItemResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'BatchDeleteCustomVocabularyItem' => [ 'name' => 'BatchDeleteCustomVocabularyItem', 'http' => [ 'method' => 'POST', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/customvocabulary/DEFAULT/batchdelete', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchDeleteCustomVocabularyItemRequest', ], 'output' => [ 'shape' => 'BatchDeleteCustomVocabularyItemResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'BatchUpdateCustomVocabularyItem' => [ 'name' => 'BatchUpdateCustomVocabularyItem', 'http' => [ 'method' => 'PUT', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/customvocabulary/DEFAULT/batchupdate', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchUpdateCustomVocabularyItemRequest', ], 'output' => [ 'shape' => 'BatchUpdateCustomVocabularyItemResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'BuildBotLocale' => [ 'name' => 'BuildBotLocale', 'http' => [ 'method' => 'POST', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'BuildBotLocaleRequest', ], 'output' => [ 'shape' => 'BuildBotLocaleResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateBot' => [ 'name' => 'CreateBot', 'http' => [ 'method' => 'PUT', 'requestUri' => '/bots/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'CreateBotRequest', ], 'output' => [ 'shape' => 'CreateBotResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateBotAlias' => [ 'name' => 'CreateBotAlias', 'http' => [ 'method' => 'PUT', 'requestUri' => '/bots/{botId}/botaliases/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'CreateBotAliasRequest', ], 'output' => [ 'shape' => 'CreateBotAliasResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateBotLocale' => [ 'name' => 'CreateBotLocale', 'http' => [ 'method' => 'PUT', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'CreateBotLocaleRequest', ], 'output' => [ 'shape' => 'CreateBotLocaleResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateBotReplica' => [ 'name' => 'CreateBotReplica', 'http' => [ 'method' => 'PUT', 'requestUri' => '/bots/{botId}/replicas/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'CreateBotReplicaRequest', ], 'output' => [ 'shape' => 'CreateBotReplicaResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateBotVersion' => [ 'name' => 'CreateBotVersion', 'http' => [ 'method' => 'PUT', 'requestUri' => '/bots/{botId}/botversions/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'CreateBotVersionRequest', ], 'output' => [ 'shape' => 'CreateBotVersionResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateExport' => [ 'name' => 'CreateExport', 'http' => [ 'method' => 'PUT', 'requestUri' => '/exports/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'CreateExportRequest', ], 'output' => [ 'shape' => 'CreateExportResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateIntent' => [ 'name' => 'CreateIntent', 'http' => [ 'method' => 'PUT', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/intents/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateIntentRequest', ], 'output' => [ 'shape' => 'CreateIntentResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateResourcePolicy' => [ 'name' => 'CreateResourcePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/policy/{resourceArn}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateResourcePolicyRequest', ], 'output' => [ 'shape' => 'CreateResourcePolicyResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateResourcePolicyStatement' => [ 'name' => 'CreateResourcePolicyStatement', 'http' => [ 'method' => 'POST', 'requestUri' => '/policy/{resourceArn}/statements/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateResourcePolicyStatementRequest', ], 'output' => [ 'shape' => 'CreateResourcePolicyStatementResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateSlot' => [ 'name' => 'CreateSlot', 'http' => [ 'method' => 'PUT', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/intents/{intentId}/slots/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateSlotRequest', ], 'output' => [ 'shape' => 'CreateSlotResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateSlotType' => [ 'name' => 'CreateSlotType', 'http' => [ 'method' => 'PUT', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/slottypes/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateSlotTypeRequest', ], 'output' => [ 'shape' => 'CreateSlotTypeResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateTestSetDiscrepancyReport' => [ 'name' => 'CreateTestSetDiscrepancyReport', 'http' => [ 'method' => 'POST', 'requestUri' => '/testsets/{testSetId}/testsetdiscrepancy', 'responseCode' => 202, ], 'input' => [ 'shape' => 'CreateTestSetDiscrepancyReportRequest', ], 'output' => [ 'shape' => 'CreateTestSetDiscrepancyReportResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'CreateUploadUrl' => [ 'name' => 'CreateUploadUrl', 'http' => [ 'method' => 'POST', 'requestUri' => '/createuploadurl/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateUploadUrlRequest', ], 'output' => [ 'shape' => 'CreateUploadUrlResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteBot' => [ 'name' => 'DeleteBot', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/bots/{botId}/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteBotRequest', ], 'output' => [ 'shape' => 'DeleteBotResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteBotAlias' => [ 'name' => 'DeleteBotAlias', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/bots/{botId}/botaliases/{botAliasId}/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteBotAliasRequest', ], 'output' => [ 'shape' => 'DeleteBotAliasResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteBotLocale' => [ 'name' => 'DeleteBotLocale', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteBotLocaleRequest', ], 'output' => [ 'shape' => 'DeleteBotLocaleResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteBotReplica' => [ 'name' => 'DeleteBotReplica', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/bots/{botId}/replicas/{replicaRegion}/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteBotReplicaRequest', ], 'output' => [ 'shape' => 'DeleteBotReplicaResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteBotVersion' => [ 'name' => 'DeleteBotVersion', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteBotVersionRequest', ], 'output' => [ 'shape' => 'DeleteBotVersionResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteCustomVocabulary' => [ 'name' => 'DeleteCustomVocabulary', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/customvocabulary', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteCustomVocabularyRequest', ], 'output' => [ 'shape' => 'DeleteCustomVocabularyResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteExport' => [ 'name' => 'DeleteExport', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/exports/{exportId}/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteExportRequest', ], 'output' => [ 'shape' => 'DeleteExportResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteImport' => [ 'name' => 'DeleteImport', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/imports/{importId}/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteImportRequest', ], 'output' => [ 'shape' => 'DeleteImportResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteIntent' => [ 'name' => 'DeleteIntent', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/intents/{intentId}/', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteIntentRequest', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteResourcePolicy' => [ 'name' => 'DeleteResourcePolicy', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/policy/{resourceArn}/', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteResourcePolicyRequest', ], 'output' => [ 'shape' => 'DeleteResourcePolicyResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeleteResourcePolicyStatement' => [ 'name' => 'DeleteResourcePolicyStatement', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/policy/{resourceArn}/statements/{statementId}/', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteResourcePolicyStatementRequest', ], 'output' => [ 'shape' => 'DeleteResourcePolicyStatementResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeleteSlot' => [ 'name' => 'DeleteSlot', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/intents/{intentId}/slots/{slotId}/', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteSlotRequest', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteSlotType' => [ 'name' => 'DeleteSlotType', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/slottypes/{slotTypeId}/', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteSlotTypeRequest', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteTestSet' => [ 'name' => 'DeleteTestSet', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/testsets/{testSetId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteTestSetRequest', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteUtterances' => [ 'name' => 'DeleteUtterances', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/bots/{botId}/utterances/', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteUtterancesRequest', ], 'output' => [ 'shape' => 'DeleteUtterancesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeBot' => [ 'name' => 'DescribeBot', 'http' => [ 'method' => 'GET', 'requestUri' => '/bots/{botId}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeBotRequest', ], 'output' => [ 'shape' => 'DescribeBotResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeBotAlias' => [ 'name' => 'DescribeBotAlias', 'http' => [ 'method' => 'GET', 'requestUri' => '/bots/{botId}/botaliases/{botAliasId}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeBotAliasRequest', ], 'output' => [ 'shape' => 'DescribeBotAliasResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeBotLocale' => [ 'name' => 'DescribeBotLocale', 'http' => [ 'method' => 'GET', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeBotLocaleRequest', ], 'output' => [ 'shape' => 'DescribeBotLocaleResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeBotRecommendation' => [ 'name' => 'DescribeBotRecommendation', 'http' => [ 'method' => 'GET', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/botrecommendations/{botRecommendationId}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeBotRecommendationRequest', ], 'output' => [ 'shape' => 'DescribeBotRecommendationResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeBotReplica' => [ 'name' => 'DescribeBotReplica', 'http' => [ 'method' => 'GET', 'requestUri' => '/bots/{botId}/replicas/{replicaRegion}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeBotReplicaRequest', ], 'output' => [ 'shape' => 'DescribeBotReplicaResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeBotResourceGeneration' => [ 'name' => 'DescribeBotResourceGeneration', 'http' => [ 'method' => 'GET', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/generations/{generationId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeBotResourceGenerationRequest', ], 'output' => [ 'shape' => 'DescribeBotResourceGenerationResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeBotVersion' => [ 'name' => 'DescribeBotVersion', 'http' => [ 'method' => 'GET', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeBotVersionRequest', ], 'output' => [ 'shape' => 'DescribeBotVersionResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeCustomVocabularyMetadata' => [ 'name' => 'DescribeCustomVocabularyMetadata', 'http' => [ 'method' => 'GET', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/customvocabulary/DEFAULT/metadata', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeCustomVocabularyMetadataRequest', ], 'output' => [ 'shape' => 'DescribeCustomVocabularyMetadataResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeExport' => [ 'name' => 'DescribeExport', 'http' => [ 'method' => 'GET', 'requestUri' => '/exports/{exportId}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeExportRequest', ], 'output' => [ 'shape' => 'DescribeExportResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeImport' => [ 'name' => 'DescribeImport', 'http' => [ 'method' => 'GET', 'requestUri' => '/imports/{importId}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeImportRequest', ], 'output' => [ 'shape' => 'DescribeImportResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeIntent' => [ 'name' => 'DescribeIntent', 'http' => [ 'method' => 'GET', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/intents/{intentId}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeIntentRequest', ], 'output' => [ 'shape' => 'DescribeIntentResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeResourcePolicy' => [ 'name' => 'DescribeResourcePolicy', 'http' => [ 'method' => 'GET', 'requestUri' => '/policy/{resourceArn}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeResourcePolicyRequest', ], 'output' => [ 'shape' => 'DescribeResourcePolicyResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DescribeSlot' => [ 'name' => 'DescribeSlot', 'http' => [ 'method' => 'GET', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/intents/{intentId}/slots/{slotId}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeSlotRequest', ], 'output' => [ 'shape' => 'DescribeSlotResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeSlotType' => [ 'name' => 'DescribeSlotType', 'http' => [ 'method' => 'GET', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/slottypes/{slotTypeId}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeSlotTypeRequest', ], 'output' => [ 'shape' => 'DescribeSlotTypeResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeTestExecution' => [ 'name' => 'DescribeTestExecution', 'http' => [ 'method' => 'GET', 'requestUri' => '/testexecutions/{testExecutionId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeTestExecutionRequest', ], 'output' => [ 'shape' => 'DescribeTestExecutionResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeTestSet' => [ 'name' => 'DescribeTestSet', 'http' => [ 'method' => 'GET', 'requestUri' => '/testsets/{testSetId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeTestSetRequest', ], 'output' => [ 'shape' => 'DescribeTestSetResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeTestSetDiscrepancyReport' => [ 'name' => 'DescribeTestSetDiscrepancyReport', 'http' => [ 'method' => 'GET', 'requestUri' => '/testsetdiscrepancy/{testSetDiscrepancyReportId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeTestSetDiscrepancyReportRequest', ], 'output' => [ 'shape' => 'DescribeTestSetDiscrepancyReportResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeTestSetGeneration' => [ 'name' => 'DescribeTestSetGeneration', 'http' => [ 'method' => 'GET', 'requestUri' => '/testsetgenerations/{testSetGenerationId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeTestSetGenerationRequest', ], 'output' => [ 'shape' => 'DescribeTestSetGenerationResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GenerateBotElement' => [ 'name' => 'GenerateBotElement', 'http' => [ 'method' => 'POST', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/generate', 'responseCode' => 202, ], 'input' => [ 'shape' => 'GenerateBotElementRequest', ], 'output' => [ 'shape' => 'GenerateBotElementResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetTestExecutionArtifactsUrl' => [ 'name' => 'GetTestExecutionArtifactsUrl', 'http' => [ 'method' => 'GET', 'requestUri' => '/testexecutions/{testExecutionId}/artifacturl', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetTestExecutionArtifactsUrlRequest', ], 'output' => [ 'shape' => 'GetTestExecutionArtifactsUrlResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListAggregatedUtterances' => [ 'name' => 'ListAggregatedUtterances', 'http' => [ 'method' => 'POST', 'requestUri' => '/bots/{botId}/aggregatedutterances/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAggregatedUtterancesRequest', ], 'output' => [ 'shape' => 'ListAggregatedUtterancesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListBotAliasReplicas' => [ 'name' => 'ListBotAliasReplicas', 'http' => [ 'method' => 'POST', 'requestUri' => '/bots/{botId}/replicas/{replicaRegion}/botaliases/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListBotAliasReplicasRequest', ], 'output' => [ 'shape' => 'ListBotAliasReplicasResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListBotAliases' => [ 'name' => 'ListBotAliases', 'http' => [ 'method' => 'POST', 'requestUri' => '/bots/{botId}/botaliases/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListBotAliasesRequest', ], 'output' => [ 'shape' => 'ListBotAliasesResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListBotLocales' => [ 'name' => 'ListBotLocales', 'http' => [ 'method' => 'POST', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListBotLocalesRequest', ], 'output' => [ 'shape' => 'ListBotLocalesResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListBotRecommendations' => [ 'name' => 'ListBotRecommendations', 'http' => [ 'method' => 'POST', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/botrecommendations/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListBotRecommendationsRequest', ], 'output' => [ 'shape' => 'ListBotRecommendationsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListBotReplicas' => [ 'name' => 'ListBotReplicas', 'http' => [ 'method' => 'POST', 'requestUri' => '/bots/{botId}/replicas/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListBotReplicasRequest', ], 'output' => [ 'shape' => 'ListBotReplicasResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListBotResourceGenerations' => [ 'name' => 'ListBotResourceGenerations', 'http' => [ 'method' => 'POST', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/generations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListBotResourceGenerationsRequest', ], 'output' => [ 'shape' => 'ListBotResourceGenerationsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListBotVersionReplicas' => [ 'name' => 'ListBotVersionReplicas', 'http' => [ 'method' => 'POST', 'requestUri' => '/bots/{botId}/replicas/{replicaRegion}/botversions/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListBotVersionReplicasRequest', ], 'output' => [ 'shape' => 'ListBotVersionReplicasResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListBotVersions' => [ 'name' => 'ListBotVersions', 'http' => [ 'method' => 'POST', 'requestUri' => '/bots/{botId}/botversions/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListBotVersionsRequest', ], 'output' => [ 'shape' => 'ListBotVersionsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListBots' => [ 'name' => 'ListBots', 'http' => [ 'method' => 'POST', 'requestUri' => '/bots/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListBotsRequest', ], 'output' => [ 'shape' => 'ListBotsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListBuiltInIntents' => [ 'name' => 'ListBuiltInIntents', 'http' => [ 'method' => 'POST', 'requestUri' => '/builtins/locales/{localeId}/intents/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListBuiltInIntentsRequest', ], 'output' => [ 'shape' => 'ListBuiltInIntentsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListBuiltInSlotTypes' => [ 'name' => 'ListBuiltInSlotTypes', 'http' => [ 'method' => 'POST', 'requestUri' => '/builtins/locales/{localeId}/slottypes/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListBuiltInSlotTypesRequest', ], 'output' => [ 'shape' => 'ListBuiltInSlotTypesResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListCustomVocabularyItems' => [ 'name' => 'ListCustomVocabularyItems', 'http' => [ 'method' => 'POST', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/customvocabulary/DEFAULT/list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListCustomVocabularyItemsRequest', ], 'output' => [ 'shape' => 'ListCustomVocabularyItemsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListExports' => [ 'name' => 'ListExports', 'http' => [ 'method' => 'POST', 'requestUri' => '/exports/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListExportsRequest', ], 'output' => [ 'shape' => 'ListExportsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListImports' => [ 'name' => 'ListImports', 'http' => [ 'method' => 'POST', 'requestUri' => '/imports/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListImportsRequest', ], 'output' => [ 'shape' => 'ListImportsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListIntentMetrics' => [ 'name' => 'ListIntentMetrics', 'http' => [ 'method' => 'POST', 'requestUri' => '/bots/{botId}/analytics/intentmetrics', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListIntentMetricsRequest', ], 'output' => [ 'shape' => 'ListIntentMetricsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListIntentPaths' => [ 'name' => 'ListIntentPaths', 'http' => [ 'method' => 'POST', 'requestUri' => '/bots/{botId}/analytics/intentpaths', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListIntentPathsRequest', ], 'output' => [ 'shape' => 'ListIntentPathsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListIntentStageMetrics' => [ 'name' => 'ListIntentStageMetrics', 'http' => [ 'method' => 'POST', 'requestUri' => '/bots/{botId}/analytics/intentstagemetrics', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListIntentStageMetricsRequest', ], 'output' => [ 'shape' => 'ListIntentStageMetricsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListIntents' => [ 'name' => 'ListIntents', 'http' => [ 'method' => 'POST', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/intents/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListIntentsRequest', ], 'output' => [ 'shape' => 'ListIntentsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListRecommendedIntents' => [ 'name' => 'ListRecommendedIntents', 'http' => [ 'method' => 'POST', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/botrecommendations/{botRecommendationId}/intents', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListRecommendedIntentsRequest', ], 'output' => [ 'shape' => 'ListRecommendedIntentsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListSessionAnalyticsData' => [ 'name' => 'ListSessionAnalyticsData', 'http' => [ 'method' => 'POST', 'requestUri' => '/bots/{botId}/analytics/sessions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSessionAnalyticsDataRequest', ], 'output' => [ 'shape' => 'ListSessionAnalyticsDataResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListSessionMetrics' => [ 'name' => 'ListSessionMetrics', 'http' => [ 'method' => 'POST', 'requestUri' => '/bots/{botId}/analytics/sessionmetrics', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSessionMetricsRequest', ], 'output' => [ 'shape' => 'ListSessionMetricsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListSlotTypes' => [ 'name' => 'ListSlotTypes', 'http' => [ 'method' => 'POST', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/slottypes/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSlotTypesRequest', ], 'output' => [ 'shape' => 'ListSlotTypesResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListSlots' => [ 'name' => 'ListSlots', 'http' => [ 'method' => 'POST', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/intents/{intentId}/slots/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSlotsRequest', ], 'output' => [ 'shape' => 'ListSlotsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceARN}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListTestExecutionResultItems' => [ 'name' => 'ListTestExecutionResultItems', 'http' => [ 'method' => 'POST', 'requestUri' => '/testexecutions/{testExecutionId}/results', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTestExecutionResultItemsRequest', ], 'output' => [ 'shape' => 'ListTestExecutionResultItemsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListTestExecutions' => [ 'name' => 'ListTestExecutions', 'http' => [ 'method' => 'POST', 'requestUri' => '/testexecutions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTestExecutionsRequest', ], 'output' => [ 'shape' => 'ListTestExecutionsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListTestSetRecords' => [ 'name' => 'ListTestSetRecords', 'http' => [ 'method' => 'POST', 'requestUri' => '/testsets/{testSetId}/records', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTestSetRecordsRequest', ], 'output' => [ 'shape' => 'ListTestSetRecordsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListTestSets' => [ 'name' => 'ListTestSets', 'http' => [ 'method' => 'POST', 'requestUri' => '/testsets', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTestSetsRequest', ], 'output' => [ 'shape' => 'ListTestSetsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListUtteranceAnalyticsData' => [ 'name' => 'ListUtteranceAnalyticsData', 'http' => [ 'method' => 'POST', 'requestUri' => '/bots/{botId}/analytics/utterances', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListUtteranceAnalyticsDataRequest', ], 'output' => [ 'shape' => 'ListUtteranceAnalyticsDataResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListUtteranceMetrics' => [ 'name' => 'ListUtteranceMetrics', 'http' => [ 'method' => 'POST', 'requestUri' => '/bots/{botId}/analytics/utterancemetrics', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListUtteranceMetricsRequest', ], 'output' => [ 'shape' => 'ListUtteranceMetricsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], ], 'SearchAssociatedTranscripts' => [ 'name' => 'SearchAssociatedTranscripts', 'http' => [ 'method' => 'POST', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/botrecommendations/{botRecommendationId}/associatedtranscripts', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SearchAssociatedTranscriptsRequest', ], 'output' => [ 'shape' => 'SearchAssociatedTranscriptsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'StartBotRecommendation' => [ 'name' => 'StartBotRecommendation', 'http' => [ 'method' => 'PUT', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/botrecommendations/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'StartBotRecommendationRequest', ], 'output' => [ 'shape' => 'StartBotRecommendationResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StartBotResourceGeneration' => [ 'name' => 'StartBotResourceGeneration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/startgeneration', 'responseCode' => 202, ], 'input' => [ 'shape' => 'StartBotResourceGenerationRequest', ], 'output' => [ 'shape' => 'StartBotResourceGenerationResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'StartImport' => [ 'name' => 'StartImport', 'http' => [ 'method' => 'PUT', 'requestUri' => '/imports/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'StartImportRequest', ], 'output' => [ 'shape' => 'StartImportResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StartTestExecution' => [ 'name' => 'StartTestExecution', 'http' => [ 'method' => 'POST', 'requestUri' => '/testsets/{testSetId}/testexecutions', 'responseCode' => 202, ], 'input' => [ 'shape' => 'StartTestExecutionRequest', ], 'output' => [ 'shape' => 'StartTestExecutionResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StartTestSetGeneration' => [ 'name' => 'StartTestSetGeneration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/testsetgenerations', 'responseCode' => 202, ], 'input' => [ 'shape' => 'StartTestSetGenerationRequest', ], 'output' => [ 'shape' => 'StartTestSetGenerationResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'StopBotRecommendation' => [ 'name' => 'StopBotRecommendation', 'http' => [ 'method' => 'PUT', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/botrecommendations/{botRecommendationId}/stopbotrecommendation', 'responseCode' => 202, ], 'input' => [ 'shape' => 'StopBotRecommendationRequest', ], 'output' => [ 'shape' => 'StopBotRecommendationResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceARN}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceARN}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'UpdateBot' => [ 'name' => 'UpdateBot', 'http' => [ 'method' => 'PUT', 'requestUri' => '/bots/{botId}/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdateBotRequest', ], 'output' => [ 'shape' => 'UpdateBotResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateBotAlias' => [ 'name' => 'UpdateBotAlias', 'http' => [ 'method' => 'PUT', 'requestUri' => '/bots/{botId}/botaliases/{botAliasId}/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdateBotAliasRequest', ], 'output' => [ 'shape' => 'UpdateBotAliasResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateBotLocale' => [ 'name' => 'UpdateBotLocale', 'http' => [ 'method' => 'PUT', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdateBotLocaleRequest', ], 'output' => [ 'shape' => 'UpdateBotLocaleResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateBotRecommendation' => [ 'name' => 'UpdateBotRecommendation', 'http' => [ 'method' => 'PUT', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/botrecommendations/{botRecommendationId}/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdateBotRecommendationRequest', ], 'output' => [ 'shape' => 'UpdateBotRecommendationResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateExport' => [ 'name' => 'UpdateExport', 'http' => [ 'method' => 'PUT', 'requestUri' => '/exports/{exportId}/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdateExportRequest', ], 'output' => [ 'shape' => 'UpdateExportResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateIntent' => [ 'name' => 'UpdateIntent', 'http' => [ 'method' => 'PUT', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/intents/{intentId}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateIntentRequest', ], 'output' => [ 'shape' => 'UpdateIntentResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateResourcePolicy' => [ 'name' => 'UpdateResourcePolicy', 'http' => [ 'method' => 'PUT', 'requestUri' => '/policy/{resourceArn}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateResourcePolicyRequest', ], 'output' => [ 'shape' => 'UpdateResourcePolicyResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateSlot' => [ 'name' => 'UpdateSlot', 'http' => [ 'method' => 'PUT', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/intents/{intentId}/slots/{slotId}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateSlotRequest', ], 'output' => [ 'shape' => 'UpdateSlotResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateSlotType' => [ 'name' => 'UpdateSlotType', 'http' => [ 'method' => 'PUT', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/slottypes/{slotTypeId}/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdateSlotTypeRequest', ], 'output' => [ 'shape' => 'UpdateSlotTypeResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateTestSet' => [ 'name' => 'UpdateTestSet', 'http' => [ 'method' => 'PUT', 'requestUri' => '/testsets/{testSetId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateTestSetRequest', ], 'output' => [ 'shape' => 'UpdateTestSetResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'ActiveContext' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'ActiveContextName', ], ], ], 'ActiveContextList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ActiveContext', ], 'max' => 20, 'min' => 0, ], 'ActiveContextName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^([A-Za-z]_?)+$', ], 'AdvancedRecognitionSetting' => [ 'type' => 'structure', 'members' => [ 'audioRecognitionStrategy' => [ 'shape' => 'AudioRecognitionStrategy', ], ], ], 'AgentTurnResult' => [ 'type' => 'structure', 'required' => [ 'expectedAgentPrompt', ], 'members' => [ 'expectedAgentPrompt' => [ 'shape' => 'TestSetAgentPrompt', ], 'actualAgentPrompt' => [ 'shape' => 'TestSetAgentPrompt', ], 'errorDetails' => [ 'shape' => 'ExecutionErrorDetails', ], 'actualElicitedSlot' => [ 'shape' => 'TestResultSlotName', ], 'actualIntent' => [ 'shape' => 'Name', ], ], ], 'AgentTurnSpecification' => [ 'type' => 'structure', 'required' => [ 'agentPrompt', ], 'members' => [ 'agentPrompt' => [ 'shape' => 'TestSetAgentPrompt', ], ], ], 'AggregatedUtterancesFilter' => [ 'type' => 'structure', 'required' => [ 'name', 'values', 'operator', ], 'members' => [ 'name' => [ 'shape' => 'AggregatedUtterancesFilterName', ], 'values' => [ 'shape' => 'FilterValues', ], 'operator' => [ 'shape' => 'AggregatedUtterancesFilterOperator', ], ], ], 'AggregatedUtterancesFilterName' => [ 'type' => 'string', 'enum' => [ 'Utterance', ], ], 'AggregatedUtterancesFilterOperator' => [ 'type' => 'string', 'enum' => [ 'CO', 'EQ', ], ], 'AggregatedUtterancesFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'AggregatedUtterancesFilter', ], 'max' => 1, 'min' => 1, ], 'AggregatedUtterancesSortAttribute' => [ 'type' => 'string', 'enum' => [ 'HitCount', 'MissedCount', ], ], 'AggregatedUtterancesSortBy' => [ 'type' => 'structure', 'required' => [ 'attribute', 'order', ], 'members' => [ 'attribute' => [ 'shape' => 'AggregatedUtterancesSortAttribute', ], 'order' => [ 'shape' => 'SortOrder', ], ], ], 'AggregatedUtterancesSummary' => [ 'type' => 'structure', 'members' => [ 'utterance' => [ 'shape' => 'Utterance', ], 'hitCount' => [ 'shape' => 'HitCount', ], 'missedCount' => [ 'shape' => 'MissedCount', ], 'utteranceFirstRecordedInAggregationDuration' => [ 'shape' => 'Timestamp', ], 'utteranceLastRecordedInAggregationDuration' => [ 'shape' => 'Timestamp', ], 'containsDataFromDeletedResources' => [ 'shape' => 'BoxedBoolean', ], ], ], 'AggregatedUtterancesSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AggregatedUtterancesSummary', ], ], 'AllowedInputTypes' => [ 'type' => 'structure', 'required' => [ 'allowAudioInput', 'allowDTMFInput', ], 'members' => [ 'allowAudioInput' => [ 'shape' => 'BoxedBoolean', ], 'allowDTMFInput' => [ 'shape' => 'BoxedBoolean', ], ], ], 'AmazonResourceName' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, ], 'AnalyticsBinByList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalyticsBinBySpecification', ], 'max' => 1, 'min' => 1, ], 'AnalyticsBinByName' => [ 'type' => 'string', 'enum' => [ 'ConversationStartTime', 'UtteranceTimestamp', ], ], 'AnalyticsBinBySpecification' => [ 'type' => 'structure', 'required' => [ 'name', 'interval', ], 'members' => [ 'name' => [ 'shape' => 'AnalyticsBinByName', ], 'interval' => [ 'shape' => 'AnalyticsInterval', ], 'order' => [ 'shape' => 'AnalyticsSortOrder', ], ], ], 'AnalyticsBinKey' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'AnalyticsBinByName', ], 'value' => [ 'shape' => 'AnalyticsBinValue', ], ], ], 'AnalyticsBinKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalyticsBinKey', ], 'max' => 1, 'min' => 1, ], 'AnalyticsBinValue' => [ 'type' => 'long', ], 'AnalyticsChannel' => [ 'type' => 'string', 'max' => 50, 'min' => 1, ], 'AnalyticsCommonFilterName' => [ 'type' => 'string', 'enum' => [ 'BotAliasId', 'BotVersion', 'LocaleId', 'Modality', 'Channel', ], ], 'AnalyticsFilterOperator' => [ 'type' => 'string', 'enum' => [ 'EQ', 'GT', 'LT', ], ], 'AnalyticsFilterValue' => [ 'type' => 'string', ], 'AnalyticsFilterValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalyticsFilterValue', ], 'max' => 5, 'min' => 1, ], 'AnalyticsGroupByValue' => [ 'type' => 'string', ], 'AnalyticsIntentField' => [ 'type' => 'string', 'enum' => [ 'IntentName', 'IntentEndState', 'IntentLevel', ], ], 'AnalyticsIntentFilter' => [ 'type' => 'structure', 'required' => [ 'name', 'operator', 'values', ], 'members' => [ 'name' => [ 'shape' => 'AnalyticsIntentFilterName', ], 'operator' => [ 'shape' => 'AnalyticsFilterOperator', ], 'values' => [ 'shape' => 'AnalyticsFilterValues', ], ], ], 'AnalyticsIntentFilterName' => [ 'type' => 'string', 'enum' => [ 'BotAliasId', 'BotVersion', 'LocaleId', 'Modality', 'Channel', 'SessionId', 'OriginatingRequestId', 'IntentName', 'IntentEndState', ], ], 'AnalyticsIntentFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalyticsIntentFilter', ], 'max' => 9, 'min' => 1, ], 'AnalyticsIntentGroupByKey' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'AnalyticsIntentField', ], 'value' => [ 'shape' => 'AnalyticsGroupByValue', ], ], ], 'AnalyticsIntentGroupByKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalyticsIntentGroupByKey', ], ], 'AnalyticsIntentGroupByList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalyticsIntentGroupBySpecification', ], 'max' => 3, 'min' => 1, ], 'AnalyticsIntentGroupBySpecification' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'AnalyticsIntentField', ], ], ], 'AnalyticsIntentMetric' => [ 'type' => 'structure', 'required' => [ 'name', 'statistic', ], 'members' => [ 'name' => [ 'shape' => 'AnalyticsIntentMetricName', ], 'statistic' => [ 'shape' => 'AnalyticsMetricStatistic', ], 'order' => [ 'shape' => 'AnalyticsSortOrder', ], ], ], 'AnalyticsIntentMetricName' => [ 'type' => 'string', 'enum' => [ 'Count', 'Success', 'Failure', 'Switched', 'Dropped', ], ], 'AnalyticsIntentMetricResult' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'AnalyticsIntentMetricName', ], 'statistic' => [ 'shape' => 'AnalyticsMetricStatistic', ], 'value' => [ 'shape' => 'AnalyticsMetricValue', ], ], ], 'AnalyticsIntentMetricResults' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalyticsIntentMetricResult', ], ], 'AnalyticsIntentMetrics' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalyticsIntentMetric', ], 'max' => 5, 'min' => 1, ], 'AnalyticsIntentNodeSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalyticsIntentNodeSummary', ], 'max' => 100, 'min' => 1, ], 'AnalyticsIntentNodeSummary' => [ 'type' => 'structure', 'members' => [ 'intentName' => [ 'shape' => 'Name', ], 'intentPath' => [ 'shape' => 'AnalyticsPath', ], 'intentCount' => [ 'shape' => 'AnalyticsNodeCount', ], 'intentLevel' => [ 'shape' => 'AnalyticsNodeLevel', ], 'nodeType' => [ 'shape' => 'AnalyticsNodeType', ], ], ], 'AnalyticsIntentResult' => [ 'type' => 'structure', 'members' => [ 'binKeys' => [ 'shape' => 'AnalyticsBinKeys', ], 'groupByKeys' => [ 'shape' => 'AnalyticsIntentGroupByKeys', ], 'metricsResults' => [ 'shape' => 'AnalyticsIntentMetricResults', ], ], ], 'AnalyticsIntentResults' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalyticsIntentResult', ], ], 'AnalyticsIntentStageField' => [ 'type' => 'string', 'enum' => [ 'IntentStageName', 'SwitchedToIntent', ], ], 'AnalyticsIntentStageFilter' => [ 'type' => 'structure', 'required' => [ 'name', 'operator', 'values', ], 'members' => [ 'name' => [ 'shape' => 'AnalyticsIntentStageFilterName', ], 'operator' => [ 'shape' => 'AnalyticsFilterOperator', ], 'values' => [ 'shape' => 'AnalyticsFilterValues', ], ], ], 'AnalyticsIntentStageFilterName' => [ 'type' => 'string', 'enum' => [ 'BotAliasId', 'BotVersion', 'LocaleId', 'Modality', 'Channel', 'SessionId', 'OriginatingRequestId', 'IntentName', 'IntentStageName', ], ], 'AnalyticsIntentStageFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalyticsIntentStageFilter', ], 'max' => 9, 'min' => 1, ], 'AnalyticsIntentStageGroupByKey' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'AnalyticsIntentStageField', ], 'value' => [ 'shape' => 'AnalyticsGroupByValue', ], ], ], 'AnalyticsIntentStageGroupByKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalyticsIntentStageGroupByKey', ], ], 'AnalyticsIntentStageGroupByList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalyticsIntentStageGroupBySpecification', ], 'max' => 2, 'min' => 1, ], 'AnalyticsIntentStageGroupBySpecification' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'AnalyticsIntentStageField', ], ], ], 'AnalyticsIntentStageMetric' => [ 'type' => 'structure', 'required' => [ 'name', 'statistic', ], 'members' => [ 'name' => [ 'shape' => 'AnalyticsIntentStageMetricName', ], 'statistic' => [ 'shape' => 'AnalyticsMetricStatistic', ], 'order' => [ 'shape' => 'AnalyticsSortOrder', ], ], ], 'AnalyticsIntentStageMetricName' => [ 'type' => 'string', 'enum' => [ 'Count', 'Success', 'Failed', 'Dropped', 'Retry', ], ], 'AnalyticsIntentStageMetricResult' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'AnalyticsIntentStageMetricName', ], 'statistic' => [ 'shape' => 'AnalyticsMetricStatistic', ], 'value' => [ 'shape' => 'AnalyticsMetricValue', ], ], ], 'AnalyticsIntentStageMetricResults' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalyticsIntentStageMetricResult', ], ], 'AnalyticsIntentStageMetrics' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalyticsIntentStageMetric', ], 'max' => 5, 'min' => 1, ], 'AnalyticsIntentStageResult' => [ 'type' => 'structure', 'members' => [ 'binKeys' => [ 'shape' => 'AnalyticsBinKeys', ], 'groupByKeys' => [ 'shape' => 'AnalyticsIntentStageGroupByKeys', ], 'metricsResults' => [ 'shape' => 'AnalyticsIntentStageMetricResults', ], ], ], 'AnalyticsIntentStageResults' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalyticsIntentStageResult', ], ], 'AnalyticsInterval' => [ 'type' => 'string', 'enum' => [ 'OneHour', 'OneDay', ], ], 'AnalyticsLongValue' => [ 'type' => 'long', ], 'AnalyticsMetricStatistic' => [ 'type' => 'string', 'enum' => [ 'Sum', 'Avg', 'Max', ], ], 'AnalyticsMetricValue' => [ 'type' => 'double', ], 'AnalyticsModality' => [ 'type' => 'string', 'enum' => [ 'Speech', 'Text', 'DTMF', 'MultiMode', ], ], 'AnalyticsNodeCount' => [ 'type' => 'integer', ], 'AnalyticsNodeLevel' => [ 'type' => 'integer', 'max' => 100, 'min' => 0, ], 'AnalyticsNodeType' => [ 'type' => 'string', 'enum' => [ 'Inner', 'Exit', ], ], 'AnalyticsOriginatingRequestId' => [ 'type' => 'string', ], 'AnalyticsPath' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'AnalyticsPathFilter' => [ 'type' => 'structure', 'required' => [ 'name', 'operator', 'values', ], 'members' => [ 'name' => [ 'shape' => 'AnalyticsCommonFilterName', ], 'operator' => [ 'shape' => 'AnalyticsFilterOperator', ], 'values' => [ 'shape' => 'AnalyticsFilterValues', ], ], ], 'AnalyticsPathFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalyticsPathFilter', ], 'max' => 5, 'min' => 1, ], 'AnalyticsSessionField' => [ 'type' => 'string', 'enum' => [ 'ConversationEndState', 'LocaleId', ], ], 'AnalyticsSessionFilter' => [ 'type' => 'structure', 'required' => [ 'name', 'operator', 'values', ], 'members' => [ 'name' => [ 'shape' => 'AnalyticsSessionFilterName', ], 'operator' => [ 'shape' => 'AnalyticsFilterOperator', ], 'values' => [ 'shape' => 'AnalyticsFilterValues', ], ], ], 'AnalyticsSessionFilterName' => [ 'type' => 'string', 'enum' => [ 'BotAliasId', 'BotVersion', 'LocaleId', 'Modality', 'Channel', 'Duration', 'ConversationEndState', 'SessionId', 'OriginatingRequestId', 'IntentPath', ], ], 'AnalyticsSessionFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalyticsSessionFilter', ], 'max' => 10, 'min' => 1, ], 'AnalyticsSessionGroupByKey' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'AnalyticsSessionField', ], 'value' => [ 'shape' => 'AnalyticsGroupByValue', ], ], ], 'AnalyticsSessionGroupByKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalyticsSessionGroupByKey', ], ], 'AnalyticsSessionGroupByList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalyticsSessionGroupBySpecification', ], 'max' => 2, 'min' => 1, ], 'AnalyticsSessionGroupBySpecification' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'AnalyticsSessionField', ], ], ], 'AnalyticsSessionId' => [ 'type' => 'string', 'pattern' => '[0-9a-zA-Z._:-]', ], 'AnalyticsSessionMetric' => [ 'type' => 'structure', 'required' => [ 'name', 'statistic', ], 'members' => [ 'name' => [ 'shape' => 'AnalyticsSessionMetricName', ], 'statistic' => [ 'shape' => 'AnalyticsMetricStatistic', ], 'order' => [ 'shape' => 'AnalyticsSortOrder', ], ], ], 'AnalyticsSessionMetricName' => [ 'type' => 'string', 'enum' => [ 'Count', 'Success', 'Failure', 'Dropped', 'Duration', 'TurnsPerConversation', 'Concurrency', ], ], 'AnalyticsSessionMetricResult' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'AnalyticsSessionMetricName', ], 'statistic' => [ 'shape' => 'AnalyticsMetricStatistic', ], 'value' => [ 'shape' => 'AnalyticsMetricValue', ], ], ], 'AnalyticsSessionMetricResults' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalyticsSessionMetricResult', ], ], 'AnalyticsSessionMetrics' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalyticsSessionMetric', ], 'max' => 7, 'min' => 1, ], 'AnalyticsSessionResult' => [ 'type' => 'structure', 'members' => [ 'binKeys' => [ 'shape' => 'AnalyticsBinKeys', ], 'groupByKeys' => [ 'shape' => 'AnalyticsSessionGroupByKeys', ], 'metricsResults' => [ 'shape' => 'AnalyticsSessionMetricResults', ], ], ], 'AnalyticsSessionResults' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalyticsSessionResult', ], ], 'AnalyticsSessionSortByName' => [ 'type' => 'string', 'enum' => [ 'ConversationStartTime', 'NumberOfTurns', 'Duration', ], ], 'AnalyticsSortOrder' => [ 'type' => 'string', 'enum' => [ 'Ascending', 'Descending', ], ], 'AnalyticsUtteranceAttribute' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'AnalyticsUtteranceAttributeName', ], ], ], 'AnalyticsUtteranceAttributeName' => [ 'type' => 'string', 'enum' => [ 'LastUsedIntent', ], ], 'AnalyticsUtteranceAttributeResult' => [ 'type' => 'structure', 'members' => [ 'lastUsedIntent' => [ 'shape' => 'Name', ], ], ], 'AnalyticsUtteranceAttributeResults' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalyticsUtteranceAttributeResult', ], ], 'AnalyticsUtteranceAttributes' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalyticsUtteranceAttribute', ], 'max' => 1, 'min' => 1, ], 'AnalyticsUtteranceField' => [ 'type' => 'string', 'enum' => [ 'UtteranceText', 'UtteranceState', ], ], 'AnalyticsUtteranceFilter' => [ 'type' => 'structure', 'required' => [ 'name', 'operator', 'values', ], 'members' => [ 'name' => [ 'shape' => 'AnalyticsUtteranceFilterName', ], 'operator' => [ 'shape' => 'AnalyticsFilterOperator', ], 'values' => [ 'shape' => 'AnalyticsFilterValues', ], ], ], 'AnalyticsUtteranceFilterName' => [ 'type' => 'string', 'enum' => [ 'BotAliasId', 'BotVersion', 'LocaleId', 'Modality', 'Channel', 'SessionId', 'OriginatingRequestId', 'UtteranceState', 'UtteranceText', ], ], 'AnalyticsUtteranceFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalyticsUtteranceFilter', ], 'max' => 9, 'min' => 1, ], 'AnalyticsUtteranceGroupByKey' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'AnalyticsUtteranceField', ], 'value' => [ 'shape' => 'AnalyticsGroupByValue', ], ], ], 'AnalyticsUtteranceGroupByKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalyticsUtteranceGroupByKey', ], ], 'AnalyticsUtteranceGroupByList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalyticsUtteranceGroupBySpecification', ], 'max' => 2, 'min' => 1, ], 'AnalyticsUtteranceGroupBySpecification' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'AnalyticsUtteranceField', ], ], ], 'AnalyticsUtteranceMetric' => [ 'type' => 'structure', 'required' => [ 'name', 'statistic', ], 'members' => [ 'name' => [ 'shape' => 'AnalyticsUtteranceMetricName', ], 'statistic' => [ 'shape' => 'AnalyticsMetricStatistic', ], 'order' => [ 'shape' => 'AnalyticsSortOrder', ], ], ], 'AnalyticsUtteranceMetricName' => [ 'type' => 'string', 'enum' => [ 'Count', 'Missed', 'Detected', 'UtteranceTimestamp', ], ], 'AnalyticsUtteranceMetricResult' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'AnalyticsUtteranceMetricName', ], 'statistic' => [ 'shape' => 'AnalyticsMetricStatistic', ], 'value' => [ 'shape' => 'AnalyticsMetricValue', ], ], ], 'AnalyticsUtteranceMetricResults' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalyticsUtteranceMetricResult', ], ], 'AnalyticsUtteranceMetrics' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalyticsUtteranceMetric', ], 'max' => 4, 'min' => 1, ], 'AnalyticsUtteranceResult' => [ 'type' => 'structure', 'members' => [ 'binKeys' => [ 'shape' => 'AnalyticsBinKeys', ], 'groupByKeys' => [ 'shape' => 'AnalyticsUtteranceGroupByKeys', ], 'metricsResults' => [ 'shape' => 'AnalyticsUtteranceMetricResults', ], 'attributeResults' => [ 'shape' => 'AnalyticsUtteranceAttributeResults', ], ], ], 'AnalyticsUtteranceResults' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalyticsUtteranceResult', ], ], 'AnalyticsUtteranceSortByName' => [ 'type' => 'string', 'enum' => [ 'UtteranceTimestamp', ], ], 'AnswerField' => [ 'type' => 'string', ], 'AssociatedTranscript' => [ 'type' => 'structure', 'members' => [ 'transcript' => [ 'shape' => 'Transcript', ], ], ], 'AssociatedTranscriptFilter' => [ 'type' => 'structure', 'required' => [ 'name', 'values', ], 'members' => [ 'name' => [ 'shape' => 'AssociatedTranscriptFilterName', ], 'values' => [ 'shape' => 'FilterValues', ], ], ], 'AssociatedTranscriptFilterName' => [ 'type' => 'string', 'enum' => [ 'IntentId', 'SlotTypeId', ], ], 'AssociatedTranscriptFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssociatedTranscriptFilter', ], 'max' => 1, 'min' => 1, ], 'AssociatedTranscriptList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssociatedTranscript', ], ], 'AttachmentTitle' => [ 'type' => 'string', 'max' => 250, 'min' => 1, ], 'AttachmentUrl' => [ 'type' => 'string', 'max' => 250, 'min' => 1, ], 'AudioAndDTMFInputSpecification' => [ 'type' => 'structure', 'required' => [ 'startTimeoutMs', ], 'members' => [ 'startTimeoutMs' => [ 'shape' => 'TimeInMilliSeconds', ], 'audioSpecification' => [ 'shape' => 'AudioSpecification', ], 'dtmfSpecification' => [ 'shape' => 'DTMFSpecification', ], ], ], 'AudioFileS3Location' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^s3://([a-z0-9\\\\.-]+)/(.+)$', ], 'AudioLogDestination' => [ 'type' => 'structure', 'required' => [ 's3Bucket', ], 'members' => [ 's3Bucket' => [ 'shape' => 'S3BucketLogDestination', ], ], ], 'AudioLogSetting' => [ 'type' => 'structure', 'required' => [ 'enabled', 'destination', ], 'members' => [ 'enabled' => [ 'shape' => 'Boolean', ], 'destination' => [ 'shape' => 'AudioLogDestination', ], 'selectiveLoggingEnabled' => [ 'shape' => 'BoxedBoolean', ], ], ], 'AudioLogSettingsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AudioLogSetting', ], 'max' => 1, 'min' => 1, ], 'AudioRecognitionStrategy' => [ 'type' => 'string', 'enum' => [ 'UseSlotValuesAsCustomVocabulary', ], ], 'AudioSpecification' => [ 'type' => 'structure', 'required' => [ 'maxLengthMs', 'endTimeoutMs', ], 'members' => [ 'maxLengthMs' => [ 'shape' => 'TimeInMilliSeconds', ], 'endTimeoutMs' => [ 'shape' => 'TimeInMilliSeconds', ], ], ], 'BatchCreateCustomVocabularyItemRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', 'localeId', 'customVocabularyItemList', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'BotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], 'customVocabularyItemList' => [ 'shape' => 'CreateCustomVocabularyItemsList', ], ], ], 'BatchCreateCustomVocabularyItemResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'BotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'errors' => [ 'shape' => 'FailedCustomVocabularyItems', ], 'resources' => [ 'shape' => 'CustomVocabularyItems', ], ], ], 'BatchDeleteCustomVocabularyItemRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', 'localeId', 'customVocabularyItemList', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'BotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], 'customVocabularyItemList' => [ 'shape' => 'DeleteCustomVocabularyItemsList', ], ], ], 'BatchDeleteCustomVocabularyItemResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'BotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'errors' => [ 'shape' => 'FailedCustomVocabularyItems', ], 'resources' => [ 'shape' => 'CustomVocabularyItems', ], ], ], 'BatchUpdateCustomVocabularyItemRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', 'localeId', 'customVocabularyItemList', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'BotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], 'customVocabularyItemList' => [ 'shape' => 'UpdateCustomVocabularyItemsList', ], ], ], 'BatchUpdateCustomVocabularyItemResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'BotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'errors' => [ 'shape' => 'FailedCustomVocabularyItems', ], 'resources' => [ 'shape' => 'CustomVocabularyItems', ], ], ], 'BedrockGuardrailConfiguration' => [ 'type' => 'structure', 'required' => [ 'identifier', 'version', ], 'members' => [ 'identifier' => [ 'shape' => 'BedrockGuardrailIdentifier', ], 'version' => [ 'shape' => 'BedrockGuardrailVersion', ], ], ], 'BedrockGuardrailIdentifier' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^(([a-z0-9]+)|(arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:guardrail/[a-z0-9]+))$', ], 'BedrockGuardrailVersion' => [ 'type' => 'string', 'pattern' => '^(([1-9][0-9]{0,7})|(DRAFT))$', ], 'BedrockKnowledgeBaseArn' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '^arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,40}:[0-9]{12}:knowledge-base\\/[A-Za-z0-9]{10}$|^[A-Za-z0-9]{10}$', ], 'BedrockKnowledgeStoreConfiguration' => [ 'type' => 'structure', 'required' => [ 'bedrockKnowledgeBaseArn', ], 'members' => [ 'bedrockKnowledgeBaseArn' => [ 'shape' => 'BedrockKnowledgeBaseArn', ], 'exactResponse' => [ 'shape' => 'Boolean', ], 'exactResponseFields' => [ 'shape' => 'BedrockKnowledgeStoreExactResponseFields', ], ], ], 'BedrockKnowledgeStoreExactResponseFields' => [ 'type' => 'structure', 'members' => [ 'answerField' => [ 'shape' => 'AnswerField', ], ], ], 'BedrockModelArn' => [ 'type' => 'string', 'pattern' => '^arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}::foundation-model\\/[a-z0-9-]{1,63}[.]{1}([a-z0-9-]{1,63}[.]){0,2}[a-z0-9-]{1,63}([:][a-z0-9-]{1,63}){0,2}$', ], 'BedrockModelCustomPrompt' => [ 'type' => 'string', 'max' => 4000, 'min' => 1, ], 'BedrockModelSpecification' => [ 'type' => 'structure', 'required' => [ 'modelArn', ], 'members' => [ 'modelArn' => [ 'shape' => 'BedrockModelArn', ], 'guardrail' => [ 'shape' => 'BedrockGuardrailConfiguration', ], 'traceStatus' => [ 'shape' => 'BedrockTraceStatus', ], 'customPrompt' => [ 'shape' => 'BedrockModelCustomPrompt', ], ], ], 'BedrockTraceStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'Boolean' => [ 'type' => 'boolean', ], 'BotAliasHistoryEvent' => [ 'type' => 'structure', 'members' => [ 'botVersion' => [ 'shape' => 'BotVersion', ], 'startDate' => [ 'shape' => 'Timestamp', ], 'endDate' => [ 'shape' => 'Timestamp', ], ], ], 'BotAliasHistoryEventsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BotAliasHistoryEvent', ], ], 'BotAliasId' => [ 'type' => 'string', 'max' => 10, 'min' => 10, 'pattern' => '^(\\bTSTALIASID\\b|[0-9a-zA-Z]+)$', ], 'BotAliasLocaleSettings' => [ 'type' => 'structure', 'required' => [ 'enabled', ], 'members' => [ 'enabled' => [ 'shape' => 'Boolean', ], 'codeHookSpecification' => [ 'shape' => 'CodeHookSpecification', ], ], ], 'BotAliasLocaleSettingsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'LocaleId', ], 'value' => [ 'shape' => 'BotAliasLocaleSettings', ], 'min' => 1, ], 'BotAliasName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^(\\bAmazonLexTestAlias\\b|[0-9a-zA-Z][_-]?)+$', ], 'BotAliasReplicaSummary' => [ 'type' => 'structure', 'members' => [ 'botAliasId' => [ 'shape' => 'BotAliasId', ], 'botAliasReplicationStatus' => [ 'shape' => 'BotAliasReplicationStatus', ], 'botVersion' => [ 'shape' => 'BotVersion', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], 'failureReasons' => [ 'shape' => 'FailureReasons', ], ], ], 'BotAliasReplicaSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BotAliasReplicaSummary', ], ], 'BotAliasReplicationStatus' => [ 'type' => 'string', 'enum' => [ 'Creating', 'Updating', 'Available', 'Deleting', 'Failed', ], ], 'BotAliasStatus' => [ 'type' => 'string', 'enum' => [ 'Creating', 'Available', 'Deleting', 'Failed', ], ], 'BotAliasSummary' => [ 'type' => 'structure', 'members' => [ 'botAliasId' => [ 'shape' => 'BotAliasId', ], 'botAliasName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'botVersion' => [ 'shape' => 'BotVersion', ], 'botAliasStatus' => [ 'shape' => 'BotAliasStatus', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'BotAliasSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BotAliasSummary', ], ], 'BotAliasTestExecutionTarget' => [ 'type' => 'structure', 'required' => [ 'botId', 'botAliasId', 'localeId', ], 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botAliasId' => [ 'shape' => 'BotAliasId', ], 'localeId' => [ 'shape' => 'LocaleId', ], ], ], 'BotExportSpecification' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', ], 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'BotVersion', ], ], ], 'BotFilter' => [ 'type' => 'structure', 'required' => [ 'name', 'values', 'operator', ], 'members' => [ 'name' => [ 'shape' => 'BotFilterName', ], 'values' => [ 'shape' => 'FilterValues', ], 'operator' => [ 'shape' => 'BotFilterOperator', ], ], ], 'BotFilterName' => [ 'type' => 'string', 'enum' => [ 'BotName', 'BotType', ], ], 'BotFilterOperator' => [ 'type' => 'string', 'enum' => [ 'CO', 'EQ', 'NE', ], ], 'BotFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'BotFilter', ], 'max' => 2, 'min' => 1, ], 'BotImportSpecification' => [ 'type' => 'structure', 'required' => [ 'botName', 'roleArn', 'dataPrivacy', ], 'members' => [ 'botName' => [ 'shape' => 'Name', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'dataPrivacy' => [ 'shape' => 'DataPrivacy', ], 'errorLogSettings' => [ 'shape' => 'ErrorLogSettings', ], 'idleSessionTTLInSeconds' => [ 'shape' => 'SessionTTL', ], 'botTags' => [ 'shape' => 'TagMap', ], 'testBotAliasTags' => [ 'shape' => 'TagMap', ], ], ], 'BotLocaleExportSpecification' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', 'localeId', ], 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'BotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], ], ], 'BotLocaleFilter' => [ 'type' => 'structure', 'required' => [ 'name', 'values', 'operator', ], 'members' => [ 'name' => [ 'shape' => 'BotLocaleFilterName', ], 'values' => [ 'shape' => 'FilterValues', ], 'operator' => [ 'shape' => 'BotLocaleFilterOperator', ], ], ], 'BotLocaleFilterName' => [ 'type' => 'string', 'enum' => [ 'BotLocaleName', ], ], 'BotLocaleFilterOperator' => [ 'type' => 'string', 'enum' => [ 'CO', 'EQ', ], ], 'BotLocaleFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'BotLocaleFilter', ], 'max' => 1, 'min' => 1, ], 'BotLocaleHistoryEvent' => [ 'type' => 'structure', 'required' => [ 'event', 'eventDate', ], 'members' => [ 'event' => [ 'shape' => 'BotLocaleHistoryEventDescription', ], 'eventDate' => [ 'shape' => 'Timestamp', ], ], ], 'BotLocaleHistoryEventDescription' => [ 'type' => 'string', ], 'BotLocaleHistoryEventsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BotLocaleHistoryEvent', ], ], 'BotLocaleImportSpecification' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', 'localeId', ], 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'nluIntentConfidenceThreshold' => [ 'shape' => 'ConfidenceThreshold', ], 'voiceSettings' => [ 'shape' => 'VoiceSettings', ], ], ], 'BotLocaleSortAttribute' => [ 'type' => 'string', 'enum' => [ 'BotLocaleName', ], ], 'BotLocaleSortBy' => [ 'type' => 'structure', 'required' => [ 'attribute', 'order', ], 'members' => [ 'attribute' => [ 'shape' => 'BotLocaleSortAttribute', ], 'order' => [ 'shape' => 'SortOrder', ], ], ], 'BotLocaleStatus' => [ 'type' => 'string', 'enum' => [ 'Creating', 'Building', 'Built', 'ReadyExpressTesting', 'Failed', 'Deleting', 'NotBuilt', 'Importing', 'Processing', ], ], 'BotLocaleSummary' => [ 'type' => 'structure', 'members' => [ 'localeId' => [ 'shape' => 'LocaleId', ], 'localeName' => [ 'shape' => 'LocaleName', ], 'description' => [ 'shape' => 'Description', ], 'botLocaleStatus' => [ 'shape' => 'BotLocaleStatus', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], 'lastBuildSubmittedDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'BotLocaleSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BotLocaleSummary', ], ], 'BotMember' => [ 'type' => 'structure', 'required' => [ 'botMemberId', 'botMemberName', 'botMemberAliasId', 'botMemberAliasName', 'botMemberVersion', ], 'members' => [ 'botMemberId' => [ 'shape' => 'Id', ], 'botMemberName' => [ 'shape' => 'Name', ], 'botMemberAliasId' => [ 'shape' => 'BotAliasId', ], 'botMemberAliasName' => [ 'shape' => 'BotAliasName', ], 'botMemberVersion' => [ 'shape' => 'BotVersion', ], ], ], 'BotMembers' => [ 'type' => 'list', 'member' => [ 'shape' => 'BotMember', ], 'max' => 10, 'min' => 0, ], 'BotRecommendationResultStatistics' => [ 'type' => 'structure', 'members' => [ 'intents' => [ 'shape' => 'IntentStatistics', ], 'slotTypes' => [ 'shape' => 'SlotTypeStatistics', ], ], ], 'BotRecommendationResults' => [ 'type' => 'structure', 'members' => [ 'botLocaleExportUrl' => [ 'shape' => 'PresignedS3Url', ], 'associatedTranscriptsUrl' => [ 'shape' => 'PresignedS3Url', ], 'statistics' => [ 'shape' => 'BotRecommendationResultStatistics', ], ], ], 'BotRecommendationStatus' => [ 'type' => 'string', 'enum' => [ 'Processing', 'Deleting', 'Deleted', 'Downloading', 'Updating', 'Available', 'Failed', 'Stopping', 'Stopped', ], ], 'BotRecommendationSummary' => [ 'type' => 'structure', 'required' => [ 'botRecommendationStatus', 'botRecommendationId', ], 'members' => [ 'botRecommendationStatus' => [ 'shape' => 'BotRecommendationStatus', ], 'botRecommendationId' => [ 'shape' => 'Id', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'BotRecommendationSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BotRecommendationSummary', ], ], 'BotReplicaStatus' => [ 'type' => 'string', 'enum' => [ 'Enabling', 'Enabled', 'Deleting', 'Failed', ], ], 'BotReplicaSummary' => [ 'type' => 'structure', 'members' => [ 'replicaRegion' => [ 'shape' => 'ReplicaRegion', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'botReplicaStatus' => [ 'shape' => 'BotReplicaStatus', ], 'failureReasons' => [ 'shape' => 'FailureReasons', ], ], ], 'BotReplicaSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BotReplicaSummary', ], ], 'BotSortAttribute' => [ 'type' => 'string', 'enum' => [ 'BotName', ], ], 'BotSortBy' => [ 'type' => 'structure', 'required' => [ 'attribute', 'order', ], 'members' => [ 'attribute' => [ 'shape' => 'BotSortAttribute', ], 'order' => [ 'shape' => 'SortOrder', ], ], ], 'BotStatus' => [ 'type' => 'string', 'enum' => [ 'Creating', 'Available', 'Inactive', 'Deleting', 'Failed', 'Versioning', 'Importing', 'Updating', ], ], 'BotSummary' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'botStatus' => [ 'shape' => 'BotStatus', ], 'latestBotVersion' => [ 'shape' => 'NumericalBotVersion', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], 'botType' => [ 'shape' => 'BotType', ], ], ], 'BotSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BotSummary', ], ], 'BotType' => [ 'type' => 'string', 'enum' => [ 'Bot', 'BotNetwork', ], ], 'BotVersion' => [ 'type' => 'string', 'max' => 5, 'min' => 1, 'pattern' => '^(DRAFT|[0-9]+)$', ], 'BotVersionLocaleDetails' => [ 'type' => 'structure', 'required' => [ 'sourceBotVersion', ], 'members' => [ 'sourceBotVersion' => [ 'shape' => 'BotVersion', ], ], ], 'BotVersionLocaleSpecification' => [ 'type' => 'map', 'key' => [ 'shape' => 'LocaleId', ], 'value' => [ 'shape' => 'BotVersionLocaleDetails', ], 'min' => 1, ], 'BotVersionReplicaSortAttribute' => [ 'type' => 'string', 'enum' => [ 'BotVersion', ], ], 'BotVersionReplicaSortBy' => [ 'type' => 'structure', 'required' => [ 'attribute', 'order', ], 'members' => [ 'attribute' => [ 'shape' => 'BotVersionReplicaSortAttribute', ], 'order' => [ 'shape' => 'SortOrder', ], ], ], 'BotVersionReplicaSummary' => [ 'type' => 'structure', 'members' => [ 'botVersion' => [ 'shape' => 'BotVersion', ], 'botVersionReplicationStatus' => [ 'shape' => 'BotVersionReplicationStatus', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'failureReasons' => [ 'shape' => 'FailureReasons', ], ], ], 'BotVersionReplicaSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BotVersionReplicaSummary', ], ], 'BotVersionReplicationStatus' => [ 'type' => 'string', 'enum' => [ 'Creating', 'Available', 'Deleting', 'Failed', ], ], 'BotVersionSortAttribute' => [ 'type' => 'string', 'enum' => [ 'BotVersion', ], ], 'BotVersionSortBy' => [ 'type' => 'structure', 'required' => [ 'attribute', 'order', ], 'members' => [ 'attribute' => [ 'shape' => 'BotVersionSortAttribute', ], 'order' => [ 'shape' => 'SortOrder', ], ], ], 'BotVersionSummary' => [ 'type' => 'structure', 'members' => [ 'botName' => [ 'shape' => 'Name', ], 'botVersion' => [ 'shape' => 'BotVersion', ], 'description' => [ 'shape' => 'Description', ], 'botStatus' => [ 'shape' => 'BotStatus', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'BotVersionSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BotVersionSummary', ], ], 'BoxedBoolean' => [ 'type' => 'boolean', 'box' => true, ], 'BuildBotLocaleRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', 'localeId', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], ], ], 'BuildBotLocaleResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'botLocaleStatus' => [ 'shape' => 'BotLocaleStatus', ], 'lastBuildSubmittedDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'BuildtimeSettings' => [ 'type' => 'structure', 'members' => [ 'descriptiveBotBuilder' => [ 'shape' => 'DescriptiveBotBuilderSpecification', ], 'sampleUtteranceGeneration' => [ 'shape' => 'SampleUtteranceGenerationSpecification', ], ], ], 'BuiltInIntentSortAttribute' => [ 'type' => 'string', 'enum' => [ 'IntentSignature', ], ], 'BuiltInIntentSortBy' => [ 'type' => 'structure', 'required' => [ 'attribute', 'order', ], 'members' => [ 'attribute' => [ 'shape' => 'BuiltInIntentSortAttribute', ], 'order' => [ 'shape' => 'SortOrder', ], ], ], 'BuiltInIntentSummary' => [ 'type' => 'structure', 'members' => [ 'intentSignature' => [ 'shape' => 'IntentSignature', ], 'description' => [ 'shape' => 'Description', ], ], ], 'BuiltInIntentSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BuiltInIntentSummary', ], ], 'BuiltInOrCustomSlotTypeId' => [ 'type' => 'string', 'max' => 25, 'min' => 1, 'pattern' => '^((AMAZON\\.)[a-zA-Z_]+?|[0-9a-zA-Z]+)$', ], 'BuiltInSlotTypeSortAttribute' => [ 'type' => 'string', 'enum' => [ 'SlotTypeSignature', ], ], 'BuiltInSlotTypeSortBy' => [ 'type' => 'structure', 'required' => [ 'attribute', 'order', ], 'members' => [ 'attribute' => [ 'shape' => 'BuiltInSlotTypeSortAttribute', ], 'order' => [ 'shape' => 'SortOrder', ], ], ], 'BuiltInSlotTypeSummary' => [ 'type' => 'structure', 'members' => [ 'slotTypeSignature' => [ 'shape' => 'SlotTypeSignature', ], 'description' => [ 'shape' => 'Description', ], ], ], 'BuiltInSlotTypeSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BuiltInSlotTypeSummary', ], ], 'BuiltInsMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 20, 'min' => 20, ], 'Button' => [ 'type' => 'structure', 'required' => [ 'text', 'value', ], 'members' => [ 'text' => [ 'shape' => 'ButtonText', ], 'value' => [ 'shape' => 'ButtonValue', ], ], ], 'ButtonText' => [ 'type' => 'string', 'max' => 50, 'min' => 1, ], 'ButtonValue' => [ 'type' => 'string', 'max' => 50, 'min' => 1, ], 'ButtonsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Button', ], 'max' => 5, 'min' => 0, ], 'ChildDirected' => [ 'type' => 'boolean', ], 'CloudWatchLogGroupArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^arn:[\\w\\-]+:logs:[\\w\\-]+:[\\d]{12}:log-group:[\\.\\-_/#A-Za-z0-9]{1,512}(?::\\*)?$', ], 'CloudWatchLogGroupLogDestination' => [ 'type' => 'structure', 'required' => [ 'cloudWatchLogGroupArn', 'logPrefix', ], 'members' => [ 'cloudWatchLogGroupArn' => [ 'shape' => 'CloudWatchLogGroupArn', ], 'logPrefix' => [ 'shape' => 'LogPrefix', ], ], ], 'CodeHookInterfaceVersion' => [ 'type' => 'string', 'max' => 5, 'min' => 1, ], 'CodeHookSpecification' => [ 'type' => 'structure', 'required' => [ 'lambdaCodeHook', ], 'members' => [ 'lambdaCodeHook' => [ 'shape' => 'LambdaCodeHook', ], ], ], 'CompositeSlotTypeSetting' => [ 'type' => 'structure', 'members' => [ 'subSlots' => [ 'shape' => 'SubSlotTypeList', ], ], ], 'Condition' => [ 'type' => 'structure', 'required' => [ 'expressionString', ], 'members' => [ 'expressionString' => [ 'shape' => 'ConditionExpression', ], ], ], 'ConditionExpression' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'ConditionKey' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'ConditionKeyValueMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ConditionKey', ], 'value' => [ 'shape' => 'ConditionValue', ], 'max' => 10, 'min' => 0, ], 'ConditionMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ConditionOperator', ], 'value' => [ 'shape' => 'ConditionKeyValueMap', ], 'max' => 10, 'min' => 0, ], 'ConditionOperator' => [ 'type' => 'string', 'min' => 1, ], 'ConditionValue' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'ConditionalBranch' => [ 'type' => 'structure', 'required' => [ 'name', 'condition', 'nextStep', ], 'members' => [ 'name' => [ 'shape' => 'Name', ], 'condition' => [ 'shape' => 'Condition', ], 'nextStep' => [ 'shape' => 'DialogState', ], 'response' => [ 'shape' => 'ResponseSpecification', ], ], ], 'ConditionalBranches' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConditionalBranch', ], 'max' => 4, 'min' => 1, ], 'ConditionalSpecification' => [ 'type' => 'structure', 'required' => [ 'active', 'conditionalBranches', 'defaultBranch', ], 'members' => [ 'active' => [ 'shape' => 'BoxedBoolean', ], 'conditionalBranches' => [ 'shape' => 'ConditionalBranches', ], 'defaultBranch' => [ 'shape' => 'DefaultConditionalBranch', ], ], ], 'ConfidenceThreshold' => [ 'type' => 'double', 'max' => 1, 'min' => 0, ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ContextTimeToLiveInSeconds' => [ 'type' => 'integer', 'max' => 86400, 'min' => 5, ], 'ContextTurnsToLive' => [ 'type' => 'integer', 'max' => 20, 'min' => 1, ], 'ConversationEndState' => [ 'type' => 'string', 'enum' => [ 'Success', 'Failure', 'Dropped', ], ], 'ConversationLevelIntentClassificationResultItem' => [ 'type' => 'structure', 'required' => [ 'intentName', 'matchResult', ], 'members' => [ 'intentName' => [ 'shape' => 'Name', ], 'matchResult' => [ 'shape' => 'TestResultMatchStatus', ], ], ], 'ConversationLevelIntentClassificationResults' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConversationLevelIntentClassificationResultItem', ], ], 'ConversationLevelResultDetail' => [ 'type' => 'structure', 'required' => [ 'endToEndResult', ], 'members' => [ 'endToEndResult' => [ 'shape' => 'TestResultMatchStatus', ], 'speechTranscriptionResult' => [ 'shape' => 'TestResultMatchStatus', ], ], ], 'ConversationLevelSlotResolutionResultItem' => [ 'type' => 'structure', 'required' => [ 'intentName', 'slotName', 'matchResult', ], 'members' => [ 'intentName' => [ 'shape' => 'Name', ], 'slotName' => [ 'shape' => 'TestResultSlotName', ], 'matchResult' => [ 'shape' => 'TestResultMatchStatus', ], ], ], 'ConversationLevelSlotResolutionResults' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConversationLevelSlotResolutionResultItem', ], ], 'ConversationLevelTestResultItem' => [ 'type' => 'structure', 'required' => [ 'conversationId', 'endToEndResult', 'intentClassificationResults', 'slotResolutionResults', ], 'members' => [ 'conversationId' => [ 'shape' => 'TestSetConversationId', ], 'endToEndResult' => [ 'shape' => 'TestResultMatchStatus', ], 'speechTranscriptionResult' => [ 'shape' => 'TestResultMatchStatus', ], 'intentClassificationResults' => [ 'shape' => 'ConversationLevelIntentClassificationResults', ], 'slotResolutionResults' => [ 'shape' => 'ConversationLevelSlotResolutionResults', ], ], ], 'ConversationLevelTestResultItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConversationLevelTestResultItem', ], ], 'ConversationLevelTestResults' => [ 'type' => 'structure', 'required' => [ 'items', ], 'members' => [ 'items' => [ 'shape' => 'ConversationLevelTestResultItemList', ], ], ], 'ConversationLevelTestResultsFilterBy' => [ 'type' => 'structure', 'members' => [ 'endToEndResult' => [ 'shape' => 'TestResultMatchStatus', ], ], ], 'ConversationLogSettings' => [ 'type' => 'structure', 'members' => [ 'textLogSettings' => [ 'shape' => 'TextLogSettingsList', ], 'audioLogSettings' => [ 'shape' => 'AudioLogSettingsList', ], ], ], 'ConversationLogsDataSource' => [ 'type' => 'structure', 'required' => [ 'botId', 'botAliasId', 'localeId', 'filter', ], 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botAliasId' => [ 'shape' => 'BotAliasId', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'filter' => [ 'shape' => 'ConversationLogsDataSourceFilterBy', ], ], ], 'ConversationLogsDataSourceFilterBy' => [ 'type' => 'structure', 'required' => [ 'startTime', 'endTime', 'inputMode', ], 'members' => [ 'startTime' => [ 'shape' => 'Timestamp', ], 'endTime' => [ 'shape' => 'Timestamp', ], 'inputMode' => [ 'shape' => 'ConversationLogsInputModeFilter', ], ], ], 'ConversationLogsInputModeFilter' => [ 'type' => 'string', 'enum' => [ 'Speech', 'Text', ], ], 'Count' => [ 'type' => 'integer', ], 'CreateBotAliasRequest' => [ 'type' => 'structure', 'required' => [ 'botAliasName', 'botId', ], 'members' => [ 'botAliasName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'botVersion' => [ 'shape' => 'NumericalBotVersion', ], 'botAliasLocaleSettings' => [ 'shape' => 'BotAliasLocaleSettingsMap', ], 'conversationLogSettings' => [ 'shape' => 'ConversationLogSettings', ], 'sentimentAnalysisSettings' => [ 'shape' => 'SentimentAnalysisSettings', ], 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateBotAliasResponse' => [ 'type' => 'structure', 'members' => [ 'botAliasId' => [ 'shape' => 'BotAliasId', ], 'botAliasName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'botVersion' => [ 'shape' => 'NumericalBotVersion', ], 'botAliasLocaleSettings' => [ 'shape' => 'BotAliasLocaleSettingsMap', ], 'conversationLogSettings' => [ 'shape' => 'ConversationLogSettings', ], 'sentimentAnalysisSettings' => [ 'shape' => 'SentimentAnalysisSettings', ], 'botAliasStatus' => [ 'shape' => 'BotAliasStatus', ], 'botId' => [ 'shape' => 'Id', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateBotLocaleRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', 'localeId', 'nluIntentConfidenceThreshold', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'description' => [ 'shape' => 'Description', ], 'nluIntentConfidenceThreshold' => [ 'shape' => 'ConfidenceThreshold', ], 'voiceSettings' => [ 'shape' => 'VoiceSettings', ], 'generativeAISettings' => [ 'shape' => 'GenerativeAISettings', ], ], ], 'CreateBotLocaleResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', ], 'localeName' => [ 'shape' => 'LocaleName', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'description' => [ 'shape' => 'Description', ], 'nluIntentConfidenceThreshold' => [ 'shape' => 'ConfidenceThreshold', ], 'voiceSettings' => [ 'shape' => 'VoiceSettings', ], 'botLocaleStatus' => [ 'shape' => 'BotLocaleStatus', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'generativeAISettings' => [ 'shape' => 'GenerativeAISettings', ], ], ], 'CreateBotReplicaRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'replicaRegion', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'replicaRegion' => [ 'shape' => 'ReplicaRegion', ], ], ], 'CreateBotReplicaResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'replicaRegion' => [ 'shape' => 'ReplicaRegion', ], 'sourceRegion' => [ 'shape' => 'ReplicaRegion', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'botReplicaStatus' => [ 'shape' => 'BotReplicaStatus', ], ], ], 'CreateBotRequest' => [ 'type' => 'structure', 'required' => [ 'botName', 'roleArn', 'dataPrivacy', 'idleSessionTTLInSeconds', ], 'members' => [ 'botName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'dataPrivacy' => [ 'shape' => 'DataPrivacy', ], 'idleSessionTTLInSeconds' => [ 'shape' => 'SessionTTL', ], 'botTags' => [ 'shape' => 'TagMap', ], 'testBotAliasTags' => [ 'shape' => 'TagMap', ], 'botType' => [ 'shape' => 'BotType', ], 'botMembers' => [ 'shape' => 'BotMembers', ], 'errorLogSettings' => [ 'shape' => 'ErrorLogSettings', ], ], ], 'CreateBotResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'dataPrivacy' => [ 'shape' => 'DataPrivacy', ], 'idleSessionTTLInSeconds' => [ 'shape' => 'SessionTTL', ], 'botStatus' => [ 'shape' => 'BotStatus', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'botTags' => [ 'shape' => 'TagMap', ], 'testBotAliasTags' => [ 'shape' => 'TagMap', ], 'botType' => [ 'shape' => 'BotType', ], 'botMembers' => [ 'shape' => 'BotMembers', ], 'errorLogSettings' => [ 'shape' => 'ErrorLogSettings', ], ], ], 'CreateBotVersionRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersionLocaleSpecification', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'description' => [ 'shape' => 'Description', ], 'botVersionLocaleSpecification' => [ 'shape' => 'BotVersionLocaleSpecification', ], ], ], 'CreateBotVersionResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'description' => [ 'shape' => 'Description', ], 'botVersion' => [ 'shape' => 'NumericalBotVersion', ], 'botVersionLocaleSpecification' => [ 'shape' => 'BotVersionLocaleSpecification', ], 'botStatus' => [ 'shape' => 'BotStatus', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'CreateCustomVocabularyItemsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NewCustomVocabularyItem', ], 'max' => 10, 'min' => 1, ], 'CreateExportRequest' => [ 'type' => 'structure', 'required' => [ 'resourceSpecification', 'fileFormat', ], 'members' => [ 'resourceSpecification' => [ 'shape' => 'ExportResourceSpecification', ], 'fileFormat' => [ 'shape' => 'ImportExportFileFormat', ], 'filePassword' => [ 'shape' => 'ImportExportFilePassword', ], ], ], 'CreateExportResponse' => [ 'type' => 'structure', 'members' => [ 'exportId' => [ 'shape' => 'Id', ], 'resourceSpecification' => [ 'shape' => 'ExportResourceSpecification', ], 'fileFormat' => [ 'shape' => 'ImportExportFileFormat', ], 'exportStatus' => [ 'shape' => 'ExportStatus', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'CreateIntentRequest' => [ 'type' => 'structure', 'required' => [ 'intentName', 'botId', 'botVersion', 'localeId', ], 'members' => [ 'intentName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'parentIntentSignature' => [ 'shape' => 'IntentSignature', ], 'sampleUtterances' => [ 'shape' => 'SampleUtterancesList', ], 'dialogCodeHook' => [ 'shape' => 'DialogCodeHookSettings', ], 'fulfillmentCodeHook' => [ 'shape' => 'FulfillmentCodeHookSettings', ], 'intentConfirmationSetting' => [ 'shape' => 'IntentConfirmationSetting', ], 'intentClosingSetting' => [ 'shape' => 'IntentClosingSetting', ], 'inputContexts' => [ 'shape' => 'InputContextsList', ], 'outputContexts' => [ 'shape' => 'OutputContextsList', ], 'kendraConfiguration' => [ 'shape' => 'KendraConfiguration', ], 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], 'initialResponseSetting' => [ 'shape' => 'InitialResponseSetting', ], 'qnAIntentConfiguration' => [ 'shape' => 'QnAIntentConfiguration', ], 'qInConnectIntentConfiguration' => [ 'shape' => 'QInConnectIntentConfiguration', ], ], ], 'CreateIntentResponse' => [ 'type' => 'structure', 'members' => [ 'intentId' => [ 'shape' => 'Id', ], 'intentName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'parentIntentSignature' => [ 'shape' => 'IntentSignature', ], 'sampleUtterances' => [ 'shape' => 'SampleUtterancesList', ], 'dialogCodeHook' => [ 'shape' => 'DialogCodeHookSettings', ], 'fulfillmentCodeHook' => [ 'shape' => 'FulfillmentCodeHookSettings', ], 'intentConfirmationSetting' => [ 'shape' => 'IntentConfirmationSetting', ], 'intentClosingSetting' => [ 'shape' => 'IntentClosingSetting', ], 'inputContexts' => [ 'shape' => 'InputContextsList', ], 'outputContexts' => [ 'shape' => 'OutputContextsList', ], 'kendraConfiguration' => [ 'shape' => 'KendraConfiguration', ], 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'initialResponseSetting' => [ 'shape' => 'InitialResponseSetting', ], 'qnAIntentConfiguration' => [ 'shape' => 'QnAIntentConfiguration', ], 'qInConnectIntentConfiguration' => [ 'shape' => 'QInConnectIntentConfiguration', ], ], ], 'CreateResourcePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'policy', ], 'members' => [ 'resourceArn' => [ 'shape' => 'AmazonResourceName', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'policy' => [ 'shape' => 'Policy', ], ], ], 'CreateResourcePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'resourceArn' => [ 'shape' => 'AmazonResourceName', ], 'revisionId' => [ 'shape' => 'RevisionId', ], ], ], 'CreateResourcePolicyStatementRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'statementId', 'effect', 'principal', 'action', ], 'members' => [ 'resourceArn' => [ 'shape' => 'AmazonResourceName', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'statementId' => [ 'shape' => 'Name', ], 'effect' => [ 'shape' => 'Effect', ], 'principal' => [ 'shape' => 'PrincipalList', ], 'action' => [ 'shape' => 'OperationList', ], 'condition' => [ 'shape' => 'ConditionMap', ], 'expectedRevisionId' => [ 'shape' => 'RevisionId', 'location' => 'querystring', 'locationName' => 'expectedRevisionId', ], ], ], 'CreateResourcePolicyStatementResponse' => [ 'type' => 'structure', 'members' => [ 'resourceArn' => [ 'shape' => 'AmazonResourceName', ], 'revisionId' => [ 'shape' => 'RevisionId', ], ], ], 'CreateSlotRequest' => [ 'type' => 'structure', 'required' => [ 'slotName', 'valueElicitationSetting', 'botId', 'botVersion', 'localeId', 'intentId', ], 'members' => [ 'slotName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'slotTypeId' => [ 'shape' => 'BuiltInOrCustomSlotTypeId', ], 'valueElicitationSetting' => [ 'shape' => 'SlotValueElicitationSetting', ], 'obfuscationSetting' => [ 'shape' => 'ObfuscationSetting', ], 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], 'intentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'intentId', ], 'multipleValuesSetting' => [ 'shape' => 'MultipleValuesSetting', ], 'subSlotSetting' => [ 'shape' => 'SubSlotSetting', ], ], ], 'CreateSlotResponse' => [ 'type' => 'structure', 'members' => [ 'slotId' => [ 'shape' => 'Id', ], 'slotName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'slotTypeId' => [ 'shape' => 'BuiltInOrCustomSlotTypeId', ], 'valueElicitationSetting' => [ 'shape' => 'SlotValueElicitationSetting', ], 'obfuscationSetting' => [ 'shape' => 'ObfuscationSetting', ], 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'intentId' => [ 'shape' => 'Id', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'multipleValuesSetting' => [ 'shape' => 'MultipleValuesSetting', ], 'subSlotSetting' => [ 'shape' => 'SubSlotSetting', ], ], ], 'CreateSlotTypeRequest' => [ 'type' => 'structure', 'required' => [ 'slotTypeName', 'botId', 'botVersion', 'localeId', ], 'members' => [ 'slotTypeName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'slotTypeValues' => [ 'shape' => 'SlotTypeValues', ], 'valueSelectionSetting' => [ 'shape' => 'SlotValueSelectionSetting', ], 'parentSlotTypeSignature' => [ 'shape' => 'SlotTypeSignature', ], 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], 'externalSourceSetting' => [ 'shape' => 'ExternalSourceSetting', ], 'compositeSlotTypeSetting' => [ 'shape' => 'CompositeSlotTypeSetting', ], ], ], 'CreateSlotTypeResponse' => [ 'type' => 'structure', 'members' => [ 'slotTypeId' => [ 'shape' => 'Id', ], 'slotTypeName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'slotTypeValues' => [ 'shape' => 'SlotTypeValues', ], 'valueSelectionSetting' => [ 'shape' => 'SlotValueSelectionSetting', ], 'parentSlotTypeSignature' => [ 'shape' => 'SlotTypeSignature', ], 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'externalSourceSetting' => [ 'shape' => 'ExternalSourceSetting', ], 'compositeSlotTypeSetting' => [ 'shape' => 'CompositeSlotTypeSetting', ], ], ], 'CreateTestSetDiscrepancyReportRequest' => [ 'type' => 'structure', 'required' => [ 'testSetId', 'target', ], 'members' => [ 'testSetId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'testSetId', ], 'target' => [ 'shape' => 'TestSetDiscrepancyReportResourceTarget', ], ], ], 'CreateTestSetDiscrepancyReportResponse' => [ 'type' => 'structure', 'members' => [ 'testSetDiscrepancyReportId' => [ 'shape' => 'Id', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'testSetId' => [ 'shape' => 'Id', ], 'target' => [ 'shape' => 'TestSetDiscrepancyReportResourceTarget', ], ], ], 'CreateUploadUrlRequest' => [ 'type' => 'structure', 'members' => [], ], 'CreateUploadUrlResponse' => [ 'type' => 'structure', 'members' => [ 'importId' => [ 'shape' => 'Id', ], 'uploadUrl' => [ 'shape' => 'PresignedS3Url', ], ], ], 'CustomPayload' => [ 'type' => 'structure', 'required' => [ 'value', ], 'members' => [ 'value' => [ 'shape' => 'CustomPayloadValue', ], ], ], 'CustomPayloadValue' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, ], 'CustomVocabularyEntryId' => [ 'type' => 'structure', 'required' => [ 'itemId', ], 'members' => [ 'itemId' => [ 'shape' => 'ItemId', ], ], ], 'CustomVocabularyExportSpecification' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', 'localeId', ], 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'BotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], ], ], 'CustomVocabularyImportSpecification' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', 'localeId', ], 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], ], ], 'CustomVocabularyItem' => [ 'type' => 'structure', 'required' => [ 'itemId', 'phrase', ], 'members' => [ 'itemId' => [ 'shape' => 'ItemId', ], 'phrase' => [ 'shape' => 'Phrase', ], 'weight' => [ 'shape' => 'Weight', ], 'displayAs' => [ 'shape' => 'Phrase', ], ], ], 'CustomVocabularyItems' => [ 'type' => 'list', 'member' => [ 'shape' => 'CustomVocabularyItem', ], ], 'CustomVocabularyStatus' => [ 'type' => 'string', 'enum' => [ 'Ready', 'Deleting', 'Exporting', 'Importing', 'Creating', ], ], 'DTMFCharacter' => [ 'type' => 'string', 'pattern' => '^[A-D0-9#*]{1}$', ], 'DTMFSpecification' => [ 'type' => 'structure', 'required' => [ 'maxLength', 'endTimeoutMs', 'deletionCharacter', 'endCharacter', ], 'members' => [ 'maxLength' => [ 'shape' => 'MaxUtteranceDigits', ], 'endTimeoutMs' => [ 'shape' => 'TimeInMilliSeconds', ], 'deletionCharacter' => [ 'shape' => 'DTMFCharacter', ], 'endCharacter' => [ 'shape' => 'DTMFCharacter', ], ], ], 'DataPrivacy' => [ 'type' => 'structure', 'required' => [ 'childDirected', ], 'members' => [ 'childDirected' => [ 'shape' => 'ChildDirected', ], ], ], 'DataSourceConfiguration' => [ 'type' => 'structure', 'members' => [ 'opensearchConfiguration' => [ 'shape' => 'OpensearchConfiguration', ], 'kendraConfiguration' => [ 'shape' => 'QnAKendraConfiguration', ], 'bedrockKnowledgeStoreConfiguration' => [ 'shape' => 'BedrockKnowledgeStoreConfiguration', ], ], ], 'DateRangeFilter' => [ 'type' => 'structure', 'required' => [ 'startDateTime', 'endDateTime', ], 'members' => [ 'startDateTime' => [ 'shape' => 'Timestamp', ], 'endDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'DefaultConditionalBranch' => [ 'type' => 'structure', 'members' => [ 'nextStep' => [ 'shape' => 'DialogState', ], 'response' => [ 'shape' => 'ResponseSpecification', ], ], ], 'DeleteBotAliasRequest' => [ 'type' => 'structure', 'required' => [ 'botAliasId', 'botId', ], 'members' => [ 'botAliasId' => [ 'shape' => 'BotAliasId', 'location' => 'uri', 'locationName' => 'botAliasId', ], 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'skipResourceInUseCheck' => [ 'shape' => 'SkipResourceInUseCheck', 'location' => 'querystring', 'locationName' => 'skipResourceInUseCheck', ], ], ], 'DeleteBotAliasResponse' => [ 'type' => 'structure', 'members' => [ 'botAliasId' => [ 'shape' => 'BotAliasId', ], 'botId' => [ 'shape' => 'Id', ], 'botAliasStatus' => [ 'shape' => 'BotAliasStatus', ], ], ], 'DeleteBotLocaleRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', 'localeId', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], ], ], 'DeleteBotLocaleResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'botLocaleStatus' => [ 'shape' => 'BotLocaleStatus', ], ], ], 'DeleteBotReplicaRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'replicaRegion', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'replicaRegion' => [ 'shape' => 'ReplicaRegion', 'location' => 'uri', 'locationName' => 'replicaRegion', ], ], ], 'DeleteBotReplicaResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'replicaRegion' => [ 'shape' => 'ReplicaRegion', ], 'botReplicaStatus' => [ 'shape' => 'BotReplicaStatus', ], ], ], 'DeleteBotRequest' => [ 'type' => 'structure', 'required' => [ 'botId', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'skipResourceInUseCheck' => [ 'shape' => 'SkipResourceInUseCheck', 'location' => 'querystring', 'locationName' => 'skipResourceInUseCheck', ], ], ], 'DeleteBotResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botStatus' => [ 'shape' => 'BotStatus', ], ], ], 'DeleteBotVersionRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'NumericalBotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'skipResourceInUseCheck' => [ 'shape' => 'SkipResourceInUseCheck', 'location' => 'querystring', 'locationName' => 'skipResourceInUseCheck', ], ], ], 'DeleteBotVersionResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'NumericalBotVersion', ], 'botStatus' => [ 'shape' => 'BotStatus', ], ], ], 'DeleteCustomVocabularyItemsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CustomVocabularyEntryId', ], 'max' => 10, 'min' => 1, ], 'DeleteCustomVocabularyRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', 'localeId', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], ], ], 'DeleteCustomVocabularyResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'customVocabularyStatus' => [ 'shape' => 'CustomVocabularyStatus', ], ], ], 'DeleteExportRequest' => [ 'type' => 'structure', 'required' => [ 'exportId', ], 'members' => [ 'exportId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'exportId', ], ], ], 'DeleteExportResponse' => [ 'type' => 'structure', 'members' => [ 'exportId' => [ 'shape' => 'Id', ], 'exportStatus' => [ 'shape' => 'ExportStatus', ], ], ], 'DeleteImportRequest' => [ 'type' => 'structure', 'required' => [ 'importId', ], 'members' => [ 'importId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'importId', ], ], ], 'DeleteImportResponse' => [ 'type' => 'structure', 'members' => [ 'importId' => [ 'shape' => 'Id', ], 'importStatus' => [ 'shape' => 'ImportStatus', ], ], ], 'DeleteIntentRequest' => [ 'type' => 'structure', 'required' => [ 'intentId', 'botId', 'botVersion', 'localeId', ], 'members' => [ 'intentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'intentId', ], 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], ], ], 'DeleteResourcePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'AmazonResourceName', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'expectedRevisionId' => [ 'shape' => 'RevisionId', 'location' => 'querystring', 'locationName' => 'expectedRevisionId', ], ], ], 'DeleteResourcePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'resourceArn' => [ 'shape' => 'AmazonResourceName', ], 'revisionId' => [ 'shape' => 'RevisionId', ], ], ], 'DeleteResourcePolicyStatementRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'statementId', ], 'members' => [ 'resourceArn' => [ 'shape' => 'AmazonResourceName', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'statementId' => [ 'shape' => 'Name', 'location' => 'uri', 'locationName' => 'statementId', ], 'expectedRevisionId' => [ 'shape' => 'RevisionId', 'location' => 'querystring', 'locationName' => 'expectedRevisionId', ], ], ], 'DeleteResourcePolicyStatementResponse' => [ 'type' => 'structure', 'members' => [ 'resourceArn' => [ 'shape' => 'AmazonResourceName', ], 'revisionId' => [ 'shape' => 'RevisionId', ], ], ], 'DeleteSlotRequest' => [ 'type' => 'structure', 'required' => [ 'slotId', 'botId', 'botVersion', 'localeId', 'intentId', ], 'members' => [ 'slotId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'slotId', ], 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], 'intentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'intentId', ], ], ], 'DeleteSlotTypeRequest' => [ 'type' => 'structure', 'required' => [ 'slotTypeId', 'botId', 'botVersion', 'localeId', ], 'members' => [ 'slotTypeId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'slotTypeId', ], 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], 'skipResourceInUseCheck' => [ 'shape' => 'SkipResourceInUseCheck', 'location' => 'querystring', 'locationName' => 'skipResourceInUseCheck', ], ], ], 'DeleteTestSetRequest' => [ 'type' => 'structure', 'required' => [ 'testSetId', ], 'members' => [ 'testSetId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'testSetId', ], ], ], 'DeleteUtterancesRequest' => [ 'type' => 'structure', 'required' => [ 'botId', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'querystring', 'locationName' => 'localeId', ], 'sessionId' => [ 'shape' => 'SessionId', 'location' => 'querystring', 'locationName' => 'sessionId', ], ], ], 'DeleteUtterancesResponse' => [ 'type' => 'structure', 'members' => [], ], 'DescribeBotAliasRequest' => [ 'type' => 'structure', 'required' => [ 'botAliasId', 'botId', ], 'members' => [ 'botAliasId' => [ 'shape' => 'BotAliasId', 'location' => 'uri', 'locationName' => 'botAliasId', ], 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], ], ], 'DescribeBotAliasResponse' => [ 'type' => 'structure', 'members' => [ 'botAliasId' => [ 'shape' => 'BotAliasId', ], 'botAliasName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'botVersion' => [ 'shape' => 'BotVersion', ], 'botAliasLocaleSettings' => [ 'shape' => 'BotAliasLocaleSettingsMap', ], 'conversationLogSettings' => [ 'shape' => 'ConversationLogSettings', ], 'sentimentAnalysisSettings' => [ 'shape' => 'SentimentAnalysisSettings', ], 'botAliasHistoryEvents' => [ 'shape' => 'BotAliasHistoryEventsList', ], 'botAliasStatus' => [ 'shape' => 'BotAliasStatus', ], 'botId' => [ 'shape' => 'Id', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], 'parentBotNetworks' => [ 'shape' => 'ParentBotNetworks', ], ], ], 'DescribeBotLocaleRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', 'localeId', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'BotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], ], ], 'DescribeBotLocaleResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'BotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'localeName' => [ 'shape' => 'LocaleName', ], 'description' => [ 'shape' => 'Description', ], 'nluIntentConfidenceThreshold' => [ 'shape' => 'ConfidenceThreshold', ], 'voiceSettings' => [ 'shape' => 'VoiceSettings', ], 'intentsCount' => [ 'shape' => 'ResourceCount', ], 'slotTypesCount' => [ 'shape' => 'ResourceCount', ], 'botLocaleStatus' => [ 'shape' => 'BotLocaleStatus', ], 'failureReasons' => [ 'shape' => 'FailureReasons', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], 'lastBuildSubmittedDateTime' => [ 'shape' => 'Timestamp', ], 'botLocaleHistoryEvents' => [ 'shape' => 'BotLocaleHistoryEventsList', ], 'recommendedActions' => [ 'shape' => 'RecommendedActions', ], 'generativeAISettings' => [ 'shape' => 'GenerativeAISettings', ], ], ], 'DescribeBotRecommendationRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', 'localeId', 'botRecommendationId', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], 'botRecommendationId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botRecommendationId', ], ], ], 'DescribeBotRecommendationResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'botRecommendationStatus' => [ 'shape' => 'BotRecommendationStatus', ], 'botRecommendationId' => [ 'shape' => 'Id', ], 'failureReasons' => [ 'shape' => 'FailureReasons', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], 'transcriptSourceSetting' => [ 'shape' => 'TranscriptSourceSetting', ], 'encryptionSetting' => [ 'shape' => 'EncryptionSetting', ], 'botRecommendationResults' => [ 'shape' => 'BotRecommendationResults', ], ], ], 'DescribeBotReplicaRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'replicaRegion', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'replicaRegion' => [ 'shape' => 'ReplicaRegion', 'location' => 'uri', 'locationName' => 'replicaRegion', ], ], ], 'DescribeBotReplicaResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'replicaRegion' => [ 'shape' => 'ReplicaRegion', ], 'sourceRegion' => [ 'shape' => 'ReplicaRegion', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'botReplicaStatus' => [ 'shape' => 'BotReplicaStatus', ], 'failureReasons' => [ 'shape' => 'FailureReasons', ], ], ], 'DescribeBotRequest' => [ 'type' => 'structure', 'required' => [ 'botId', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], ], ], 'DescribeBotResourceGenerationRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', 'localeId', 'generationId', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'BotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], 'generationId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'generationId', ], ], ], 'DescribeBotResourceGenerationResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'BotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'generationId' => [ 'shape' => 'Id', ], 'failureReasons' => [ 'shape' => 'FailureReasons', ], 'generationStatus' => [ 'shape' => 'GenerationStatus', ], 'generationInputPrompt' => [ 'shape' => 'GenerationInput', ], 'generatedBotLocaleUrl' => [ 'shape' => 'PresignedS3Url', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'modelArn' => [ 'shape' => 'BedrockModelArn', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeBotResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'dataPrivacy' => [ 'shape' => 'DataPrivacy', ], 'idleSessionTTLInSeconds' => [ 'shape' => 'SessionTTL', ], 'botStatus' => [ 'shape' => 'BotStatus', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], 'botType' => [ 'shape' => 'BotType', ], 'botMembers' => [ 'shape' => 'BotMembers', ], 'failureReasons' => [ 'shape' => 'FailureReasons', ], 'errorLogSettings' => [ 'shape' => 'ErrorLogSettings', ], ], ], 'DescribeBotVersionRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'NumericalBotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], ], ], 'DescribeBotVersionResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botName' => [ 'shape' => 'Name', ], 'botVersion' => [ 'shape' => 'NumericalBotVersion', ], 'description' => [ 'shape' => 'Description', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'dataPrivacy' => [ 'shape' => 'DataPrivacy', ], 'idleSessionTTLInSeconds' => [ 'shape' => 'SessionTTL', ], 'botStatus' => [ 'shape' => 'BotStatus', ], 'failureReasons' => [ 'shape' => 'FailureReasons', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'parentBotNetworks' => [ 'shape' => 'ParentBotNetworks', ], 'botType' => [ 'shape' => 'BotType', ], 'botMembers' => [ 'shape' => 'BotMembers', ], ], ], 'DescribeCustomVocabularyMetadataRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', 'localeId', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'BotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], ], ], 'DescribeCustomVocabularyMetadataResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'BotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'customVocabularyStatus' => [ 'shape' => 'CustomVocabularyStatus', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeExportRequest' => [ 'type' => 'structure', 'required' => [ 'exportId', ], 'members' => [ 'exportId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'exportId', ], ], ], 'DescribeExportResponse' => [ 'type' => 'structure', 'members' => [ 'exportId' => [ 'shape' => 'Id', ], 'resourceSpecification' => [ 'shape' => 'ExportResourceSpecification', ], 'fileFormat' => [ 'shape' => 'ImportExportFileFormat', ], 'exportStatus' => [ 'shape' => 'ExportStatus', ], 'failureReasons' => [ 'shape' => 'FailureReasons', ], 'downloadUrl' => [ 'shape' => 'PresignedS3Url', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeImportRequest' => [ 'type' => 'structure', 'required' => [ 'importId', ], 'members' => [ 'importId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'importId', ], ], ], 'DescribeImportResponse' => [ 'type' => 'structure', 'members' => [ 'importId' => [ 'shape' => 'Id', ], 'resourceSpecification' => [ 'shape' => 'ImportResourceSpecification', ], 'importedResourceId' => [ 'shape' => 'ImportedResourceId', ], 'importedResourceName' => [ 'shape' => 'Name', ], 'mergeStrategy' => [ 'shape' => 'MergeStrategy', ], 'importStatus' => [ 'shape' => 'ImportStatus', ], 'failureReasons' => [ 'shape' => 'FailureReasons', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeIntentRequest' => [ 'type' => 'structure', 'required' => [ 'intentId', 'botId', 'botVersion', 'localeId', ], 'members' => [ 'intentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'intentId', ], 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'BotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], ], ], 'DescribeIntentResponse' => [ 'type' => 'structure', 'members' => [ 'intentId' => [ 'shape' => 'Id', ], 'intentName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'parentIntentSignature' => [ 'shape' => 'IntentSignature', ], 'sampleUtterances' => [ 'shape' => 'SampleUtterancesList', ], 'dialogCodeHook' => [ 'shape' => 'DialogCodeHookSettings', ], 'fulfillmentCodeHook' => [ 'shape' => 'FulfillmentCodeHookSettings', ], 'slotPriorities' => [ 'shape' => 'SlotPrioritiesList', ], 'intentConfirmationSetting' => [ 'shape' => 'IntentConfirmationSetting', ], 'intentClosingSetting' => [ 'shape' => 'IntentClosingSetting', ], 'inputContexts' => [ 'shape' => 'InputContextsList', ], 'outputContexts' => [ 'shape' => 'OutputContextsList', ], 'kendraConfiguration' => [ 'shape' => 'KendraConfiguration', ], 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], 'initialResponseSetting' => [ 'shape' => 'InitialResponseSetting', ], 'qnAIntentConfiguration' => [ 'shape' => 'QnAIntentConfiguration', ], 'qInConnectIntentConfiguration' => [ 'shape' => 'QInConnectIntentConfiguration', ], ], ], 'DescribeResourcePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'AmazonResourceName', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'DescribeResourcePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'resourceArn' => [ 'shape' => 'AmazonResourceName', ], 'policy' => [ 'shape' => 'Policy', ], 'revisionId' => [ 'shape' => 'RevisionId', ], ], ], 'DescribeSlotRequest' => [ 'type' => 'structure', 'required' => [ 'slotId', 'botId', 'botVersion', 'localeId', 'intentId', ], 'members' => [ 'slotId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'slotId', ], 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'BotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], 'intentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'intentId', ], ], ], 'DescribeSlotResponse' => [ 'type' => 'structure', 'members' => [ 'slotId' => [ 'shape' => 'Id', ], 'slotName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'slotTypeId' => [ 'shape' => 'BuiltInOrCustomSlotTypeId', ], 'valueElicitationSetting' => [ 'shape' => 'SlotValueElicitationSetting', ], 'obfuscationSetting' => [ 'shape' => 'ObfuscationSetting', ], 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'BotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'intentId' => [ 'shape' => 'Id', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], 'multipleValuesSetting' => [ 'shape' => 'MultipleValuesSetting', ], 'subSlotSetting' => [ 'shape' => 'SubSlotSetting', ], ], ], 'DescribeSlotTypeRequest' => [ 'type' => 'structure', 'required' => [ 'slotTypeId', 'botId', 'botVersion', 'localeId', ], 'members' => [ 'slotTypeId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'slotTypeId', ], 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'BotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], ], ], 'DescribeSlotTypeResponse' => [ 'type' => 'structure', 'members' => [ 'slotTypeId' => [ 'shape' => 'Id', ], 'slotTypeName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'slotTypeValues' => [ 'shape' => 'SlotTypeValues', ], 'valueSelectionSetting' => [ 'shape' => 'SlotValueSelectionSetting', ], 'parentSlotTypeSignature' => [ 'shape' => 'SlotTypeSignature', ], 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'BotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], 'externalSourceSetting' => [ 'shape' => 'ExternalSourceSetting', ], 'compositeSlotTypeSetting' => [ 'shape' => 'CompositeSlotTypeSetting', ], ], ], 'DescribeTestExecutionRequest' => [ 'type' => 'structure', 'required' => [ 'testExecutionId', ], 'members' => [ 'testExecutionId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'testExecutionId', ], ], ], 'DescribeTestExecutionResponse' => [ 'type' => 'structure', 'members' => [ 'testExecutionId' => [ 'shape' => 'Id', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], 'testExecutionStatus' => [ 'shape' => 'TestExecutionStatus', ], 'testSetId' => [ 'shape' => 'Id', ], 'testSetName' => [ 'shape' => 'Name', ], 'target' => [ 'shape' => 'TestExecutionTarget', ], 'apiMode' => [ 'shape' => 'TestExecutionApiMode', ], 'testExecutionModality' => [ 'shape' => 'TestExecutionModality', ], 'failureReasons' => [ 'shape' => 'FailureReasons', ], ], ], 'DescribeTestSetDiscrepancyReportRequest' => [ 'type' => 'structure', 'required' => [ 'testSetDiscrepancyReportId', ], 'members' => [ 'testSetDiscrepancyReportId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'testSetDiscrepancyReportId', ], ], ], 'DescribeTestSetDiscrepancyReportResponse' => [ 'type' => 'structure', 'members' => [ 'testSetDiscrepancyReportId' => [ 'shape' => 'Id', ], 'testSetId' => [ 'shape' => 'Id', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'target' => [ 'shape' => 'TestSetDiscrepancyReportResourceTarget', ], 'testSetDiscrepancyReportStatus' => [ 'shape' => 'TestSetDiscrepancyReportStatus', ], 'lastUpdatedDataTime' => [ 'shape' => 'Timestamp', ], 'testSetDiscrepancyTopErrors' => [ 'shape' => 'TestSetDiscrepancyErrors', ], 'testSetDiscrepancyRawOutputUrl' => [ 'shape' => 'PresignedS3Url', ], 'failureReasons' => [ 'shape' => 'FailureReasons', ], ], ], 'DescribeTestSetGenerationRequest' => [ 'type' => 'structure', 'required' => [ 'testSetGenerationId', ], 'members' => [ 'testSetGenerationId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'testSetGenerationId', ], ], ], 'DescribeTestSetGenerationResponse' => [ 'type' => 'structure', 'members' => [ 'testSetGenerationId' => [ 'shape' => 'Id', ], 'testSetGenerationStatus' => [ 'shape' => 'TestSetGenerationStatus', ], 'failureReasons' => [ 'shape' => 'FailureReasons', ], 'testSetId' => [ 'shape' => 'Id', ], 'testSetName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'storageLocation' => [ 'shape' => 'TestSetStorageLocation', ], 'generationDataSource' => [ 'shape' => 'TestSetGenerationDataSource', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeTestSetRequest' => [ 'type' => 'structure', 'required' => [ 'testSetId', ], 'members' => [ 'testSetId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'testSetId', ], ], ], 'DescribeTestSetResponse' => [ 'type' => 'structure', 'members' => [ 'testSetId' => [ 'shape' => 'Id', ], 'testSetName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'modality' => [ 'shape' => 'TestSetModality', ], 'status' => [ 'shape' => 'TestSetStatus', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'numTurns' => [ 'shape' => 'Count', ], 'storageLocation' => [ 'shape' => 'TestSetStorageLocation', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'Description' => [ 'type' => 'string', 'max' => 2000, 'min' => 0, ], 'DescriptiveBotBuilderSpecification' => [ 'type' => 'structure', 'required' => [ 'enabled', ], 'members' => [ 'enabled' => [ 'shape' => 'Boolean', ], 'bedrockModelSpecification' => [ 'shape' => 'BedrockModelSpecification', ], ], ], 'DialogAction' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'type' => [ 'shape' => 'DialogActionType', ], 'slotToElicit' => [ 'shape' => 'Name', ], 'suppressNextMessage' => [ 'shape' => 'BoxedBoolean', ], ], ], 'DialogActionType' => [ 'type' => 'string', 'enum' => [ 'ElicitIntent', 'StartIntent', 'ElicitSlot', 'EvaluateConditional', 'InvokeDialogCodeHook', 'ConfirmIntent', 'FulfillIntent', 'CloseIntent', 'EndConversation', ], ], 'DialogCodeHookInvocationSetting' => [ 'type' => 'structure', 'required' => [ 'enableCodeHookInvocation', 'active', 'postCodeHookSpecification', ], 'members' => [ 'enableCodeHookInvocation' => [ 'shape' => 'BoxedBoolean', ], 'active' => [ 'shape' => 'BoxedBoolean', ], 'invocationLabel' => [ 'shape' => 'Name', ], 'postCodeHookSpecification' => [ 'shape' => 'PostDialogCodeHookInvocationSpecification', ], ], ], 'DialogCodeHookSettings' => [ 'type' => 'structure', 'required' => [ 'enabled', ], 'members' => [ 'enabled' => [ 'shape' => 'Boolean', ], ], ], 'DialogState' => [ 'type' => 'structure', 'members' => [ 'dialogAction' => [ 'shape' => 'DialogAction', ], 'intent' => [ 'shape' => 'IntentOverride', ], 'sessionAttributes' => [ 'shape' => 'StringMap', ], ], ], 'DomainEndpoint' => [ 'type' => 'string', 'pattern' => '^(http|https):\\/\\/+[^\\s]+[\\w]', ], 'DraftBotVersion' => [ 'type' => 'string', 'max' => 5, 'min' => 5, 'pattern' => '^DRAFT$', ], 'Effect' => [ 'type' => 'string', 'enum' => [ 'Allow', 'Deny', ], ], 'ElicitationCodeHookInvocationSetting' => [ 'type' => 'structure', 'required' => [ 'enableCodeHookInvocation', ], 'members' => [ 'enableCodeHookInvocation' => [ 'shape' => 'BoxedBoolean', ], 'invocationLabel' => [ 'shape' => 'Name', ], ], ], 'Enabled' => [ 'type' => 'boolean', ], 'EncryptionSetting' => [ 'type' => 'structure', 'members' => [ 'kmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'botLocaleExportPassword' => [ 'shape' => 'FilePassword', ], 'associatedTranscriptsPassword' => [ 'shape' => 'FilePassword', ], ], ], 'ErrorCode' => [ 'type' => 'string', 'enum' => [ 'DUPLICATE_INPUT', 'RESOURCE_DOES_NOT_EXIST', 'RESOURCE_ALREADY_EXISTS', 'INTERNAL_SERVER_FAILURE', ], ], 'ErrorLogSettings' => [ 'type' => 'structure', 'required' => [ 'enabled', ], 'members' => [ 'enabled' => [ 'shape' => 'BoxedBoolean', ], ], ], 'ErrorMessage' => [ 'type' => 'string', ], 'ExactResponseFields' => [ 'type' => 'structure', 'required' => [ 'questionField', 'answerField', ], 'members' => [ 'questionField' => [ 'shape' => 'QuestionField', ], 'answerField' => [ 'shape' => 'AnswerField', ], ], ], 'ExceptionMessage' => [ 'type' => 'string', ], 'ExecutionErrorDetails' => [ 'type' => 'structure', 'required' => [ 'errorCode', 'errorMessage', ], 'members' => [ 'errorCode' => [ 'shape' => 'NonEmptyString', ], 'errorMessage' => [ 'shape' => 'NonEmptyString', ], ], ], 'ExportFilter' => [ 'type' => 'structure', 'required' => [ 'name', 'values', 'operator', ], 'members' => [ 'name' => [ 'shape' => 'ExportFilterName', ], 'values' => [ 'shape' => 'FilterValues', ], 'operator' => [ 'shape' => 'ExportFilterOperator', ], ], ], 'ExportFilterName' => [ 'type' => 'string', 'enum' => [ 'ExportResourceType', ], ], 'ExportFilterOperator' => [ 'type' => 'string', 'enum' => [ 'CO', 'EQ', ], ], 'ExportFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExportFilter', ], 'max' => 1, 'min' => 1, ], 'ExportResourceSpecification' => [ 'type' => 'structure', 'members' => [ 'botExportSpecification' => [ 'shape' => 'BotExportSpecification', ], 'botLocaleExportSpecification' => [ 'shape' => 'BotLocaleExportSpecification', ], 'customVocabularyExportSpecification' => [ 'shape' => 'CustomVocabularyExportSpecification', ], 'testSetExportSpecification' => [ 'shape' => 'TestSetExportSpecification', ], ], ], 'ExportSortAttribute' => [ 'type' => 'string', 'enum' => [ 'LastUpdatedDateTime', ], ], 'ExportSortBy' => [ 'type' => 'structure', 'required' => [ 'attribute', 'order', ], 'members' => [ 'attribute' => [ 'shape' => 'ExportSortAttribute', ], 'order' => [ 'shape' => 'SortOrder', ], ], ], 'ExportStatus' => [ 'type' => 'string', 'enum' => [ 'InProgress', 'Completed', 'Failed', 'Deleting', ], ], 'ExportSummary' => [ 'type' => 'structure', 'members' => [ 'exportId' => [ 'shape' => 'Id', ], 'resourceSpecification' => [ 'shape' => 'ExportResourceSpecification', ], 'fileFormat' => [ 'shape' => 'ImportExportFileFormat', ], 'exportStatus' => [ 'shape' => 'ExportStatus', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'ExportSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExportSummary', ], ], 'ExternalSourceSetting' => [ 'type' => 'structure', 'members' => [ 'grammarSlotTypeSetting' => [ 'shape' => 'GrammarSlotTypeSetting', ], ], ], 'FailedCustomVocabularyItem' => [ 'type' => 'structure', 'members' => [ 'itemId' => [ 'shape' => 'ItemId', ], 'errorMessage' => [ 'shape' => 'ErrorMessage', ], 'errorCode' => [ 'shape' => 'ErrorCode', ], ], ], 'FailedCustomVocabularyItems' => [ 'type' => 'list', 'member' => [ 'shape' => 'FailedCustomVocabularyItem', ], ], 'FailureReason' => [ 'type' => 'string', ], 'FailureReasons' => [ 'type' => 'list', 'member' => [ 'shape' => 'FailureReason', ], ], 'FilePassword' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'sensitive' => true, ], 'FilterValue' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^[0-9a-zA-Z_()\\s-]+$', ], 'FilterValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'FilterValue', ], 'max' => 1, 'min' => 1, ], 'FulfillmentCodeHookSettings' => [ 'type' => 'structure', 'required' => [ 'enabled', ], 'members' => [ 'enabled' => [ 'shape' => 'Boolean', ], 'postFulfillmentStatusSpecification' => [ 'shape' => 'PostFulfillmentStatusSpecification', ], 'fulfillmentUpdatesSpecification' => [ 'shape' => 'FulfillmentUpdatesSpecification', ], 'active' => [ 'shape' => 'BoxedBoolean', ], ], ], 'FulfillmentStartResponseDelay' => [ 'type' => 'integer', 'max' => 900, 'min' => 1, ], 'FulfillmentStartResponseSpecification' => [ 'type' => 'structure', 'required' => [ 'delayInSeconds', 'messageGroups', ], 'members' => [ 'delayInSeconds' => [ 'shape' => 'FulfillmentStartResponseDelay', ], 'messageGroups' => [ 'shape' => 'MessageGroupsList', ], 'allowInterrupt' => [ 'shape' => 'BoxedBoolean', ], ], ], 'FulfillmentTimeout' => [ 'type' => 'integer', 'max' => 900, 'min' => 1, ], 'FulfillmentUpdateResponseFrequency' => [ 'type' => 'integer', 'max' => 900, 'min' => 1, ], 'FulfillmentUpdateResponseSpecification' => [ 'type' => 'structure', 'required' => [ 'frequencyInSeconds', 'messageGroups', ], 'members' => [ 'frequencyInSeconds' => [ 'shape' => 'FulfillmentUpdateResponseFrequency', ], 'messageGroups' => [ 'shape' => 'MessageGroupsList', ], 'allowInterrupt' => [ 'shape' => 'BoxedBoolean', ], ], ], 'FulfillmentUpdatesSpecification' => [ 'type' => 'structure', 'required' => [ 'active', ], 'members' => [ 'active' => [ 'shape' => 'BoxedBoolean', ], 'startResponse' => [ 'shape' => 'FulfillmentStartResponseSpecification', ], 'updateResponse' => [ 'shape' => 'FulfillmentUpdateResponseSpecification', ], 'timeoutInSeconds' => [ 'shape' => 'FulfillmentTimeout', ], ], ], 'GenerateBotElementRequest' => [ 'type' => 'structure', 'required' => [ 'intentId', 'botId', 'botVersion', 'localeId', ], 'members' => [ 'intentId' => [ 'shape' => 'Id', ], 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'BotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], ], ], 'GenerateBotElementResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'intentId' => [ 'shape' => 'Id', ], 'sampleUtterances' => [ 'shape' => 'SampleUtterancesList', ], ], ], 'GenerationInput' => [ 'type' => 'string', 'max' => 2000, 'min' => 100, ], 'GenerationSortBy' => [ 'type' => 'structure', 'required' => [ 'attribute', 'order', ], 'members' => [ 'attribute' => [ 'shape' => 'GenerationSortByAttribute', ], 'order' => [ 'shape' => 'SortOrder', ], ], ], 'GenerationSortByAttribute' => [ 'type' => 'string', 'enum' => [ 'creationStartTime', 'lastUpdatedTime', ], ], 'GenerationStatus' => [ 'type' => 'string', 'enum' => [ 'Failed', 'Complete', 'InProgress', ], ], 'GenerationSummary' => [ 'type' => 'structure', 'members' => [ 'generationId' => [ 'shape' => 'Id', ], 'generationStatus' => [ 'shape' => 'GenerationStatus', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'GenerationSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GenerationSummary', ], ], 'GenerativeAISettings' => [ 'type' => 'structure', 'members' => [ 'runtimeSettings' => [ 'shape' => 'RuntimeSettings', ], 'buildtimeSettings' => [ 'shape' => 'BuildtimeSettings', ], ], ], 'GetTestExecutionArtifactsUrlRequest' => [ 'type' => 'structure', 'required' => [ 'testExecutionId', ], 'members' => [ 'testExecutionId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'testExecutionId', ], ], ], 'GetTestExecutionArtifactsUrlResponse' => [ 'type' => 'structure', 'members' => [ 'testExecutionId' => [ 'shape' => 'Id', ], 'downloadArtifactsUrl' => [ 'shape' => 'PresignedS3Url', ], ], ], 'GrammarSlotTypeSetting' => [ 'type' => 'structure', 'members' => [ 'source' => [ 'shape' => 'GrammarSlotTypeSource', ], ], ], 'GrammarSlotTypeSource' => [ 'type' => 'structure', 'required' => [ 's3BucketName', 's3ObjectKey', ], 'members' => [ 's3BucketName' => [ 'shape' => 'S3BucketName', ], 's3ObjectKey' => [ 'shape' => 'S3ObjectPath', ], 'kmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], ], ], 'HitCount' => [ 'type' => 'integer', ], 'Id' => [ 'type' => 'string', 'max' => 10, 'min' => 10, 'pattern' => '^[0-9a-zA-Z]+$', ], 'ImageResponseCard' => [ 'type' => 'structure', 'required' => [ 'title', ], 'members' => [ 'title' => [ 'shape' => 'AttachmentTitle', ], 'subtitle' => [ 'shape' => 'AttachmentTitle', ], 'imageUrl' => [ 'shape' => 'AttachmentUrl', ], 'buttons' => [ 'shape' => 'ButtonsList', ], ], ], 'ImportExportFileFormat' => [ 'type' => 'string', 'enum' => [ 'LexJson', 'TSV', 'CSV', ], ], 'ImportExportFilePassword' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'sensitive' => true, ], 'ImportFilter' => [ 'type' => 'structure', 'required' => [ 'name', 'values', 'operator', ], 'members' => [ 'name' => [ 'shape' => 'ImportFilterName', ], 'values' => [ 'shape' => 'FilterValues', ], 'operator' => [ 'shape' => 'ImportFilterOperator', ], ], ], 'ImportFilterName' => [ 'type' => 'string', 'enum' => [ 'ImportResourceType', ], ], 'ImportFilterOperator' => [ 'type' => 'string', 'enum' => [ 'CO', 'EQ', ], ], 'ImportFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImportFilter', ], 'max' => 1, 'min' => 1, ], 'ImportResourceSpecification' => [ 'type' => 'structure', 'members' => [ 'botImportSpecification' => [ 'shape' => 'BotImportSpecification', ], 'botLocaleImportSpecification' => [ 'shape' => 'BotLocaleImportSpecification', ], 'customVocabularyImportSpecification' => [ 'shape' => 'CustomVocabularyImportSpecification', ], 'testSetImportResourceSpecification' => [ 'shape' => 'TestSetImportResourceSpecification', ], ], ], 'ImportResourceType' => [ 'type' => 'string', 'enum' => [ 'Bot', 'BotLocale', 'CustomVocabulary', 'TestSet', ], ], 'ImportSortAttribute' => [ 'type' => 'string', 'enum' => [ 'LastUpdatedDateTime', ], ], 'ImportSortBy' => [ 'type' => 'structure', 'required' => [ 'attribute', 'order', ], 'members' => [ 'attribute' => [ 'shape' => 'ImportSortAttribute', ], 'order' => [ 'shape' => 'SortOrder', ], ], ], 'ImportStatus' => [ 'type' => 'string', 'enum' => [ 'InProgress', 'Completed', 'Failed', 'Deleting', ], ], 'ImportSummary' => [ 'type' => 'structure', 'members' => [ 'importId' => [ 'shape' => 'Id', ], 'importedResourceId' => [ 'shape' => 'ImportedResourceId', ], 'importedResourceName' => [ 'shape' => 'Name', ], 'importStatus' => [ 'shape' => 'ImportStatus', ], 'mergeStrategy' => [ 'shape' => 'MergeStrategy', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], 'importedResourceType' => [ 'shape' => 'ImportResourceType', ], ], ], 'ImportSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImportSummary', ], ], 'ImportedResourceId' => [ 'type' => 'string', 'max' => 10, 'min' => 5, 'pattern' => '^([0-9a-zA-Z_])+$', ], 'IncludeField' => [ 'type' => 'string', ], 'InitialResponseSetting' => [ 'type' => 'structure', 'members' => [ 'initialResponse' => [ 'shape' => 'ResponseSpecification', ], 'nextStep' => [ 'shape' => 'DialogState', ], 'conditional' => [ 'shape' => 'ConditionalSpecification', ], 'codeHook' => [ 'shape' => 'DialogCodeHookInvocationSetting', ], ], ], 'InputContext' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'Name', ], ], ], 'InputContextsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InputContext', ], 'max' => 5, 'min' => 0, ], 'InputSessionStateSpecification' => [ 'type' => 'structure', 'members' => [ 'sessionAttributes' => [ 'shape' => 'StringMap', ], 'activeContexts' => [ 'shape' => 'ActiveContextList', ], 'runtimeHints' => [ 'shape' => 'RuntimeHints', ], ], ], 'IntentClassificationTestResultItem' => [ 'type' => 'structure', 'required' => [ 'intentName', 'multiTurnConversation', 'resultCounts', ], 'members' => [ 'intentName' => [ 'shape' => 'Name', ], 'multiTurnConversation' => [ 'shape' => 'Boolean', ], 'resultCounts' => [ 'shape' => 'IntentClassificationTestResultItemCounts', ], ], ], 'IntentClassificationTestResultItemCounts' => [ 'type' => 'structure', 'required' => [ 'totalResultCount', 'intentMatchResultCounts', ], 'members' => [ 'totalResultCount' => [ 'shape' => 'Count', ], 'speechTranscriptionResultCounts' => [ 'shape' => 'TestResultMatchStatusCountMap', ], 'intentMatchResultCounts' => [ 'shape' => 'TestResultMatchStatusCountMap', ], ], ], 'IntentClassificationTestResultItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IntentClassificationTestResultItem', ], ], 'IntentClassificationTestResults' => [ 'type' => 'structure', 'required' => [ 'items', ], 'members' => [ 'items' => [ 'shape' => 'IntentClassificationTestResultItemList', ], ], ], 'IntentClosingSetting' => [ 'type' => 'structure', 'members' => [ 'closingResponse' => [ 'shape' => 'ResponseSpecification', ], 'active' => [ 'shape' => 'BoxedBoolean', ], 'nextStep' => [ 'shape' => 'DialogState', ], 'conditional' => [ 'shape' => 'ConditionalSpecification', ], ], ], 'IntentConfirmationSetting' => [ 'type' => 'structure', 'required' => [ 'promptSpecification', ], 'members' => [ 'promptSpecification' => [ 'shape' => 'PromptSpecification', ], 'declinationResponse' => [ 'shape' => 'ResponseSpecification', ], 'active' => [ 'shape' => 'BoxedBoolean', ], 'confirmationResponse' => [ 'shape' => 'ResponseSpecification', ], 'confirmationNextStep' => [ 'shape' => 'DialogState', ], 'confirmationConditional' => [ 'shape' => 'ConditionalSpecification', ], 'declinationNextStep' => [ 'shape' => 'DialogState', ], 'declinationConditional' => [ 'shape' => 'ConditionalSpecification', ], 'failureResponse' => [ 'shape' => 'ResponseSpecification', ], 'failureNextStep' => [ 'shape' => 'DialogState', ], 'failureConditional' => [ 'shape' => 'ConditionalSpecification', ], 'codeHook' => [ 'shape' => 'DialogCodeHookInvocationSetting', ], 'elicitationCodeHook' => [ 'shape' => 'ElicitationCodeHookInvocationSetting', ], ], ], 'IntentFilter' => [ 'type' => 'structure', 'required' => [ 'name', 'values', 'operator', ], 'members' => [ 'name' => [ 'shape' => 'IntentFilterName', ], 'values' => [ 'shape' => 'FilterValues', ], 'operator' => [ 'shape' => 'IntentFilterOperator', ], ], ], 'IntentFilterName' => [ 'type' => 'string', 'enum' => [ 'IntentName', ], ], 'IntentFilterOperator' => [ 'type' => 'string', 'enum' => [ 'CO', 'EQ', ], ], 'IntentFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'IntentFilter', ], 'max' => 1, 'min' => 1, ], 'IntentLevelSlotResolutionTestResultItem' => [ 'type' => 'structure', 'required' => [ 'intentName', 'multiTurnConversation', 'slotResolutionResults', ], 'members' => [ 'intentName' => [ 'shape' => 'Name', ], 'multiTurnConversation' => [ 'shape' => 'Boolean', ], 'slotResolutionResults' => [ 'shape' => 'SlotResolutionTestResultItems', ], ], ], 'IntentLevelSlotResolutionTestResultItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IntentLevelSlotResolutionTestResultItem', ], ], 'IntentLevelSlotResolutionTestResults' => [ 'type' => 'structure', 'required' => [ 'items', ], 'members' => [ 'items' => [ 'shape' => 'IntentLevelSlotResolutionTestResultItemList', ], ], ], 'IntentOverride' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'slots' => [ 'shape' => 'SlotValueOverrideMap', ], ], ], 'IntentSignature' => [ 'type' => 'string', ], 'IntentSortAttribute' => [ 'type' => 'string', 'enum' => [ 'IntentName', 'LastUpdatedDateTime', ], ], 'IntentSortBy' => [ 'type' => 'structure', 'required' => [ 'attribute', 'order', ], 'members' => [ 'attribute' => [ 'shape' => 'IntentSortAttribute', ], 'order' => [ 'shape' => 'SortOrder', ], ], ], 'IntentState' => [ 'type' => 'string', 'enum' => [ 'Failed', 'Fulfilled', 'InProgress', 'ReadyForFulfillment', 'Waiting', 'FulfillmentInProgress', ], ], 'IntentStatistics' => [ 'type' => 'structure', 'members' => [ 'discoveredIntentCount' => [ 'shape' => 'Count', ], ], ], 'IntentSummary' => [ 'type' => 'structure', 'members' => [ 'intentId' => [ 'shape' => 'Id', ], 'intentName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'parentIntentSignature' => [ 'shape' => 'IntentSignature', ], 'inputContexts' => [ 'shape' => 'InputContextsList', ], 'outputContexts' => [ 'shape' => 'OutputContextsList', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'IntentSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IntentSummary', ], ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'InvokedIntentSample' => [ 'type' => 'structure', 'members' => [ 'intentName' => [ 'shape' => 'Name', ], ], ], 'InvokedIntentSamples' => [ 'type' => 'list', 'member' => [ 'shape' => 'InvokedIntentSample', ], ], 'ItemId' => [ 'type' => 'string', 'max' => 32, 'min' => 1, ], 'KendraConfiguration' => [ 'type' => 'structure', 'required' => [ 'kendraIndex', ], 'members' => [ 'kendraIndex' => [ 'shape' => 'KendraIndexArn', ], 'queryFilterStringEnabled' => [ 'shape' => 'Boolean', ], 'queryFilterString' => [ 'shape' => 'QueryFilterString', ], ], ], 'KendraIndexArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 32, 'pattern' => '^arn:aws:kendra:[a-z]+-[a-z]+-[0-9]:[0-9]{12}:index\\/[a-zA-Z0-9][a-zA-Z0-9_-]*$', ], 'KmsKeyArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:[\\w\\-]+:kms:[\\w\\-]+:[\\d]{12}:(?:key\\/[\\w\\-]+|alias\\/[a-zA-Z0-9:\\/_\\-]{1,256})$', ], 'LambdaARN' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws:lambda:[a-z]+-[a-z]+-[0-9]:[0-9]{12}:function:[a-zA-Z0-9-_]+(/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})?(:[a-zA-Z0-9-_]+)?', ], 'LambdaCodeHook' => [ 'type' => 'structure', 'required' => [ 'lambdaARN', 'codeHookInterfaceVersion', ], 'members' => [ 'lambdaARN' => [ 'shape' => 'LambdaARN', ], 'codeHookInterfaceVersion' => [ 'shape' => 'CodeHookInterfaceVersion', ], ], ], 'LexTranscriptFilter' => [ 'type' => 'structure', 'members' => [ 'dateRangeFilter' => [ 'shape' => 'DateRangeFilter', ], ], ], 'ListAggregatedUtterancesRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'localeId', 'aggregationDuration', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botAliasId' => [ 'shape' => 'BotAliasId', ], 'botVersion' => [ 'shape' => 'BotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'aggregationDuration' => [ 'shape' => 'UtteranceAggregationDuration', ], 'sortBy' => [ 'shape' => 'AggregatedUtterancesSortBy', ], 'filters' => [ 'shape' => 'AggregatedUtterancesFilters', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAggregatedUtterancesResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botAliasId' => [ 'shape' => 'BotAliasId', ], 'botVersion' => [ 'shape' => 'BotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'aggregationDuration' => [ 'shape' => 'UtteranceAggregationDuration', ], 'aggregationWindowStartTime' => [ 'shape' => 'Timestamp', ], 'aggregationWindowEndTime' => [ 'shape' => 'Timestamp', ], 'aggregationLastRefreshedDateTime' => [ 'shape' => 'Timestamp', ], 'aggregatedUtterancesSummaries' => [ 'shape' => 'AggregatedUtterancesSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListBotAliasReplicasRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'replicaRegion', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'replicaRegion' => [ 'shape' => 'ReplicaRegion', 'location' => 'uri', 'locationName' => 'replicaRegion', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListBotAliasReplicasResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'sourceRegion' => [ 'shape' => 'ReplicaRegion', ], 'replicaRegion' => [ 'shape' => 'ReplicaRegion', ], 'botAliasReplicaSummaries' => [ 'shape' => 'BotAliasReplicaSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListBotAliasesRequest' => [ 'type' => 'structure', 'required' => [ 'botId', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListBotAliasesResponse' => [ 'type' => 'structure', 'members' => [ 'botAliasSummaries' => [ 'shape' => 'BotAliasSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'botId' => [ 'shape' => 'Id', ], ], ], 'ListBotLocalesRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'BotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'sortBy' => [ 'shape' => 'BotLocaleSortBy', ], 'filters' => [ 'shape' => 'BotLocaleFilters', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListBotLocalesResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'BotVersion', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'botLocaleSummaries' => [ 'shape' => 'BotLocaleSummaryList', ], ], ], 'ListBotRecommendationsRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', 'localeId', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListBotRecommendationsResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'botRecommendationSummaries' => [ 'shape' => 'BotRecommendationSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListBotReplicasRequest' => [ 'type' => 'structure', 'required' => [ 'botId', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], ], ], 'ListBotReplicasResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'sourceRegion' => [ 'shape' => 'ReplicaRegion', ], 'botReplicaSummaries' => [ 'shape' => 'BotReplicaSummaryList', ], ], ], 'ListBotResourceGenerationsRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', 'localeId', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'BotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], 'sortBy' => [ 'shape' => 'GenerationSortBy', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListBotResourceGenerationsResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'BotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'generationSummaries' => [ 'shape' => 'GenerationSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListBotVersionReplicasRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'replicaRegion', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'replicaRegion' => [ 'shape' => 'ReplicaRegion', 'location' => 'uri', 'locationName' => 'replicaRegion', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'sortBy' => [ 'shape' => 'BotVersionReplicaSortBy', ], ], ], 'ListBotVersionReplicasResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'sourceRegion' => [ 'shape' => 'ReplicaRegion', ], 'replicaRegion' => [ 'shape' => 'ReplicaRegion', ], 'botVersionReplicaSummaries' => [ 'shape' => 'BotVersionReplicaSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListBotVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'botId', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'sortBy' => [ 'shape' => 'BotVersionSortBy', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListBotVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersionSummaries' => [ 'shape' => 'BotVersionSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListBotsRequest' => [ 'type' => 'structure', 'members' => [ 'sortBy' => [ 'shape' => 'BotSortBy', ], 'filters' => [ 'shape' => 'BotFilters', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListBotsResponse' => [ 'type' => 'structure', 'members' => [ 'botSummaries' => [ 'shape' => 'BotSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListBuiltInIntentsRequest' => [ 'type' => 'structure', 'required' => [ 'localeId', ], 'members' => [ 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], 'sortBy' => [ 'shape' => 'BuiltInIntentSortBy', ], 'maxResults' => [ 'shape' => 'BuiltInsMaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListBuiltInIntentsResponse' => [ 'type' => 'structure', 'members' => [ 'builtInIntentSummaries' => [ 'shape' => 'BuiltInIntentSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'localeId' => [ 'shape' => 'LocaleId', ], ], ], 'ListBuiltInSlotTypesRequest' => [ 'type' => 'structure', 'required' => [ 'localeId', ], 'members' => [ 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], 'sortBy' => [ 'shape' => 'BuiltInSlotTypeSortBy', ], 'maxResults' => [ 'shape' => 'BuiltInsMaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListBuiltInSlotTypesResponse' => [ 'type' => 'structure', 'members' => [ 'builtInSlotTypeSummaries' => [ 'shape' => 'BuiltInSlotTypeSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'localeId' => [ 'shape' => 'LocaleId', ], ], ], 'ListCustomVocabularyItemsRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', 'localeId', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'BotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListCustomVocabularyItemsResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'BotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'customVocabularyItems' => [ 'shape' => 'CustomVocabularyItems', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListExportsRequest' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'BotVersion', ], 'sortBy' => [ 'shape' => 'ExportSortBy', ], 'filters' => [ 'shape' => 'ExportFilters', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'localeId' => [ 'shape' => 'LocaleId', ], ], ], 'ListExportsResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'BotVersion', ], 'exportSummaries' => [ 'shape' => 'ExportSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'localeId' => [ 'shape' => 'LocaleId', ], ], ], 'ListImportsRequest' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', ], 'sortBy' => [ 'shape' => 'ImportSortBy', ], 'filters' => [ 'shape' => 'ImportFilters', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'localeId' => [ 'shape' => 'LocaleId', ], ], ], 'ListImportsResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', ], 'importSummaries' => [ 'shape' => 'ImportSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'localeId' => [ 'shape' => 'LocaleId', ], ], ], 'ListIntentMetricsRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'startDateTime', 'endDateTime', 'metrics', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'startDateTime' => [ 'shape' => 'Timestamp', ], 'endDateTime' => [ 'shape' => 'Timestamp', ], 'metrics' => [ 'shape' => 'AnalyticsIntentMetrics', ], 'binBy' => [ 'shape' => 'AnalyticsBinByList', ], 'groupBy' => [ 'shape' => 'AnalyticsIntentGroupByList', ], 'filters' => [ 'shape' => 'AnalyticsIntentFilters', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListIntentMetricsResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'results' => [ 'shape' => 'AnalyticsIntentResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListIntentPathsRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'startDateTime', 'endDateTime', 'intentPath', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'startDateTime' => [ 'shape' => 'Timestamp', ], 'endDateTime' => [ 'shape' => 'Timestamp', ], 'intentPath' => [ 'shape' => 'AnalyticsPath', ], 'filters' => [ 'shape' => 'AnalyticsPathFilters', ], ], ], 'ListIntentPathsResponse' => [ 'type' => 'structure', 'members' => [ 'nodeSummaries' => [ 'shape' => 'AnalyticsIntentNodeSummaries', ], ], ], 'ListIntentStageMetricsRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'startDateTime', 'endDateTime', 'metrics', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'startDateTime' => [ 'shape' => 'Timestamp', ], 'endDateTime' => [ 'shape' => 'Timestamp', ], 'metrics' => [ 'shape' => 'AnalyticsIntentStageMetrics', ], 'binBy' => [ 'shape' => 'AnalyticsBinByList', ], 'groupBy' => [ 'shape' => 'AnalyticsIntentStageGroupByList', ], 'filters' => [ 'shape' => 'AnalyticsIntentStageFilters', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListIntentStageMetricsResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'results' => [ 'shape' => 'AnalyticsIntentStageResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListIntentsRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', 'localeId', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'BotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], 'sortBy' => [ 'shape' => 'IntentSortBy', ], 'filters' => [ 'shape' => 'IntentFilters', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListIntentsResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'BotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'intentSummaries' => [ 'shape' => 'IntentSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListRecommendedIntentsRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', 'localeId', 'botRecommendationId', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], 'botRecommendationId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botRecommendationId', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListRecommendedIntentsResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'botRecommendationId' => [ 'shape' => 'Id', ], 'summaryList' => [ 'shape' => 'RecommendedIntentSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSessionAnalyticsDataRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'startDateTime', 'endDateTime', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'startDateTime' => [ 'shape' => 'Timestamp', ], 'endDateTime' => [ 'shape' => 'Timestamp', ], 'sortBy' => [ 'shape' => 'SessionDataSortBy', ], 'filters' => [ 'shape' => 'AnalyticsSessionFilters', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSessionAnalyticsDataResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'sessions' => [ 'shape' => 'SessionSpecifications', ], ], ], 'ListSessionMetricsRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'startDateTime', 'endDateTime', 'metrics', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'startDateTime' => [ 'shape' => 'Timestamp', ], 'endDateTime' => [ 'shape' => 'Timestamp', ], 'metrics' => [ 'shape' => 'AnalyticsSessionMetrics', ], 'binBy' => [ 'shape' => 'AnalyticsBinByList', ], 'groupBy' => [ 'shape' => 'AnalyticsSessionGroupByList', ], 'filters' => [ 'shape' => 'AnalyticsSessionFilters', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSessionMetricsResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'results' => [ 'shape' => 'AnalyticsSessionResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSlotTypesRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', 'localeId', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'BotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], 'sortBy' => [ 'shape' => 'SlotTypeSortBy', ], 'filters' => [ 'shape' => 'SlotTypeFilters', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSlotTypesResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'BotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'slotTypeSummaries' => [ 'shape' => 'SlotTypeSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSlotsRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', 'localeId', 'intentId', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'BotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], 'intentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'intentId', ], 'sortBy' => [ 'shape' => 'SlotSortBy', ], 'filters' => [ 'shape' => 'SlotFilters', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSlotsResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'BotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'intentId' => [ 'shape' => 'Id', ], 'slotSummaries' => [ 'shape' => 'SlotSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceARN', ], 'members' => [ 'resourceARN' => [ 'shape' => 'AmazonResourceName', 'location' => 'uri', 'locationName' => 'resourceARN', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'ListTestExecutionResultItemsRequest' => [ 'type' => 'structure', 'required' => [ 'testExecutionId', 'resultFilterBy', ], 'members' => [ 'testExecutionId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'testExecutionId', ], 'resultFilterBy' => [ 'shape' => 'TestExecutionResultFilterBy', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTestExecutionResultItemsResponse' => [ 'type' => 'structure', 'members' => [ 'testExecutionResults' => [ 'shape' => 'TestExecutionResultItems', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTestExecutionsRequest' => [ 'type' => 'structure', 'members' => [ 'sortBy' => [ 'shape' => 'TestExecutionSortBy', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTestExecutionsResponse' => [ 'type' => 'structure', 'members' => [ 'testExecutions' => [ 'shape' => 'TestExecutionSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTestSetRecordsRequest' => [ 'type' => 'structure', 'required' => [ 'testSetId', ], 'members' => [ 'testSetId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'testSetId', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTestSetRecordsResponse' => [ 'type' => 'structure', 'members' => [ 'testSetRecords' => [ 'shape' => 'TestSetTurnRecordList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTestSetsRequest' => [ 'type' => 'structure', 'members' => [ 'sortBy' => [ 'shape' => 'TestSetSortBy', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTestSetsResponse' => [ 'type' => 'structure', 'members' => [ 'testSets' => [ 'shape' => 'TestSetSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListUtteranceAnalyticsDataRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'startDateTime', 'endDateTime', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'startDateTime' => [ 'shape' => 'Timestamp', ], 'endDateTime' => [ 'shape' => 'Timestamp', ], 'sortBy' => [ 'shape' => 'UtteranceDataSortBy', ], 'filters' => [ 'shape' => 'AnalyticsUtteranceFilters', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListUtteranceAnalyticsDataResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'utterances' => [ 'shape' => 'UtteranceSpecifications', ], ], ], 'ListUtteranceMetricsRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'startDateTime', 'endDateTime', 'metrics', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'startDateTime' => [ 'shape' => 'Timestamp', ], 'endDateTime' => [ 'shape' => 'Timestamp', ], 'metrics' => [ 'shape' => 'AnalyticsUtteranceMetrics', ], 'binBy' => [ 'shape' => 'AnalyticsBinByList', ], 'groupBy' => [ 'shape' => 'AnalyticsUtteranceGroupByList', ], 'attributes' => [ 'shape' => 'AnalyticsUtteranceAttributes', ], 'filters' => [ 'shape' => 'AnalyticsUtteranceFilters', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListUtteranceMetricsResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'results' => [ 'shape' => 'AnalyticsUtteranceResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'LocaleId' => [ 'type' => 'string', ], 'LocaleName' => [ 'type' => 'string', ], 'LogPrefix' => [ 'type' => 'string', 'max' => 1024, ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 1, ], 'MaxUtteranceDigits' => [ 'type' => 'integer', 'max' => 1024, 'min' => 1, ], 'MergeStrategy' => [ 'type' => 'string', 'enum' => [ 'Overwrite', 'FailOnConflict', 'Append', ], ], 'Message' => [ 'type' => 'structure', 'members' => [ 'plainTextMessage' => [ 'shape' => 'PlainTextMessage', ], 'customPayload' => [ 'shape' => 'CustomPayload', ], 'ssmlMessage' => [ 'shape' => 'SSMLMessage', ], 'imageResponseCard' => [ 'shape' => 'ImageResponseCard', ], ], ], 'MessageGroup' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'Message', ], 'variations' => [ 'shape' => 'MessageVariationsList', ], ], ], 'MessageGroupsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MessageGroup', ], 'max' => 5, 'min' => 1, ], 'MessageSelectionStrategy' => [ 'type' => 'string', 'enum' => [ 'Random', 'Ordered', ], ], 'MessageVariationsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Message', ], 'max' => 2, 'min' => 0, ], 'MissedCount' => [ 'type' => 'integer', ], 'MultipleValuesSetting' => [ 'type' => 'structure', 'members' => [ 'allowMultipleValues' => [ 'shape' => 'Boolean', ], ], ], 'Name' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^([0-9a-zA-Z][_-]?){1,100}$', ], 'NewCustomVocabularyItem' => [ 'type' => 'structure', 'required' => [ 'phrase', ], 'members' => [ 'phrase' => [ 'shape' => 'Phrase', ], 'weight' => [ 'shape' => 'Weight', ], 'displayAs' => [ 'shape' => 'Phrase', ], ], ], 'NextIndex' => [ 'type' => 'integer', 'box' => true, 'max' => 10000000, 'min' => 0, ], 'NextToken' => [ 'type' => 'string', ], 'NluImprovementSpecification' => [ 'type' => 'structure', 'required' => [ 'enabled', ], 'members' => [ 'enabled' => [ 'shape' => 'Enabled', ], ], ], 'NonEmptyString' => [ 'type' => 'string', 'min' => 1, ], 'NumericalBotVersion' => [ 'type' => 'string', 'max' => 5, 'min' => 1, 'pattern' => '^[0-9]+$', ], 'OSIncludeFields' => [ 'type' => 'list', 'member' => [ 'shape' => 'IncludeField', ], 'max' => 5, 'min' => 1, ], 'OSIndexName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^(?![_-])[a-z0-9][a-z0-9_\\-]{0,254}$', ], 'ObfuscationSetting' => [ 'type' => 'structure', 'required' => [ 'obfuscationSettingType', ], 'members' => [ 'obfuscationSettingType' => [ 'shape' => 'ObfuscationSettingType', ], ], ], 'ObfuscationSettingType' => [ 'type' => 'string', 'enum' => [ 'None', 'DefaultObfuscation', ], ], 'ObjectPrefix' => [ 'type' => 'string', 'min' => 1, 'pattern' => '^[\\/]?+[a-zA-Z0-9!_.*\'()-]+(\\/[a-zA-Z0-9!_.*\'()-]+)*$', ], 'ObjectPrefixes' => [ 'type' => 'list', 'member' => [ 'shape' => 'ObjectPrefix', ], 'max' => 2, 'min' => 1, ], 'OpensearchConfiguration' => [ 'type' => 'structure', 'required' => [ 'domainEndpoint', 'indexName', ], 'members' => [ 'domainEndpoint' => [ 'shape' => 'DomainEndpoint', ], 'indexName' => [ 'shape' => 'OSIndexName', ], 'exactResponse' => [ 'shape' => 'Boolean', ], 'exactResponseFields' => [ 'shape' => 'ExactResponseFields', ], 'includeFields' => [ 'shape' => 'OSIncludeFields', ], ], ], 'Operation' => [ 'type' => 'string', 'max' => 50, 'min' => 5, 'pattern' => 'lex:[a-zA-Z*]+$', ], 'OperationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Operation', ], ], 'OutputContext' => [ 'type' => 'structure', 'required' => [ 'name', 'timeToLiveInSeconds', 'turnsToLive', ], 'members' => [ 'name' => [ 'shape' => 'Name', ], 'timeToLiveInSeconds' => [ 'shape' => 'ContextTimeToLiveInSeconds', ], 'turnsToLive' => [ 'shape' => 'ContextTurnsToLive', ], ], ], 'OutputContextsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OutputContext', ], 'max' => 10, 'min' => 0, ], 'OverallTestResultItem' => [ 'type' => 'structure', 'required' => [ 'multiTurnConversation', 'totalResultCount', 'endToEndResultCounts', ], 'members' => [ 'multiTurnConversation' => [ 'shape' => 'Boolean', ], 'totalResultCount' => [ 'shape' => 'Count', ], 'speechTranscriptionResultCounts' => [ 'shape' => 'TestResultMatchStatusCountMap', ], 'endToEndResultCounts' => [ 'shape' => 'TestResultMatchStatusCountMap', ], ], ], 'OverallTestResultItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OverallTestResultItem', ], ], 'OverallTestResults' => [ 'type' => 'structure', 'required' => [ 'items', ], 'members' => [ 'items' => [ 'shape' => 'OverallTestResultItemList', ], ], ], 'ParentBotNetwork' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', ], 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'BotVersion', ], ], ], 'ParentBotNetworks' => [ 'type' => 'list', 'member' => [ 'shape' => 'ParentBotNetwork', ], 'max' => 30, 'min' => 0, ], 'PathFormat' => [ 'type' => 'structure', 'members' => [ 'objectPrefixes' => [ 'shape' => 'ObjectPrefixes', ], ], ], 'Phrase' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'PlainTextMessage' => [ 'type' => 'structure', 'required' => [ 'value', ], 'members' => [ 'value' => [ 'shape' => 'PlainTextMessageValue', ], ], ], 'PlainTextMessageValue' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, ], 'Policy' => [ 'type' => 'string', 'min' => 2, ], 'PostDialogCodeHookInvocationSpecification' => [ 'type' => 'structure', 'members' => [ 'successResponse' => [ 'shape' => 'ResponseSpecification', ], 'successNextStep' => [ 'shape' => 'DialogState', ], 'successConditional' => [ 'shape' => 'ConditionalSpecification', ], 'failureResponse' => [ 'shape' => 'ResponseSpecification', ], 'failureNextStep' => [ 'shape' => 'DialogState', ], 'failureConditional' => [ 'shape' => 'ConditionalSpecification', ], 'timeoutResponse' => [ 'shape' => 'ResponseSpecification', ], 'timeoutNextStep' => [ 'shape' => 'DialogState', ], 'timeoutConditional' => [ 'shape' => 'ConditionalSpecification', ], ], ], 'PostFulfillmentStatusSpecification' => [ 'type' => 'structure', 'members' => [ 'successResponse' => [ 'shape' => 'ResponseSpecification', ], 'failureResponse' => [ 'shape' => 'ResponseSpecification', ], 'timeoutResponse' => [ 'shape' => 'ResponseSpecification', ], 'successNextStep' => [ 'shape' => 'DialogState', ], 'successConditional' => [ 'shape' => 'ConditionalSpecification', ], 'failureNextStep' => [ 'shape' => 'DialogState', ], 'failureConditional' => [ 'shape' => 'ConditionalSpecification', ], 'timeoutNextStep' => [ 'shape' => 'DialogState', ], 'timeoutConditional' => [ 'shape' => 'ConditionalSpecification', ], ], ], 'PreconditionFailedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 412, ], 'exception' => true, ], 'PresignedS3Url' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'Principal' => [ 'type' => 'structure', 'members' => [ 'service' => [ 'shape' => 'ServicePrincipal', ], 'arn' => [ 'shape' => 'PrincipalArn', ], ], ], 'PrincipalArn' => [ 'type' => 'string', 'max' => 1024, 'min' => 30, 'pattern' => '^arn:aws:iam::[0-9]{12}:(root|(user|role)/.*)$', ], 'PrincipalList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Principal', ], ], 'PriorityValue' => [ 'type' => 'integer', 'max' => 100, 'min' => 0, ], 'PromptAttempt' => [ 'type' => 'string', 'enum' => [ 'Initial', 'Retry1', 'Retry2', 'Retry3', 'Retry4', 'Retry5', ], ], 'PromptAttemptSpecification' => [ 'type' => 'structure', 'required' => [ 'allowedInputTypes', ], 'members' => [ 'allowInterrupt' => [ 'shape' => 'BoxedBoolean', ], 'allowedInputTypes' => [ 'shape' => 'AllowedInputTypes', ], 'audioAndDTMFInputSpecification' => [ 'shape' => 'AudioAndDTMFInputSpecification', ], 'textInputSpecification' => [ 'shape' => 'TextInputSpecification', ], ], ], 'PromptAttemptsSpecificationMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'PromptAttempt', ], 'value' => [ 'shape' => 'PromptAttemptSpecification', ], ], 'PromptMaxRetries' => [ 'type' => 'integer', 'max' => 5, 'min' => 0, ], 'PromptSpecification' => [ 'type' => 'structure', 'required' => [ 'messageGroups', 'maxRetries', ], 'members' => [ 'messageGroups' => [ 'shape' => 'MessageGroupsList', ], 'maxRetries' => [ 'shape' => 'PromptMaxRetries', ], 'allowInterrupt' => [ 'shape' => 'BoxedBoolean', ], 'messageSelectionStrategy' => [ 'shape' => 'MessageSelectionStrategy', ], 'promptAttemptsSpecification' => [ 'shape' => 'PromptAttemptsSpecificationMap', ], ], ], 'QInConnectAssistantARN' => [ 'type' => 'string', 'pattern' => '^arn:[a-z-]*?:wisdom:[a-z0-9-]*?:[0-9]{12}:[a-z-]*?/[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}(?:/[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}){0,2}$', ], 'QInConnectAssistantConfiguration' => [ 'type' => 'structure', 'required' => [ 'assistantArn', ], 'members' => [ 'assistantArn' => [ 'shape' => 'QInConnectAssistantARN', ], ], ], 'QInConnectIntentConfiguration' => [ 'type' => 'structure', 'members' => [ 'qInConnectAssistantConfiguration' => [ 'shape' => 'QInConnectAssistantConfiguration', ], ], ], 'QnAIntentConfiguration' => [ 'type' => 'structure', 'members' => [ 'dataSourceConfiguration' => [ 'shape' => 'DataSourceConfiguration', ], 'bedrockModelConfiguration' => [ 'shape' => 'BedrockModelSpecification', ], ], ], 'QnAKendraConfiguration' => [ 'type' => 'structure', 'required' => [ 'kendraIndex', ], 'members' => [ 'kendraIndex' => [ 'shape' => 'KendraIndexArn', ], 'queryFilterStringEnabled' => [ 'shape' => 'Boolean', ], 'queryFilterString' => [ 'shape' => 'QueryFilterString', ], 'exactResponse' => [ 'shape' => 'Boolean', ], ], ], 'QueryFilterString' => [ 'type' => 'string', 'max' => 5000, 'min' => 1, ], 'QuestionField' => [ 'type' => 'string', ], 'RecommendedAction' => [ 'type' => 'string', ], 'RecommendedActions' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecommendedAction', ], ], 'RecommendedIntentSummary' => [ 'type' => 'structure', 'members' => [ 'intentId' => [ 'shape' => 'Id', ], 'intentName' => [ 'shape' => 'Name', ], 'sampleUtterancesCount' => [ 'shape' => 'SampleUtterancesCount', ], ], ], 'RecommendedIntentSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecommendedIntentSummary', ], ], 'RecordNumber' => [ 'type' => 'long', 'max' => 200000, 'min' => 1, ], 'RegexPattern' => [ 'type' => 'string', 'max' => 300, 'min' => 1, ], 'RelativeAggregationDuration' => [ 'type' => 'structure', 'required' => [ 'timeDimension', 'timeValue', ], 'members' => [ 'timeDimension' => [ 'shape' => 'TimeDimension', ], 'timeValue' => [ 'shape' => 'TimeValue', ], ], ], 'ReplicaRegion' => [ 'type' => 'string', 'max' => 25, 'min' => 2, ], 'ResourceCount' => [ 'type' => 'integer', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'ResponseSpecification' => [ 'type' => 'structure', 'required' => [ 'messageGroups', ], 'members' => [ 'messageGroups' => [ 'shape' => 'MessageGroupsList', ], 'allowInterrupt' => [ 'shape' => 'BoxedBoolean', ], ], ], 'RetryAfterSeconds' => [ 'type' => 'integer', ], 'RevisionId' => [ 'type' => 'string', 'max' => 5, 'min' => 1, 'pattern' => '^[0-9]+$', ], 'RoleArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 32, 'pattern' => '^arn:aws:iam::[0-9]{12}:role/.*$', ], 'RuntimeHintDetails' => [ 'type' => 'structure', 'members' => [ 'runtimeHintValues' => [ 'shape' => 'RuntimeHintValuesList', ], 'subSlotHints' => [ 'shape' => 'SlotHintsSlotMap', ], ], ], 'RuntimeHintPhrase' => [ 'type' => 'string', 'max' => 140, 'min' => 1, ], 'RuntimeHintValue' => [ 'type' => 'structure', 'required' => [ 'phrase', ], 'members' => [ 'phrase' => [ 'shape' => 'RuntimeHintPhrase', ], ], ], 'RuntimeHintValuesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RuntimeHintValue', ], 'max' => 100, 'min' => 1, ], 'RuntimeHints' => [ 'type' => 'structure', 'members' => [ 'slotHints' => [ 'shape' => 'SlotHintsIntentMap', ], ], ], 'RuntimeSettings' => [ 'type' => 'structure', 'members' => [ 'slotResolutionImprovement' => [ 'shape' => 'SlotResolutionImprovementSpecification', ], 'nluImprovement' => [ 'shape' => 'NluImprovementSpecification', ], ], ], 'S3BucketArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^arn:[\\w\\-]+:s3:::[a-z0-9][\\.\\-a-z0-9]{1,61}[a-z0-9]$', ], 'S3BucketLogDestination' => [ 'type' => 'structure', 'required' => [ 's3BucketArn', 'logPrefix', ], 'members' => [ 'kmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], 's3BucketArn' => [ 'shape' => 'S3BucketArn', ], 'logPrefix' => [ 'shape' => 'LogPrefix', ], ], ], 'S3BucketName' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '^[a-z0-9][\\.\\-a-z0-9]{1,61}[a-z0-9]$', ], 'S3BucketTranscriptSource' => [ 'type' => 'structure', 'required' => [ 's3BucketName', 'transcriptFormat', ], 'members' => [ 's3BucketName' => [ 'shape' => 'S3BucketName', ], 'pathFormat' => [ 'shape' => 'PathFormat', ], 'transcriptFormat' => [ 'shape' => 'TranscriptFormat', ], 'transcriptFilter' => [ 'shape' => 'TranscriptFilter', ], 'kmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], ], ], 'S3ObjectPath' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '[\\.\\-\\!\\*\\_\\\'\\(\\)a-zA-Z0-9][\\.\\-\\!\\*\\_\\\'\\(\\)\\/a-zA-Z0-9]*$', ], 'SSMLMessage' => [ 'type' => 'structure', 'required' => [ 'value', ], 'members' => [ 'value' => [ 'shape' => 'SSMLMessageValue', ], ], ], 'SSMLMessageValue' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, ], 'SampleUtterance' => [ 'type' => 'structure', 'required' => [ 'utterance', ], 'members' => [ 'utterance' => [ 'shape' => 'Utterance', ], ], ], 'SampleUtteranceGenerationSpecification' => [ 'type' => 'structure', 'required' => [ 'enabled', ], 'members' => [ 'enabled' => [ 'shape' => 'Boolean', ], 'bedrockModelSpecification' => [ 'shape' => 'BedrockModelSpecification', ], ], ], 'SampleUtterancesCount' => [ 'type' => 'integer', ], 'SampleUtterancesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SampleUtterance', ], ], 'SampleValue' => [ 'type' => 'structure', 'required' => [ 'value', ], 'members' => [ 'value' => [ 'shape' => 'Value', ], ], ], 'SearchAssociatedTranscriptsRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', 'localeId', 'botRecommendationId', 'filters', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'BotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], 'botRecommendationId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botRecommendationId', ], 'searchOrder' => [ 'shape' => 'SearchOrder', ], 'filters' => [ 'shape' => 'AssociatedTranscriptFilters', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextIndex' => [ 'shape' => 'NextIndex', ], ], ], 'SearchAssociatedTranscriptsResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'BotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'botRecommendationId' => [ 'shape' => 'Id', ], 'nextIndex' => [ 'shape' => 'NextIndex', ], 'associatedTranscripts' => [ 'shape' => 'AssociatedTranscriptList', ], 'totalResults' => [ 'shape' => 'MaxResults', ], ], ], 'SearchOrder' => [ 'type' => 'string', 'enum' => [ 'Ascending', 'Descending', ], ], 'SentimentAnalysisSettings' => [ 'type' => 'structure', 'required' => [ 'detectSentiment', ], 'members' => [ 'detectSentiment' => [ 'shape' => 'Boolean', ], ], ], 'ServicePrincipal' => [ 'type' => 'string', 'max' => 1024, 'min' => 15, 'pattern' => '^[0-9a-zA-Z_.]+$', ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 402, ], 'exception' => true, ], 'SessionDataSortBy' => [ 'type' => 'structure', 'required' => [ 'name', 'order', ], 'members' => [ 'name' => [ 'shape' => 'AnalyticsSessionSortByName', ], 'order' => [ 'shape' => 'AnalyticsSortOrder', ], ], ], 'SessionId' => [ 'type' => 'string', 'max' => 100, 'min' => 2, 'pattern' => '[0-9a-zA-Z._:-]+', ], 'SessionSpecification' => [ 'type' => 'structure', 'members' => [ 'botAliasId' => [ 'shape' => 'BotAliasId', ], 'botVersion' => [ 'shape' => 'NumericalBotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'channel' => [ 'shape' => 'AnalyticsChannel', ], 'sessionId' => [ 'shape' => 'AnalyticsSessionId', ], 'conversationStartTime' => [ 'shape' => 'Timestamp', ], 'conversationEndTime' => [ 'shape' => 'Timestamp', ], 'conversationDurationSeconds' => [ 'shape' => 'AnalyticsLongValue', ], 'conversationEndState' => [ 'shape' => 'ConversationEndState', ], 'mode' => [ 'shape' => 'AnalyticsModality', ], 'numberOfTurns' => [ 'shape' => 'AnalyticsLongValue', ], 'invokedIntentSamples' => [ 'shape' => 'InvokedIntentSamples', ], 'originatingRequestId' => [ 'shape' => 'AnalyticsOriginatingRequestId', ], ], ], 'SessionSpecifications' => [ 'type' => 'list', 'member' => [ 'shape' => 'SessionSpecification', ], ], 'SessionTTL' => [ 'type' => 'integer', 'max' => 86400, 'min' => 60, ], 'SkipResourceInUseCheck' => [ 'type' => 'boolean', ], 'SlotCaptureSetting' => [ 'type' => 'structure', 'members' => [ 'captureResponse' => [ 'shape' => 'ResponseSpecification', ], 'captureNextStep' => [ 'shape' => 'DialogState', ], 'captureConditional' => [ 'shape' => 'ConditionalSpecification', ], 'failureResponse' => [ 'shape' => 'ResponseSpecification', ], 'failureNextStep' => [ 'shape' => 'DialogState', ], 'failureConditional' => [ 'shape' => 'ConditionalSpecification', ], 'codeHook' => [ 'shape' => 'DialogCodeHookInvocationSetting', ], 'elicitationCodeHook' => [ 'shape' => 'ElicitationCodeHookInvocationSetting', ], ], ], 'SlotConstraint' => [ 'type' => 'string', 'enum' => [ 'Required', 'Optional', ], ], 'SlotDefaultValue' => [ 'type' => 'structure', 'required' => [ 'defaultValue', ], 'members' => [ 'defaultValue' => [ 'shape' => 'SlotDefaultValueString', ], ], ], 'SlotDefaultValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SlotDefaultValue', ], 'max' => 10, 'min' => 0, ], 'SlotDefaultValueSpecification' => [ 'type' => 'structure', 'required' => [ 'defaultValueList', ], 'members' => [ 'defaultValueList' => [ 'shape' => 'SlotDefaultValueList', ], ], ], 'SlotDefaultValueString' => [ 'type' => 'string', 'max' => 202, 'min' => 1, ], 'SlotFilter' => [ 'type' => 'structure', 'required' => [ 'name', 'values', 'operator', ], 'members' => [ 'name' => [ 'shape' => 'SlotFilterName', ], 'values' => [ 'shape' => 'FilterValues', ], 'operator' => [ 'shape' => 'SlotFilterOperator', ], ], ], 'SlotFilterName' => [ 'type' => 'string', 'enum' => [ 'SlotName', ], ], 'SlotFilterOperator' => [ 'type' => 'string', 'enum' => [ 'CO', 'EQ', ], ], 'SlotFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'SlotFilter', ], 'max' => 1, 'min' => 1, ], 'SlotHintsIntentMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'Name', ], 'value' => [ 'shape' => 'SlotHintsSlotMap', ], ], 'SlotHintsSlotMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'Name', ], 'value' => [ 'shape' => 'RuntimeHintDetails', ], ], 'SlotPrioritiesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SlotPriority', ], ], 'SlotPriority' => [ 'type' => 'structure', 'required' => [ 'priority', 'slotId', ], 'members' => [ 'priority' => [ 'shape' => 'PriorityValue', ], 'slotId' => [ 'shape' => 'Id', ], ], ], 'SlotResolutionImprovementSpecification' => [ 'type' => 'structure', 'required' => [ 'enabled', ], 'members' => [ 'enabled' => [ 'shape' => 'Enabled', ], 'bedrockModelSpecification' => [ 'shape' => 'BedrockModelSpecification', ], ], ], 'SlotResolutionSetting' => [ 'type' => 'structure', 'required' => [ 'slotResolutionStrategy', ], 'members' => [ 'slotResolutionStrategy' => [ 'shape' => 'SlotResolutionStrategy', ], ], ], 'SlotResolutionStrategy' => [ 'type' => 'string', 'enum' => [ 'EnhancedFallback', 'Default', ], ], 'SlotResolutionTestResultItem' => [ 'type' => 'structure', 'required' => [ 'slotName', 'resultCounts', ], 'members' => [ 'slotName' => [ 'shape' => 'TestResultSlotName', ], 'resultCounts' => [ 'shape' => 'SlotResolutionTestResultItemCounts', ], ], ], 'SlotResolutionTestResultItemCounts' => [ 'type' => 'structure', 'required' => [ 'totalResultCount', 'slotMatchResultCounts', ], 'members' => [ 'totalResultCount' => [ 'shape' => 'Count', ], 'speechTranscriptionResultCounts' => [ 'shape' => 'TestResultMatchStatusCountMap', ], 'slotMatchResultCounts' => [ 'shape' => 'TestResultMatchStatusCountMap', ], ], ], 'SlotResolutionTestResultItems' => [ 'type' => 'list', 'member' => [ 'shape' => 'SlotResolutionTestResultItem', ], ], 'SlotShape' => [ 'type' => 'string', 'enum' => [ 'Scalar', 'List', ], ], 'SlotSortAttribute' => [ 'type' => 'string', 'enum' => [ 'SlotName', 'LastUpdatedDateTime', ], ], 'SlotSortBy' => [ 'type' => 'structure', 'required' => [ 'attribute', 'order', ], 'members' => [ 'attribute' => [ 'shape' => 'SlotSortAttribute', ], 'order' => [ 'shape' => 'SortOrder', ], ], ], 'SlotSummary' => [ 'type' => 'structure', 'members' => [ 'slotId' => [ 'shape' => 'Id', ], 'slotName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'slotConstraint' => [ 'shape' => 'SlotConstraint', ], 'slotTypeId' => [ 'shape' => 'BuiltInOrCustomSlotTypeId', ], 'valueElicitationPromptSpecification' => [ 'shape' => 'PromptSpecification', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'SlotSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SlotSummary', ], ], 'SlotTypeCategory' => [ 'type' => 'string', 'enum' => [ 'Custom', 'Extended', 'ExternalGrammar', 'Composite', ], ], 'SlotTypeFilter' => [ 'type' => 'structure', 'required' => [ 'name', 'values', 'operator', ], 'members' => [ 'name' => [ 'shape' => 'SlotTypeFilterName', ], 'values' => [ 'shape' => 'FilterValues', ], 'operator' => [ 'shape' => 'SlotTypeFilterOperator', ], ], ], 'SlotTypeFilterName' => [ 'type' => 'string', 'enum' => [ 'SlotTypeName', 'ExternalSourceType', ], ], 'SlotTypeFilterOperator' => [ 'type' => 'string', 'enum' => [ 'CO', 'EQ', ], ], 'SlotTypeFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'SlotTypeFilter', ], 'max' => 1, 'min' => 1, ], 'SlotTypeSignature' => [ 'type' => 'string', ], 'SlotTypeSortAttribute' => [ 'type' => 'string', 'enum' => [ 'SlotTypeName', 'LastUpdatedDateTime', ], ], 'SlotTypeSortBy' => [ 'type' => 'structure', 'required' => [ 'attribute', 'order', ], 'members' => [ 'attribute' => [ 'shape' => 'SlotTypeSortAttribute', ], 'order' => [ 'shape' => 'SortOrder', ], ], ], 'SlotTypeStatistics' => [ 'type' => 'structure', 'members' => [ 'discoveredSlotTypeCount' => [ 'shape' => 'Count', ], ], ], 'SlotTypeSummary' => [ 'type' => 'structure', 'members' => [ 'slotTypeId' => [ 'shape' => 'Id', ], 'slotTypeName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'parentSlotTypeSignature' => [ 'shape' => 'SlotTypeSignature', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], 'slotTypeCategory' => [ 'shape' => 'SlotTypeCategory', ], ], ], 'SlotTypeSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SlotTypeSummary', ], ], 'SlotTypeValue' => [ 'type' => 'structure', 'members' => [ 'sampleValue' => [ 'shape' => 'SampleValue', ], 'synonyms' => [ 'shape' => 'SynonymList', ], ], ], 'SlotTypeValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'SlotTypeValue', ], 'max' => 10000, 'min' => 1, ], 'SlotValue' => [ 'type' => 'structure', 'members' => [ 'interpretedValue' => [ 'shape' => 'NonEmptyString', ], ], ], 'SlotValueElicitationSetting' => [ 'type' => 'structure', 'required' => [ 'slotConstraint', ], 'members' => [ 'defaultValueSpecification' => [ 'shape' => 'SlotDefaultValueSpecification', ], 'slotConstraint' => [ 'shape' => 'SlotConstraint', ], 'promptSpecification' => [ 'shape' => 'PromptSpecification', ], 'sampleUtterances' => [ 'shape' => 'SampleUtterancesList', ], 'waitAndContinueSpecification' => [ 'shape' => 'WaitAndContinueSpecification', ], 'slotCaptureSetting' => [ 'shape' => 'SlotCaptureSetting', ], 'slotResolutionSetting' => [ 'shape' => 'SlotResolutionSetting', ], ], ], 'SlotValueOverride' => [ 'type' => 'structure', 'members' => [ 'shape' => [ 'shape' => 'SlotShape', ], 'value' => [ 'shape' => 'SlotValue', ], 'values' => [ 'shape' => 'SlotValues', ], ], ], 'SlotValueOverrideMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'Name', ], 'value' => [ 'shape' => 'SlotValueOverride', ], ], 'SlotValueRegexFilter' => [ 'type' => 'structure', 'required' => [ 'pattern', ], 'members' => [ 'pattern' => [ 'shape' => 'RegexPattern', ], ], ], 'SlotValueResolutionStrategy' => [ 'type' => 'string', 'enum' => [ 'OriginalValue', 'TopResolution', 'Concatenation', ], ], 'SlotValueSelectionSetting' => [ 'type' => 'structure', 'required' => [ 'resolutionStrategy', ], 'members' => [ 'resolutionStrategy' => [ 'shape' => 'SlotValueResolutionStrategy', ], 'regexFilter' => [ 'shape' => 'SlotValueRegexFilter', ], 'advancedRecognitionSetting' => [ 'shape' => 'AdvancedRecognitionSetting', ], ], ], 'SlotValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'SlotValueOverride', ], ], 'SortOrder' => [ 'type' => 'string', 'enum' => [ 'Ascending', 'Descending', ], ], 'Specifications' => [ 'type' => 'structure', 'required' => [ 'slotTypeId', 'valueElicitationSetting', ], 'members' => [ 'slotTypeId' => [ 'shape' => 'BuiltInOrCustomSlotTypeId', ], 'valueElicitationSetting' => [ 'shape' => 'SubSlotValueElicitationSetting', ], ], ], 'StartBotRecommendationRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', 'localeId', 'transcriptSourceSetting', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], 'transcriptSourceSetting' => [ 'shape' => 'TranscriptSourceSetting', ], 'encryptionSetting' => [ 'shape' => 'EncryptionSetting', ], ], ], 'StartBotRecommendationResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'botRecommendationStatus' => [ 'shape' => 'BotRecommendationStatus', ], 'botRecommendationId' => [ 'shape' => 'Id', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'transcriptSourceSetting' => [ 'shape' => 'TranscriptSourceSetting', ], 'encryptionSetting' => [ 'shape' => 'EncryptionSetting', ], ], ], 'StartBotResourceGenerationRequest' => [ 'type' => 'structure', 'required' => [ 'generationInputPrompt', 'botId', 'botVersion', 'localeId', ], 'members' => [ 'generationInputPrompt' => [ 'shape' => 'GenerationInput', ], 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'BotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], ], ], 'StartBotResourceGenerationResponse' => [ 'type' => 'structure', 'members' => [ 'generationInputPrompt' => [ 'shape' => 'GenerationInput', ], 'generationId' => [ 'shape' => 'Id', ], 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'BotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'generationStatus' => [ 'shape' => 'GenerationStatus', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'StartImportRequest' => [ 'type' => 'structure', 'required' => [ 'importId', 'resourceSpecification', 'mergeStrategy', ], 'members' => [ 'importId' => [ 'shape' => 'Id', ], 'resourceSpecification' => [ 'shape' => 'ImportResourceSpecification', ], 'mergeStrategy' => [ 'shape' => 'MergeStrategy', ], 'filePassword' => [ 'shape' => 'ImportExportFilePassword', ], ], ], 'StartImportResponse' => [ 'type' => 'structure', 'members' => [ 'importId' => [ 'shape' => 'Id', ], 'resourceSpecification' => [ 'shape' => 'ImportResourceSpecification', ], 'mergeStrategy' => [ 'shape' => 'MergeStrategy', ], 'importStatus' => [ 'shape' => 'ImportStatus', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'StartTestExecutionRequest' => [ 'type' => 'structure', 'required' => [ 'testSetId', 'target', 'apiMode', ], 'members' => [ 'testSetId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'testSetId', ], 'target' => [ 'shape' => 'TestExecutionTarget', ], 'apiMode' => [ 'shape' => 'TestExecutionApiMode', ], 'testExecutionModality' => [ 'shape' => 'TestExecutionModality', ], ], ], 'StartTestExecutionResponse' => [ 'type' => 'structure', 'members' => [ 'testExecutionId' => [ 'shape' => 'Id', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'testSetId' => [ 'shape' => 'Id', ], 'target' => [ 'shape' => 'TestExecutionTarget', ], 'apiMode' => [ 'shape' => 'TestExecutionApiMode', ], 'testExecutionModality' => [ 'shape' => 'TestExecutionModality', ], ], ], 'StartTestSetGenerationRequest' => [ 'type' => 'structure', 'required' => [ 'testSetName', 'storageLocation', 'generationDataSource', 'roleArn', ], 'members' => [ 'testSetName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'storageLocation' => [ 'shape' => 'TestSetStorageLocation', ], 'generationDataSource' => [ 'shape' => 'TestSetGenerationDataSource', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'testSetTags' => [ 'shape' => 'TagMap', ], ], ], 'StartTestSetGenerationResponse' => [ 'type' => 'structure', 'members' => [ 'testSetGenerationId' => [ 'shape' => 'Id', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'testSetGenerationStatus' => [ 'shape' => 'TestSetGenerationStatus', ], 'testSetName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'storageLocation' => [ 'shape' => 'TestSetStorageLocation', ], 'generationDataSource' => [ 'shape' => 'TestSetGenerationDataSource', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'testSetTags' => [ 'shape' => 'TagMap', ], ], ], 'StillWaitingResponseFrequency' => [ 'type' => 'integer', 'max' => 300, 'min' => 1, ], 'StillWaitingResponseSpecification' => [ 'type' => 'structure', 'required' => [ 'messageGroups', 'frequencyInSeconds', 'timeoutInSeconds', ], 'members' => [ 'messageGroups' => [ 'shape' => 'MessageGroupsList', ], 'frequencyInSeconds' => [ 'shape' => 'StillWaitingResponseFrequency', ], 'timeoutInSeconds' => [ 'shape' => 'StillWaitingResponseTimeout', ], 'allowInterrupt' => [ 'shape' => 'BoxedBoolean', ], ], ], 'StillWaitingResponseTimeout' => [ 'type' => 'integer', 'max' => 900, 'min' => 1, ], 'StopBotRecommendationRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', 'localeId', 'botRecommendationId', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], 'botRecommendationId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botRecommendationId', ], ], ], 'StopBotRecommendationResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'botRecommendationStatus' => [ 'shape' => 'BotRecommendationStatus', ], 'botRecommendationId' => [ 'shape' => 'Id', ], ], ], 'String' => [ 'type' => 'string', ], 'StringMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'NonEmptyString', ], 'value' => [ 'shape' => 'String', ], ], 'SubSlotExpression' => [ 'type' => 'string', 'max' => 640, 'min' => 0, 'pattern' => '[0-9A-Za-z_\\-\\s\\(\\)]+', ], 'SubSlotSetting' => [ 'type' => 'structure', 'members' => [ 'expression' => [ 'shape' => 'SubSlotExpression', ], 'slotSpecifications' => [ 'shape' => 'SubSlotSpecificationMap', ], ], ], 'SubSlotSpecificationMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'Name', ], 'value' => [ 'shape' => 'Specifications', ], 'max' => 6, 'min' => 0, ], 'SubSlotTypeComposition' => [ 'type' => 'structure', 'required' => [ 'name', 'slotTypeId', ], 'members' => [ 'name' => [ 'shape' => 'Name', ], 'slotTypeId' => [ 'shape' => 'BuiltInOrCustomSlotTypeId', ], ], ], 'SubSlotTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubSlotTypeComposition', ], 'max' => 6, 'min' => 0, ], 'SubSlotValueElicitationSetting' => [ 'type' => 'structure', 'required' => [ 'promptSpecification', ], 'members' => [ 'defaultValueSpecification' => [ 'shape' => 'SlotDefaultValueSpecification', ], 'promptSpecification' => [ 'shape' => 'PromptSpecification', ], 'sampleUtterances' => [ 'shape' => 'SampleUtterancesList', ], 'waitAndContinueSpecification' => [ 'shape' => 'WaitAndContinueSpecification', ], ], ], 'SynonymList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SampleValue', ], 'max' => 10000, 'min' => 1, ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 200, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceARN', 'tags', ], 'members' => [ 'resourceARN' => [ 'shape' => 'AmazonResourceName', 'location' => 'uri', 'locationName' => 'resourceARN', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'TestExecutionApiMode' => [ 'type' => 'string', 'enum' => [ 'Streaming', 'NonStreaming', ], ], 'TestExecutionModality' => [ 'type' => 'string', 'enum' => [ 'Text', 'Audio', ], ], 'TestExecutionResultFilterBy' => [ 'type' => 'structure', 'required' => [ 'resultTypeFilter', ], 'members' => [ 'resultTypeFilter' => [ 'shape' => 'TestResultTypeFilter', ], 'conversationLevelTestResultsFilterBy' => [ 'shape' => 'ConversationLevelTestResultsFilterBy', ], ], ], 'TestExecutionResultItems' => [ 'type' => 'structure', 'members' => [ 'overallTestResults' => [ 'shape' => 'OverallTestResults', ], 'conversationLevelTestResults' => [ 'shape' => 'ConversationLevelTestResults', ], 'intentClassificationTestResults' => [ 'shape' => 'IntentClassificationTestResults', ], 'intentLevelSlotResolutionTestResults' => [ 'shape' => 'IntentLevelSlotResolutionTestResults', ], 'utteranceLevelTestResults' => [ 'shape' => 'UtteranceLevelTestResults', ], ], ], 'TestExecutionSortAttribute' => [ 'type' => 'string', 'enum' => [ 'TestSetName', 'CreationDateTime', ], ], 'TestExecutionSortBy' => [ 'type' => 'structure', 'required' => [ 'attribute', 'order', ], 'members' => [ 'attribute' => [ 'shape' => 'TestExecutionSortAttribute', ], 'order' => [ 'shape' => 'SortOrder', ], ], ], 'TestExecutionStatus' => [ 'type' => 'string', 'enum' => [ 'Pending', 'Waiting', 'InProgress', 'Completed', 'Failed', 'Stopping', 'Stopped', ], ], 'TestExecutionSummary' => [ 'type' => 'structure', 'members' => [ 'testExecutionId' => [ 'shape' => 'Id', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], 'testExecutionStatus' => [ 'shape' => 'TestExecutionStatus', ], 'testSetId' => [ 'shape' => 'Id', ], 'testSetName' => [ 'shape' => 'Name', ], 'target' => [ 'shape' => 'TestExecutionTarget', ], 'apiMode' => [ 'shape' => 'TestExecutionApiMode', ], 'testExecutionModality' => [ 'shape' => 'TestExecutionModality', ], ], ], 'TestExecutionSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TestExecutionSummary', ], ], 'TestExecutionTarget' => [ 'type' => 'structure', 'members' => [ 'botAliasTarget' => [ 'shape' => 'BotAliasTestExecutionTarget', ], ], ], 'TestResultMatchStatus' => [ 'type' => 'string', 'enum' => [ 'Matched', 'Mismatched', 'ExecutionError', ], ], 'TestResultMatchStatusCountMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TestResultMatchStatus', ], 'value' => [ 'shape' => 'Count', ], ], 'TestResultSlotName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^([0-9a-zA-Z][_.-]?)+$', ], 'TestResultTypeFilter' => [ 'type' => 'string', 'enum' => [ 'OverallTestResults', 'ConversationLevelTestResults', 'IntentClassificationTestResults', 'SlotResolutionTestResults', 'UtteranceLevelResults', ], ], 'TestSetAgentPrompt' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'TestSetConversationId' => [ 'type' => 'string', 'max' => 50, 'min' => 1, 'pattern' => '^([0-9a-zA-Z][_-]?)+$', ], 'TestSetDiscrepancyErrors' => [ 'type' => 'structure', 'required' => [ 'intentDiscrepancies', 'slotDiscrepancies', ], 'members' => [ 'intentDiscrepancies' => [ 'shape' => 'TestSetIntentDiscrepancyList', ], 'slotDiscrepancies' => [ 'shape' => 'TestSetSlotDiscrepancyList', ], ], ], 'TestSetDiscrepancyReportBotAliasTarget' => [ 'type' => 'structure', 'required' => [ 'botId', 'botAliasId', 'localeId', ], 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botAliasId' => [ 'shape' => 'BotAliasId', ], 'localeId' => [ 'shape' => 'LocaleId', ], ], ], 'TestSetDiscrepancyReportResourceTarget' => [ 'type' => 'structure', 'members' => [ 'botAliasTarget' => [ 'shape' => 'TestSetDiscrepancyReportBotAliasTarget', ], ], ], 'TestSetDiscrepancyReportStatus' => [ 'type' => 'string', 'enum' => [ 'InProgress', 'Completed', 'Failed', ], ], 'TestSetExportSpecification' => [ 'type' => 'structure', 'required' => [ 'testSetId', ], 'members' => [ 'testSetId' => [ 'shape' => 'Id', ], ], ], 'TestSetGenerationDataSource' => [ 'type' => 'structure', 'members' => [ 'conversationLogsDataSource' => [ 'shape' => 'ConversationLogsDataSource', ], ], ], 'TestSetGenerationStatus' => [ 'type' => 'string', 'enum' => [ 'Generating', 'Ready', 'Failed', 'Pending', ], ], 'TestSetImportInputLocation' => [ 'type' => 'structure', 'required' => [ 's3BucketName', 's3Path', ], 'members' => [ 's3BucketName' => [ 'shape' => 'S3BucketName', ], 's3Path' => [ 'shape' => 'S3ObjectPath', ], ], ], 'TestSetImportResourceSpecification' => [ 'type' => 'structure', 'required' => [ 'testSetName', 'roleArn', 'storageLocation', 'importInputLocation', 'modality', ], 'members' => [ 'testSetName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'storageLocation' => [ 'shape' => 'TestSetStorageLocation', ], 'importInputLocation' => [ 'shape' => 'TestSetImportInputLocation', ], 'modality' => [ 'shape' => 'TestSetModality', ], 'testSetTags' => [ 'shape' => 'TagMap', ], ], ], 'TestSetIntentDiscrepancyItem' => [ 'type' => 'structure', 'required' => [ 'intentName', 'errorMessage', ], 'members' => [ 'intentName' => [ 'shape' => 'Name', ], 'errorMessage' => [ 'shape' => 'String', ], ], ], 'TestSetIntentDiscrepancyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TestSetIntentDiscrepancyItem', ], ], 'TestSetModality' => [ 'type' => 'string', 'enum' => [ 'Text', 'Audio', ], ], 'TestSetSlotDiscrepancyItem' => [ 'type' => 'structure', 'required' => [ 'intentName', 'slotName', 'errorMessage', ], 'members' => [ 'intentName' => [ 'shape' => 'Name', ], 'slotName' => [ 'shape' => 'Name', ], 'errorMessage' => [ 'shape' => 'String', ], ], ], 'TestSetSlotDiscrepancyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TestSetSlotDiscrepancyItem', ], ], 'TestSetSortAttribute' => [ 'type' => 'string', 'enum' => [ 'TestSetName', 'LastUpdatedDateTime', ], ], 'TestSetSortBy' => [ 'type' => 'structure', 'required' => [ 'attribute', 'order', ], 'members' => [ 'attribute' => [ 'shape' => 'TestSetSortAttribute', ], 'order' => [ 'shape' => 'SortOrder', ], ], ], 'TestSetStatus' => [ 'type' => 'string', 'enum' => [ 'Importing', 'PendingAnnotation', 'Deleting', 'ValidationError', 'Ready', ], ], 'TestSetStorageLocation' => [ 'type' => 'structure', 'required' => [ 's3BucketName', 's3Path', ], 'members' => [ 's3BucketName' => [ 'shape' => 'S3BucketName', ], 's3Path' => [ 'shape' => 'S3ObjectPath', ], 'kmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], ], ], 'TestSetSummary' => [ 'type' => 'structure', 'members' => [ 'testSetId' => [ 'shape' => 'Id', ], 'testSetName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'modality' => [ 'shape' => 'TestSetModality', ], 'status' => [ 'shape' => 'TestSetStatus', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'numTurns' => [ 'shape' => 'Count', ], 'storageLocation' => [ 'shape' => 'TestSetStorageLocation', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'TestSetSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TestSetSummary', ], ], 'TestSetTurnRecord' => [ 'type' => 'structure', 'required' => [ 'recordNumber', 'turnSpecification', ], 'members' => [ 'recordNumber' => [ 'shape' => 'RecordNumber', ], 'conversationId' => [ 'shape' => 'TestSetConversationId', ], 'turnNumber' => [ 'shape' => 'TurnNumber', ], 'turnSpecification' => [ 'shape' => 'TurnSpecification', ], ], ], 'TestSetTurnRecordList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TestSetTurnRecord', ], ], 'TestSetTurnResult' => [ 'type' => 'structure', 'members' => [ 'agent' => [ 'shape' => 'AgentTurnResult', ], 'user' => [ 'shape' => 'UserTurnResult', ], ], ], 'TestSetUtteranceText' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'TextInputSpecification' => [ 'type' => 'structure', 'required' => [ 'startTimeoutMs', ], 'members' => [ 'startTimeoutMs' => [ 'shape' => 'TimeInMilliSeconds', ], ], ], 'TextLogDestination' => [ 'type' => 'structure', 'required' => [ 'cloudWatch', ], 'members' => [ 'cloudWatch' => [ 'shape' => 'CloudWatchLogGroupLogDestination', ], ], ], 'TextLogSetting' => [ 'type' => 'structure', 'required' => [ 'enabled', 'destination', ], 'members' => [ 'enabled' => [ 'shape' => 'Boolean', ], 'destination' => [ 'shape' => 'TextLogDestination', ], 'selectiveLoggingEnabled' => [ 'shape' => 'BoxedBoolean', ], ], ], 'TextLogSettingsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TextLogSetting', ], 'max' => 1, 'min' => 1, ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'retryAfterSeconds' => [ 'shape' => 'RetryAfterSeconds', 'location' => 'header', 'locationName' => 'Retry-After', ], 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], 'TimeDimension' => [ 'type' => 'string', 'enum' => [ 'Hours', 'Days', 'Weeks', ], ], 'TimeInMilliSeconds' => [ 'type' => 'integer', 'min' => 1, ], 'TimeValue' => [ 'type' => 'integer', 'max' => 24, 'min' => 1, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'Transcript' => [ 'type' => 'string', 'max' => 6000000, 'min' => 1, 'pattern' => '.*', ], 'TranscriptFilter' => [ 'type' => 'structure', 'members' => [ 'lexTranscriptFilter' => [ 'shape' => 'LexTranscriptFilter', ], ], ], 'TranscriptFormat' => [ 'type' => 'string', 'enum' => [ 'Lex', ], ], 'TranscriptSourceSetting' => [ 'type' => 'structure', 'members' => [ 's3BucketTranscriptSource' => [ 'shape' => 'S3BucketTranscriptSource', ], ], ], 'TurnNumber' => [ 'type' => 'integer', 'max' => 30, 'min' => 0, ], 'TurnSpecification' => [ 'type' => 'structure', 'members' => [ 'agentTurn' => [ 'shape' => 'AgentTurnSpecification', ], 'userTurn' => [ 'shape' => 'UserTurnSpecification', ], ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceARN', 'tagKeys', ], 'members' => [ 'resourceARN' => [ 'shape' => 'AmazonResourceName', 'location' => 'uri', 'locationName' => 'resourceARN', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateBotAliasRequest' => [ 'type' => 'structure', 'required' => [ 'botAliasId', 'botAliasName', 'botId', ], 'members' => [ 'botAliasId' => [ 'shape' => 'BotAliasId', 'location' => 'uri', 'locationName' => 'botAliasId', ], 'botAliasName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'botVersion' => [ 'shape' => 'BotVersion', ], 'botAliasLocaleSettings' => [ 'shape' => 'BotAliasLocaleSettingsMap', ], 'conversationLogSettings' => [ 'shape' => 'ConversationLogSettings', ], 'sentimentAnalysisSettings' => [ 'shape' => 'SentimentAnalysisSettings', ], 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], ], ], 'UpdateBotAliasResponse' => [ 'type' => 'structure', 'members' => [ 'botAliasId' => [ 'shape' => 'BotAliasId', ], 'botAliasName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'botVersion' => [ 'shape' => 'BotVersion', ], 'botAliasLocaleSettings' => [ 'shape' => 'BotAliasLocaleSettingsMap', ], 'conversationLogSettings' => [ 'shape' => 'ConversationLogSettings', ], 'sentimentAnalysisSettings' => [ 'shape' => 'SentimentAnalysisSettings', ], 'botAliasStatus' => [ 'shape' => 'BotAliasStatus', ], 'botId' => [ 'shape' => 'Id', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'UpdateBotLocaleRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', 'localeId', 'nluIntentConfidenceThreshold', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], 'description' => [ 'shape' => 'Description', ], 'nluIntentConfidenceThreshold' => [ 'shape' => 'ConfidenceThreshold', ], 'voiceSettings' => [ 'shape' => 'VoiceSettings', ], 'generativeAISettings' => [ 'shape' => 'GenerativeAISettings', ], ], ], 'UpdateBotLocaleResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'localeName' => [ 'shape' => 'LocaleName', ], 'description' => [ 'shape' => 'Description', ], 'nluIntentConfidenceThreshold' => [ 'shape' => 'ConfidenceThreshold', ], 'voiceSettings' => [ 'shape' => 'VoiceSettings', ], 'botLocaleStatus' => [ 'shape' => 'BotLocaleStatus', ], 'failureReasons' => [ 'shape' => 'FailureReasons', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], 'recommendedActions' => [ 'shape' => 'RecommendedActions', ], 'generativeAISettings' => [ 'shape' => 'GenerativeAISettings', ], ], ], 'UpdateBotRecommendationRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', 'localeId', 'botRecommendationId', 'encryptionSetting', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], 'botRecommendationId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botRecommendationId', ], 'encryptionSetting' => [ 'shape' => 'EncryptionSetting', ], ], ], 'UpdateBotRecommendationResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'botRecommendationStatus' => [ 'shape' => 'BotRecommendationStatus', ], 'botRecommendationId' => [ 'shape' => 'Id', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], 'transcriptSourceSetting' => [ 'shape' => 'TranscriptSourceSetting', ], 'encryptionSetting' => [ 'shape' => 'EncryptionSetting', ], ], ], 'UpdateBotRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'botName', 'roleArn', 'dataPrivacy', 'idleSessionTTLInSeconds', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'dataPrivacy' => [ 'shape' => 'DataPrivacy', ], 'idleSessionTTLInSeconds' => [ 'shape' => 'SessionTTL', ], 'botType' => [ 'shape' => 'BotType', ], 'botMembers' => [ 'shape' => 'BotMembers', ], 'errorLogSettings' => [ 'shape' => 'ErrorLogSettings', ], ], ], 'UpdateBotResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'dataPrivacy' => [ 'shape' => 'DataPrivacy', ], 'idleSessionTTLInSeconds' => [ 'shape' => 'SessionTTL', ], 'botStatus' => [ 'shape' => 'BotStatus', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], 'botType' => [ 'shape' => 'BotType', ], 'botMembers' => [ 'shape' => 'BotMembers', ], 'errorLogSettings' => [ 'shape' => 'ErrorLogSettings', ], ], ], 'UpdateCustomVocabularyItemsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CustomVocabularyItem', ], 'max' => 10, 'min' => 1, ], 'UpdateExportRequest' => [ 'type' => 'structure', 'required' => [ 'exportId', ], 'members' => [ 'exportId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'exportId', ], 'filePassword' => [ 'shape' => 'ImportExportFilePassword', ], ], ], 'UpdateExportResponse' => [ 'type' => 'structure', 'members' => [ 'exportId' => [ 'shape' => 'Id', ], 'resourceSpecification' => [ 'shape' => 'ExportResourceSpecification', ], 'fileFormat' => [ 'shape' => 'ImportExportFileFormat', ], 'exportStatus' => [ 'shape' => 'ExportStatus', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'UpdateIntentRequest' => [ 'type' => 'structure', 'required' => [ 'intentId', 'intentName', 'botId', 'botVersion', 'localeId', ], 'members' => [ 'intentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'intentId', ], 'intentName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'parentIntentSignature' => [ 'shape' => 'IntentSignature', ], 'sampleUtterances' => [ 'shape' => 'SampleUtterancesList', ], 'dialogCodeHook' => [ 'shape' => 'DialogCodeHookSettings', ], 'fulfillmentCodeHook' => [ 'shape' => 'FulfillmentCodeHookSettings', ], 'slotPriorities' => [ 'shape' => 'SlotPrioritiesList', ], 'intentConfirmationSetting' => [ 'shape' => 'IntentConfirmationSetting', ], 'intentClosingSetting' => [ 'shape' => 'IntentClosingSetting', ], 'inputContexts' => [ 'shape' => 'InputContextsList', ], 'outputContexts' => [ 'shape' => 'OutputContextsList', ], 'kendraConfiguration' => [ 'shape' => 'KendraConfiguration', ], 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], 'initialResponseSetting' => [ 'shape' => 'InitialResponseSetting', ], 'qnAIntentConfiguration' => [ 'shape' => 'QnAIntentConfiguration', ], 'qInConnectIntentConfiguration' => [ 'shape' => 'QInConnectIntentConfiguration', ], ], ], 'UpdateIntentResponse' => [ 'type' => 'structure', 'members' => [ 'intentId' => [ 'shape' => 'Id', ], 'intentName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'parentIntentSignature' => [ 'shape' => 'IntentSignature', ], 'sampleUtterances' => [ 'shape' => 'SampleUtterancesList', ], 'dialogCodeHook' => [ 'shape' => 'DialogCodeHookSettings', ], 'fulfillmentCodeHook' => [ 'shape' => 'FulfillmentCodeHookSettings', ], 'slotPriorities' => [ 'shape' => 'SlotPrioritiesList', ], 'intentConfirmationSetting' => [ 'shape' => 'IntentConfirmationSetting', ], 'intentClosingSetting' => [ 'shape' => 'IntentClosingSetting', ], 'inputContexts' => [ 'shape' => 'InputContextsList', ], 'outputContexts' => [ 'shape' => 'OutputContextsList', ], 'kendraConfiguration' => [ 'shape' => 'KendraConfiguration', ], 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], 'initialResponseSetting' => [ 'shape' => 'InitialResponseSetting', ], 'qnAIntentConfiguration' => [ 'shape' => 'QnAIntentConfiguration', ], 'qInConnectIntentConfiguration' => [ 'shape' => 'QInConnectIntentConfiguration', ], ], ], 'UpdateResourcePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'policy', ], 'members' => [ 'resourceArn' => [ 'shape' => 'AmazonResourceName', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'policy' => [ 'shape' => 'Policy', ], 'expectedRevisionId' => [ 'shape' => 'RevisionId', 'location' => 'querystring', 'locationName' => 'expectedRevisionId', ], ], ], 'UpdateResourcePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'resourceArn' => [ 'shape' => 'AmazonResourceName', ], 'revisionId' => [ 'shape' => 'RevisionId', ], ], ], 'UpdateSlotRequest' => [ 'type' => 'structure', 'required' => [ 'slotId', 'slotName', 'valueElicitationSetting', 'botId', 'botVersion', 'localeId', 'intentId', ], 'members' => [ 'slotId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'slotId', ], 'slotName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'slotTypeId' => [ 'shape' => 'BuiltInOrCustomSlotTypeId', ], 'valueElicitationSetting' => [ 'shape' => 'SlotValueElicitationSetting', ], 'obfuscationSetting' => [ 'shape' => 'ObfuscationSetting', ], 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], 'intentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'intentId', ], 'multipleValuesSetting' => [ 'shape' => 'MultipleValuesSetting', ], 'subSlotSetting' => [ 'shape' => 'SubSlotSetting', ], ], ], 'UpdateSlotResponse' => [ 'type' => 'structure', 'members' => [ 'slotId' => [ 'shape' => 'Id', ], 'slotName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'slotTypeId' => [ 'shape' => 'BuiltInOrCustomSlotTypeId', ], 'valueElicitationSetting' => [ 'shape' => 'SlotValueElicitationSetting', ], 'obfuscationSetting' => [ 'shape' => 'ObfuscationSetting', ], 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'intentId' => [ 'shape' => 'Id', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], 'multipleValuesSetting' => [ 'shape' => 'MultipleValuesSetting', ], 'subSlotSetting' => [ 'shape' => 'SubSlotSetting', ], ], ], 'UpdateSlotTypeRequest' => [ 'type' => 'structure', 'required' => [ 'slotTypeId', 'slotTypeName', 'botId', 'botVersion', 'localeId', ], 'members' => [ 'slotTypeId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'slotTypeId', ], 'slotTypeName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'slotTypeValues' => [ 'shape' => 'SlotTypeValues', ], 'valueSelectionSetting' => [ 'shape' => 'SlotValueSelectionSetting', ], 'parentSlotTypeSignature' => [ 'shape' => 'SlotTypeSignature', ], 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], 'externalSourceSetting' => [ 'shape' => 'ExternalSourceSetting', ], 'compositeSlotTypeSetting' => [ 'shape' => 'CompositeSlotTypeSetting', ], ], ], 'UpdateSlotTypeResponse' => [ 'type' => 'structure', 'members' => [ 'slotTypeId' => [ 'shape' => 'Id', ], 'slotTypeName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'slotTypeValues' => [ 'shape' => 'SlotTypeValues', ], 'valueSelectionSetting' => [ 'shape' => 'SlotValueSelectionSetting', ], 'parentSlotTypeSignature' => [ 'shape' => 'SlotTypeSignature', ], 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], 'externalSourceSetting' => [ 'shape' => 'ExternalSourceSetting', ], 'compositeSlotTypeSetting' => [ 'shape' => 'CompositeSlotTypeSetting', ], ], ], 'UpdateTestSetRequest' => [ 'type' => 'structure', 'required' => [ 'testSetId', 'testSetName', ], 'members' => [ 'testSetId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'testSetId', ], 'testSetName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], ], ], 'UpdateTestSetResponse' => [ 'type' => 'structure', 'members' => [ 'testSetId' => [ 'shape' => 'Id', ], 'testSetName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'modality' => [ 'shape' => 'TestSetModality', ], 'status' => [ 'shape' => 'TestSetStatus', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'numTurns' => [ 'shape' => 'Count', ], 'storageLocation' => [ 'shape' => 'TestSetStorageLocation', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'UserTurnInputSpecification' => [ 'type' => 'structure', 'required' => [ 'utteranceInput', ], 'members' => [ 'utteranceInput' => [ 'shape' => 'UtteranceInputSpecification', ], 'requestAttributes' => [ 'shape' => 'StringMap', ], 'sessionState' => [ 'shape' => 'InputSessionStateSpecification', ], ], ], 'UserTurnIntentOutput' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'Name', ], 'slots' => [ 'shape' => 'UserTurnSlotOutputMap', ], ], ], 'UserTurnOutputSpecification' => [ 'type' => 'structure', 'required' => [ 'intent', ], 'members' => [ 'intent' => [ 'shape' => 'UserTurnIntentOutput', ], 'activeContexts' => [ 'shape' => 'ActiveContextList', ], 'transcript' => [ 'shape' => 'TestSetUtteranceText', ], ], ], 'UserTurnResult' => [ 'type' => 'structure', 'required' => [ 'input', 'expectedOutput', ], 'members' => [ 'input' => [ 'shape' => 'UserTurnInputSpecification', ], 'expectedOutput' => [ 'shape' => 'UserTurnOutputSpecification', ], 'actualOutput' => [ 'shape' => 'UserTurnOutputSpecification', ], 'errorDetails' => [ 'shape' => 'ExecutionErrorDetails', ], 'endToEndResult' => [ 'shape' => 'TestResultMatchStatus', ], 'intentMatchResult' => [ 'shape' => 'TestResultMatchStatus', ], 'slotMatchResult' => [ 'shape' => 'TestResultMatchStatus', ], 'speechTranscriptionResult' => [ 'shape' => 'TestResultMatchStatus', ], 'conversationLevelResult' => [ 'shape' => 'ConversationLevelResultDetail', ], ], ], 'UserTurnSlotOutput' => [ 'type' => 'structure', 'members' => [ 'value' => [ 'shape' => 'NonEmptyString', ], 'values' => [ 'shape' => 'UserTurnSlotOutputList', ], 'subSlots' => [ 'shape' => 'UserTurnSlotOutputMap', ], ], ], 'UserTurnSlotOutputList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserTurnSlotOutput', ], ], 'UserTurnSlotOutputMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'Name', ], 'value' => [ 'shape' => 'UserTurnSlotOutput', ], ], 'UserTurnSpecification' => [ 'type' => 'structure', 'required' => [ 'input', 'expected', ], 'members' => [ 'input' => [ 'shape' => 'UserTurnInputSpecification', ], 'expected' => [ 'shape' => 'UserTurnOutputSpecification', ], ], ], 'Utterance' => [ 'type' => 'string', ], 'UtteranceAggregationDuration' => [ 'type' => 'structure', 'required' => [ 'relativeAggregationDuration', ], 'members' => [ 'relativeAggregationDuration' => [ 'shape' => 'RelativeAggregationDuration', ], ], ], 'UtteranceAudioInputSpecification' => [ 'type' => 'structure', 'required' => [ 'audioFileS3Location', ], 'members' => [ 'audioFileS3Location' => [ 'shape' => 'AudioFileS3Location', ], ], ], 'UtteranceBotResponse' => [ 'type' => 'structure', 'members' => [ 'content' => [ 'shape' => 'String', ], 'contentType' => [ 'shape' => 'UtteranceContentType', ], 'imageResponseCard' => [ 'shape' => 'ImageResponseCard', ], ], ], 'UtteranceBotResponses' => [ 'type' => 'list', 'member' => [ 'shape' => 'UtteranceBotResponse', ], ], 'UtteranceContentType' => [ 'type' => 'string', 'enum' => [ 'PlainText', 'CustomPayload', 'SSML', 'ImageResponseCard', ], ], 'UtteranceDataSortBy' => [ 'type' => 'structure', 'required' => [ 'name', 'order', ], 'members' => [ 'name' => [ 'shape' => 'AnalyticsUtteranceSortByName', ], 'order' => [ 'shape' => 'AnalyticsSortOrder', ], ], ], 'UtteranceInputSpecification' => [ 'type' => 'structure', 'members' => [ 'textInput' => [ 'shape' => 'TestSetUtteranceText', ], 'audioInput' => [ 'shape' => 'UtteranceAudioInputSpecification', ], ], ], 'UtteranceLevelTestResultItem' => [ 'type' => 'structure', 'required' => [ 'recordNumber', 'turnResult', ], 'members' => [ 'recordNumber' => [ 'shape' => 'RecordNumber', ], 'conversationId' => [ 'shape' => 'TestSetConversationId', ], 'turnResult' => [ 'shape' => 'TestSetTurnResult', ], ], ], 'UtteranceLevelTestResultItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UtteranceLevelTestResultItem', ], ], 'UtteranceLevelTestResults' => [ 'type' => 'structure', 'required' => [ 'items', ], 'members' => [ 'items' => [ 'shape' => 'UtteranceLevelTestResultItemList', ], ], ], 'UtteranceSpecification' => [ 'type' => 'structure', 'members' => [ 'botAliasId' => [ 'shape' => 'BotAliasId', ], 'botVersion' => [ 'shape' => 'NumericalBotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'sessionId' => [ 'shape' => 'AnalyticsSessionId', ], 'channel' => [ 'shape' => 'AnalyticsChannel', ], 'mode' => [ 'shape' => 'AnalyticsModality', ], 'conversationStartTime' => [ 'shape' => 'Timestamp', ], 'conversationEndTime' => [ 'shape' => 'Timestamp', ], 'utterance' => [ 'shape' => 'String', ], 'utteranceTimestamp' => [ 'shape' => 'Timestamp', ], 'audioVoiceDurationMillis' => [ 'shape' => 'AnalyticsLongValue', ], 'utteranceUnderstood' => [ 'shape' => 'UtteranceUnderstood', ], 'inputType' => [ 'shape' => 'String', ], 'outputType' => [ 'shape' => 'String', ], 'associatedIntentName' => [ 'shape' => 'Name', ], 'associatedSlotName' => [ 'shape' => 'Name', ], 'intentState' => [ 'shape' => 'IntentState', ], 'dialogActionType' => [ 'shape' => 'String', ], 'botResponseAudioVoiceId' => [ 'shape' => 'String', ], 'slotsFilledInSession' => [ 'shape' => 'String', ], 'utteranceRequestId' => [ 'shape' => 'Id', ], 'botResponses' => [ 'shape' => 'UtteranceBotResponses', ], ], ], 'UtteranceSpecifications' => [ 'type' => 'list', 'member' => [ 'shape' => 'UtteranceSpecification', ], ], 'UtteranceUnderstood' => [ 'type' => 'boolean', ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'Value' => [ 'type' => 'string', 'max' => 140, 'min' => 1, ], 'VoiceEngine' => [ 'type' => 'string', 'enum' => [ 'standard', 'neural', 'long-form', 'generative', ], ], 'VoiceId' => [ 'type' => 'string', ], 'VoiceSettings' => [ 'type' => 'structure', 'required' => [ 'voiceId', ], 'members' => [ 'voiceId' => [ 'shape' => 'VoiceId', ], 'engine' => [ 'shape' => 'VoiceEngine', ], ], ], 'WaitAndContinueSpecification' => [ 'type' => 'structure', 'required' => [ 'waitingResponse', 'continueResponse', ], 'members' => [ 'waitingResponse' => [ 'shape' => 'ResponseSpecification', ], 'continueResponse' => [ 'shape' => 'ResponseSpecification', ], 'stillWaitingResponse' => [ 'shape' => 'StillWaitingResponseSpecification', ], 'active' => [ 'shape' => 'BoxedBoolean', ], ], ], 'Weight' => [ 'type' => 'integer', 'box' => true, 'max' => 3, 'min' => 0, ], ],];
