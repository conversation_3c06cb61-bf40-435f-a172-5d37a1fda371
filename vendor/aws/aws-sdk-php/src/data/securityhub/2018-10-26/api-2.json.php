<?php
// This file was auto-generated from sdk-root/src/data/securityhub/2018-10-26/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2018-10-26', 'endpointPrefix' => 'securityhub', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'AWS SecurityHub', 'serviceId' => 'SecurityHub', 'signatureVersion' => 'v4', 'signingName' => 'securityhub', 'uid' => 'securityhub-2018-10-26', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'AcceptAdministratorInvitation' => [ 'name' => 'AcceptAdministratorInvitation', 'http' => [ 'method' => 'POST', 'requestUri' => '/administrator', ], 'input' => [ 'shape' => 'AcceptAdministratorInvitationRequest', ], 'output' => [ 'shape' => 'AcceptAdministratorInvitationResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidAccessException', ], ], ], 'AcceptInvitation' => [ 'name' => 'AcceptInvitation', 'http' => [ 'method' => 'POST', 'requestUri' => '/master', ], 'input' => [ 'shape' => 'AcceptInvitationRequest', ], 'output' => [ 'shape' => 'AcceptInvitationResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidAccessException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This API has been deprecated, use AcceptAdministratorInvitation API instead.', ], 'BatchDeleteAutomationRules' => [ 'name' => 'BatchDeleteAutomationRules', 'http' => [ 'method' => 'POST', 'requestUri' => '/automationrules/delete', ], 'input' => [ 'shape' => 'BatchDeleteAutomationRulesRequest', ], 'output' => [ 'shape' => 'BatchDeleteAutomationRulesResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'BatchDisableStandards' => [ 'name' => 'BatchDisableStandards', 'http' => [ 'method' => 'POST', 'requestUri' => '/standards/deregister', ], 'input' => [ 'shape' => 'BatchDisableStandardsRequest', ], 'output' => [ 'shape' => 'BatchDisableStandardsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'BatchEnableStandards' => [ 'name' => 'BatchEnableStandards', 'http' => [ 'method' => 'POST', 'requestUri' => '/standards/register', ], 'input' => [ 'shape' => 'BatchEnableStandardsRequest', ], 'output' => [ 'shape' => 'BatchEnableStandardsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'BatchGetAutomationRules' => [ 'name' => 'BatchGetAutomationRules', 'http' => [ 'method' => 'POST', 'requestUri' => '/automationrules/get', ], 'input' => [ 'shape' => 'BatchGetAutomationRulesRequest', ], 'output' => [ 'shape' => 'BatchGetAutomationRulesResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'BatchGetConfigurationPolicyAssociations' => [ 'name' => 'BatchGetConfigurationPolicyAssociations', 'http' => [ 'method' => 'POST', 'requestUri' => '/configurationPolicyAssociation/batchget', ], 'input' => [ 'shape' => 'BatchGetConfigurationPolicyAssociationsRequest', ], 'output' => [ 'shape' => 'BatchGetConfigurationPolicyAssociationsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'BatchGetSecurityControls' => [ 'name' => 'BatchGetSecurityControls', 'http' => [ 'method' => 'POST', 'requestUri' => '/securityControls/batchGet', ], 'input' => [ 'shape' => 'BatchGetSecurityControlsRequest', ], 'output' => [ 'shape' => 'BatchGetSecurityControlsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'BatchGetStandardsControlAssociations' => [ 'name' => 'BatchGetStandardsControlAssociations', 'http' => [ 'method' => 'POST', 'requestUri' => '/associations/batchGet', ], 'input' => [ 'shape' => 'BatchGetStandardsControlAssociationsRequest', ], 'output' => [ 'shape' => 'BatchGetStandardsControlAssociationsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'BatchImportFindings' => [ 'name' => 'BatchImportFindings', 'http' => [ 'method' => 'POST', 'requestUri' => '/findings/import', ], 'input' => [ 'shape' => 'BatchImportFindingsRequest', ], 'output' => [ 'shape' => 'BatchImportFindingsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidAccessException', ], ], ], 'BatchUpdateAutomationRules' => [ 'name' => 'BatchUpdateAutomationRules', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/automationrules/update', ], 'input' => [ 'shape' => 'BatchUpdateAutomationRulesRequest', ], 'output' => [ 'shape' => 'BatchUpdateAutomationRulesResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'BatchUpdateFindings' => [ 'name' => 'BatchUpdateFindings', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/findings/batchupdate', ], 'input' => [ 'shape' => 'BatchUpdateFindingsRequest', ], 'output' => [ 'shape' => 'BatchUpdateFindingsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidAccessException', ], ], ], 'BatchUpdateFindingsV2' => [ 'name' => 'BatchUpdateFindingsV2', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/findingsv2/batchupdatev2', ], 'input' => [ 'shape' => 'BatchUpdateFindingsV2Request', ], 'output' => [ 'shape' => 'BatchUpdateFindingsV2Response', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'BatchUpdateStandardsControlAssociations' => [ 'name' => 'BatchUpdateStandardsControlAssociations', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/associations', ], 'input' => [ 'shape' => 'BatchUpdateStandardsControlAssociationsRequest', ], 'output' => [ 'shape' => 'BatchUpdateStandardsControlAssociationsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ConnectorRegistrationsV2' => [ 'name' => 'ConnectorRegistrationsV2', 'http' => [ 'method' => 'POST', 'requestUri' => '/connectorsv2/registrations', ], 'input' => [ 'shape' => 'ConnectorRegistrationsV2Request', ], 'output' => [ 'shape' => 'ConnectorRegistrationsV2Response', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'CreateActionTarget' => [ 'name' => 'CreateActionTarget', 'http' => [ 'method' => 'POST', 'requestUri' => '/actionTargets', ], 'input' => [ 'shape' => 'CreateActionTargetRequest', ], 'output' => [ 'shape' => 'CreateActionTargetResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceConflictException', ], ], ], 'CreateAggregatorV2' => [ 'name' => 'CreateAggregatorV2', 'http' => [ 'method' => 'POST', 'requestUri' => '/aggregatorv2/create', ], 'input' => [ 'shape' => 'CreateAggregatorV2Request', ], 'output' => [ 'shape' => 'CreateAggregatorV2Response', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateAutomationRule' => [ 'name' => 'CreateAutomationRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/automationrules/create', ], 'input' => [ 'shape' => 'CreateAutomationRuleRequest', ], 'output' => [ 'shape' => 'CreateAutomationRuleResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'CreateAutomationRuleV2' => [ 'name' => 'CreateAutomationRuleV2', 'http' => [ 'method' => 'POST', 'requestUri' => '/automationrulesv2/create', ], 'input' => [ 'shape' => 'CreateAutomationRuleV2Request', ], 'output' => [ 'shape' => 'CreateAutomationRuleV2Response', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateConfigurationPolicy' => [ 'name' => 'CreateConfigurationPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/configurationPolicy/create', ], 'input' => [ 'shape' => 'CreateConfigurationPolicyRequest', ], 'output' => [ 'shape' => 'CreateConfigurationPolicyResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceConflictException', ], ], ], 'CreateConnectorV2' => [ 'name' => 'CreateConnectorV2', 'http' => [ 'method' => 'POST', 'requestUri' => '/connectorsv2', ], 'input' => [ 'shape' => 'CreateConnectorV2Request', ], 'output' => [ 'shape' => 'CreateConnectorV2Response', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'CreateFindingAggregator' => [ 'name' => 'CreateFindingAggregator', 'http' => [ 'method' => 'POST', 'requestUri' => '/findingAggregator/create', ], 'input' => [ 'shape' => 'CreateFindingAggregatorRequest', ], 'output' => [ 'shape' => 'CreateFindingAggregatorResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'CreateInsight' => [ 'name' => 'CreateInsight', 'http' => [ 'method' => 'POST', 'requestUri' => '/insights', ], 'input' => [ 'shape' => 'CreateInsightRequest', ], 'output' => [ 'shape' => 'CreateInsightResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'ResourceConflictException', ], ], ], 'CreateMembers' => [ 'name' => 'CreateMembers', 'http' => [ 'method' => 'POST', 'requestUri' => '/members', ], 'input' => [ 'shape' => 'CreateMembersRequest', ], 'output' => [ 'shape' => 'CreateMembersResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateTicketV2' => [ 'name' => 'CreateTicketV2', 'http' => [ 'method' => 'POST', 'requestUri' => '/ticketsv2', ], 'input' => [ 'shape' => 'CreateTicketV2Request', ], 'output' => [ 'shape' => 'CreateTicketV2Response', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeclineInvitations' => [ 'name' => 'DeclineInvitations', 'http' => [ 'method' => 'POST', 'requestUri' => '/invitations/decline', ], 'input' => [ 'shape' => 'DeclineInvitationsRequest', ], 'output' => [ 'shape' => 'DeclineInvitationsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeleteActionTarget' => [ 'name' => 'DeleteActionTarget', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/actionTargets/{ActionTargetArn+}', ], 'input' => [ 'shape' => 'DeleteActionTargetRequest', ], 'output' => [ 'shape' => 'DeleteActionTargetResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeleteAggregatorV2' => [ 'name' => 'DeleteAggregatorV2', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/aggregatorv2/delete/{AggregatorV2Arn+}', ], 'input' => [ 'shape' => 'DeleteAggregatorV2Request', ], 'output' => [ 'shape' => 'DeleteAggregatorV2Response', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteAutomationRuleV2' => [ 'name' => 'DeleteAutomationRuleV2', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/automationrulesv2/{Identifier}', ], 'input' => [ 'shape' => 'DeleteAutomationRuleV2Request', ], 'output' => [ 'shape' => 'DeleteAutomationRuleV2Response', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteConfigurationPolicy' => [ 'name' => 'DeleteConfigurationPolicy', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/configurationPolicy/{Identifier}', ], 'input' => [ 'shape' => 'DeleteConfigurationPolicyRequest', ], 'output' => [ 'shape' => 'DeleteConfigurationPolicyResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceConflictException', ], ], ], 'DeleteConnectorV2' => [ 'name' => 'DeleteConnectorV2', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/connectorsv2/{ConnectorId+}', ], 'input' => [ 'shape' => 'DeleteConnectorV2Request', ], 'output' => [ 'shape' => 'DeleteConnectorV2Response', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeleteFindingAggregator' => [ 'name' => 'DeleteFindingAggregator', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/findingAggregator/delete/{FindingAggregatorArn+}', ], 'input' => [ 'shape' => 'DeleteFindingAggregatorRequest', ], 'output' => [ 'shape' => 'DeleteFindingAggregatorResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeleteInsight' => [ 'name' => 'DeleteInsight', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/insights/{InsightArn+}', ], 'input' => [ 'shape' => 'DeleteInsightRequest', ], 'output' => [ 'shape' => 'DeleteInsightResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeleteInvitations' => [ 'name' => 'DeleteInvitations', 'http' => [ 'method' => 'POST', 'requestUri' => '/invitations/delete', ], 'input' => [ 'shape' => 'DeleteInvitationsRequest', ], 'output' => [ 'shape' => 'DeleteInvitationsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidAccessException', ], ], ], 'DeleteMembers' => [ 'name' => 'DeleteMembers', 'http' => [ 'method' => 'POST', 'requestUri' => '/members/delete', ], 'input' => [ 'shape' => 'DeleteMembersRequest', ], 'output' => [ 'shape' => 'DeleteMembersResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeActionTargets' => [ 'name' => 'DescribeActionTargets', 'http' => [ 'method' => 'POST', 'requestUri' => '/actionTargets/get', ], 'input' => [ 'shape' => 'DescribeActionTargetsRequest', ], 'output' => [ 'shape' => 'DescribeActionTargetsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeHub' => [ 'name' => 'DescribeHub', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts', ], 'input' => [ 'shape' => 'DescribeHubRequest', ], 'output' => [ 'shape' => 'DescribeHubResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeOrganizationConfiguration' => [ 'name' => 'DescribeOrganizationConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/organization/configuration', ], 'input' => [ 'shape' => 'DescribeOrganizationConfigurationRequest', ], 'output' => [ 'shape' => 'DescribeOrganizationConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'DescribeProducts' => [ 'name' => 'DescribeProducts', 'http' => [ 'method' => 'GET', 'requestUri' => '/products', ], 'input' => [ 'shape' => 'DescribeProductsRequest', ], 'output' => [ 'shape' => 'DescribeProductsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'DescribeProductsV2' => [ 'name' => 'DescribeProductsV2', 'http' => [ 'method' => 'GET', 'requestUri' => '/productsV2', ], 'input' => [ 'shape' => 'DescribeProductsV2Request', ], 'output' => [ 'shape' => 'DescribeProductsV2Response', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'DescribeSecurityHubV2' => [ 'name' => 'DescribeSecurityHubV2', 'http' => [ 'method' => 'GET', 'requestUri' => '/hubv2', ], 'input' => [ 'shape' => 'DescribeSecurityHubV2Request', ], 'output' => [ 'shape' => 'DescribeSecurityHubV2Response', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeStandards' => [ 'name' => 'DescribeStandards', 'http' => [ 'method' => 'GET', 'requestUri' => '/standards', ], 'input' => [ 'shape' => 'DescribeStandardsRequest', ], 'output' => [ 'shape' => 'DescribeStandardsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], ], ], 'DescribeStandardsControls' => [ 'name' => 'DescribeStandardsControls', 'http' => [ 'method' => 'GET', 'requestUri' => '/standards/controls/{StandardsSubscriptionArn+}', ], 'input' => [ 'shape' => 'DescribeStandardsControlsRequest', ], 'output' => [ 'shape' => 'DescribeStandardsControlsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DisableImportFindingsForProduct' => [ 'name' => 'DisableImportFindingsForProduct', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/productSubscriptions/{ProductSubscriptionArn+}', ], 'input' => [ 'shape' => 'DisableImportFindingsForProductRequest', ], 'output' => [ 'shape' => 'DisableImportFindingsForProductResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'DisableOrganizationAdminAccount' => [ 'name' => 'DisableOrganizationAdminAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/organization/admin/disable', ], 'input' => [ 'shape' => 'DisableOrganizationAdminAccountRequest', ], 'output' => [ 'shape' => 'DisableOrganizationAdminAccountResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DisableSecurityHub' => [ 'name' => 'DisableSecurityHub', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/accounts', ], 'input' => [ 'shape' => 'DisableSecurityHubRequest', ], 'output' => [ 'shape' => 'DisableSecurityHubResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DisableSecurityHubV2' => [ 'name' => 'DisableSecurityHubV2', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/hubv2', ], 'input' => [ 'shape' => 'DisableSecurityHubV2Request', ], 'output' => [ 'shape' => 'DisableSecurityHubV2Response', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'DisassociateFromAdministratorAccount' => [ 'name' => 'DisassociateFromAdministratorAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/administrator/disassociate', ], 'input' => [ 'shape' => 'DisassociateFromAdministratorAccountRequest', ], 'output' => [ 'shape' => 'DisassociateFromAdministratorAccountResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DisassociateFromMasterAccount' => [ 'name' => 'DisassociateFromMasterAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/master/disassociate', ], 'input' => [ 'shape' => 'DisassociateFromMasterAccountRequest', ], 'output' => [ 'shape' => 'DisassociateFromMasterAccountResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This API has been deprecated, use DisassociateFromAdministratorAccount API instead.', ], 'DisassociateMembers' => [ 'name' => 'DisassociateMembers', 'http' => [ 'method' => 'POST', 'requestUri' => '/members/disassociate', ], 'input' => [ 'shape' => 'DisassociateMembersRequest', ], 'output' => [ 'shape' => 'DisassociateMembersResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'EnableImportFindingsForProduct' => [ 'name' => 'EnableImportFindingsForProduct', 'http' => [ 'method' => 'POST', 'requestUri' => '/productSubscriptions', ], 'input' => [ 'shape' => 'EnableImportFindingsForProductRequest', ], 'output' => [ 'shape' => 'EnableImportFindingsForProductResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'EnableOrganizationAdminAccount' => [ 'name' => 'EnableOrganizationAdminAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/organization/admin/enable', ], 'input' => [ 'shape' => 'EnableOrganizationAdminAccountRequest', ], 'output' => [ 'shape' => 'EnableOrganizationAdminAccountResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'EnableSecurityHub' => [ 'name' => 'EnableSecurityHub', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts', ], 'input' => [ 'shape' => 'EnableSecurityHubRequest', ], 'output' => [ 'shape' => 'EnableSecurityHubResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'EnableSecurityHubV2' => [ 'name' => 'EnableSecurityHubV2', 'http' => [ 'method' => 'POST', 'requestUri' => '/hubv2', ], 'input' => [ 'shape' => 'EnableSecurityHubV2Request', ], 'output' => [ 'shape' => 'EnableSecurityHubV2Response', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetAdministratorAccount' => [ 'name' => 'GetAdministratorAccount', 'http' => [ 'method' => 'GET', 'requestUri' => '/administrator', ], 'input' => [ 'shape' => 'GetAdministratorAccountRequest', ], 'output' => [ 'shape' => 'GetAdministratorAccountResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetAggregatorV2' => [ 'name' => 'GetAggregatorV2', 'http' => [ 'method' => 'GET', 'requestUri' => '/aggregatorv2/get/{AggregatorV2Arn+}', ], 'input' => [ 'shape' => 'GetAggregatorV2Request', ], 'output' => [ 'shape' => 'GetAggregatorV2Response', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetAutomationRuleV2' => [ 'name' => 'GetAutomationRuleV2', 'http' => [ 'method' => 'GET', 'requestUri' => '/automationrulesv2/{Identifier}', ], 'input' => [ 'shape' => 'GetAutomationRuleV2Request', ], 'output' => [ 'shape' => 'GetAutomationRuleV2Response', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetConfigurationPolicy' => [ 'name' => 'GetConfigurationPolicy', 'http' => [ 'method' => 'GET', 'requestUri' => '/configurationPolicy/get/{Identifier}', ], 'input' => [ 'shape' => 'GetConfigurationPolicyRequest', ], 'output' => [ 'shape' => 'GetConfigurationPolicyResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetConfigurationPolicyAssociation' => [ 'name' => 'GetConfigurationPolicyAssociation', 'http' => [ 'method' => 'POST', 'requestUri' => '/configurationPolicyAssociation/get', ], 'input' => [ 'shape' => 'GetConfigurationPolicyAssociationRequest', ], 'output' => [ 'shape' => 'GetConfigurationPolicyAssociationResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetConnectorV2' => [ 'name' => 'GetConnectorV2', 'http' => [ 'method' => 'GET', 'requestUri' => '/connectorsv2/{ConnectorId+}', ], 'input' => [ 'shape' => 'GetConnectorV2Request', ], 'output' => [ 'shape' => 'GetConnectorV2Response', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetEnabledStandards' => [ 'name' => 'GetEnabledStandards', 'http' => [ 'method' => 'POST', 'requestUri' => '/standards/get', ], 'input' => [ 'shape' => 'GetEnabledStandardsRequest', ], 'output' => [ 'shape' => 'GetEnabledStandardsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'GetFindingAggregator' => [ 'name' => 'GetFindingAggregator', 'http' => [ 'method' => 'GET', 'requestUri' => '/findingAggregator/get/{FindingAggregatorArn+}', ], 'input' => [ 'shape' => 'GetFindingAggregatorRequest', ], 'output' => [ 'shape' => 'GetFindingAggregatorResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetFindingHistory' => [ 'name' => 'GetFindingHistory', 'http' => [ 'method' => 'POST', 'requestUri' => '/findingHistory/get', ], 'input' => [ 'shape' => 'GetFindingHistoryRequest', ], 'output' => [ 'shape' => 'GetFindingHistoryResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'GetFindingStatisticsV2' => [ 'name' => 'GetFindingStatisticsV2', 'http' => [ 'method' => 'POST', 'requestUri' => '/findingsv2/statistics', ], 'input' => [ 'shape' => 'GetFindingStatisticsV2Request', ], 'output' => [ 'shape' => 'GetFindingStatisticsV2Response', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetFindings' => [ 'name' => 'GetFindings', 'http' => [ 'method' => 'POST', 'requestUri' => '/findings', ], 'input' => [ 'shape' => 'GetFindingsRequest', ], 'output' => [ 'shape' => 'GetFindingsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'GetFindingsV2' => [ 'name' => 'GetFindingsV2', 'http' => [ 'method' => 'POST', 'requestUri' => '/findingsv2', ], 'input' => [ 'shape' => 'GetFindingsV2Request', ], 'output' => [ 'shape' => 'GetFindingsV2Response', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetInsightResults' => [ 'name' => 'GetInsightResults', 'http' => [ 'method' => 'GET', 'requestUri' => '/insights/results/{InsightArn+}', ], 'input' => [ 'shape' => 'GetInsightResultsRequest', ], 'output' => [ 'shape' => 'GetInsightResultsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetInsights' => [ 'name' => 'GetInsights', 'http' => [ 'method' => 'POST', 'requestUri' => '/insights/get', ], 'input' => [ 'shape' => 'GetInsightsRequest', ], 'output' => [ 'shape' => 'GetInsightsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetInvitationsCount' => [ 'name' => 'GetInvitationsCount', 'http' => [ 'method' => 'GET', 'requestUri' => '/invitations/count', ], 'input' => [ 'shape' => 'GetInvitationsCountRequest', ], 'output' => [ 'shape' => 'GetInvitationsCountResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'GetMasterAccount' => [ 'name' => 'GetMasterAccount', 'http' => [ 'method' => 'GET', 'requestUri' => '/master', ], 'input' => [ 'shape' => 'GetMasterAccountRequest', ], 'output' => [ 'shape' => 'GetMasterAccountResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This API has been deprecated, use GetAdministratorAccount API instead.', ], 'GetMembers' => [ 'name' => 'GetMembers', 'http' => [ 'method' => 'POST', 'requestUri' => '/members/get', ], 'input' => [ 'shape' => 'GetMembersRequest', ], 'output' => [ 'shape' => 'GetMembersResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetResourcesStatisticsV2' => [ 'name' => 'GetResourcesStatisticsV2', 'http' => [ 'method' => 'POST', 'requestUri' => '/resourcesv2/statistics', ], 'input' => [ 'shape' => 'GetResourcesStatisticsV2Request', ], 'output' => [ 'shape' => 'GetResourcesStatisticsV2Response', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetResourcesV2' => [ 'name' => 'GetResourcesV2', 'http' => [ 'method' => 'POST', 'requestUri' => '/resourcesv2', ], 'input' => [ 'shape' => 'GetResourcesV2Request', ], 'output' => [ 'shape' => 'GetResourcesV2Response', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetSecurityControlDefinition' => [ 'name' => 'GetSecurityControlDefinition', 'http' => [ 'method' => 'GET', 'requestUri' => '/securityControl/definition', ], 'input' => [ 'shape' => 'GetSecurityControlDefinitionRequest', ], 'output' => [ 'shape' => 'GetSecurityControlDefinitionResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'InviteMembers' => [ 'name' => 'InviteMembers', 'http' => [ 'method' => 'POST', 'requestUri' => '/members/invite', ], 'input' => [ 'shape' => 'InviteMembersRequest', ], 'output' => [ 'shape' => 'InviteMembersResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListAggregatorsV2' => [ 'name' => 'ListAggregatorsV2', 'http' => [ 'method' => 'GET', 'requestUri' => '/aggregatorv2/list', ], 'input' => [ 'shape' => 'ListAggregatorsV2Request', ], 'output' => [ 'shape' => 'ListAggregatorsV2Response', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], ], 'ListAutomationRules' => [ 'name' => 'ListAutomationRules', 'http' => [ 'method' => 'GET', 'requestUri' => '/automationrules/list', ], 'input' => [ 'shape' => 'ListAutomationRulesRequest', ], 'output' => [ 'shape' => 'ListAutomationRulesResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'ListAutomationRulesV2' => [ 'name' => 'ListAutomationRulesV2', 'http' => [ 'method' => 'GET', 'requestUri' => '/automationrulesv2/list', ], 'input' => [ 'shape' => 'ListAutomationRulesV2Request', ], 'output' => [ 'shape' => 'ListAutomationRulesV2Response', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'ListConfigurationPolicies' => [ 'name' => 'ListConfigurationPolicies', 'http' => [ 'method' => 'GET', 'requestUri' => '/configurationPolicy/list', ], 'input' => [ 'shape' => 'ListConfigurationPoliciesRequest', ], 'output' => [ 'shape' => 'ListConfigurationPoliciesResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListConfigurationPolicyAssociations' => [ 'name' => 'ListConfigurationPolicyAssociations', 'http' => [ 'method' => 'POST', 'requestUri' => '/configurationPolicyAssociation/list', ], 'input' => [ 'shape' => 'ListConfigurationPolicyAssociationsRequest', ], 'output' => [ 'shape' => 'ListConfigurationPolicyAssociationsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListConnectorsV2' => [ 'name' => 'ListConnectorsV2', 'http' => [ 'method' => 'GET', 'requestUri' => '/connectorsv2', ], 'input' => [ 'shape' => 'ListConnectorsV2Request', ], 'output' => [ 'shape' => 'ListConnectorsV2Response', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListEnabledProductsForImport' => [ 'name' => 'ListEnabledProductsForImport', 'http' => [ 'method' => 'GET', 'requestUri' => '/productSubscriptions', ], 'input' => [ 'shape' => 'ListEnabledProductsForImportRequest', ], 'output' => [ 'shape' => 'ListEnabledProductsForImportResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidAccessException', ], ], ], 'ListFindingAggregators' => [ 'name' => 'ListFindingAggregators', 'http' => [ 'method' => 'GET', 'requestUri' => '/findingAggregator/list', ], 'input' => [ 'shape' => 'ListFindingAggregatorsRequest', ], 'output' => [ 'shape' => 'ListFindingAggregatorsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'ListInvitations' => [ 'name' => 'ListInvitations', 'http' => [ 'method' => 'GET', 'requestUri' => '/invitations', ], 'input' => [ 'shape' => 'ListInvitationsRequest', ], 'output' => [ 'shape' => 'ListInvitationsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'ListMembers' => [ 'name' => 'ListMembers', 'http' => [ 'method' => 'GET', 'requestUri' => '/members', ], 'input' => [ 'shape' => 'ListMembersRequest', ], 'output' => [ 'shape' => 'ListMembersResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'ListOrganizationAdminAccounts' => [ 'name' => 'ListOrganizationAdminAccounts', 'http' => [ 'method' => 'GET', 'requestUri' => '/organization/admin', ], 'input' => [ 'shape' => 'ListOrganizationAdminAccountsRequest', ], 'output' => [ 'shape' => 'ListOrganizationAdminAccountsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'ListSecurityControlDefinitions' => [ 'name' => 'ListSecurityControlDefinitions', 'http' => [ 'method' => 'GET', 'requestUri' => '/securityControls/definitions', ], 'input' => [ 'shape' => 'ListSecurityControlDefinitionsRequest', ], 'output' => [ 'shape' => 'ListSecurityControlDefinitionsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'ListStandardsControlAssociations' => [ 'name' => 'ListStandardsControlAssociations', 'http' => [ 'method' => 'GET', 'requestUri' => '/associations', ], 'input' => [ 'shape' => 'ListStandardsControlAssociationsRequest', ], 'output' => [ 'shape' => 'ListStandardsControlAssociationsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{ResourceArn}', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'StartConfigurationPolicyAssociation' => [ 'name' => 'StartConfigurationPolicyAssociation', 'http' => [ 'method' => 'POST', 'requestUri' => '/configurationPolicyAssociation/associate', ], 'input' => [ 'shape' => 'StartConfigurationPolicyAssociationRequest', ], 'output' => [ 'shape' => 'StartConfigurationPolicyAssociationResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'StartConfigurationPolicyDisassociation' => [ 'name' => 'StartConfigurationPolicyDisassociation', 'http' => [ 'method' => 'POST', 'requestUri' => '/configurationPolicyAssociation/disassociate', ], 'input' => [ 'shape' => 'StartConfigurationPolicyDisassociationRequest', ], 'output' => [ 'shape' => 'StartConfigurationPolicyDisassociationResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{ResourceArn}', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{ResourceArn}', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateActionTarget' => [ 'name' => 'UpdateActionTarget', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/actionTargets/{ActionTargetArn+}', ], 'input' => [ 'shape' => 'UpdateActionTargetRequest', ], 'output' => [ 'shape' => 'UpdateActionTargetResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateAggregatorV2' => [ 'name' => 'UpdateAggregatorV2', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/aggregatorv2/update/{AggregatorV2Arn+}', ], 'input' => [ 'shape' => 'UpdateAggregatorV2Request', ], 'output' => [ 'shape' => 'UpdateAggregatorV2Response', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateAutomationRuleV2' => [ 'name' => 'UpdateAutomationRuleV2', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/automationrulesv2/{Identifier}', ], 'input' => [ 'shape' => 'UpdateAutomationRuleV2Request', ], 'output' => [ 'shape' => 'UpdateAutomationRuleV2Response', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateConfigurationPolicy' => [ 'name' => 'UpdateConfigurationPolicy', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/configurationPolicy/{Identifier}', ], 'input' => [ 'shape' => 'UpdateConfigurationPolicyRequest', ], 'output' => [ 'shape' => 'UpdateConfigurationPolicyResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceConflictException', ], ], ], 'UpdateConnectorV2' => [ 'name' => 'UpdateConnectorV2', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/connectorsv2/{ConnectorId+}', ], 'input' => [ 'shape' => 'UpdateConnectorV2Request', ], 'output' => [ 'shape' => 'UpdateConnectorV2Response', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateFindingAggregator' => [ 'name' => 'UpdateFindingAggregator', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/findingAggregator/update', ], 'input' => [ 'shape' => 'UpdateFindingAggregatorRequest', ], 'output' => [ 'shape' => 'UpdateFindingAggregatorResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateFindings' => [ 'name' => 'UpdateFindings', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/findings', ], 'input' => [ 'shape' => 'UpdateFindingsRequest', ], 'output' => [ 'shape' => 'UpdateFindingsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateInsight' => [ 'name' => 'UpdateInsight', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/insights/{InsightArn+}', ], 'input' => [ 'shape' => 'UpdateInsightRequest', ], 'output' => [ 'shape' => 'UpdateInsightResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateOrganizationConfiguration' => [ 'name' => 'UpdateOrganizationConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/organization/configuration', ], 'input' => [ 'shape' => 'UpdateOrganizationConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateOrganizationConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceConflictException', ], ], ], 'UpdateSecurityControl' => [ 'name' => 'UpdateSecurityControl', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/securityControl/update', ], 'input' => [ 'shape' => 'UpdateSecurityControlRequest', ], 'output' => [ 'shape' => 'UpdateSecurityControlResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], ], ], 'UpdateSecurityHubConfiguration' => [ 'name' => 'UpdateSecurityHubConfiguration', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/accounts', ], 'input' => [ 'shape' => 'UpdateSecurityHubConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateSecurityHubConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateStandardsControl' => [ 'name' => 'UpdateStandardsControl', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/standards/control/{StandardsControlArn+}', ], 'input' => [ 'shape' => 'UpdateStandardsControlRequest', ], 'output' => [ 'shape' => 'UpdateStandardsControlResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], ], ], ], 'shapes' => [ 'AcceptAdministratorInvitationRequest' => [ 'type' => 'structure', 'required' => [ 'AdministratorId', 'InvitationId', ], 'members' => [ 'AdministratorId' => [ 'shape' => 'NonEmptyString', ], 'InvitationId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AcceptAdministratorInvitationResponse' => [ 'type' => 'structure', 'members' => [], ], 'AcceptInvitationRequest' => [ 'type' => 'structure', 'required' => [ 'MasterId', 'InvitationId', ], 'members' => [ 'MasterId' => [ 'shape' => 'NonEmptyString', ], 'InvitationId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AcceptInvitationResponse' => [ 'type' => 'structure', 'members' => [], ], 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'NonEmptyString', ], 'Code' => [ 'shape' => 'NonEmptyString', ], ], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'AccountDetails' => [ 'type' => 'structure', 'required' => [ 'AccountId', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'Email' => [ 'shape' => 'NonEmptyString', ], ], ], 'AccountDetailsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountDetails', ], 'max' => 50, 'min' => 1, ], 'AccountId' => [ 'type' => 'string', ], 'AccountIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], ], 'Action' => [ 'type' => 'structure', 'members' => [ 'ActionType' => [ 'shape' => 'NonEmptyString', ], 'NetworkConnectionAction' => [ 'shape' => 'NetworkConnectionAction', ], 'AwsApiCallAction' => [ 'shape' => 'AwsApiCallAction', ], 'DnsRequestAction' => [ 'shape' => 'DnsRequestAction', ], 'PortProbeAction' => [ 'shape' => 'PortProbeAction', ], ], ], 'ActionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AutomationRulesAction', ], 'max' => 1, 'min' => 1, ], 'ActionLocalIpDetails' => [ 'type' => 'structure', 'members' => [ 'IpAddressV4' => [ 'shape' => 'NonEmptyString', ], ], ], 'ActionLocalPortDetails' => [ 'type' => 'structure', 'members' => [ 'Port' => [ 'shape' => 'Integer', ], 'PortName' => [ 'shape' => 'NonEmptyString', ], ], ], 'ActionRemoteIpDetails' => [ 'type' => 'structure', 'members' => [ 'IpAddressV4' => [ 'shape' => 'NonEmptyString', ], 'Organization' => [ 'shape' => 'IpOrganizationDetails', ], 'Country' => [ 'shape' => 'Country', ], 'City' => [ 'shape' => 'City', ], 'GeoLocation' => [ 'shape' => 'GeoLocation', ], ], ], 'ActionRemotePortDetails' => [ 'type' => 'structure', 'members' => [ 'Port' => [ 'shape' => 'Integer', ], 'PortName' => [ 'shape' => 'NonEmptyString', ], ], ], 'ActionTarget' => [ 'type' => 'structure', 'required' => [ 'ActionTargetArn', 'Name', 'Description', ], 'members' => [ 'ActionTargetArn' => [ 'shape' => 'NonEmptyString', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], ], ], 'ActionTargetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ActionTarget', ], ], 'Actor' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'NonEmptyString', ], 'User' => [ 'shape' => 'ActorUser', ], 'Session' => [ 'shape' => 'ActorSession', ], ], ], 'ActorSession' => [ 'type' => 'structure', 'members' => [ 'Uid' => [ 'shape' => 'NonEmptyString', ], 'MfaStatus' => [ 'shape' => 'ActorSessionMfaStatus', ], 'CreatedTime' => [ 'shape' => 'Long', ], 'Issuer' => [ 'shape' => 'NonEmptyString', ], ], ], 'ActorSessionMfaStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'ActorUser' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'Uid' => [ 'shape' => 'NonEmptyString', ], 'Type' => [ 'shape' => 'NonEmptyString', ], 'CredentialUid' => [ 'shape' => 'NonEmptyString', ], 'Account' => [ 'shape' => 'UserAccount', ], ], ], 'ActorsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Actor', ], 'max' => 10, 'min' => 0, ], 'Adjustment' => [ 'type' => 'structure', 'members' => [ 'Metric' => [ 'shape' => 'NonEmptyString', ], 'Reason' => [ 'shape' => 'NonEmptyString', ], ], ], 'AdjustmentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Adjustment', ], ], 'AdminAccount' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'AdminStatus', ], ], ], 'AdminAccounts' => [ 'type' => 'list', 'member' => [ 'shape' => 'AdminAccount', ], ], 'AdminStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLE_IN_PROGRESS', ], ], 'AdminsMaxResults' => [ 'type' => 'integer', 'max' => 10, 'min' => 1, ], 'AggregatorV2' => [ 'type' => 'structure', 'members' => [ 'AggregatorV2Arn' => [ 'shape' => 'NonEmptyString', ], ], ], 'AggregatorV2List' => [ 'type' => 'list', 'member' => [ 'shape' => 'AggregatorV2', ], ], 'AllowedOperators' => [ 'type' => 'string', 'enum' => [ 'AND', 'OR', ], ], 'AlphaNumericNonEmptyString' => [ 'type' => 'string', 'pattern' => '^([^\\u0000-\\u007F]|[-_ a-zA-Z0-9])+$', ], 'ArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], ], 'AssociatedStandard' => [ 'type' => 'structure', 'members' => [ 'StandardsId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AssociatedStandardsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssociatedStandard', ], ], 'AssociationFilters' => [ 'type' => 'structure', 'members' => [ 'ConfigurationPolicyId' => [ 'shape' => 'NonEmptyString', ], 'AssociationType' => [ 'shape' => 'AssociationType', ], 'AssociationStatus' => [ 'shape' => 'ConfigurationPolicyAssociationStatus', ], ], ], 'AssociationSetDetails' => [ 'type' => 'structure', 'members' => [ 'AssociationState' => [ 'shape' => 'AssociationStateDetails', ], 'GatewayId' => [ 'shape' => 'NonEmptyString', ], 'Main' => [ 'shape' => 'Boolean', ], 'RouteTableAssociationId' => [ 'shape' => 'NonEmptyString', ], 'RouteTableId' => [ 'shape' => 'NonEmptyString', ], 'SubnetId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AssociationSetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssociationSetDetails', ], ], 'AssociationStateDetails' => [ 'type' => 'structure', 'members' => [ 'State' => [ 'shape' => 'NonEmptyString', ], 'StatusMessage' => [ 'shape' => 'NonEmptyString', ], ], ], 'AssociationStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'AssociationType' => [ 'type' => 'string', 'enum' => [ 'INHERITED', 'APPLIED', ], ], 'AutoEnableStandards' => [ 'type' => 'string', 'enum' => [ 'NONE', 'DEFAULT', ], ], 'AutomationRulesAction' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'AutomationRulesActionType', ], 'FindingFieldsUpdate' => [ 'shape' => 'AutomationRulesFindingFieldsUpdate', ], ], ], 'AutomationRulesActionListV2' => [ 'type' => 'list', 'member' => [ 'shape' => 'AutomationRulesActionV2', ], 'max' => 1, 'min' => 1, ], 'AutomationRulesActionType' => [ 'type' => 'string', 'enum' => [ 'FINDING_FIELDS_UPDATE', ], ], 'AutomationRulesActionTypeListV2' => [ 'type' => 'list', 'member' => [ 'shape' => 'AutomationRulesActionTypeObjectV2', ], ], 'AutomationRulesActionTypeObjectV2' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'AutomationRulesActionTypeV2', ], ], ], 'AutomationRulesActionTypeV2' => [ 'type' => 'string', 'enum' => [ 'FINDING_FIELDS_UPDATE', 'EXTERNAL_INTEGRATION', ], ], 'AutomationRulesActionV2' => [ 'type' => 'structure', 'required' => [ 'Type', ], 'members' => [ 'Type' => [ 'shape' => 'AutomationRulesActionTypeV2', ], 'FindingFieldsUpdate' => [ 'shape' => 'AutomationRulesFindingFieldsUpdateV2', ], 'ExternalIntegrationConfiguration' => [ 'shape' => 'ExternalIntegrationConfiguration', ], ], ], 'AutomationRulesArnsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], 'max' => 100, 'min' => 1, ], 'AutomationRulesConfig' => [ 'type' => 'structure', 'members' => [ 'RuleArn' => [ 'shape' => 'NonEmptyString', ], 'RuleStatus' => [ 'shape' => 'RuleStatus', ], 'RuleOrder' => [ 'shape' => 'RuleOrderValue', ], 'RuleName' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'IsTerminal' => [ 'shape' => 'Boolean', ], 'Criteria' => [ 'shape' => 'AutomationRulesFindingFilters', ], 'Actions' => [ 'shape' => 'ActionList', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], 'CreatedBy' => [ 'shape' => 'NonEmptyString', ], ], ], 'AutomationRulesConfigList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AutomationRulesConfig', ], ], 'AutomationRulesFindingFieldsUpdate' => [ 'type' => 'structure', 'members' => [ 'Note' => [ 'shape' => 'NoteUpdate', ], 'Severity' => [ 'shape' => 'SeverityUpdate', ], 'VerificationState' => [ 'shape' => 'VerificationState', ], 'Confidence' => [ 'shape' => 'RatioScale', ], 'Criticality' => [ 'shape' => 'RatioScale', ], 'Types' => [ 'shape' => 'TypeList', ], 'UserDefinedFields' => [ 'shape' => 'FieldMap', ], 'Workflow' => [ 'shape' => 'WorkflowUpdate', ], 'RelatedFindings' => [ 'shape' => 'RelatedFindingList', ], ], ], 'AutomationRulesFindingFieldsUpdateV2' => [ 'type' => 'structure', 'members' => [ 'SeverityId' => [ 'shape' => 'Integer', ], 'Comment' => [ 'shape' => 'NonEmptyString', ], 'StatusId' => [ 'shape' => 'Integer', ], ], ], 'AutomationRulesFindingFilters' => [ 'type' => 'structure', 'members' => [ 'ProductArn' => [ 'shape' => 'StringFilterList', ], 'AwsAccountId' => [ 'shape' => 'StringFilterList', ], 'Id' => [ 'shape' => 'StringFilterList', ], 'GeneratorId' => [ 'shape' => 'StringFilterList', ], 'Type' => [ 'shape' => 'StringFilterList', ], 'FirstObservedAt' => [ 'shape' => 'DateFilterList', ], 'LastObservedAt' => [ 'shape' => 'DateFilterList', ], 'CreatedAt' => [ 'shape' => 'DateFilterList', ], 'UpdatedAt' => [ 'shape' => 'DateFilterList', ], 'Confidence' => [ 'shape' => 'NumberFilterList', ], 'Criticality' => [ 'shape' => 'NumberFilterList', ], 'Title' => [ 'shape' => 'StringFilterList', ], 'Description' => [ 'shape' => 'StringFilterList', ], 'SourceUrl' => [ 'shape' => 'StringFilterList', ], 'ProductName' => [ 'shape' => 'StringFilterList', ], 'CompanyName' => [ 'shape' => 'StringFilterList', ], 'SeverityLabel' => [ 'shape' => 'StringFilterList', ], 'ResourceType' => [ 'shape' => 'StringFilterList', ], 'ResourceId' => [ 'shape' => 'StringFilterList', ], 'ResourcePartition' => [ 'shape' => 'StringFilterList', ], 'ResourceRegion' => [ 'shape' => 'StringFilterList', ], 'ResourceTags' => [ 'shape' => 'MapFilterList', ], 'ResourceDetailsOther' => [ 'shape' => 'MapFilterList', ], 'ComplianceStatus' => [ 'shape' => 'StringFilterList', ], 'ComplianceSecurityControlId' => [ 'shape' => 'StringFilterList', ], 'ComplianceAssociatedStandardsId' => [ 'shape' => 'StringFilterList', ], 'VerificationState' => [ 'shape' => 'StringFilterList', ], 'WorkflowStatus' => [ 'shape' => 'StringFilterList', ], 'RecordState' => [ 'shape' => 'StringFilterList', ], 'RelatedFindingsProductArn' => [ 'shape' => 'StringFilterList', ], 'RelatedFindingsId' => [ 'shape' => 'StringFilterList', ], 'NoteText' => [ 'shape' => 'StringFilterList', ], 'NoteUpdatedAt' => [ 'shape' => 'DateFilterList', ], 'NoteUpdatedBy' => [ 'shape' => 'StringFilterList', ], 'UserDefinedFields' => [ 'shape' => 'MapFilterList', ], 'ResourceApplicationArn' => [ 'shape' => 'StringFilterList', ], 'ResourceApplicationName' => [ 'shape' => 'StringFilterList', ], 'AwsAccountName' => [ 'shape' => 'StringFilterList', ], ], ], 'AutomationRulesMetadata' => [ 'type' => 'structure', 'members' => [ 'RuleArn' => [ 'shape' => 'NonEmptyString', ], 'RuleStatus' => [ 'shape' => 'RuleStatus', ], 'RuleOrder' => [ 'shape' => 'RuleOrderValue', ], 'RuleName' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'IsTerminal' => [ 'shape' => 'Boolean', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], 'CreatedBy' => [ 'shape' => 'NonEmptyString', ], ], ], 'AutomationRulesMetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AutomationRulesMetadata', ], ], 'AutomationRulesMetadataListV2' => [ 'type' => 'list', 'member' => [ 'shape' => 'AutomationRulesMetadataV2', ], ], 'AutomationRulesMetadataV2' => [ 'type' => 'structure', 'members' => [ 'RuleArn' => [ 'shape' => 'NonEmptyString', ], 'RuleId' => [ 'shape' => 'NonEmptyString', ], 'RuleOrder' => [ 'shape' => 'RuleOrderValueV2', ], 'RuleName' => [ 'shape' => 'NonEmptyString', ], 'RuleStatus' => [ 'shape' => 'RuleStatusV2', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'Actions' => [ 'shape' => 'AutomationRulesActionTypeListV2', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'AvailabilityZone' => [ 'type' => 'structure', 'members' => [ 'ZoneName' => [ 'shape' => 'NonEmptyString', ], 'SubnetId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AvailabilityZones' => [ 'type' => 'list', 'member' => [ 'shape' => 'AvailabilityZone', ], ], 'AwsAmazonMqBrokerDetails' => [ 'type' => 'structure', 'members' => [ 'AuthenticationStrategy' => [ 'shape' => 'NonEmptyString', ], 'AutoMinorVersionUpgrade' => [ 'shape' => 'Boolean', ], 'BrokerArn' => [ 'shape' => 'NonEmptyString', ], 'BrokerName' => [ 'shape' => 'NonEmptyString', ], 'DeploymentMode' => [ 'shape' => 'NonEmptyString', ], 'EncryptionOptions' => [ 'shape' => 'AwsAmazonMqBrokerEncryptionOptionsDetails', ], 'EngineType' => [ 'shape' => 'NonEmptyString', ], 'EngineVersion' => [ 'shape' => 'NonEmptyString', ], 'HostInstanceType' => [ 'shape' => 'NonEmptyString', ], 'BrokerId' => [ 'shape' => 'NonEmptyString', ], 'LdapServerMetadata' => [ 'shape' => 'AwsAmazonMqBrokerLdapServerMetadataDetails', ], 'Logs' => [ 'shape' => 'AwsAmazonMqBrokerLogsDetails', ], 'MaintenanceWindowStartTime' => [ 'shape' => 'AwsAmazonMqBrokerMaintenanceWindowStartTimeDetails', ], 'PubliclyAccessible' => [ 'shape' => 'Boolean', ], 'SecurityGroups' => [ 'shape' => 'StringList', ], 'StorageType' => [ 'shape' => 'NonEmptyString', ], 'SubnetIds' => [ 'shape' => 'StringList', ], 'Users' => [ 'shape' => 'AwsAmazonMqBrokerUsersList', ], ], ], 'AwsAmazonMqBrokerEncryptionOptionsDetails' => [ 'type' => 'structure', 'members' => [ 'KmsKeyId' => [ 'shape' => 'NonEmptyString', ], 'UseAwsOwnedKey' => [ 'shape' => 'Boolean', ], ], ], 'AwsAmazonMqBrokerLdapServerMetadataDetails' => [ 'type' => 'structure', 'members' => [ 'Hosts' => [ 'shape' => 'StringList', ], 'RoleBase' => [ 'shape' => 'NonEmptyString', ], 'RoleName' => [ 'shape' => 'NonEmptyString', ], 'RoleSearchMatching' => [ 'shape' => 'NonEmptyString', ], 'RoleSearchSubtree' => [ 'shape' => 'Boolean', ], 'ServiceAccountUsername' => [ 'shape' => 'NonEmptyString', ], 'UserBase' => [ 'shape' => 'NonEmptyString', ], 'UserRoleName' => [ 'shape' => 'NonEmptyString', ], 'UserSearchMatching' => [ 'shape' => 'NonEmptyString', ], 'UserSearchSubtree' => [ 'shape' => 'Boolean', ], ], ], 'AwsAmazonMqBrokerLogsDetails' => [ 'type' => 'structure', 'members' => [ 'Audit' => [ 'shape' => 'Boolean', ], 'General' => [ 'shape' => 'Boolean', ], 'AuditLogGroup' => [ 'shape' => 'NonEmptyString', ], 'GeneralLogGroup' => [ 'shape' => 'NonEmptyString', ], 'Pending' => [ 'shape' => 'AwsAmazonMqBrokerLogsPendingDetails', ], ], ], 'AwsAmazonMqBrokerLogsPendingDetails' => [ 'type' => 'structure', 'members' => [ 'Audit' => [ 'shape' => 'Boolean', ], 'General' => [ 'shape' => 'Boolean', ], ], ], 'AwsAmazonMqBrokerMaintenanceWindowStartTimeDetails' => [ 'type' => 'structure', 'members' => [ 'DayOfWeek' => [ 'shape' => 'NonEmptyString', ], 'TimeOfDay' => [ 'shape' => 'NonEmptyString', ], 'TimeZone' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsAmazonMqBrokerUsersDetails' => [ 'type' => 'structure', 'members' => [ 'PendingChange' => [ 'shape' => 'NonEmptyString', ], 'Username' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsAmazonMqBrokerUsersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsAmazonMqBrokerUsersDetails', ], ], 'AwsApiCallAction' => [ 'type' => 'structure', 'members' => [ 'Api' => [ 'shape' => 'NonEmptyString', ], 'ServiceName' => [ 'shape' => 'NonEmptyString', ], 'CallerType' => [ 'shape' => 'NonEmptyString', ], 'RemoteIpDetails' => [ 'shape' => 'ActionRemoteIpDetails', ], 'DomainDetails' => [ 'shape' => 'AwsApiCallActionDomainDetails', ], 'AffectedResources' => [ 'shape' => 'FieldMap', ], 'FirstSeen' => [ 'shape' => 'NonEmptyString', ], 'LastSeen' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsApiCallActionDomainDetails' => [ 'type' => 'structure', 'members' => [ 'Domain' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsApiGatewayAccessLogSettings' => [ 'type' => 'structure', 'members' => [ 'Format' => [ 'shape' => 'NonEmptyString', ], 'DestinationArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsApiGatewayCanarySettings' => [ 'type' => 'structure', 'members' => [ 'PercentTraffic' => [ 'shape' => 'Double', ], 'DeploymentId' => [ 'shape' => 'NonEmptyString', ], 'StageVariableOverrides' => [ 'shape' => 'FieldMap', ], 'UseStageCache' => [ 'shape' => 'Boolean', ], ], ], 'AwsApiGatewayEndpointConfiguration' => [ 'type' => 'structure', 'members' => [ 'Types' => [ 'shape' => 'NonEmptyStringList', ], ], ], 'AwsApiGatewayMethodSettings' => [ 'type' => 'structure', 'members' => [ 'MetricsEnabled' => [ 'shape' => 'Boolean', ], 'LoggingLevel' => [ 'shape' => 'NonEmptyString', ], 'DataTraceEnabled' => [ 'shape' => 'Boolean', ], 'ThrottlingBurstLimit' => [ 'shape' => 'Integer', ], 'ThrottlingRateLimit' => [ 'shape' => 'Double', ], 'CachingEnabled' => [ 'shape' => 'Boolean', ], 'CacheTtlInSeconds' => [ 'shape' => 'Integer', ], 'CacheDataEncrypted' => [ 'shape' => 'Boolean', ], 'RequireAuthorizationForCacheControl' => [ 'shape' => 'Boolean', ], 'UnauthorizedCacheControlHeaderStrategy' => [ 'shape' => 'NonEmptyString', ], 'HttpMethod' => [ 'shape' => 'NonEmptyString', ], 'ResourcePath' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsApiGatewayMethodSettingsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsApiGatewayMethodSettings', ], ], 'AwsApiGatewayRestApiDetails' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'NonEmptyString', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'CreatedDate' => [ 'shape' => 'NonEmptyString', ], 'Version' => [ 'shape' => 'NonEmptyString', ], 'BinaryMediaTypes' => [ 'shape' => 'NonEmptyStringList', ], 'MinimumCompressionSize' => [ 'shape' => 'Integer', ], 'ApiKeySource' => [ 'shape' => 'NonEmptyString', ], 'EndpointConfiguration' => [ 'shape' => 'AwsApiGatewayEndpointConfiguration', ], ], ], 'AwsApiGatewayStageDetails' => [ 'type' => 'structure', 'members' => [ 'DeploymentId' => [ 'shape' => 'NonEmptyString', ], 'ClientCertificateId' => [ 'shape' => 'NonEmptyString', ], 'StageName' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'CacheClusterEnabled' => [ 'shape' => 'Boolean', ], 'CacheClusterSize' => [ 'shape' => 'NonEmptyString', ], 'CacheClusterStatus' => [ 'shape' => 'NonEmptyString', ], 'MethodSettings' => [ 'shape' => 'AwsApiGatewayMethodSettingsList', ], 'Variables' => [ 'shape' => 'FieldMap', ], 'DocumentationVersion' => [ 'shape' => 'NonEmptyString', ], 'AccessLogSettings' => [ 'shape' => 'AwsApiGatewayAccessLogSettings', ], 'CanarySettings' => [ 'shape' => 'AwsApiGatewayCanarySettings', ], 'TracingEnabled' => [ 'shape' => 'Boolean', ], 'CreatedDate' => [ 'shape' => 'NonEmptyString', ], 'LastUpdatedDate' => [ 'shape' => 'NonEmptyString', ], 'WebAclArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsApiGatewayV2ApiDetails' => [ 'type' => 'structure', 'members' => [ 'ApiEndpoint' => [ 'shape' => 'NonEmptyString', ], 'ApiId' => [ 'shape' => 'NonEmptyString', ], 'ApiKeySelectionExpression' => [ 'shape' => 'NonEmptyString', ], 'CreatedDate' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'Version' => [ 'shape' => 'NonEmptyString', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'ProtocolType' => [ 'shape' => 'NonEmptyString', ], 'RouteSelectionExpression' => [ 'shape' => 'NonEmptyString', ], 'CorsConfiguration' => [ 'shape' => 'AwsCorsConfiguration', ], ], ], 'AwsApiGatewayV2RouteSettings' => [ 'type' => 'structure', 'members' => [ 'DetailedMetricsEnabled' => [ 'shape' => 'Boolean', ], 'LoggingLevel' => [ 'shape' => 'NonEmptyString', ], 'DataTraceEnabled' => [ 'shape' => 'Boolean', ], 'ThrottlingBurstLimit' => [ 'shape' => 'Integer', ], 'ThrottlingRateLimit' => [ 'shape' => 'Double', ], ], ], 'AwsApiGatewayV2StageDetails' => [ 'type' => 'structure', 'members' => [ 'ClientCertificateId' => [ 'shape' => 'NonEmptyString', ], 'CreatedDate' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'DefaultRouteSettings' => [ 'shape' => 'AwsApiGatewayV2RouteSettings', ], 'DeploymentId' => [ 'shape' => 'NonEmptyString', ], 'LastUpdatedDate' => [ 'shape' => 'NonEmptyString', ], 'RouteSettings' => [ 'shape' => 'AwsApiGatewayV2RouteSettings', ], 'StageName' => [ 'shape' => 'NonEmptyString', ], 'StageVariables' => [ 'shape' => 'FieldMap', ], 'AccessLogSettings' => [ 'shape' => 'AwsApiGatewayAccessLogSettings', ], 'AutoDeploy' => [ 'shape' => 'Boolean', ], 'LastDeploymentStatusMessage' => [ 'shape' => 'NonEmptyString', ], 'ApiGatewayManaged' => [ 'shape' => 'Boolean', ], ], ], 'AwsAppSyncGraphQlApiAdditionalAuthenticationProvidersDetails' => [ 'type' => 'structure', 'members' => [ 'AuthenticationType' => [ 'shape' => 'NonEmptyString', ], 'LambdaAuthorizerConfig' => [ 'shape' => 'AwsAppSyncGraphQlApiLambdaAuthorizerConfigDetails', ], 'OpenIdConnectConfig' => [ 'shape' => 'AwsAppSyncGraphQlApiOpenIdConnectConfigDetails', ], 'UserPoolConfig' => [ 'shape' => 'AwsAppSyncGraphQlApiUserPoolConfigDetails', ], ], ], 'AwsAppSyncGraphQlApiAdditionalAuthenticationProvidersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsAppSyncGraphQlApiAdditionalAuthenticationProvidersDetails', ], ], 'AwsAppSyncGraphQlApiDetails' => [ 'type' => 'structure', 'members' => [ 'ApiId' => [ 'shape' => 'NonEmptyString', ], 'Id' => [ 'shape' => 'NonEmptyString', ], 'OpenIdConnectConfig' => [ 'shape' => 'AwsAppSyncGraphQlApiOpenIdConnectConfigDetails', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'LambdaAuthorizerConfig' => [ 'shape' => 'AwsAppSyncGraphQlApiLambdaAuthorizerConfigDetails', ], 'XrayEnabled' => [ 'shape' => 'Boolean', ], 'Arn' => [ 'shape' => 'NonEmptyString', ], 'UserPoolConfig' => [ 'shape' => 'AwsAppSyncGraphQlApiUserPoolConfigDetails', ], 'AuthenticationType' => [ 'shape' => 'NonEmptyString', ], 'LogConfig' => [ 'shape' => 'AwsAppSyncGraphQlApiLogConfigDetails', ], 'AdditionalAuthenticationProviders' => [ 'shape' => 'AwsAppSyncGraphQlApiAdditionalAuthenticationProvidersList', ], 'WafWebAclArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsAppSyncGraphQlApiLambdaAuthorizerConfigDetails' => [ 'type' => 'structure', 'members' => [ 'AuthorizerResultTtlInSeconds' => [ 'shape' => 'Integer', ], 'AuthorizerUri' => [ 'shape' => 'NonEmptyString', ], 'IdentityValidationExpression' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsAppSyncGraphQlApiLogConfigDetails' => [ 'type' => 'structure', 'members' => [ 'CloudWatchLogsRoleArn' => [ 'shape' => 'NonEmptyString', ], 'ExcludeVerboseContent' => [ 'shape' => 'Boolean', ], 'FieldLogLevel' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsAppSyncGraphQlApiOpenIdConnectConfigDetails' => [ 'type' => 'structure', 'members' => [ 'AuthTtL' => [ 'shape' => 'Long', ], 'ClientId' => [ 'shape' => 'NonEmptyString', ], 'IatTtL' => [ 'shape' => 'Long', ], 'Issuer' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsAppSyncGraphQlApiUserPoolConfigDetails' => [ 'type' => 'structure', 'members' => [ 'AppIdClientRegex' => [ 'shape' => 'NonEmptyString', ], 'AwsRegion' => [ 'shape' => 'NonEmptyString', ], 'DefaultAction' => [ 'shape' => 'NonEmptyString', ], 'UserPoolId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsAthenaWorkGroupConfigurationDetails' => [ 'type' => 'structure', 'members' => [ 'ResultConfiguration' => [ 'shape' => 'AwsAthenaWorkGroupConfigurationResultConfigurationDetails', ], ], ], 'AwsAthenaWorkGroupConfigurationResultConfigurationDetails' => [ 'type' => 'structure', 'members' => [ 'EncryptionConfiguration' => [ 'shape' => 'AwsAthenaWorkGroupConfigurationResultConfigurationEncryptionConfigurationDetails', ], ], ], 'AwsAthenaWorkGroupConfigurationResultConfigurationEncryptionConfigurationDetails' => [ 'type' => 'structure', 'members' => [ 'EncryptionOption' => [ 'shape' => 'NonEmptyString', ], 'KmsKey' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsAthenaWorkGroupDetails' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'State' => [ 'shape' => 'NonEmptyString', ], 'Configuration' => [ 'shape' => 'AwsAthenaWorkGroupConfigurationDetails', ], ], ], 'AwsAutoScalingAutoScalingGroupAvailabilityZonesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsAutoScalingAutoScalingGroupAvailabilityZonesListDetails', ], ], 'AwsAutoScalingAutoScalingGroupAvailabilityZonesListDetails' => [ 'type' => 'structure', 'members' => [ 'Value' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsAutoScalingAutoScalingGroupDetails' => [ 'type' => 'structure', 'members' => [ 'LaunchConfigurationName' => [ 'shape' => 'NonEmptyString', ], 'LoadBalancerNames' => [ 'shape' => 'StringList', ], 'HealthCheckType' => [ 'shape' => 'NonEmptyString', ], 'HealthCheckGracePeriod' => [ 'shape' => 'Integer', ], 'CreatedTime' => [ 'shape' => 'NonEmptyString', ], 'MixedInstancesPolicy' => [ 'shape' => 'AwsAutoScalingAutoScalingGroupMixedInstancesPolicyDetails', ], 'AvailabilityZones' => [ 'shape' => 'AwsAutoScalingAutoScalingGroupAvailabilityZonesList', ], 'LaunchTemplate' => [ 'shape' => 'AwsAutoScalingAutoScalingGroupLaunchTemplateLaunchTemplateSpecification', ], 'CapacityRebalance' => [ 'shape' => 'Boolean', ], ], ], 'AwsAutoScalingAutoScalingGroupLaunchTemplateLaunchTemplateSpecification' => [ 'type' => 'structure', 'members' => [ 'LaunchTemplateId' => [ 'shape' => 'NonEmptyString', ], 'LaunchTemplateName' => [ 'shape' => 'NonEmptyString', ], 'Version' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsAutoScalingAutoScalingGroupMixedInstancesPolicyDetails' => [ 'type' => 'structure', 'members' => [ 'InstancesDistribution' => [ 'shape' => 'AwsAutoScalingAutoScalingGroupMixedInstancesPolicyInstancesDistributionDetails', ], 'LaunchTemplate' => [ 'shape' => 'AwsAutoScalingAutoScalingGroupMixedInstancesPolicyLaunchTemplateDetails', ], ], ], 'AwsAutoScalingAutoScalingGroupMixedInstancesPolicyInstancesDistributionDetails' => [ 'type' => 'structure', 'members' => [ 'OnDemandAllocationStrategy' => [ 'shape' => 'NonEmptyString', ], 'OnDemandBaseCapacity' => [ 'shape' => 'Integer', ], 'OnDemandPercentageAboveBaseCapacity' => [ 'shape' => 'Integer', ], 'SpotAllocationStrategy' => [ 'shape' => 'NonEmptyString', ], 'SpotInstancePools' => [ 'shape' => 'Integer', ], 'SpotMaxPrice' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsAutoScalingAutoScalingGroupMixedInstancesPolicyLaunchTemplateDetails' => [ 'type' => 'structure', 'members' => [ 'LaunchTemplateSpecification' => [ 'shape' => 'AwsAutoScalingAutoScalingGroupMixedInstancesPolicyLaunchTemplateLaunchTemplateSpecification', ], 'Overrides' => [ 'shape' => 'AwsAutoScalingAutoScalingGroupMixedInstancesPolicyLaunchTemplateOverridesList', ], ], ], 'AwsAutoScalingAutoScalingGroupMixedInstancesPolicyLaunchTemplateLaunchTemplateSpecification' => [ 'type' => 'structure', 'members' => [ 'LaunchTemplateId' => [ 'shape' => 'NonEmptyString', ], 'LaunchTemplateName' => [ 'shape' => 'NonEmptyString', ], 'Version' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsAutoScalingAutoScalingGroupMixedInstancesPolicyLaunchTemplateOverridesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsAutoScalingAutoScalingGroupMixedInstancesPolicyLaunchTemplateOverridesListDetails', ], ], 'AwsAutoScalingAutoScalingGroupMixedInstancesPolicyLaunchTemplateOverridesListDetails' => [ 'type' => 'structure', 'members' => [ 'InstanceType' => [ 'shape' => 'NonEmptyString', ], 'WeightedCapacity' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsAutoScalingLaunchConfigurationBlockDeviceMappingsDetails' => [ 'type' => 'structure', 'members' => [ 'DeviceName' => [ 'shape' => 'NonEmptyString', ], 'Ebs' => [ 'shape' => 'AwsAutoScalingLaunchConfigurationBlockDeviceMappingsEbsDetails', ], 'NoDevice' => [ 'shape' => 'Boolean', ], 'VirtualName' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsAutoScalingLaunchConfigurationBlockDeviceMappingsEbsDetails' => [ 'type' => 'structure', 'members' => [ 'DeleteOnTermination' => [ 'shape' => 'Boolean', ], 'Encrypted' => [ 'shape' => 'Boolean', ], 'Iops' => [ 'shape' => 'Integer', ], 'SnapshotId' => [ 'shape' => 'NonEmptyString', ], 'VolumeSize' => [ 'shape' => 'Integer', ], 'VolumeType' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsAutoScalingLaunchConfigurationBlockDeviceMappingsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsAutoScalingLaunchConfigurationBlockDeviceMappingsDetails', ], ], 'AwsAutoScalingLaunchConfigurationDetails' => [ 'type' => 'structure', 'members' => [ 'AssociatePublicIpAddress' => [ 'shape' => 'Boolean', ], 'BlockDeviceMappings' => [ 'shape' => 'AwsAutoScalingLaunchConfigurationBlockDeviceMappingsList', ], 'ClassicLinkVpcId' => [ 'shape' => 'NonEmptyString', ], 'ClassicLinkVpcSecurityGroups' => [ 'shape' => 'NonEmptyStringList', ], 'CreatedTime' => [ 'shape' => 'NonEmptyString', ], 'EbsOptimized' => [ 'shape' => 'Boolean', ], 'IamInstanceProfile' => [ 'shape' => 'NonEmptyString', ], 'ImageId' => [ 'shape' => 'NonEmptyString', ], 'InstanceMonitoring' => [ 'shape' => 'AwsAutoScalingLaunchConfigurationInstanceMonitoringDetails', ], 'InstanceType' => [ 'shape' => 'NonEmptyString', ], 'KernelId' => [ 'shape' => 'NonEmptyString', ], 'KeyName' => [ 'shape' => 'NonEmptyString', ], 'LaunchConfigurationName' => [ 'shape' => 'NonEmptyString', ], 'PlacementTenancy' => [ 'shape' => 'NonEmptyString', ], 'RamdiskId' => [ 'shape' => 'NonEmptyString', ], 'SecurityGroups' => [ 'shape' => 'NonEmptyStringList', ], 'SpotPrice' => [ 'shape' => 'NonEmptyString', ], 'UserData' => [ 'shape' => 'NonEmptyString', ], 'MetadataOptions' => [ 'shape' => 'AwsAutoScalingLaunchConfigurationMetadataOptions', ], ], ], 'AwsAutoScalingLaunchConfigurationInstanceMonitoringDetails' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], ], ], 'AwsAutoScalingLaunchConfigurationMetadataOptions' => [ 'type' => 'structure', 'members' => [ 'HttpEndpoint' => [ 'shape' => 'NonEmptyString', ], 'HttpPutResponseHopLimit' => [ 'shape' => 'Integer', ], 'HttpTokens' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsBackupBackupPlanAdvancedBackupSettingsDetails' => [ 'type' => 'structure', 'members' => [ 'BackupOptions' => [ 'shape' => 'FieldMap', ], 'ResourceType' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsBackupBackupPlanAdvancedBackupSettingsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsBackupBackupPlanAdvancedBackupSettingsDetails', ], ], 'AwsBackupBackupPlanBackupPlanDetails' => [ 'type' => 'structure', 'members' => [ 'BackupPlanName' => [ 'shape' => 'NonEmptyString', ], 'AdvancedBackupSettings' => [ 'shape' => 'AwsBackupBackupPlanAdvancedBackupSettingsList', ], 'BackupPlanRule' => [ 'shape' => 'AwsBackupBackupPlanRuleList', ], ], ], 'AwsBackupBackupPlanDetails' => [ 'type' => 'structure', 'members' => [ 'BackupPlan' => [ 'shape' => 'AwsBackupBackupPlanBackupPlanDetails', ], 'BackupPlanArn' => [ 'shape' => 'NonEmptyString', ], 'BackupPlanId' => [ 'shape' => 'NonEmptyString', ], 'VersionId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsBackupBackupPlanLifecycleDetails' => [ 'type' => 'structure', 'members' => [ 'DeleteAfterDays' => [ 'shape' => 'Long', ], 'MoveToColdStorageAfterDays' => [ 'shape' => 'Long', ], ], ], 'AwsBackupBackupPlanRuleCopyActionsDetails' => [ 'type' => 'structure', 'members' => [ 'DestinationBackupVaultArn' => [ 'shape' => 'NonEmptyString', ], 'Lifecycle' => [ 'shape' => 'AwsBackupBackupPlanLifecycleDetails', ], ], ], 'AwsBackupBackupPlanRuleCopyActionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsBackupBackupPlanRuleCopyActionsDetails', ], ], 'AwsBackupBackupPlanRuleDetails' => [ 'type' => 'structure', 'members' => [ 'TargetBackupVault' => [ 'shape' => 'NonEmptyString', ], 'StartWindowMinutes' => [ 'shape' => 'Long', ], 'ScheduleExpression' => [ 'shape' => 'NonEmptyString', ], 'RuleName' => [ 'shape' => 'NonEmptyString', ], 'RuleId' => [ 'shape' => 'NonEmptyString', ], 'EnableContinuousBackup' => [ 'shape' => 'Boolean', ], 'CompletionWindowMinutes' => [ 'shape' => 'Long', ], 'CopyActions' => [ 'shape' => 'AwsBackupBackupPlanRuleCopyActionsList', ], 'Lifecycle' => [ 'shape' => 'AwsBackupBackupPlanLifecycleDetails', ], ], ], 'AwsBackupBackupPlanRuleList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsBackupBackupPlanRuleDetails', ], ], 'AwsBackupBackupVaultDetails' => [ 'type' => 'structure', 'members' => [ 'BackupVaultArn' => [ 'shape' => 'NonEmptyString', ], 'BackupVaultName' => [ 'shape' => 'NonEmptyString', ], 'EncryptionKeyArn' => [ 'shape' => 'NonEmptyString', ], 'Notifications' => [ 'shape' => 'AwsBackupBackupVaultNotificationsDetails', ], 'AccessPolicy' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsBackupBackupVaultNotificationsDetails' => [ 'type' => 'structure', 'members' => [ 'BackupVaultEvents' => [ 'shape' => 'NonEmptyStringList', ], 'SnsTopicArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsBackupRecoveryPointCalculatedLifecycleDetails' => [ 'type' => 'structure', 'members' => [ 'DeleteAt' => [ 'shape' => 'NonEmptyString', ], 'MoveToColdStorageAt' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsBackupRecoveryPointCreatedByDetails' => [ 'type' => 'structure', 'members' => [ 'BackupPlanArn' => [ 'shape' => 'NonEmptyString', ], 'BackupPlanId' => [ 'shape' => 'NonEmptyString', ], 'BackupPlanVersion' => [ 'shape' => 'NonEmptyString', ], 'BackupRuleId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsBackupRecoveryPointDetails' => [ 'type' => 'structure', 'members' => [ 'BackupSizeInBytes' => [ 'shape' => 'Long', ], 'BackupVaultArn' => [ 'shape' => 'NonEmptyString', ], 'BackupVaultName' => [ 'shape' => 'NonEmptyString', ], 'CalculatedLifecycle' => [ 'shape' => 'AwsBackupRecoveryPointCalculatedLifecycleDetails', ], 'CompletionDate' => [ 'shape' => 'NonEmptyString', ], 'CreatedBy' => [ 'shape' => 'AwsBackupRecoveryPointCreatedByDetails', ], 'CreationDate' => [ 'shape' => 'NonEmptyString', ], 'EncryptionKeyArn' => [ 'shape' => 'NonEmptyString', ], 'IamRoleArn' => [ 'shape' => 'NonEmptyString', ], 'IsEncrypted' => [ 'shape' => 'Boolean', ], 'LastRestoreTime' => [ 'shape' => 'NonEmptyString', ], 'Lifecycle' => [ 'shape' => 'AwsBackupRecoveryPointLifecycleDetails', ], 'RecoveryPointArn' => [ 'shape' => 'NonEmptyString', ], 'ResourceArn' => [ 'shape' => 'NonEmptyString', ], 'ResourceType' => [ 'shape' => 'NonEmptyString', ], 'SourceBackupVaultArn' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], 'StatusMessage' => [ 'shape' => 'NonEmptyString', ], 'StorageClass' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsBackupRecoveryPointLifecycleDetails' => [ 'type' => 'structure', 'members' => [ 'DeleteAfterDays' => [ 'shape' => 'Long', ], 'MoveToColdStorageAfterDays' => [ 'shape' => 'Long', ], ], ], 'AwsCertificateManagerCertificateDetails' => [ 'type' => 'structure', 'members' => [ 'CertificateAuthorityArn' => [ 'shape' => 'NonEmptyString', ], 'CreatedAt' => [ 'shape' => 'NonEmptyString', ], 'DomainName' => [ 'shape' => 'NonEmptyString', ], 'DomainValidationOptions' => [ 'shape' => 'AwsCertificateManagerCertificateDomainValidationOptions', ], 'ExtendedKeyUsages' => [ 'shape' => 'AwsCertificateManagerCertificateExtendedKeyUsages', ], 'FailureReason' => [ 'shape' => 'NonEmptyString', ], 'ImportedAt' => [ 'shape' => 'NonEmptyString', ], 'InUseBy' => [ 'shape' => 'StringList', ], 'IssuedAt' => [ 'shape' => 'NonEmptyString', ], 'Issuer' => [ 'shape' => 'NonEmptyString', ], 'KeyAlgorithm' => [ 'shape' => 'NonEmptyString', ], 'KeyUsages' => [ 'shape' => 'AwsCertificateManagerCertificateKeyUsages', ], 'NotAfter' => [ 'shape' => 'NonEmptyString', ], 'NotBefore' => [ 'shape' => 'NonEmptyString', ], 'Options' => [ 'shape' => 'AwsCertificateManagerCertificateOptions', ], 'RenewalEligibility' => [ 'shape' => 'NonEmptyString', ], 'RenewalSummary' => [ 'shape' => 'AwsCertificateManagerCertificateRenewalSummary', ], 'Serial' => [ 'shape' => 'NonEmptyString', ], 'SignatureAlgorithm' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], 'Subject' => [ 'shape' => 'NonEmptyString', ], 'SubjectAlternativeNames' => [ 'shape' => 'StringList', ], 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsCertificateManagerCertificateDomainValidationOption' => [ 'type' => 'structure', 'members' => [ 'DomainName' => [ 'shape' => 'NonEmptyString', ], 'ResourceRecord' => [ 'shape' => 'AwsCertificateManagerCertificateResourceRecord', ], 'ValidationDomain' => [ 'shape' => 'NonEmptyString', ], 'ValidationEmails' => [ 'shape' => 'StringList', ], 'ValidationMethod' => [ 'shape' => 'NonEmptyString', ], 'ValidationStatus' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsCertificateManagerCertificateDomainValidationOptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsCertificateManagerCertificateDomainValidationOption', ], ], 'AwsCertificateManagerCertificateExtendedKeyUsage' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'OId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsCertificateManagerCertificateExtendedKeyUsages' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsCertificateManagerCertificateExtendedKeyUsage', ], ], 'AwsCertificateManagerCertificateKeyUsage' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsCertificateManagerCertificateKeyUsages' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsCertificateManagerCertificateKeyUsage', ], ], 'AwsCertificateManagerCertificateOptions' => [ 'type' => 'structure', 'members' => [ 'CertificateTransparencyLoggingPreference' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsCertificateManagerCertificateRenewalSummary' => [ 'type' => 'structure', 'members' => [ 'DomainValidationOptions' => [ 'shape' => 'AwsCertificateManagerCertificateDomainValidationOptions', ], 'RenewalStatus' => [ 'shape' => 'NonEmptyString', ], 'RenewalStatusReason' => [ 'shape' => 'NonEmptyString', ], 'UpdatedAt' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsCertificateManagerCertificateResourceRecord' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'Type' => [ 'shape' => 'NonEmptyString', ], 'Value' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsCloudFormationStackDetails' => [ 'type' => 'structure', 'members' => [ 'Capabilities' => [ 'shape' => 'NonEmptyStringList', ], 'CreationTime' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'DisableRollback' => [ 'shape' => 'Boolean', ], 'DriftInformation' => [ 'shape' => 'AwsCloudFormationStackDriftInformationDetails', ], 'EnableTerminationProtection' => [ 'shape' => 'Boolean', ], 'LastUpdatedTime' => [ 'shape' => 'NonEmptyString', ], 'NotificationArns' => [ 'shape' => 'NonEmptyStringList', ], 'Outputs' => [ 'shape' => 'AwsCloudFormationStackOutputsList', ], 'RoleArn' => [ 'shape' => 'NonEmptyString', ], 'StackId' => [ 'shape' => 'NonEmptyString', ], 'StackName' => [ 'shape' => 'NonEmptyString', ], 'StackStatus' => [ 'shape' => 'NonEmptyString', ], 'StackStatusReason' => [ 'shape' => 'NonEmptyString', ], 'TimeoutInMinutes' => [ 'shape' => 'Integer', ], ], ], 'AwsCloudFormationStackDriftInformationDetails' => [ 'type' => 'structure', 'members' => [ 'StackDriftStatus' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsCloudFormationStackOutputsDetails' => [ 'type' => 'structure', 'members' => [ 'Description' => [ 'shape' => 'NonEmptyString', ], 'OutputKey' => [ 'shape' => 'NonEmptyString', ], 'OutputValue' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsCloudFormationStackOutputsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsCloudFormationStackOutputsDetails', ], ], 'AwsCloudFrontDistributionCacheBehavior' => [ 'type' => 'structure', 'members' => [ 'ViewerProtocolPolicy' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsCloudFrontDistributionCacheBehaviors' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => 'AwsCloudFrontDistributionCacheBehaviorsItemList', ], ], ], 'AwsCloudFrontDistributionCacheBehaviorsItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsCloudFrontDistributionCacheBehavior', ], ], 'AwsCloudFrontDistributionDefaultCacheBehavior' => [ 'type' => 'structure', 'members' => [ 'ViewerProtocolPolicy' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsCloudFrontDistributionDetails' => [ 'type' => 'structure', 'members' => [ 'CacheBehaviors' => [ 'shape' => 'AwsCloudFrontDistributionCacheBehaviors', ], 'DefaultCacheBehavior' => [ 'shape' => 'AwsCloudFrontDistributionDefaultCacheBehavior', ], 'DefaultRootObject' => [ 'shape' => 'NonEmptyString', ], 'DomainName' => [ 'shape' => 'NonEmptyString', ], 'ETag' => [ 'shape' => 'NonEmptyString', ], 'LastModifiedTime' => [ 'shape' => 'NonEmptyString', ], 'Logging' => [ 'shape' => 'AwsCloudFrontDistributionLogging', ], 'Origins' => [ 'shape' => 'AwsCloudFrontDistributionOrigins', ], 'OriginGroups' => [ 'shape' => 'AwsCloudFrontDistributionOriginGroups', ], 'ViewerCertificate' => [ 'shape' => 'AwsCloudFrontDistributionViewerCertificate', ], 'Status' => [ 'shape' => 'NonEmptyString', ], 'WebAclId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsCloudFrontDistributionLogging' => [ 'type' => 'structure', 'members' => [ 'Bucket' => [ 'shape' => 'NonEmptyString', ], 'Enabled' => [ 'shape' => 'Boolean', ], 'IncludeCookies' => [ 'shape' => 'Boolean', ], 'Prefix' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsCloudFrontDistributionOriginCustomOriginConfig' => [ 'type' => 'structure', 'members' => [ 'HttpPort' => [ 'shape' => 'Integer', ], 'HttpsPort' => [ 'shape' => 'Integer', ], 'OriginKeepaliveTimeout' => [ 'shape' => 'Integer', ], 'OriginProtocolPolicy' => [ 'shape' => 'NonEmptyString', ], 'OriginReadTimeout' => [ 'shape' => 'Integer', ], 'OriginSslProtocols' => [ 'shape' => 'AwsCloudFrontDistributionOriginSslProtocols', ], ], ], 'AwsCloudFrontDistributionOriginGroup' => [ 'type' => 'structure', 'members' => [ 'FailoverCriteria' => [ 'shape' => 'AwsCloudFrontDistributionOriginGroupFailover', ], ], ], 'AwsCloudFrontDistributionOriginGroupFailover' => [ 'type' => 'structure', 'members' => [ 'StatusCodes' => [ 'shape' => 'AwsCloudFrontDistributionOriginGroupFailoverStatusCodes', ], ], ], 'AwsCloudFrontDistributionOriginGroupFailoverStatusCodes' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => 'AwsCloudFrontDistributionOriginGroupFailoverStatusCodesItemList', ], 'Quantity' => [ 'shape' => 'Integer', ], ], ], 'AwsCloudFrontDistributionOriginGroupFailoverStatusCodesItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Integer', ], ], 'AwsCloudFrontDistributionOriginGroups' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => 'AwsCloudFrontDistributionOriginGroupsItemList', ], ], ], 'AwsCloudFrontDistributionOriginGroupsItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsCloudFrontDistributionOriginGroup', ], ], 'AwsCloudFrontDistributionOriginItem' => [ 'type' => 'structure', 'members' => [ 'DomainName' => [ 'shape' => 'NonEmptyString', ], 'Id' => [ 'shape' => 'NonEmptyString', ], 'OriginPath' => [ 'shape' => 'NonEmptyString', ], 'S3OriginConfig' => [ 'shape' => 'AwsCloudFrontDistributionOriginS3OriginConfig', ], 'CustomOriginConfig' => [ 'shape' => 'AwsCloudFrontDistributionOriginCustomOriginConfig', ], ], ], 'AwsCloudFrontDistributionOriginItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsCloudFrontDistributionOriginItem', ], ], 'AwsCloudFrontDistributionOriginS3OriginConfig' => [ 'type' => 'structure', 'members' => [ 'OriginAccessIdentity' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsCloudFrontDistributionOriginSslProtocols' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => 'NonEmptyStringList', ], 'Quantity' => [ 'shape' => 'Integer', ], ], ], 'AwsCloudFrontDistributionOrigins' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => 'AwsCloudFrontDistributionOriginItemList', ], ], ], 'AwsCloudFrontDistributionViewerCertificate' => [ 'type' => 'structure', 'members' => [ 'AcmCertificateArn' => [ 'shape' => 'NonEmptyString', ], 'Certificate' => [ 'shape' => 'NonEmptyString', ], 'CertificateSource' => [ 'shape' => 'NonEmptyString', ], 'CloudFrontDefaultCertificate' => [ 'shape' => 'Boolean', ], 'IamCertificateId' => [ 'shape' => 'NonEmptyString', ], 'MinimumProtocolVersion' => [ 'shape' => 'NonEmptyString', ], 'SslSupportMethod' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsCloudTrailTrailDetails' => [ 'type' => 'structure', 'members' => [ 'CloudWatchLogsLogGroupArn' => [ 'shape' => 'NonEmptyString', ], 'CloudWatchLogsRoleArn' => [ 'shape' => 'NonEmptyString', ], 'HasCustomEventSelectors' => [ 'shape' => 'Boolean', ], 'HomeRegion' => [ 'shape' => 'NonEmptyString', ], 'IncludeGlobalServiceEvents' => [ 'shape' => 'Boolean', ], 'IsMultiRegionTrail' => [ 'shape' => 'Boolean', ], 'IsOrganizationTrail' => [ 'shape' => 'Boolean', ], 'KmsKeyId' => [ 'shape' => 'NonEmptyString', ], 'LogFileValidationEnabled' => [ 'shape' => 'Boolean', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'S3BucketName' => [ 'shape' => 'NonEmptyString', ], 'S3KeyPrefix' => [ 'shape' => 'NonEmptyString', ], 'SnsTopicArn' => [ 'shape' => 'NonEmptyString', ], 'SnsTopicName' => [ 'shape' => 'NonEmptyString', ], 'TrailArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsCloudWatchAlarmDetails' => [ 'type' => 'structure', 'members' => [ 'ActionsEnabled' => [ 'shape' => 'Boolean', ], 'AlarmActions' => [ 'shape' => 'NonEmptyStringList', ], 'AlarmArn' => [ 'shape' => 'NonEmptyString', ], 'AlarmConfigurationUpdatedTimestamp' => [ 'shape' => 'NonEmptyString', ], 'AlarmDescription' => [ 'shape' => 'NonEmptyString', ], 'AlarmName' => [ 'shape' => 'NonEmptyString', ], 'ComparisonOperator' => [ 'shape' => 'NonEmptyString', ], 'DatapointsToAlarm' => [ 'shape' => 'Integer', ], 'Dimensions' => [ 'shape' => 'AwsCloudWatchAlarmDimensionsList', ], 'EvaluateLowSampleCountPercentile' => [ 'shape' => 'NonEmptyString', ], 'EvaluationPeriods' => [ 'shape' => 'Integer', ], 'ExtendedStatistic' => [ 'shape' => 'NonEmptyString', ], 'InsufficientDataActions' => [ 'shape' => 'NonEmptyStringList', ], 'MetricName' => [ 'shape' => 'NonEmptyString', ], 'Namespace' => [ 'shape' => 'NonEmptyString', ], 'OkActions' => [ 'shape' => 'NonEmptyStringList', ], 'Period' => [ 'shape' => 'Integer', ], 'Statistic' => [ 'shape' => 'NonEmptyString', ], 'Threshold' => [ 'shape' => 'Double', ], 'ThresholdMetricId' => [ 'shape' => 'NonEmptyString', ], 'TreatMissingData' => [ 'shape' => 'NonEmptyString', ], 'Unit' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsCloudWatchAlarmDimensionsDetails' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'Value' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsCloudWatchAlarmDimensionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsCloudWatchAlarmDimensionsDetails', ], ], 'AwsCodeBuildProjectArtifactsDetails' => [ 'type' => 'structure', 'members' => [ 'ArtifactIdentifier' => [ 'shape' => 'NonEmptyString', ], 'EncryptionDisabled' => [ 'shape' => 'Boolean', ], 'Location' => [ 'shape' => 'NonEmptyString', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'NamespaceType' => [ 'shape' => 'NonEmptyString', ], 'OverrideArtifactName' => [ 'shape' => 'Boolean', ], 'Packaging' => [ 'shape' => 'NonEmptyString', ], 'Path' => [ 'shape' => 'NonEmptyString', ], 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsCodeBuildProjectArtifactsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsCodeBuildProjectArtifactsDetails', ], ], 'AwsCodeBuildProjectDetails' => [ 'type' => 'structure', 'members' => [ 'EncryptionKey' => [ 'shape' => 'NonEmptyString', ], 'Artifacts' => [ 'shape' => 'AwsCodeBuildProjectArtifactsList', ], 'Environment' => [ 'shape' => 'AwsCodeBuildProjectEnvironment', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'Source' => [ 'shape' => 'AwsCodeBuildProjectSource', ], 'ServiceRole' => [ 'shape' => 'NonEmptyString', ], 'LogsConfig' => [ 'shape' => 'AwsCodeBuildProjectLogsConfigDetails', ], 'VpcConfig' => [ 'shape' => 'AwsCodeBuildProjectVpcConfig', ], 'SecondaryArtifacts' => [ 'shape' => 'AwsCodeBuildProjectArtifactsList', ], ], ], 'AwsCodeBuildProjectEnvironment' => [ 'type' => 'structure', 'members' => [ 'Certificate' => [ 'shape' => 'NonEmptyString', ], 'EnvironmentVariables' => [ 'shape' => 'AwsCodeBuildProjectEnvironmentEnvironmentVariablesList', ], 'PrivilegedMode' => [ 'shape' => 'Boolean', ], 'ImagePullCredentialsType' => [ 'shape' => 'NonEmptyString', ], 'RegistryCredential' => [ 'shape' => 'AwsCodeBuildProjectEnvironmentRegistryCredential', ], 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsCodeBuildProjectEnvironmentEnvironmentVariablesDetails' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'Type' => [ 'shape' => 'NonEmptyString', ], 'Value' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsCodeBuildProjectEnvironmentEnvironmentVariablesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsCodeBuildProjectEnvironmentEnvironmentVariablesDetails', ], ], 'AwsCodeBuildProjectEnvironmentRegistryCredential' => [ 'type' => 'structure', 'members' => [ 'Credential' => [ 'shape' => 'NonEmptyString', ], 'CredentialProvider' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsCodeBuildProjectLogsConfigCloudWatchLogsDetails' => [ 'type' => 'structure', 'members' => [ 'GroupName' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], 'StreamName' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsCodeBuildProjectLogsConfigDetails' => [ 'type' => 'structure', 'members' => [ 'CloudWatchLogs' => [ 'shape' => 'AwsCodeBuildProjectLogsConfigCloudWatchLogsDetails', ], 'S3Logs' => [ 'shape' => 'AwsCodeBuildProjectLogsConfigS3LogsDetails', ], ], ], 'AwsCodeBuildProjectLogsConfigS3LogsDetails' => [ 'type' => 'structure', 'members' => [ 'EncryptionDisabled' => [ 'shape' => 'Boolean', ], 'Location' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsCodeBuildProjectSource' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'NonEmptyString', ], 'Location' => [ 'shape' => 'NonEmptyString', ], 'GitCloneDepth' => [ 'shape' => 'Integer', ], 'InsecureSsl' => [ 'shape' => 'Boolean', ], ], ], 'AwsCodeBuildProjectVpcConfig' => [ 'type' => 'structure', 'members' => [ 'VpcId' => [ 'shape' => 'NonEmptyString', ], 'Subnets' => [ 'shape' => 'NonEmptyStringList', ], 'SecurityGroupIds' => [ 'shape' => 'NonEmptyStringList', ], ], ], 'AwsCorsConfiguration' => [ 'type' => 'structure', 'members' => [ 'AllowOrigins' => [ 'shape' => 'NonEmptyStringList', ], 'AllowCredentials' => [ 'shape' => 'Boolean', ], 'ExposeHeaders' => [ 'shape' => 'NonEmptyStringList', ], 'MaxAge' => [ 'shape' => 'Integer', ], 'AllowMethods' => [ 'shape' => 'NonEmptyStringList', ], 'AllowHeaders' => [ 'shape' => 'NonEmptyStringList', ], ], ], 'AwsDmsEndpointDetails' => [ 'type' => 'structure', 'members' => [ 'CertificateArn' => [ 'shape' => 'NonEmptyString', ], 'DatabaseName' => [ 'shape' => 'NonEmptyString', ], 'EndpointArn' => [ 'shape' => 'NonEmptyString', ], 'EndpointIdentifier' => [ 'shape' => 'NonEmptyString', ], 'EndpointType' => [ 'shape' => 'NonEmptyString', ], 'EngineName' => [ 'shape' => 'NonEmptyString', ], 'ExternalId' => [ 'shape' => 'NonEmptyString', ], 'ExtraConnectionAttributes' => [ 'shape' => 'NonEmptyString', ], 'KmsKeyId' => [ 'shape' => 'NonEmptyString', ], 'Port' => [ 'shape' => 'Integer', ], 'ServerName' => [ 'shape' => 'NonEmptyString', ], 'SslMode' => [ 'shape' => 'NonEmptyString', ], 'Username' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsDmsReplicationInstanceDetails' => [ 'type' => 'structure', 'members' => [ 'AllocatedStorage' => [ 'shape' => 'Integer', ], 'AutoMinorVersionUpgrade' => [ 'shape' => 'Boolean', ], 'AvailabilityZone' => [ 'shape' => 'NonEmptyString', ], 'EngineVersion' => [ 'shape' => 'NonEmptyString', ], 'KmsKeyId' => [ 'shape' => 'NonEmptyString', ], 'MultiAZ' => [ 'shape' => 'Boolean', ], 'PreferredMaintenanceWindow' => [ 'shape' => 'NonEmptyString', ], 'PubliclyAccessible' => [ 'shape' => 'Boolean', ], 'ReplicationInstanceClass' => [ 'shape' => 'NonEmptyString', ], 'ReplicationInstanceIdentifier' => [ 'shape' => 'NonEmptyString', ], 'ReplicationSubnetGroup' => [ 'shape' => 'AwsDmsReplicationInstanceReplicationSubnetGroupDetails', ], 'VpcSecurityGroups' => [ 'shape' => 'AwsDmsReplicationInstanceVpcSecurityGroupsList', ], ], ], 'AwsDmsReplicationInstanceReplicationSubnetGroupDetails' => [ 'type' => 'structure', 'members' => [ 'ReplicationSubnetGroupIdentifier' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsDmsReplicationInstanceVpcSecurityGroupsDetails' => [ 'type' => 'structure', 'members' => [ 'VpcSecurityGroupId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsDmsReplicationInstanceVpcSecurityGroupsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsDmsReplicationInstanceVpcSecurityGroupsDetails', ], ], 'AwsDmsReplicationTaskDetails' => [ 'type' => 'structure', 'members' => [ 'CdcStartPosition' => [ 'shape' => 'NonEmptyString', ], 'CdcStartTime' => [ 'shape' => 'NonEmptyString', ], 'CdcStopPosition' => [ 'shape' => 'NonEmptyString', ], 'MigrationType' => [ 'shape' => 'NonEmptyString', ], 'Id' => [ 'shape' => 'NonEmptyString', ], 'ResourceIdentifier' => [ 'shape' => 'NonEmptyString', ], 'ReplicationInstanceArn' => [ 'shape' => 'NonEmptyString', ], 'ReplicationTaskIdentifier' => [ 'shape' => 'NonEmptyString', ], 'ReplicationTaskSettings' => [ 'shape' => 'NonEmptyString', ], 'SourceEndpointArn' => [ 'shape' => 'NonEmptyString', ], 'TableMappings' => [ 'shape' => 'NonEmptyString', ], 'TargetEndpointArn' => [ 'shape' => 'NonEmptyString', ], 'TaskData' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsDynamoDbTableAttributeDefinition' => [ 'type' => 'structure', 'members' => [ 'AttributeName' => [ 'shape' => 'NonEmptyString', ], 'AttributeType' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsDynamoDbTableAttributeDefinitionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsDynamoDbTableAttributeDefinition', ], ], 'AwsDynamoDbTableBillingModeSummary' => [ 'type' => 'structure', 'members' => [ 'BillingMode' => [ 'shape' => 'NonEmptyString', ], 'LastUpdateToPayPerRequestDateTime' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsDynamoDbTableDetails' => [ 'type' => 'structure', 'members' => [ 'AttributeDefinitions' => [ 'shape' => 'AwsDynamoDbTableAttributeDefinitionList', ], 'BillingModeSummary' => [ 'shape' => 'AwsDynamoDbTableBillingModeSummary', ], 'CreationDateTime' => [ 'shape' => 'NonEmptyString', ], 'GlobalSecondaryIndexes' => [ 'shape' => 'AwsDynamoDbTableGlobalSecondaryIndexList', ], 'GlobalTableVersion' => [ 'shape' => 'NonEmptyString', ], 'ItemCount' => [ 'shape' => 'Integer', ], 'KeySchema' => [ 'shape' => 'AwsDynamoDbTableKeySchemaList', ], 'LatestStreamArn' => [ 'shape' => 'NonEmptyString', ], 'LatestStreamLabel' => [ 'shape' => 'NonEmptyString', ], 'LocalSecondaryIndexes' => [ 'shape' => 'AwsDynamoDbTableLocalSecondaryIndexList', ], 'ProvisionedThroughput' => [ 'shape' => 'AwsDynamoDbTableProvisionedThroughput', ], 'Replicas' => [ 'shape' => 'AwsDynamoDbTableReplicaList', ], 'RestoreSummary' => [ 'shape' => 'AwsDynamoDbTableRestoreSummary', ], 'SseDescription' => [ 'shape' => 'AwsDynamoDbTableSseDescription', ], 'StreamSpecification' => [ 'shape' => 'AwsDynamoDbTableStreamSpecification', ], 'TableId' => [ 'shape' => 'NonEmptyString', ], 'TableName' => [ 'shape' => 'NonEmptyString', ], 'TableSizeBytes' => [ 'shape' => 'SizeBytes', ], 'TableStatus' => [ 'shape' => 'NonEmptyString', ], 'DeletionProtectionEnabled' => [ 'shape' => 'Boolean', ], ], ], 'AwsDynamoDbTableGlobalSecondaryIndex' => [ 'type' => 'structure', 'members' => [ 'Backfilling' => [ 'shape' => 'Boolean', ], 'IndexArn' => [ 'shape' => 'NonEmptyString', ], 'IndexName' => [ 'shape' => 'NonEmptyString', ], 'IndexSizeBytes' => [ 'shape' => 'SizeBytes', ], 'IndexStatus' => [ 'shape' => 'NonEmptyString', ], 'ItemCount' => [ 'shape' => 'Integer', ], 'KeySchema' => [ 'shape' => 'AwsDynamoDbTableKeySchemaList', ], 'Projection' => [ 'shape' => 'AwsDynamoDbTableProjection', ], 'ProvisionedThroughput' => [ 'shape' => 'AwsDynamoDbTableProvisionedThroughput', ], ], ], 'AwsDynamoDbTableGlobalSecondaryIndexList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsDynamoDbTableGlobalSecondaryIndex', ], ], 'AwsDynamoDbTableKeySchema' => [ 'type' => 'structure', 'members' => [ 'AttributeName' => [ 'shape' => 'NonEmptyString', ], 'KeyType' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsDynamoDbTableKeySchemaList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsDynamoDbTableKeySchema', ], ], 'AwsDynamoDbTableLocalSecondaryIndex' => [ 'type' => 'structure', 'members' => [ 'IndexArn' => [ 'shape' => 'NonEmptyString', ], 'IndexName' => [ 'shape' => 'NonEmptyString', ], 'KeySchema' => [ 'shape' => 'AwsDynamoDbTableKeySchemaList', ], 'Projection' => [ 'shape' => 'AwsDynamoDbTableProjection', ], ], ], 'AwsDynamoDbTableLocalSecondaryIndexList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsDynamoDbTableLocalSecondaryIndex', ], ], 'AwsDynamoDbTableProjection' => [ 'type' => 'structure', 'members' => [ 'NonKeyAttributes' => [ 'shape' => 'StringList', ], 'ProjectionType' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsDynamoDbTableProvisionedThroughput' => [ 'type' => 'structure', 'members' => [ 'LastDecreaseDateTime' => [ 'shape' => 'NonEmptyString', ], 'LastIncreaseDateTime' => [ 'shape' => 'NonEmptyString', ], 'NumberOfDecreasesToday' => [ 'shape' => 'Integer', ], 'ReadCapacityUnits' => [ 'shape' => 'Integer', ], 'WriteCapacityUnits' => [ 'shape' => 'Integer', ], ], ], 'AwsDynamoDbTableProvisionedThroughputOverride' => [ 'type' => 'structure', 'members' => [ 'ReadCapacityUnits' => [ 'shape' => 'Integer', ], ], ], 'AwsDynamoDbTableReplica' => [ 'type' => 'structure', 'members' => [ 'GlobalSecondaryIndexes' => [ 'shape' => 'AwsDynamoDbTableReplicaGlobalSecondaryIndexList', ], 'KmsMasterKeyId' => [ 'shape' => 'NonEmptyString', ], 'ProvisionedThroughputOverride' => [ 'shape' => 'AwsDynamoDbTableProvisionedThroughputOverride', ], 'RegionName' => [ 'shape' => 'NonEmptyString', ], 'ReplicaStatus' => [ 'shape' => 'NonEmptyString', ], 'ReplicaStatusDescription' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsDynamoDbTableReplicaGlobalSecondaryIndex' => [ 'type' => 'structure', 'members' => [ 'IndexName' => [ 'shape' => 'NonEmptyString', ], 'ProvisionedThroughputOverride' => [ 'shape' => 'AwsDynamoDbTableProvisionedThroughputOverride', ], ], ], 'AwsDynamoDbTableReplicaGlobalSecondaryIndexList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsDynamoDbTableReplicaGlobalSecondaryIndex', ], ], 'AwsDynamoDbTableReplicaList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsDynamoDbTableReplica', ], ], 'AwsDynamoDbTableRestoreSummary' => [ 'type' => 'structure', 'members' => [ 'SourceBackupArn' => [ 'shape' => 'NonEmptyString', ], 'SourceTableArn' => [ 'shape' => 'NonEmptyString', ], 'RestoreDateTime' => [ 'shape' => 'NonEmptyString', ], 'RestoreInProgress' => [ 'shape' => 'Boolean', ], ], ], 'AwsDynamoDbTableSseDescription' => [ 'type' => 'structure', 'members' => [ 'InaccessibleEncryptionDateTime' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], 'SseType' => [ 'shape' => 'NonEmptyString', ], 'KmsMasterKeyArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsDynamoDbTableStreamSpecification' => [ 'type' => 'structure', 'members' => [ 'StreamEnabled' => [ 'shape' => 'Boolean', ], 'StreamViewType' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2ClientVpnEndpointAuthenticationOptionsActiveDirectoryDetails' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2ClientVpnEndpointAuthenticationOptionsDetails' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'NonEmptyString', ], 'ActiveDirectory' => [ 'shape' => 'AwsEc2ClientVpnEndpointAuthenticationOptionsActiveDirectoryDetails', ], 'MutualAuthentication' => [ 'shape' => 'AwsEc2ClientVpnEndpointAuthenticationOptionsMutualAuthenticationDetails', ], 'FederatedAuthentication' => [ 'shape' => 'AwsEc2ClientVpnEndpointAuthenticationOptionsFederatedAuthenticationDetails', ], ], ], 'AwsEc2ClientVpnEndpointAuthenticationOptionsFederatedAuthenticationDetails' => [ 'type' => 'structure', 'members' => [ 'SamlProviderArn' => [ 'shape' => 'NonEmptyString', ], 'SelfServiceSamlProviderArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2ClientVpnEndpointAuthenticationOptionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEc2ClientVpnEndpointAuthenticationOptionsDetails', ], ], 'AwsEc2ClientVpnEndpointAuthenticationOptionsMutualAuthenticationDetails' => [ 'type' => 'structure', 'members' => [ 'ClientRootCertificateChain' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2ClientVpnEndpointClientConnectOptionsDetails' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], 'LambdaFunctionArn' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'AwsEc2ClientVpnEndpointClientConnectOptionsStatusDetails', ], ], ], 'AwsEc2ClientVpnEndpointClientConnectOptionsStatusDetails' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'NonEmptyString', ], 'Message' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2ClientVpnEndpointClientLoginBannerOptionsDetails' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], 'BannerText' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2ClientVpnEndpointConnectionLogOptionsDetails' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], 'CloudwatchLogGroup' => [ 'shape' => 'NonEmptyString', ], 'CloudwatchLogStream' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2ClientVpnEndpointDetails' => [ 'type' => 'structure', 'members' => [ 'ClientVpnEndpointId' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'ClientCidrBlock' => [ 'shape' => 'NonEmptyString', ], 'DnsServer' => [ 'shape' => 'StringList', ], 'SplitTunnel' => [ 'shape' => 'Boolean', ], 'TransportProtocol' => [ 'shape' => 'NonEmptyString', ], 'VpnPort' => [ 'shape' => 'Integer', ], 'ServerCertificateArn' => [ 'shape' => 'NonEmptyString', ], 'AuthenticationOptions' => [ 'shape' => 'AwsEc2ClientVpnEndpointAuthenticationOptionsList', ], 'ConnectionLogOptions' => [ 'shape' => 'AwsEc2ClientVpnEndpointConnectionLogOptionsDetails', ], 'SecurityGroupIdSet' => [ 'shape' => 'StringList', ], 'VpcId' => [ 'shape' => 'NonEmptyString', ], 'SelfServicePortalUrl' => [ 'shape' => 'NonEmptyString', ], 'ClientConnectOptions' => [ 'shape' => 'AwsEc2ClientVpnEndpointClientConnectOptionsDetails', ], 'SessionTimeoutHours' => [ 'shape' => 'Integer', ], 'ClientLoginBannerOptions' => [ 'shape' => 'AwsEc2ClientVpnEndpointClientLoginBannerOptionsDetails', ], ], ], 'AwsEc2EipDetails' => [ 'type' => 'structure', 'members' => [ 'InstanceId' => [ 'shape' => 'NonEmptyString', ], 'PublicIp' => [ 'shape' => 'NonEmptyString', ], 'AllocationId' => [ 'shape' => 'NonEmptyString', ], 'AssociationId' => [ 'shape' => 'NonEmptyString', ], 'Domain' => [ 'shape' => 'NonEmptyString', ], 'PublicIpv4Pool' => [ 'shape' => 'NonEmptyString', ], 'NetworkBorderGroup' => [ 'shape' => 'NonEmptyString', ], 'NetworkInterfaceId' => [ 'shape' => 'NonEmptyString', ], 'NetworkInterfaceOwnerId' => [ 'shape' => 'NonEmptyString', ], 'PrivateIpAddress' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2InstanceDetails' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'NonEmptyString', ], 'ImageId' => [ 'shape' => 'NonEmptyString', ], 'IpV4Addresses' => [ 'shape' => 'StringList', ], 'IpV6Addresses' => [ 'shape' => 'StringList', ], 'KeyName' => [ 'shape' => 'NonEmptyString', ], 'IamInstanceProfileArn' => [ 'shape' => 'NonEmptyString', ], 'VpcId' => [ 'shape' => 'NonEmptyString', ], 'SubnetId' => [ 'shape' => 'NonEmptyString', ], 'LaunchedAt' => [ 'shape' => 'NonEmptyString', ], 'NetworkInterfaces' => [ 'shape' => 'AwsEc2InstanceNetworkInterfacesList', ], 'VirtualizationType' => [ 'shape' => 'NonEmptyString', ], 'MetadataOptions' => [ 'shape' => 'AwsEc2InstanceMetadataOptions', ], 'Monitoring' => [ 'shape' => 'AwsEc2InstanceMonitoringDetails', ], ], ], 'AwsEc2InstanceMetadataOptions' => [ 'type' => 'structure', 'members' => [ 'HttpEndpoint' => [ 'shape' => 'NonEmptyString', ], 'HttpProtocolIpv6' => [ 'shape' => 'NonEmptyString', ], 'HttpPutResponseHopLimit' => [ 'shape' => 'Integer', ], 'HttpTokens' => [ 'shape' => 'NonEmptyString', ], 'InstanceMetadataTags' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2InstanceMonitoringDetails' => [ 'type' => 'structure', 'members' => [ 'State' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2InstanceNetworkInterfacesDetails' => [ 'type' => 'structure', 'members' => [ 'NetworkInterfaceId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2InstanceNetworkInterfacesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEc2InstanceNetworkInterfacesDetails', ], ], 'AwsEc2LaunchTemplateDataBlockDeviceMappingSetDetails' => [ 'type' => 'structure', 'members' => [ 'DeviceName' => [ 'shape' => 'NonEmptyString', ], 'Ebs' => [ 'shape' => 'AwsEc2LaunchTemplateDataBlockDeviceMappingSetEbsDetails', ], 'NoDevice' => [ 'shape' => 'NonEmptyString', ], 'VirtualName' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2LaunchTemplateDataBlockDeviceMappingSetEbsDetails' => [ 'type' => 'structure', 'members' => [ 'DeleteOnTermination' => [ 'shape' => 'Boolean', ], 'Encrypted' => [ 'shape' => 'Boolean', ], 'Iops' => [ 'shape' => 'Integer', ], 'KmsKeyId' => [ 'shape' => 'NonEmptyString', ], 'SnapshotId' => [ 'shape' => 'NonEmptyString', ], 'Throughput' => [ 'shape' => 'Integer', ], 'VolumeSize' => [ 'shape' => 'Integer', ], 'VolumeType' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2LaunchTemplateDataBlockDeviceMappingSetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEc2LaunchTemplateDataBlockDeviceMappingSetDetails', ], ], 'AwsEc2LaunchTemplateDataCapacityReservationSpecificationCapacityReservationTargetDetails' => [ 'type' => 'structure', 'members' => [ 'CapacityReservationId' => [ 'shape' => 'NonEmptyString', ], 'CapacityReservationResourceGroupArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2LaunchTemplateDataCapacityReservationSpecificationDetails' => [ 'type' => 'structure', 'members' => [ 'CapacityReservationPreference' => [ 'shape' => 'NonEmptyString', ], 'CapacityReservationTarget' => [ 'shape' => 'AwsEc2LaunchTemplateDataCapacityReservationSpecificationCapacityReservationTargetDetails', ], ], ], 'AwsEc2LaunchTemplateDataCpuOptionsDetails' => [ 'type' => 'structure', 'members' => [ 'CoreCount' => [ 'shape' => 'Integer', ], 'ThreadsPerCore' => [ 'shape' => 'Integer', ], ], ], 'AwsEc2LaunchTemplateDataCreditSpecificationDetails' => [ 'type' => 'structure', 'members' => [ 'CpuCredits' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2LaunchTemplateDataDetails' => [ 'type' => 'structure', 'members' => [ 'BlockDeviceMappingSet' => [ 'shape' => 'AwsEc2LaunchTemplateDataBlockDeviceMappingSetList', ], 'CapacityReservationSpecification' => [ 'shape' => 'AwsEc2LaunchTemplateDataCapacityReservationSpecificationDetails', ], 'CpuOptions' => [ 'shape' => 'AwsEc2LaunchTemplateDataCpuOptionsDetails', ], 'CreditSpecification' => [ 'shape' => 'AwsEc2LaunchTemplateDataCreditSpecificationDetails', ], 'DisableApiStop' => [ 'shape' => 'Boolean', ], 'DisableApiTermination' => [ 'shape' => 'Boolean', ], 'EbsOptimized' => [ 'shape' => 'Boolean', ], 'ElasticGpuSpecificationSet' => [ 'shape' => 'AwsEc2LaunchTemplateDataElasticGpuSpecificationSetList', ], 'ElasticInferenceAcceleratorSet' => [ 'shape' => 'AwsEc2LaunchTemplateDataElasticInferenceAcceleratorSetList', ], 'EnclaveOptions' => [ 'shape' => 'AwsEc2LaunchTemplateDataEnclaveOptionsDetails', ], 'HibernationOptions' => [ 'shape' => 'AwsEc2LaunchTemplateDataHibernationOptionsDetails', ], 'IamInstanceProfile' => [ 'shape' => 'AwsEc2LaunchTemplateDataIamInstanceProfileDetails', ], 'ImageId' => [ 'shape' => 'NonEmptyString', ], 'InstanceInitiatedShutdownBehavior' => [ 'shape' => 'NonEmptyString', ], 'InstanceMarketOptions' => [ 'shape' => 'AwsEc2LaunchTemplateDataInstanceMarketOptionsDetails', ], 'InstanceRequirements' => [ 'shape' => 'AwsEc2LaunchTemplateDataInstanceRequirementsDetails', ], 'InstanceType' => [ 'shape' => 'NonEmptyString', ], 'KernelId' => [ 'shape' => 'NonEmptyString', ], 'KeyName' => [ 'shape' => 'NonEmptyString', ], 'LicenseSet' => [ 'shape' => 'AwsEc2LaunchTemplateDataLicenseSetList', ], 'MaintenanceOptions' => [ 'shape' => 'AwsEc2LaunchTemplateDataMaintenanceOptionsDetails', ], 'MetadataOptions' => [ 'shape' => 'AwsEc2LaunchTemplateDataMetadataOptionsDetails', ], 'Monitoring' => [ 'shape' => 'AwsEc2LaunchTemplateDataMonitoringDetails', ], 'NetworkInterfaceSet' => [ 'shape' => 'AwsEc2LaunchTemplateDataNetworkInterfaceSetList', ], 'Placement' => [ 'shape' => 'AwsEc2LaunchTemplateDataPlacementDetails', ], 'PrivateDnsNameOptions' => [ 'shape' => 'AwsEc2LaunchTemplateDataPrivateDnsNameOptionsDetails', ], 'RamDiskId' => [ 'shape' => 'NonEmptyString', ], 'SecurityGroupIdSet' => [ 'shape' => 'NonEmptyStringList', ], 'SecurityGroupSet' => [ 'shape' => 'NonEmptyStringList', ], 'UserData' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2LaunchTemplateDataElasticGpuSpecificationSetDetails' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2LaunchTemplateDataElasticGpuSpecificationSetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEc2LaunchTemplateDataElasticGpuSpecificationSetDetails', ], ], 'AwsEc2LaunchTemplateDataElasticInferenceAcceleratorSetDetails' => [ 'type' => 'structure', 'members' => [ 'Count' => [ 'shape' => 'Integer', ], 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2LaunchTemplateDataElasticInferenceAcceleratorSetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEc2LaunchTemplateDataElasticInferenceAcceleratorSetDetails', ], ], 'AwsEc2LaunchTemplateDataEnclaveOptionsDetails' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], ], ], 'AwsEc2LaunchTemplateDataHibernationOptionsDetails' => [ 'type' => 'structure', 'members' => [ 'Configured' => [ 'shape' => 'Boolean', ], ], ], 'AwsEc2LaunchTemplateDataIamInstanceProfileDetails' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'NonEmptyString', ], 'Name' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2LaunchTemplateDataInstanceMarketOptionsDetails' => [ 'type' => 'structure', 'members' => [ 'MarketType' => [ 'shape' => 'NonEmptyString', ], 'SpotOptions' => [ 'shape' => 'AwsEc2LaunchTemplateDataInstanceMarketOptionsSpotOptionsDetails', ], ], ], 'AwsEc2LaunchTemplateDataInstanceMarketOptionsSpotOptionsDetails' => [ 'type' => 'structure', 'members' => [ 'BlockDurationMinutes' => [ 'shape' => 'Integer', ], 'InstanceInterruptionBehavior' => [ 'shape' => 'NonEmptyString', ], 'MaxPrice' => [ 'shape' => 'NonEmptyString', ], 'SpotInstanceType' => [ 'shape' => 'NonEmptyString', ], 'ValidUntil' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2LaunchTemplateDataInstanceRequirementsAcceleratorCountDetails' => [ 'type' => 'structure', 'members' => [ 'Max' => [ 'shape' => 'Integer', ], 'Min' => [ 'shape' => 'Integer', ], ], ], 'AwsEc2LaunchTemplateDataInstanceRequirementsAcceleratorTotalMemoryMiBDetails' => [ 'type' => 'structure', 'members' => [ 'Max' => [ 'shape' => 'Integer', ], 'Min' => [ 'shape' => 'Integer', ], ], ], 'AwsEc2LaunchTemplateDataInstanceRequirementsBaselineEbsBandwidthMbpsDetails' => [ 'type' => 'structure', 'members' => [ 'Max' => [ 'shape' => 'Integer', ], 'Min' => [ 'shape' => 'Integer', ], ], ], 'AwsEc2LaunchTemplateDataInstanceRequirementsDetails' => [ 'type' => 'structure', 'members' => [ 'AcceleratorCount' => [ 'shape' => 'AwsEc2LaunchTemplateDataInstanceRequirementsAcceleratorCountDetails', ], 'AcceleratorManufacturers' => [ 'shape' => 'NonEmptyStringList', ], 'AcceleratorNames' => [ 'shape' => 'NonEmptyStringList', ], 'AcceleratorTotalMemoryMiB' => [ 'shape' => 'AwsEc2LaunchTemplateDataInstanceRequirementsAcceleratorTotalMemoryMiBDetails', ], 'AcceleratorTypes' => [ 'shape' => 'NonEmptyStringList', ], 'BareMetal' => [ 'shape' => 'NonEmptyString', ], 'BaselineEbsBandwidthMbps' => [ 'shape' => 'AwsEc2LaunchTemplateDataInstanceRequirementsBaselineEbsBandwidthMbpsDetails', ], 'BurstablePerformance' => [ 'shape' => 'NonEmptyString', ], 'CpuManufacturers' => [ 'shape' => 'NonEmptyStringList', ], 'ExcludedInstanceTypes' => [ 'shape' => 'NonEmptyStringList', ], 'InstanceGenerations' => [ 'shape' => 'NonEmptyStringList', ], 'LocalStorage' => [ 'shape' => 'NonEmptyString', ], 'LocalStorageTypes' => [ 'shape' => 'NonEmptyStringList', ], 'MemoryGiBPerVCpu' => [ 'shape' => 'AwsEc2LaunchTemplateDataInstanceRequirementsMemoryGiBPerVCpuDetails', ], 'MemoryMiB' => [ 'shape' => 'AwsEc2LaunchTemplateDataInstanceRequirementsMemoryMiBDetails', ], 'NetworkInterfaceCount' => [ 'shape' => 'AwsEc2LaunchTemplateDataInstanceRequirementsNetworkInterfaceCountDetails', ], 'OnDemandMaxPricePercentageOverLowestPrice' => [ 'shape' => 'Integer', ], 'RequireHibernateSupport' => [ 'shape' => 'Boolean', ], 'SpotMaxPricePercentageOverLowestPrice' => [ 'shape' => 'Integer', ], 'TotalLocalStorageGB' => [ 'shape' => 'AwsEc2LaunchTemplateDataInstanceRequirementsTotalLocalStorageGBDetails', ], 'VCpuCount' => [ 'shape' => 'AwsEc2LaunchTemplateDataInstanceRequirementsVCpuCountDetails', ], ], ], 'AwsEc2LaunchTemplateDataInstanceRequirementsMemoryGiBPerVCpuDetails' => [ 'type' => 'structure', 'members' => [ 'Max' => [ 'shape' => 'Double', ], 'Min' => [ 'shape' => 'Double', ], ], ], 'AwsEc2LaunchTemplateDataInstanceRequirementsMemoryMiBDetails' => [ 'type' => 'structure', 'members' => [ 'Max' => [ 'shape' => 'Integer', ], 'Min' => [ 'shape' => 'Integer', ], ], ], 'AwsEc2LaunchTemplateDataInstanceRequirementsNetworkInterfaceCountDetails' => [ 'type' => 'structure', 'members' => [ 'Max' => [ 'shape' => 'Integer', ], 'Min' => [ 'shape' => 'Integer', ], ], ], 'AwsEc2LaunchTemplateDataInstanceRequirementsTotalLocalStorageGBDetails' => [ 'type' => 'structure', 'members' => [ 'Max' => [ 'shape' => 'Double', ], 'Min' => [ 'shape' => 'Double', ], ], ], 'AwsEc2LaunchTemplateDataInstanceRequirementsVCpuCountDetails' => [ 'type' => 'structure', 'members' => [ 'Max' => [ 'shape' => 'Integer', ], 'Min' => [ 'shape' => 'Integer', ], ], ], 'AwsEc2LaunchTemplateDataLicenseSetDetails' => [ 'type' => 'structure', 'members' => [ 'LicenseConfigurationArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2LaunchTemplateDataLicenseSetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEc2LaunchTemplateDataLicenseSetDetails', ], ], 'AwsEc2LaunchTemplateDataMaintenanceOptionsDetails' => [ 'type' => 'structure', 'members' => [ 'AutoRecovery' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2LaunchTemplateDataMetadataOptionsDetails' => [ 'type' => 'structure', 'members' => [ 'HttpEndpoint' => [ 'shape' => 'NonEmptyString', ], 'HttpProtocolIpv6' => [ 'shape' => 'NonEmptyString', ], 'HttpTokens' => [ 'shape' => 'NonEmptyString', ], 'HttpPutResponseHopLimit' => [ 'shape' => 'Integer', ], 'InstanceMetadataTags' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2LaunchTemplateDataMonitoringDetails' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], ], ], 'AwsEc2LaunchTemplateDataNetworkInterfaceSetDetails' => [ 'type' => 'structure', 'members' => [ 'AssociateCarrierIpAddress' => [ 'shape' => 'Boolean', ], 'AssociatePublicIpAddress' => [ 'shape' => 'Boolean', ], 'DeleteOnTermination' => [ 'shape' => 'Boolean', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'DeviceIndex' => [ 'shape' => 'Integer', ], 'Groups' => [ 'shape' => 'NonEmptyStringList', ], 'InterfaceType' => [ 'shape' => 'NonEmptyString', ], 'Ipv4PrefixCount' => [ 'shape' => 'Integer', ], 'Ipv4Prefixes' => [ 'shape' => 'AwsEc2LaunchTemplateDataNetworkInterfaceSetIpv4PrefixesList', ], 'Ipv6AddressCount' => [ 'shape' => 'Integer', ], 'Ipv6Addresses' => [ 'shape' => 'AwsEc2LaunchTemplateDataNetworkInterfaceSetIpv6AddressesList', ], 'Ipv6PrefixCount' => [ 'shape' => 'Integer', ], 'Ipv6Prefixes' => [ 'shape' => 'AwsEc2LaunchTemplateDataNetworkInterfaceSetIpv6PrefixesList', ], 'NetworkCardIndex' => [ 'shape' => 'Integer', ], 'NetworkInterfaceId' => [ 'shape' => 'NonEmptyString', ], 'PrivateIpAddress' => [ 'shape' => 'NonEmptyString', ], 'PrivateIpAddresses' => [ 'shape' => 'AwsEc2LaunchTemplateDataNetworkInterfaceSetPrivateIpAddressesList', ], 'SecondaryPrivateIpAddressCount' => [ 'shape' => 'Integer', ], 'SubnetId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2LaunchTemplateDataNetworkInterfaceSetIpv4PrefixesDetails' => [ 'type' => 'structure', 'members' => [ 'Ipv4Prefix' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2LaunchTemplateDataNetworkInterfaceSetIpv4PrefixesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEc2LaunchTemplateDataNetworkInterfaceSetIpv4PrefixesDetails', ], ], 'AwsEc2LaunchTemplateDataNetworkInterfaceSetIpv6AddressesDetails' => [ 'type' => 'structure', 'members' => [ 'Ipv6Address' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2LaunchTemplateDataNetworkInterfaceSetIpv6AddressesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEc2LaunchTemplateDataNetworkInterfaceSetIpv6AddressesDetails', ], ], 'AwsEc2LaunchTemplateDataNetworkInterfaceSetIpv6PrefixesDetails' => [ 'type' => 'structure', 'members' => [ 'Ipv6Prefix' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2LaunchTemplateDataNetworkInterfaceSetIpv6PrefixesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEc2LaunchTemplateDataNetworkInterfaceSetIpv6PrefixesDetails', ], ], 'AwsEc2LaunchTemplateDataNetworkInterfaceSetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEc2LaunchTemplateDataNetworkInterfaceSetDetails', ], ], 'AwsEc2LaunchTemplateDataNetworkInterfaceSetPrivateIpAddressesDetails' => [ 'type' => 'structure', 'members' => [ 'Primary' => [ 'shape' => 'Boolean', ], 'PrivateIpAddress' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2LaunchTemplateDataNetworkInterfaceSetPrivateIpAddressesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEc2LaunchTemplateDataNetworkInterfaceSetPrivateIpAddressesDetails', ], ], 'AwsEc2LaunchTemplateDataPlacementDetails' => [ 'type' => 'structure', 'members' => [ 'Affinity' => [ 'shape' => 'NonEmptyString', ], 'AvailabilityZone' => [ 'shape' => 'NonEmptyString', ], 'GroupName' => [ 'shape' => 'NonEmptyString', ], 'HostId' => [ 'shape' => 'NonEmptyString', ], 'HostResourceGroupArn' => [ 'shape' => 'NonEmptyString', ], 'PartitionNumber' => [ 'shape' => 'Integer', ], 'SpreadDomain' => [ 'shape' => 'NonEmptyString', ], 'Tenancy' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2LaunchTemplateDataPrivateDnsNameOptionsDetails' => [ 'type' => 'structure', 'members' => [ 'EnableResourceNameDnsAAAARecord' => [ 'shape' => 'Boolean', ], 'EnableResourceNameDnsARecord' => [ 'shape' => 'Boolean', ], 'HostnameType' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2LaunchTemplateDetails' => [ 'type' => 'structure', 'members' => [ 'LaunchTemplateName' => [ 'shape' => 'NonEmptyString', ], 'Id' => [ 'shape' => 'NonEmptyString', ], 'LaunchTemplateData' => [ 'shape' => 'AwsEc2LaunchTemplateDataDetails', ], 'DefaultVersionNumber' => [ 'shape' => 'Long', ], 'LatestVersionNumber' => [ 'shape' => 'Long', ], ], ], 'AwsEc2NetworkAclAssociation' => [ 'type' => 'structure', 'members' => [ 'NetworkAclAssociationId' => [ 'shape' => 'NonEmptyString', ], 'NetworkAclId' => [ 'shape' => 'NonEmptyString', ], 'SubnetId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2NetworkAclAssociationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEc2NetworkAclAssociation', ], ], 'AwsEc2NetworkAclDetails' => [ 'type' => 'structure', 'members' => [ 'IsDefault' => [ 'shape' => 'Boolean', ], 'NetworkAclId' => [ 'shape' => 'NonEmptyString', ], 'OwnerId' => [ 'shape' => 'NonEmptyString', ], 'VpcId' => [ 'shape' => 'NonEmptyString', ], 'Associations' => [ 'shape' => 'AwsEc2NetworkAclAssociationList', ], 'Entries' => [ 'shape' => 'AwsEc2NetworkAclEntryList', ], ], ], 'AwsEc2NetworkAclEntry' => [ 'type' => 'structure', 'members' => [ 'CidrBlock' => [ 'shape' => 'NonEmptyString', ], 'Egress' => [ 'shape' => 'Boolean', ], 'IcmpTypeCode' => [ 'shape' => 'IcmpTypeCode', ], 'Ipv6CidrBlock' => [ 'shape' => 'NonEmptyString', ], 'PortRange' => [ 'shape' => 'PortRangeFromTo', ], 'Protocol' => [ 'shape' => 'NonEmptyString', ], 'RuleAction' => [ 'shape' => 'NonEmptyString', ], 'RuleNumber' => [ 'shape' => 'Integer', ], ], ], 'AwsEc2NetworkAclEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEc2NetworkAclEntry', ], ], 'AwsEc2NetworkInterfaceAttachment' => [ 'type' => 'structure', 'members' => [ 'AttachTime' => [ 'shape' => 'NonEmptyString', ], 'AttachmentId' => [ 'shape' => 'NonEmptyString', ], 'DeleteOnTermination' => [ 'shape' => 'Boolean', ], 'DeviceIndex' => [ 'shape' => 'Integer', ], 'InstanceId' => [ 'shape' => 'NonEmptyString', ], 'InstanceOwnerId' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2NetworkInterfaceDetails' => [ 'type' => 'structure', 'members' => [ 'Attachment' => [ 'shape' => 'AwsEc2NetworkInterfaceAttachment', ], 'NetworkInterfaceId' => [ 'shape' => 'NonEmptyString', ], 'SecurityGroups' => [ 'shape' => 'AwsEc2NetworkInterfaceSecurityGroupList', ], 'SourceDestCheck' => [ 'shape' => 'Boolean', ], 'IpV6Addresses' => [ 'shape' => 'AwsEc2NetworkInterfaceIpV6AddressList', ], 'PrivateIpAddresses' => [ 'shape' => 'AwsEc2NetworkInterfacePrivateIpAddressList', ], 'PublicDnsName' => [ 'shape' => 'NonEmptyString', ], 'PublicIp' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2NetworkInterfaceIpV6AddressDetail' => [ 'type' => 'structure', 'members' => [ 'IpV6Address' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2NetworkInterfaceIpV6AddressList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEc2NetworkInterfaceIpV6AddressDetail', ], ], 'AwsEc2NetworkInterfacePrivateIpAddressDetail' => [ 'type' => 'structure', 'members' => [ 'PrivateIpAddress' => [ 'shape' => 'NonEmptyString', ], 'PrivateDnsName' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2NetworkInterfacePrivateIpAddressList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEc2NetworkInterfacePrivateIpAddressDetail', ], ], 'AwsEc2NetworkInterfaceSecurityGroup' => [ 'type' => 'structure', 'members' => [ 'GroupName' => [ 'shape' => 'NonEmptyString', ], 'GroupId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2NetworkInterfaceSecurityGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEc2NetworkInterfaceSecurityGroup', ], ], 'AwsEc2RouteTableDetails' => [ 'type' => 'structure', 'members' => [ 'AssociationSet' => [ 'shape' => 'AssociationSetList', ], 'OwnerId' => [ 'shape' => 'NonEmptyString', ], 'PropagatingVgwSet' => [ 'shape' => 'PropagatingVgwSetList', ], 'RouteTableId' => [ 'shape' => 'NonEmptyString', ], 'RouteSet' => [ 'shape' => 'RouteSetList', ], 'VpcId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2SecurityGroupDetails' => [ 'type' => 'structure', 'members' => [ 'GroupName' => [ 'shape' => 'NonEmptyString', ], 'GroupId' => [ 'shape' => 'NonEmptyString', ], 'OwnerId' => [ 'shape' => 'NonEmptyString', ], 'VpcId' => [ 'shape' => 'NonEmptyString', ], 'IpPermissions' => [ 'shape' => 'AwsEc2SecurityGroupIpPermissionList', ], 'IpPermissionsEgress' => [ 'shape' => 'AwsEc2SecurityGroupIpPermissionList', ], ], ], 'AwsEc2SecurityGroupIpPermission' => [ 'type' => 'structure', 'members' => [ 'IpProtocol' => [ 'shape' => 'NonEmptyString', ], 'FromPort' => [ 'shape' => 'Integer', ], 'ToPort' => [ 'shape' => 'Integer', ], 'UserIdGroupPairs' => [ 'shape' => 'AwsEc2SecurityGroupUserIdGroupPairList', ], 'IpRanges' => [ 'shape' => 'AwsEc2SecurityGroupIpRangeList', ], 'Ipv6Ranges' => [ 'shape' => 'AwsEc2SecurityGroupIpv6RangeList', ], 'PrefixListIds' => [ 'shape' => 'AwsEc2SecurityGroupPrefixListIdList', ], ], ], 'AwsEc2SecurityGroupIpPermissionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEc2SecurityGroupIpPermission', ], ], 'AwsEc2SecurityGroupIpRange' => [ 'type' => 'structure', 'members' => [ 'CidrIp' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2SecurityGroupIpRangeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEc2SecurityGroupIpRange', ], ], 'AwsEc2SecurityGroupIpv6Range' => [ 'type' => 'structure', 'members' => [ 'CidrIpv6' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2SecurityGroupIpv6RangeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEc2SecurityGroupIpv6Range', ], ], 'AwsEc2SecurityGroupPrefixListId' => [ 'type' => 'structure', 'members' => [ 'PrefixListId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2SecurityGroupPrefixListIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEc2SecurityGroupPrefixListId', ], ], 'AwsEc2SecurityGroupUserIdGroupPair' => [ 'type' => 'structure', 'members' => [ 'GroupId' => [ 'shape' => 'NonEmptyString', ], 'GroupName' => [ 'shape' => 'NonEmptyString', ], 'PeeringStatus' => [ 'shape' => 'NonEmptyString', ], 'UserId' => [ 'shape' => 'NonEmptyString', ], 'VpcId' => [ 'shape' => 'NonEmptyString', ], 'VpcPeeringConnectionId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2SecurityGroupUserIdGroupPairList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEc2SecurityGroupUserIdGroupPair', ], ], 'AwsEc2SubnetDetails' => [ 'type' => 'structure', 'members' => [ 'AssignIpv6AddressOnCreation' => [ 'shape' => 'Boolean', ], 'AvailabilityZone' => [ 'shape' => 'NonEmptyString', ], 'AvailabilityZoneId' => [ 'shape' => 'NonEmptyString', ], 'AvailableIpAddressCount' => [ 'shape' => 'Integer', ], 'CidrBlock' => [ 'shape' => 'NonEmptyString', ], 'DefaultForAz' => [ 'shape' => 'Boolean', ], 'MapPublicIpOnLaunch' => [ 'shape' => 'Boolean', ], 'OwnerId' => [ 'shape' => 'NonEmptyString', ], 'State' => [ 'shape' => 'NonEmptyString', ], 'SubnetArn' => [ 'shape' => 'NonEmptyString', ], 'SubnetId' => [ 'shape' => 'NonEmptyString', ], 'VpcId' => [ 'shape' => 'NonEmptyString', ], 'Ipv6CidrBlockAssociationSet' => [ 'shape' => 'Ipv6CidrBlockAssociationList', ], ], ], 'AwsEc2TransitGatewayDetails' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'DefaultRouteTablePropagation' => [ 'shape' => 'NonEmptyString', ], 'AutoAcceptSharedAttachments' => [ 'shape' => 'NonEmptyString', ], 'DefaultRouteTableAssociation' => [ 'shape' => 'NonEmptyString', ], 'TransitGatewayCidrBlocks' => [ 'shape' => 'NonEmptyStringList', ], 'AssociationDefaultRouteTableId' => [ 'shape' => 'NonEmptyString', ], 'PropagationDefaultRouteTableId' => [ 'shape' => 'NonEmptyString', ], 'VpnEcmpSupport' => [ 'shape' => 'NonEmptyString', ], 'DnsSupport' => [ 'shape' => 'NonEmptyString', ], 'MulticastSupport' => [ 'shape' => 'NonEmptyString', ], 'AmazonSideAsn' => [ 'shape' => 'Integer', ], ], ], 'AwsEc2VolumeAttachment' => [ 'type' => 'structure', 'members' => [ 'AttachTime' => [ 'shape' => 'NonEmptyString', ], 'DeleteOnTermination' => [ 'shape' => 'Boolean', ], 'InstanceId' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2VolumeAttachmentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEc2VolumeAttachment', ], ], 'AwsEc2VolumeDetails' => [ 'type' => 'structure', 'members' => [ 'CreateTime' => [ 'shape' => 'NonEmptyString', ], 'DeviceName' => [ 'shape' => 'NonEmptyString', ], 'Encrypted' => [ 'shape' => 'Boolean', ], 'Size' => [ 'shape' => 'Integer', ], 'SnapshotId' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], 'KmsKeyId' => [ 'shape' => 'NonEmptyString', ], 'Attachments' => [ 'shape' => 'AwsEc2VolumeAttachmentList', ], 'VolumeId' => [ 'shape' => 'NonEmptyString', ], 'VolumeType' => [ 'shape' => 'NonEmptyString', ], 'VolumeScanStatus' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2VpcDetails' => [ 'type' => 'structure', 'members' => [ 'CidrBlockAssociationSet' => [ 'shape' => 'CidrBlockAssociationList', ], 'Ipv6CidrBlockAssociationSet' => [ 'shape' => 'Ipv6CidrBlockAssociationList', ], 'DhcpOptionsId' => [ 'shape' => 'NonEmptyString', ], 'State' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2VpcEndpointServiceDetails' => [ 'type' => 'structure', 'members' => [ 'AcceptanceRequired' => [ 'shape' => 'Boolean', ], 'AvailabilityZones' => [ 'shape' => 'NonEmptyStringList', ], 'BaseEndpointDnsNames' => [ 'shape' => 'NonEmptyStringList', ], 'ManagesVpcEndpoints' => [ 'shape' => 'Boolean', ], 'GatewayLoadBalancerArns' => [ 'shape' => 'NonEmptyStringList', ], 'NetworkLoadBalancerArns' => [ 'shape' => 'NonEmptyStringList', ], 'PrivateDnsName' => [ 'shape' => 'NonEmptyString', ], 'ServiceId' => [ 'shape' => 'NonEmptyString', ], 'ServiceName' => [ 'shape' => 'NonEmptyString', ], 'ServiceState' => [ 'shape' => 'NonEmptyString', ], 'ServiceType' => [ 'shape' => 'AwsEc2VpcEndpointServiceServiceTypeList', ], ], ], 'AwsEc2VpcEndpointServiceServiceTypeDetails' => [ 'type' => 'structure', 'members' => [ 'ServiceType' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2VpcEndpointServiceServiceTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEc2VpcEndpointServiceServiceTypeDetails', ], ], 'AwsEc2VpcPeeringConnectionDetails' => [ 'type' => 'structure', 'members' => [ 'AccepterVpcInfo' => [ 'shape' => 'AwsEc2VpcPeeringConnectionVpcInfoDetails', ], 'ExpirationTime' => [ 'shape' => 'NonEmptyString', ], 'RequesterVpcInfo' => [ 'shape' => 'AwsEc2VpcPeeringConnectionVpcInfoDetails', ], 'Status' => [ 'shape' => 'AwsEc2VpcPeeringConnectionStatusDetails', ], 'VpcPeeringConnectionId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2VpcPeeringConnectionStatusDetails' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'NonEmptyString', ], 'Message' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2VpcPeeringConnectionVpcInfoDetails' => [ 'type' => 'structure', 'members' => [ 'CidrBlock' => [ 'shape' => 'NonEmptyString', ], 'CidrBlockSet' => [ 'shape' => 'VpcInfoCidrBlockSetList', ], 'Ipv6CidrBlockSet' => [ 'shape' => 'VpcInfoIpv6CidrBlockSetList', ], 'OwnerId' => [ 'shape' => 'NonEmptyString', ], 'PeeringOptions' => [ 'shape' => 'VpcInfoPeeringOptionsDetails', ], 'Region' => [ 'shape' => 'NonEmptyString', ], 'VpcId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2VpnConnectionDetails' => [ 'type' => 'structure', 'members' => [ 'VpnConnectionId' => [ 'shape' => 'NonEmptyString', ], 'State' => [ 'shape' => 'NonEmptyString', ], 'CustomerGatewayId' => [ 'shape' => 'NonEmptyString', ], 'CustomerGatewayConfiguration' => [ 'shape' => 'NonEmptyString', ], 'Type' => [ 'shape' => 'NonEmptyString', ], 'VpnGatewayId' => [ 'shape' => 'NonEmptyString', ], 'Category' => [ 'shape' => 'NonEmptyString', ], 'VgwTelemetry' => [ 'shape' => 'AwsEc2VpnConnectionVgwTelemetryList', ], 'Options' => [ 'shape' => 'AwsEc2VpnConnectionOptionsDetails', ], 'Routes' => [ 'shape' => 'AwsEc2VpnConnectionRoutesList', ], 'TransitGatewayId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2VpnConnectionOptionsDetails' => [ 'type' => 'structure', 'members' => [ 'StaticRoutesOnly' => [ 'shape' => 'Boolean', ], 'TunnelOptions' => [ 'shape' => 'AwsEc2VpnConnectionOptionsTunnelOptionsList', ], ], ], 'AwsEc2VpnConnectionOptionsTunnelOptionsDetails' => [ 'type' => 'structure', 'members' => [ 'DpdTimeoutSeconds' => [ 'shape' => 'Integer', ], 'IkeVersions' => [ 'shape' => 'NonEmptyStringList', ], 'OutsideIpAddress' => [ 'shape' => 'NonEmptyString', ], 'Phase1DhGroupNumbers' => [ 'shape' => 'IntegerList', ], 'Phase1EncryptionAlgorithms' => [ 'shape' => 'NonEmptyStringList', ], 'Phase1IntegrityAlgorithms' => [ 'shape' => 'NonEmptyStringList', ], 'Phase1LifetimeSeconds' => [ 'shape' => 'Integer', ], 'Phase2DhGroupNumbers' => [ 'shape' => 'IntegerList', ], 'Phase2EncryptionAlgorithms' => [ 'shape' => 'NonEmptyStringList', ], 'Phase2IntegrityAlgorithms' => [ 'shape' => 'NonEmptyStringList', ], 'Phase2LifetimeSeconds' => [ 'shape' => 'Integer', ], 'PreSharedKey' => [ 'shape' => 'NonEmptyString', ], 'RekeyFuzzPercentage' => [ 'shape' => 'Integer', ], 'RekeyMarginTimeSeconds' => [ 'shape' => 'Integer', ], 'ReplayWindowSize' => [ 'shape' => 'Integer', ], 'TunnelInsideCidr' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2VpnConnectionOptionsTunnelOptionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEc2VpnConnectionOptionsTunnelOptionsDetails', ], ], 'AwsEc2VpnConnectionRoutesDetails' => [ 'type' => 'structure', 'members' => [ 'DestinationCidrBlock' => [ 'shape' => 'NonEmptyString', ], 'State' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2VpnConnectionRoutesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEc2VpnConnectionRoutesDetails', ], ], 'AwsEc2VpnConnectionVgwTelemetryDetails' => [ 'type' => 'structure', 'members' => [ 'AcceptedRouteCount' => [ 'shape' => 'Integer', ], 'CertificateArn' => [ 'shape' => 'NonEmptyString', ], 'LastStatusChange' => [ 'shape' => 'NonEmptyString', ], 'OutsideIpAddress' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], 'StatusMessage' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2VpnConnectionVgwTelemetryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEc2VpnConnectionVgwTelemetryDetails', ], ], 'AwsEcrContainerImageDetails' => [ 'type' => 'structure', 'members' => [ 'RegistryId' => [ 'shape' => 'NonEmptyString', ], 'RepositoryName' => [ 'shape' => 'NonEmptyString', ], 'Architecture' => [ 'shape' => 'NonEmptyString', ], 'ImageDigest' => [ 'shape' => 'NonEmptyString', ], 'ImageTags' => [ 'shape' => 'NonEmptyStringList', ], 'ImagePublishedAt' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcrRepositoryDetails' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'NonEmptyString', ], 'ImageScanningConfiguration' => [ 'shape' => 'AwsEcrRepositoryImageScanningConfigurationDetails', ], 'ImageTagMutability' => [ 'shape' => 'NonEmptyString', ], 'LifecyclePolicy' => [ 'shape' => 'AwsEcrRepositoryLifecyclePolicyDetails', ], 'RepositoryName' => [ 'shape' => 'NonEmptyString', ], 'RepositoryPolicyText' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcrRepositoryImageScanningConfigurationDetails' => [ 'type' => 'structure', 'members' => [ 'ScanOnPush' => [ 'shape' => 'Boolean', ], ], ], 'AwsEcrRepositoryLifecyclePolicyDetails' => [ 'type' => 'structure', 'members' => [ 'LifecyclePolicyText' => [ 'shape' => 'NonEmptyString', ], 'RegistryId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsClusterClusterSettingsDetails' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'Value' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsClusterClusterSettingsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsClusterClusterSettingsDetails', ], ], 'AwsEcsClusterConfigurationDetails' => [ 'type' => 'structure', 'members' => [ 'ExecuteCommandConfiguration' => [ 'shape' => 'AwsEcsClusterConfigurationExecuteCommandConfigurationDetails', ], ], ], 'AwsEcsClusterConfigurationExecuteCommandConfigurationDetails' => [ 'type' => 'structure', 'members' => [ 'KmsKeyId' => [ 'shape' => 'NonEmptyString', ], 'LogConfiguration' => [ 'shape' => 'AwsEcsClusterConfigurationExecuteCommandConfigurationLogConfigurationDetails', ], 'Logging' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsClusterConfigurationExecuteCommandConfigurationLogConfigurationDetails' => [ 'type' => 'structure', 'members' => [ 'CloudWatchEncryptionEnabled' => [ 'shape' => 'Boolean', ], 'CloudWatchLogGroupName' => [ 'shape' => 'NonEmptyString', ], 'S3BucketName' => [ 'shape' => 'NonEmptyString', ], 'S3EncryptionEnabled' => [ 'shape' => 'Boolean', ], 'S3KeyPrefix' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsClusterDefaultCapacityProviderStrategyDetails' => [ 'type' => 'structure', 'members' => [ 'Base' => [ 'shape' => 'Integer', ], 'CapacityProvider' => [ 'shape' => 'NonEmptyString', ], 'Weight' => [ 'shape' => 'Integer', ], ], ], 'AwsEcsClusterDefaultCapacityProviderStrategyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsClusterDefaultCapacityProviderStrategyDetails', ], ], 'AwsEcsClusterDetails' => [ 'type' => 'structure', 'members' => [ 'ClusterArn' => [ 'shape' => 'NonEmptyString', ], 'ActiveServicesCount' => [ 'shape' => 'Integer', ], 'CapacityProviders' => [ 'shape' => 'NonEmptyStringList', ], 'ClusterSettings' => [ 'shape' => 'AwsEcsClusterClusterSettingsList', ], 'Configuration' => [ 'shape' => 'AwsEcsClusterConfigurationDetails', ], 'DefaultCapacityProviderStrategy' => [ 'shape' => 'AwsEcsClusterDefaultCapacityProviderStrategyList', ], 'ClusterName' => [ 'shape' => 'NonEmptyString', ], 'RegisteredContainerInstancesCount' => [ 'shape' => 'Integer', ], 'RunningTasksCount' => [ 'shape' => 'Integer', ], 'Status' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsContainerDetails' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'Image' => [ 'shape' => 'NonEmptyString', ], 'MountPoints' => [ 'shape' => 'AwsMountPointList', ], 'Privileged' => [ 'shape' => 'Boolean', ], ], ], 'AwsEcsContainerDetailsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsContainerDetails', ], ], 'AwsEcsServiceCapacityProviderStrategyDetails' => [ 'type' => 'structure', 'members' => [ 'Base' => [ 'shape' => 'Integer', ], 'CapacityProvider' => [ 'shape' => 'NonEmptyString', ], 'Weight' => [ 'shape' => 'Integer', ], ], ], 'AwsEcsServiceCapacityProviderStrategyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsServiceCapacityProviderStrategyDetails', ], ], 'AwsEcsServiceDeploymentConfigurationDeploymentCircuitBreakerDetails' => [ 'type' => 'structure', 'members' => [ 'Enable' => [ 'shape' => 'Boolean', ], 'Rollback' => [ 'shape' => 'Boolean', ], ], ], 'AwsEcsServiceDeploymentConfigurationDetails' => [ 'type' => 'structure', 'members' => [ 'DeploymentCircuitBreaker' => [ 'shape' => 'AwsEcsServiceDeploymentConfigurationDeploymentCircuitBreakerDetails', ], 'MaximumPercent' => [ 'shape' => 'Integer', ], 'MinimumHealthyPercent' => [ 'shape' => 'Integer', ], ], ], 'AwsEcsServiceDeploymentControllerDetails' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsServiceDetails' => [ 'type' => 'structure', 'members' => [ 'CapacityProviderStrategy' => [ 'shape' => 'AwsEcsServiceCapacityProviderStrategyList', ], 'Cluster' => [ 'shape' => 'NonEmptyString', ], 'DeploymentConfiguration' => [ 'shape' => 'AwsEcsServiceDeploymentConfigurationDetails', ], 'DeploymentController' => [ 'shape' => 'AwsEcsServiceDeploymentControllerDetails', ], 'DesiredCount' => [ 'shape' => 'Integer', ], 'EnableEcsManagedTags' => [ 'shape' => 'Boolean', ], 'EnableExecuteCommand' => [ 'shape' => 'Boolean', ], 'HealthCheckGracePeriodSeconds' => [ 'shape' => 'Integer', ], 'LaunchType' => [ 'shape' => 'NonEmptyString', ], 'LoadBalancers' => [ 'shape' => 'AwsEcsServiceLoadBalancersList', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'NetworkConfiguration' => [ 'shape' => 'AwsEcsServiceNetworkConfigurationDetails', ], 'PlacementConstraints' => [ 'shape' => 'AwsEcsServicePlacementConstraintsList', ], 'PlacementStrategies' => [ 'shape' => 'AwsEcsServicePlacementStrategiesList', ], 'PlatformVersion' => [ 'shape' => 'NonEmptyString', ], 'PropagateTags' => [ 'shape' => 'NonEmptyString', ], 'Role' => [ 'shape' => 'NonEmptyString', ], 'SchedulingStrategy' => [ 'shape' => 'NonEmptyString', ], 'ServiceArn' => [ 'shape' => 'NonEmptyString', ], 'ServiceName' => [ 'shape' => 'NonEmptyString', ], 'ServiceRegistries' => [ 'shape' => 'AwsEcsServiceServiceRegistriesList', ], 'TaskDefinition' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsServiceLoadBalancersDetails' => [ 'type' => 'structure', 'members' => [ 'ContainerName' => [ 'shape' => 'NonEmptyString', ], 'ContainerPort' => [ 'shape' => 'Integer', ], 'LoadBalancerName' => [ 'shape' => 'NonEmptyString', ], 'TargetGroupArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsServiceLoadBalancersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsServiceLoadBalancersDetails', ], ], 'AwsEcsServiceNetworkConfigurationAwsVpcConfigurationDetails' => [ 'type' => 'structure', 'members' => [ 'AssignPublicIp' => [ 'shape' => 'NonEmptyString', ], 'SecurityGroups' => [ 'shape' => 'NonEmptyStringList', ], 'Subnets' => [ 'shape' => 'NonEmptyStringList', ], ], ], 'AwsEcsServiceNetworkConfigurationDetails' => [ 'type' => 'structure', 'members' => [ 'AwsVpcConfiguration' => [ 'shape' => 'AwsEcsServiceNetworkConfigurationAwsVpcConfigurationDetails', ], ], ], 'AwsEcsServicePlacementConstraintsDetails' => [ 'type' => 'structure', 'members' => [ 'Expression' => [ 'shape' => 'NonEmptyString', ], 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsServicePlacementConstraintsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsServicePlacementConstraintsDetails', ], ], 'AwsEcsServicePlacementStrategiesDetails' => [ 'type' => 'structure', 'members' => [ 'Field' => [ 'shape' => 'NonEmptyString', ], 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsServicePlacementStrategiesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsServicePlacementStrategiesDetails', ], ], 'AwsEcsServiceServiceRegistriesDetails' => [ 'type' => 'structure', 'members' => [ 'ContainerName' => [ 'shape' => 'NonEmptyString', ], 'ContainerPort' => [ 'shape' => 'Integer', ], 'Port' => [ 'shape' => 'Integer', ], 'RegistryArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsServiceServiceRegistriesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsServiceServiceRegistriesDetails', ], ], 'AwsEcsTaskDefinitionContainerDefinitionsDependsOnDetails' => [ 'type' => 'structure', 'members' => [ 'Condition' => [ 'shape' => 'NonEmptyString', ], 'ContainerName' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsTaskDefinitionContainerDefinitionsDependsOnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsDependsOnDetails', ], ], 'AwsEcsTaskDefinitionContainerDefinitionsDetails' => [ 'type' => 'structure', 'members' => [ 'Command' => [ 'shape' => 'NonEmptyStringList', ], 'Cpu' => [ 'shape' => 'Integer', ], 'DependsOn' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsDependsOnList', ], 'DisableNetworking' => [ 'shape' => 'Boolean', ], 'DnsSearchDomains' => [ 'shape' => 'NonEmptyStringList', ], 'DnsServers' => [ 'shape' => 'NonEmptyStringList', ], 'DockerLabels' => [ 'shape' => 'FieldMap', ], 'DockerSecurityOptions' => [ 'shape' => 'NonEmptyStringList', ], 'EntryPoint' => [ 'shape' => 'NonEmptyStringList', ], 'Environment' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsEnvironmentList', ], 'EnvironmentFiles' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsEnvironmentFilesList', ], 'Essential' => [ 'shape' => 'Boolean', ], 'ExtraHosts' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsExtraHostsList', ], 'FirelensConfiguration' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsFirelensConfigurationDetails', ], 'HealthCheck' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsHealthCheckDetails', ], 'Hostname' => [ 'shape' => 'NonEmptyString', ], 'Image' => [ 'shape' => 'NonEmptyString', ], 'Interactive' => [ 'shape' => 'Boolean', ], 'Links' => [ 'shape' => 'NonEmptyStringList', ], 'LinuxParameters' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsLinuxParametersDetails', ], 'LogConfiguration' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsLogConfigurationDetails', ], 'Memory' => [ 'shape' => 'Integer', ], 'MemoryReservation' => [ 'shape' => 'Integer', ], 'MountPoints' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsMountPointsList', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'PortMappings' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsPortMappingsList', ], 'Privileged' => [ 'shape' => 'Boolean', ], 'PseudoTerminal' => [ 'shape' => 'Boolean', ], 'ReadonlyRootFilesystem' => [ 'shape' => 'Boolean', ], 'RepositoryCredentials' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsRepositoryCredentialsDetails', ], 'ResourceRequirements' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsResourceRequirementsList', ], 'Secrets' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsSecretsList', ], 'StartTimeout' => [ 'shape' => 'Integer', ], 'StopTimeout' => [ 'shape' => 'Integer', ], 'SystemControls' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsSystemControlsList', ], 'Ulimits' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsUlimitsList', ], 'User' => [ 'shape' => 'NonEmptyString', ], 'VolumesFrom' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsVolumesFromList', ], 'WorkingDirectory' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsTaskDefinitionContainerDefinitionsEnvironmentDetails' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'Value' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsTaskDefinitionContainerDefinitionsEnvironmentFilesDetails' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'NonEmptyString', ], 'Value' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsTaskDefinitionContainerDefinitionsEnvironmentFilesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsEnvironmentFilesDetails', ], ], 'AwsEcsTaskDefinitionContainerDefinitionsEnvironmentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsEnvironmentDetails', ], ], 'AwsEcsTaskDefinitionContainerDefinitionsExtraHostsDetails' => [ 'type' => 'structure', 'members' => [ 'Hostname' => [ 'shape' => 'NonEmptyString', ], 'IpAddress' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsTaskDefinitionContainerDefinitionsExtraHostsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsExtraHostsDetails', ], ], 'AwsEcsTaskDefinitionContainerDefinitionsFirelensConfigurationDetails' => [ 'type' => 'structure', 'members' => [ 'Options' => [ 'shape' => 'FieldMap', ], 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsTaskDefinitionContainerDefinitionsHealthCheckDetails' => [ 'type' => 'structure', 'members' => [ 'Command' => [ 'shape' => 'NonEmptyStringList', ], 'Interval' => [ 'shape' => 'Integer', ], 'Retries' => [ 'shape' => 'Integer', ], 'StartPeriod' => [ 'shape' => 'Integer', ], 'Timeout' => [ 'shape' => 'Integer', ], ], ], 'AwsEcsTaskDefinitionContainerDefinitionsLinuxParametersCapabilitiesDetails' => [ 'type' => 'structure', 'members' => [ 'Add' => [ 'shape' => 'NonEmptyStringList', ], 'Drop' => [ 'shape' => 'NonEmptyStringList', ], ], ], 'AwsEcsTaskDefinitionContainerDefinitionsLinuxParametersDetails' => [ 'type' => 'structure', 'members' => [ 'Capabilities' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsLinuxParametersCapabilitiesDetails', ], 'Devices' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsLinuxParametersDevicesList', ], 'InitProcessEnabled' => [ 'shape' => 'Boolean', ], 'MaxSwap' => [ 'shape' => 'Integer', ], 'SharedMemorySize' => [ 'shape' => 'Integer', ], 'Swappiness' => [ 'shape' => 'Integer', ], 'Tmpfs' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsLinuxParametersTmpfsList', ], ], ], 'AwsEcsTaskDefinitionContainerDefinitionsLinuxParametersDevicesDetails' => [ 'type' => 'structure', 'members' => [ 'ContainerPath' => [ 'shape' => 'NonEmptyString', ], 'HostPath' => [ 'shape' => 'NonEmptyString', ], 'Permissions' => [ 'shape' => 'NonEmptyStringList', ], ], ], 'AwsEcsTaskDefinitionContainerDefinitionsLinuxParametersDevicesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsLinuxParametersDevicesDetails', ], ], 'AwsEcsTaskDefinitionContainerDefinitionsLinuxParametersTmpfsDetails' => [ 'type' => 'structure', 'members' => [ 'ContainerPath' => [ 'shape' => 'NonEmptyString', ], 'MountOptions' => [ 'shape' => 'NonEmptyStringList', ], 'Size' => [ 'shape' => 'Integer', ], ], ], 'AwsEcsTaskDefinitionContainerDefinitionsLinuxParametersTmpfsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsLinuxParametersTmpfsDetails', ], ], 'AwsEcsTaskDefinitionContainerDefinitionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsDetails', ], ], 'AwsEcsTaskDefinitionContainerDefinitionsLogConfigurationDetails' => [ 'type' => 'structure', 'members' => [ 'LogDriver' => [ 'shape' => 'NonEmptyString', ], 'Options' => [ 'shape' => 'FieldMap', ], 'SecretOptions' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsLogConfigurationSecretOptionsList', ], ], ], 'AwsEcsTaskDefinitionContainerDefinitionsLogConfigurationSecretOptionsDetails' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'ValueFrom' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsTaskDefinitionContainerDefinitionsLogConfigurationSecretOptionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsLogConfigurationSecretOptionsDetails', ], ], 'AwsEcsTaskDefinitionContainerDefinitionsMountPointsDetails' => [ 'type' => 'structure', 'members' => [ 'ContainerPath' => [ 'shape' => 'NonEmptyString', ], 'ReadOnly' => [ 'shape' => 'Boolean', ], 'SourceVolume' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsTaskDefinitionContainerDefinitionsMountPointsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsMountPointsDetails', ], ], 'AwsEcsTaskDefinitionContainerDefinitionsPortMappingsDetails' => [ 'type' => 'structure', 'members' => [ 'ContainerPort' => [ 'shape' => 'Integer', ], 'HostPort' => [ 'shape' => 'Integer', ], 'Protocol' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsTaskDefinitionContainerDefinitionsPortMappingsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsPortMappingsDetails', ], ], 'AwsEcsTaskDefinitionContainerDefinitionsRepositoryCredentialsDetails' => [ 'type' => 'structure', 'members' => [ 'CredentialsParameter' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsTaskDefinitionContainerDefinitionsResourceRequirementsDetails' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'NonEmptyString', ], 'Value' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsTaskDefinitionContainerDefinitionsResourceRequirementsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsResourceRequirementsDetails', ], ], 'AwsEcsTaskDefinitionContainerDefinitionsSecretsDetails' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'ValueFrom' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsTaskDefinitionContainerDefinitionsSecretsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsSecretsDetails', ], ], 'AwsEcsTaskDefinitionContainerDefinitionsSystemControlsDetails' => [ 'type' => 'structure', 'members' => [ 'Namespace' => [ 'shape' => 'NonEmptyString', ], 'Value' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsTaskDefinitionContainerDefinitionsSystemControlsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsSystemControlsDetails', ], ], 'AwsEcsTaskDefinitionContainerDefinitionsUlimitsDetails' => [ 'type' => 'structure', 'members' => [ 'HardLimit' => [ 'shape' => 'Integer', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'SoftLimit' => [ 'shape' => 'Integer', ], ], ], 'AwsEcsTaskDefinitionContainerDefinitionsUlimitsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsUlimitsDetails', ], ], 'AwsEcsTaskDefinitionContainerDefinitionsVolumesFromDetails' => [ 'type' => 'structure', 'members' => [ 'ReadOnly' => [ 'shape' => 'Boolean', ], 'SourceContainer' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsTaskDefinitionContainerDefinitionsVolumesFromList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsVolumesFromDetails', ], ], 'AwsEcsTaskDefinitionDetails' => [ 'type' => 'structure', 'members' => [ 'ContainerDefinitions' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsList', ], 'Cpu' => [ 'shape' => 'NonEmptyString', ], 'ExecutionRoleArn' => [ 'shape' => 'NonEmptyString', ], 'Family' => [ 'shape' => 'NonEmptyString', ], 'InferenceAccelerators' => [ 'shape' => 'AwsEcsTaskDefinitionInferenceAcceleratorsList', ], 'IpcMode' => [ 'shape' => 'NonEmptyString', ], 'Memory' => [ 'shape' => 'NonEmptyString', ], 'NetworkMode' => [ 'shape' => 'NonEmptyString', ], 'PidMode' => [ 'shape' => 'NonEmptyString', ], 'PlacementConstraints' => [ 'shape' => 'AwsEcsTaskDefinitionPlacementConstraintsList', ], 'ProxyConfiguration' => [ 'shape' => 'AwsEcsTaskDefinitionProxyConfigurationDetails', ], 'RequiresCompatibilities' => [ 'shape' => 'NonEmptyStringList', ], 'TaskRoleArn' => [ 'shape' => 'NonEmptyString', ], 'Volumes' => [ 'shape' => 'AwsEcsTaskDefinitionVolumesList', ], 'Status' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsTaskDefinitionInferenceAcceleratorsDetails' => [ 'type' => 'structure', 'members' => [ 'DeviceName' => [ 'shape' => 'NonEmptyString', ], 'DeviceType' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsTaskDefinitionInferenceAcceleratorsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsTaskDefinitionInferenceAcceleratorsDetails', ], ], 'AwsEcsTaskDefinitionPlacementConstraintsDetails' => [ 'type' => 'structure', 'members' => [ 'Expression' => [ 'shape' => 'NonEmptyString', ], 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsTaskDefinitionPlacementConstraintsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsTaskDefinitionPlacementConstraintsDetails', ], ], 'AwsEcsTaskDefinitionProxyConfigurationDetails' => [ 'type' => 'structure', 'members' => [ 'ContainerName' => [ 'shape' => 'NonEmptyString', ], 'ProxyConfigurationProperties' => [ 'shape' => 'AwsEcsTaskDefinitionProxyConfigurationProxyConfigurationPropertiesList', ], 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsTaskDefinitionProxyConfigurationProxyConfigurationPropertiesDetails' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'Value' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsTaskDefinitionProxyConfigurationProxyConfigurationPropertiesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsTaskDefinitionProxyConfigurationProxyConfigurationPropertiesDetails', ], ], 'AwsEcsTaskDefinitionVolumesDetails' => [ 'type' => 'structure', 'members' => [ 'DockerVolumeConfiguration' => [ 'shape' => 'AwsEcsTaskDefinitionVolumesDockerVolumeConfigurationDetails', ], 'EfsVolumeConfiguration' => [ 'shape' => 'AwsEcsTaskDefinitionVolumesEfsVolumeConfigurationDetails', ], 'Host' => [ 'shape' => 'AwsEcsTaskDefinitionVolumesHostDetails', ], 'Name' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsTaskDefinitionVolumesDockerVolumeConfigurationDetails' => [ 'type' => 'structure', 'members' => [ 'Autoprovision' => [ 'shape' => 'Boolean', ], 'Driver' => [ 'shape' => 'NonEmptyString', ], 'DriverOpts' => [ 'shape' => 'FieldMap', ], 'Labels' => [ 'shape' => 'FieldMap', ], 'Scope' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsTaskDefinitionVolumesEfsVolumeConfigurationAuthorizationConfigDetails' => [ 'type' => 'structure', 'members' => [ 'AccessPointId' => [ 'shape' => 'NonEmptyString', ], 'Iam' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsTaskDefinitionVolumesEfsVolumeConfigurationDetails' => [ 'type' => 'structure', 'members' => [ 'AuthorizationConfig' => [ 'shape' => 'AwsEcsTaskDefinitionVolumesEfsVolumeConfigurationAuthorizationConfigDetails', ], 'FilesystemId' => [ 'shape' => 'NonEmptyString', ], 'RootDirectory' => [ 'shape' => 'NonEmptyString', ], 'TransitEncryption' => [ 'shape' => 'NonEmptyString', ], 'TransitEncryptionPort' => [ 'shape' => 'Integer', ], ], ], 'AwsEcsTaskDefinitionVolumesHostDetails' => [ 'type' => 'structure', 'members' => [ 'SourcePath' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsTaskDefinitionVolumesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsTaskDefinitionVolumesDetails', ], ], 'AwsEcsTaskDetails' => [ 'type' => 'structure', 'members' => [ 'ClusterArn' => [ 'shape' => 'NonEmptyString', ], 'TaskDefinitionArn' => [ 'shape' => 'NonEmptyString', ], 'Version' => [ 'shape' => 'NonEmptyString', ], 'CreatedAt' => [ 'shape' => 'NonEmptyString', ], 'StartedAt' => [ 'shape' => 'NonEmptyString', ], 'StartedBy' => [ 'shape' => 'NonEmptyString', ], 'Group' => [ 'shape' => 'NonEmptyString', ], 'Volumes' => [ 'shape' => 'AwsEcsTaskVolumeDetailsList', ], 'Containers' => [ 'shape' => 'AwsEcsContainerDetailsList', ], ], ], 'AwsEcsTaskVolumeDetails' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'Host' => [ 'shape' => 'AwsEcsTaskVolumeHostDetails', ], ], ], 'AwsEcsTaskVolumeDetailsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsTaskVolumeDetails', ], ], 'AwsEcsTaskVolumeHostDetails' => [ 'type' => 'structure', 'members' => [ 'SourcePath' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEfsAccessPointDetails' => [ 'type' => 'structure', 'members' => [ 'AccessPointId' => [ 'shape' => 'NonEmptyString', ], 'Arn' => [ 'shape' => 'NonEmptyString', ], 'ClientToken' => [ 'shape' => 'NonEmptyString', ], 'FileSystemId' => [ 'shape' => 'NonEmptyString', ], 'PosixUser' => [ 'shape' => 'AwsEfsAccessPointPosixUserDetails', ], 'RootDirectory' => [ 'shape' => 'AwsEfsAccessPointRootDirectoryDetails', ], ], ], 'AwsEfsAccessPointPosixUserDetails' => [ 'type' => 'structure', 'members' => [ 'Gid' => [ 'shape' => 'NonEmptyString', ], 'SecondaryGids' => [ 'shape' => 'NonEmptyStringList', ], 'Uid' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEfsAccessPointRootDirectoryCreationInfoDetails' => [ 'type' => 'structure', 'members' => [ 'OwnerGid' => [ 'shape' => 'NonEmptyString', ], 'OwnerUid' => [ 'shape' => 'NonEmptyString', ], 'Permissions' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEfsAccessPointRootDirectoryDetails' => [ 'type' => 'structure', 'members' => [ 'CreationInfo' => [ 'shape' => 'AwsEfsAccessPointRootDirectoryCreationInfoDetails', ], 'Path' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEksClusterDetails' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'NonEmptyString', ], 'CertificateAuthorityData' => [ 'shape' => 'NonEmptyString', ], 'ClusterStatus' => [ 'shape' => 'NonEmptyString', ], 'Endpoint' => [ 'shape' => 'NonEmptyString', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'ResourcesVpcConfig' => [ 'shape' => 'AwsEksClusterResourcesVpcConfigDetails', ], 'RoleArn' => [ 'shape' => 'NonEmptyString', ], 'Version' => [ 'shape' => 'NonEmptyString', ], 'Logging' => [ 'shape' => 'AwsEksClusterLoggingDetails', ], ], ], 'AwsEksClusterLoggingClusterLoggingDetails' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], 'Types' => [ 'shape' => 'NonEmptyStringList', ], ], ], 'AwsEksClusterLoggingClusterLoggingList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEksClusterLoggingClusterLoggingDetails', ], ], 'AwsEksClusterLoggingDetails' => [ 'type' => 'structure', 'members' => [ 'ClusterLogging' => [ 'shape' => 'AwsEksClusterLoggingClusterLoggingList', ], ], ], 'AwsEksClusterResourcesVpcConfigDetails' => [ 'type' => 'structure', 'members' => [ 'SecurityGroupIds' => [ 'shape' => 'NonEmptyStringList', ], 'SubnetIds' => [ 'shape' => 'NonEmptyStringList', ], 'EndpointPublicAccess' => [ 'shape' => 'Boolean', ], ], ], 'AwsElasticBeanstalkEnvironmentDetails' => [ 'type' => 'structure', 'members' => [ 'ApplicationName' => [ 'shape' => 'NonEmptyString', ], 'Cname' => [ 'shape' => 'NonEmptyString', ], 'DateCreated' => [ 'shape' => 'NonEmptyString', ], 'DateUpdated' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'EndpointUrl' => [ 'shape' => 'NonEmptyString', ], 'EnvironmentArn' => [ 'shape' => 'NonEmptyString', ], 'EnvironmentId' => [ 'shape' => 'NonEmptyString', ], 'EnvironmentLinks' => [ 'shape' => 'AwsElasticBeanstalkEnvironmentEnvironmentLinks', ], 'EnvironmentName' => [ 'shape' => 'NonEmptyString', ], 'OptionSettings' => [ 'shape' => 'AwsElasticBeanstalkEnvironmentOptionSettings', ], 'PlatformArn' => [ 'shape' => 'NonEmptyString', ], 'SolutionStackName' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], 'Tier' => [ 'shape' => 'AwsElasticBeanstalkEnvironmentTier', ], 'VersionLabel' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsElasticBeanstalkEnvironmentEnvironmentLink' => [ 'type' => 'structure', 'members' => [ 'EnvironmentName' => [ 'shape' => 'NonEmptyString', ], 'LinkName' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsElasticBeanstalkEnvironmentEnvironmentLinks' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsElasticBeanstalkEnvironmentEnvironmentLink', ], ], 'AwsElasticBeanstalkEnvironmentOptionSetting' => [ 'type' => 'structure', 'members' => [ 'Namespace' => [ 'shape' => 'NonEmptyString', ], 'OptionName' => [ 'shape' => 'NonEmptyString', ], 'ResourceName' => [ 'shape' => 'NonEmptyString', ], 'Value' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsElasticBeanstalkEnvironmentOptionSettings' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsElasticBeanstalkEnvironmentOptionSetting', ], ], 'AwsElasticBeanstalkEnvironmentTier' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'Type' => [ 'shape' => 'NonEmptyString', ], 'Version' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsElasticsearchDomainDetails' => [ 'type' => 'structure', 'members' => [ 'AccessPolicies' => [ 'shape' => 'NonEmptyString', ], 'DomainEndpointOptions' => [ 'shape' => 'AwsElasticsearchDomainDomainEndpointOptions', ], 'DomainId' => [ 'shape' => 'NonEmptyString', ], 'DomainName' => [ 'shape' => 'NonEmptyString', ], 'Endpoint' => [ 'shape' => 'NonEmptyString', ], 'Endpoints' => [ 'shape' => 'FieldMap', ], 'ElasticsearchVersion' => [ 'shape' => 'NonEmptyString', ], 'ElasticsearchClusterConfig' => [ 'shape' => 'AwsElasticsearchDomainElasticsearchClusterConfigDetails', ], 'EncryptionAtRestOptions' => [ 'shape' => 'AwsElasticsearchDomainEncryptionAtRestOptions', ], 'LogPublishingOptions' => [ 'shape' => 'AwsElasticsearchDomainLogPublishingOptions', ], 'NodeToNodeEncryptionOptions' => [ 'shape' => 'AwsElasticsearchDomainNodeToNodeEncryptionOptions', ], 'ServiceSoftwareOptions' => [ 'shape' => 'AwsElasticsearchDomainServiceSoftwareOptions', ], 'VPCOptions' => [ 'shape' => 'AwsElasticsearchDomainVPCOptions', ], ], ], 'AwsElasticsearchDomainDomainEndpointOptions' => [ 'type' => 'structure', 'members' => [ 'EnforceHTTPS' => [ 'shape' => 'Boolean', ], 'TLSSecurityPolicy' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsElasticsearchDomainElasticsearchClusterConfigDetails' => [ 'type' => 'structure', 'members' => [ 'DedicatedMasterCount' => [ 'shape' => 'Integer', ], 'DedicatedMasterEnabled' => [ 'shape' => 'Boolean', ], 'DedicatedMasterType' => [ 'shape' => 'NonEmptyString', ], 'InstanceCount' => [ 'shape' => 'Integer', ], 'InstanceType' => [ 'shape' => 'NonEmptyString', ], 'ZoneAwarenessConfig' => [ 'shape' => 'AwsElasticsearchDomainElasticsearchClusterConfigZoneAwarenessConfigDetails', ], 'ZoneAwarenessEnabled' => [ 'shape' => 'Boolean', ], ], ], 'AwsElasticsearchDomainElasticsearchClusterConfigZoneAwarenessConfigDetails' => [ 'type' => 'structure', 'members' => [ 'AvailabilityZoneCount' => [ 'shape' => 'Integer', ], ], ], 'AwsElasticsearchDomainEncryptionAtRestOptions' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], 'KmsKeyId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsElasticsearchDomainLogPublishingOptions' => [ 'type' => 'structure', 'members' => [ 'IndexSlowLogs' => [ 'shape' => 'AwsElasticsearchDomainLogPublishingOptionsLogConfig', ], 'SearchSlowLogs' => [ 'shape' => 'AwsElasticsearchDomainLogPublishingOptionsLogConfig', ], 'AuditLogs' => [ 'shape' => 'AwsElasticsearchDomainLogPublishingOptionsLogConfig', ], ], ], 'AwsElasticsearchDomainLogPublishingOptionsLogConfig' => [ 'type' => 'structure', 'members' => [ 'CloudWatchLogsLogGroupArn' => [ 'shape' => 'NonEmptyString', ], 'Enabled' => [ 'shape' => 'Boolean', ], ], ], 'AwsElasticsearchDomainNodeToNodeEncryptionOptions' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], ], ], 'AwsElasticsearchDomainServiceSoftwareOptions' => [ 'type' => 'structure', 'members' => [ 'AutomatedUpdateDate' => [ 'shape' => 'NonEmptyString', ], 'Cancellable' => [ 'shape' => 'Boolean', ], 'CurrentVersion' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'NewVersion' => [ 'shape' => 'NonEmptyString', ], 'UpdateAvailable' => [ 'shape' => 'Boolean', ], 'UpdateStatus' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsElasticsearchDomainVPCOptions' => [ 'type' => 'structure', 'members' => [ 'AvailabilityZones' => [ 'shape' => 'NonEmptyStringList', ], 'SecurityGroupIds' => [ 'shape' => 'NonEmptyStringList', ], 'SubnetIds' => [ 'shape' => 'NonEmptyStringList', ], 'VPCId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsElbAppCookieStickinessPolicies' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsElbAppCookieStickinessPolicy', ], ], 'AwsElbAppCookieStickinessPolicy' => [ 'type' => 'structure', 'members' => [ 'CookieName' => [ 'shape' => 'NonEmptyString', ], 'PolicyName' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsElbLbCookieStickinessPolicies' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsElbLbCookieStickinessPolicy', ], ], 'AwsElbLbCookieStickinessPolicy' => [ 'type' => 'structure', 'members' => [ 'CookieExpirationPeriod' => [ 'shape' => 'Long', ], 'PolicyName' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsElbLoadBalancerAccessLog' => [ 'type' => 'structure', 'members' => [ 'EmitInterval' => [ 'shape' => 'Integer', ], 'Enabled' => [ 'shape' => 'Boolean', ], 'S3BucketName' => [ 'shape' => 'NonEmptyString', ], 'S3BucketPrefix' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsElbLoadBalancerAdditionalAttribute' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'NonEmptyString', ], 'Value' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsElbLoadBalancerAdditionalAttributeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsElbLoadBalancerAdditionalAttribute', ], ], 'AwsElbLoadBalancerAttributes' => [ 'type' => 'structure', 'members' => [ 'AccessLog' => [ 'shape' => 'AwsElbLoadBalancerAccessLog', ], 'ConnectionDraining' => [ 'shape' => 'AwsElbLoadBalancerConnectionDraining', ], 'ConnectionSettings' => [ 'shape' => 'AwsElbLoadBalancerConnectionSettings', ], 'CrossZoneLoadBalancing' => [ 'shape' => 'AwsElbLoadBalancerCrossZoneLoadBalancing', ], 'AdditionalAttributes' => [ 'shape' => 'AwsElbLoadBalancerAdditionalAttributeList', ], ], ], 'AwsElbLoadBalancerBackendServerDescription' => [ 'type' => 'structure', 'members' => [ 'InstancePort' => [ 'shape' => 'Integer', ], 'PolicyNames' => [ 'shape' => 'StringList', ], ], ], 'AwsElbLoadBalancerBackendServerDescriptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsElbLoadBalancerBackendServerDescription', ], ], 'AwsElbLoadBalancerConnectionDraining' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], 'Timeout' => [ 'shape' => 'Integer', ], ], ], 'AwsElbLoadBalancerConnectionSettings' => [ 'type' => 'structure', 'members' => [ 'IdleTimeout' => [ 'shape' => 'Integer', ], ], ], 'AwsElbLoadBalancerCrossZoneLoadBalancing' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], ], ], 'AwsElbLoadBalancerDetails' => [ 'type' => 'structure', 'members' => [ 'AvailabilityZones' => [ 'shape' => 'StringList', ], 'BackendServerDescriptions' => [ 'shape' => 'AwsElbLoadBalancerBackendServerDescriptions', ], 'CanonicalHostedZoneName' => [ 'shape' => 'NonEmptyString', ], 'CanonicalHostedZoneNameID' => [ 'shape' => 'NonEmptyString', ], 'CreatedTime' => [ 'shape' => 'NonEmptyString', ], 'DnsName' => [ 'shape' => 'NonEmptyString', ], 'HealthCheck' => [ 'shape' => 'AwsElbLoadBalancerHealthCheck', ], 'Instances' => [ 'shape' => 'AwsElbLoadBalancerInstances', ], 'ListenerDescriptions' => [ 'shape' => 'AwsElbLoadBalancerListenerDescriptions', ], 'LoadBalancerAttributes' => [ 'shape' => 'AwsElbLoadBalancerAttributes', ], 'LoadBalancerName' => [ 'shape' => 'NonEmptyString', ], 'Policies' => [ 'shape' => 'AwsElbLoadBalancerPolicies', ], 'Scheme' => [ 'shape' => 'NonEmptyString', ], 'SecurityGroups' => [ 'shape' => 'StringList', ], 'SourceSecurityGroup' => [ 'shape' => 'AwsElbLoadBalancerSourceSecurityGroup', ], 'Subnets' => [ 'shape' => 'StringList', ], 'VpcId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsElbLoadBalancerHealthCheck' => [ 'type' => 'structure', 'members' => [ 'HealthyThreshold' => [ 'shape' => 'Integer', ], 'Interval' => [ 'shape' => 'Integer', ], 'Target' => [ 'shape' => 'NonEmptyString', ], 'Timeout' => [ 'shape' => 'Integer', ], 'UnhealthyThreshold' => [ 'shape' => 'Integer', ], ], ], 'AwsElbLoadBalancerInstance' => [ 'type' => 'structure', 'members' => [ 'InstanceId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsElbLoadBalancerInstances' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsElbLoadBalancerInstance', ], ], 'AwsElbLoadBalancerListener' => [ 'type' => 'structure', 'members' => [ 'InstancePort' => [ 'shape' => 'Integer', ], 'InstanceProtocol' => [ 'shape' => 'NonEmptyString', ], 'LoadBalancerPort' => [ 'shape' => 'Integer', ], 'Protocol' => [ 'shape' => 'NonEmptyString', ], 'SslCertificateId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsElbLoadBalancerListenerDescription' => [ 'type' => 'structure', 'members' => [ 'Listener' => [ 'shape' => 'AwsElbLoadBalancerListener', ], 'PolicyNames' => [ 'shape' => 'StringList', ], ], ], 'AwsElbLoadBalancerListenerDescriptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsElbLoadBalancerListenerDescription', ], ], 'AwsElbLoadBalancerPolicies' => [ 'type' => 'structure', 'members' => [ 'AppCookieStickinessPolicies' => [ 'shape' => 'AwsElbAppCookieStickinessPolicies', ], 'LbCookieStickinessPolicies' => [ 'shape' => 'AwsElbLbCookieStickinessPolicies', ], 'OtherPolicies' => [ 'shape' => 'StringList', ], ], ], 'AwsElbLoadBalancerSourceSecurityGroup' => [ 'type' => 'structure', 'members' => [ 'GroupName' => [ 'shape' => 'NonEmptyString', ], 'OwnerAlias' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsElbv2LoadBalancerAttribute' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'NonEmptyString', ], 'Value' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsElbv2LoadBalancerAttributes' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsElbv2LoadBalancerAttribute', ], ], 'AwsElbv2LoadBalancerDetails' => [ 'type' => 'structure', 'members' => [ 'AvailabilityZones' => [ 'shape' => 'AvailabilityZones', ], 'CanonicalHostedZoneId' => [ 'shape' => 'NonEmptyString', ], 'CreatedTime' => [ 'shape' => 'NonEmptyString', ], 'DNSName' => [ 'shape' => 'NonEmptyString', ], 'IpAddressType' => [ 'shape' => 'NonEmptyString', ], 'Scheme' => [ 'shape' => 'NonEmptyString', ], 'SecurityGroups' => [ 'shape' => 'SecurityGroups', ], 'State' => [ 'shape' => 'LoadBalancerState', ], 'Type' => [ 'shape' => 'NonEmptyString', ], 'VpcId' => [ 'shape' => 'NonEmptyString', ], 'LoadBalancerAttributes' => [ 'shape' => 'AwsElbv2LoadBalancerAttributes', ], ], ], 'AwsEventSchemasRegistryDetails' => [ 'type' => 'structure', 'members' => [ 'Description' => [ 'shape' => 'NonEmptyString', ], 'RegistryArn' => [ 'shape' => 'NonEmptyString', ], 'RegistryName' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEventsEndpointDetails' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'EndpointId' => [ 'shape' => 'NonEmptyString', ], 'EndpointUrl' => [ 'shape' => 'NonEmptyString', ], 'EventBuses' => [ 'shape' => 'AwsEventsEndpointEventBusesList', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'ReplicationConfig' => [ 'shape' => 'AwsEventsEndpointReplicationConfigDetails', ], 'RoleArn' => [ 'shape' => 'NonEmptyString', ], 'RoutingConfig' => [ 'shape' => 'AwsEventsEndpointRoutingConfigDetails', ], 'State' => [ 'shape' => 'NonEmptyString', ], 'StateReason' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEventsEndpointEventBusesDetails' => [ 'type' => 'structure', 'members' => [ 'EventBusArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEventsEndpointEventBusesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEventsEndpointEventBusesDetails', ], ], 'AwsEventsEndpointReplicationConfigDetails' => [ 'type' => 'structure', 'members' => [ 'State' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEventsEndpointRoutingConfigDetails' => [ 'type' => 'structure', 'members' => [ 'FailoverConfig' => [ 'shape' => 'AwsEventsEndpointRoutingConfigFailoverConfigDetails', ], ], ], 'AwsEventsEndpointRoutingConfigFailoverConfigDetails' => [ 'type' => 'structure', 'members' => [ 'Primary' => [ 'shape' => 'AwsEventsEndpointRoutingConfigFailoverConfigPrimaryDetails', ], 'Secondary' => [ 'shape' => 'AwsEventsEndpointRoutingConfigFailoverConfigSecondaryDetails', ], ], ], 'AwsEventsEndpointRoutingConfigFailoverConfigPrimaryDetails' => [ 'type' => 'structure', 'members' => [ 'HealthCheck' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEventsEndpointRoutingConfigFailoverConfigSecondaryDetails' => [ 'type' => 'structure', 'members' => [ 'Route' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEventsEventbusDetails' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'NonEmptyString', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'Policy' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsGuardDutyDetectorDataSourcesCloudTrailDetails' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsGuardDutyDetectorDataSourcesDetails' => [ 'type' => 'structure', 'members' => [ 'CloudTrail' => [ 'shape' => 'AwsGuardDutyDetectorDataSourcesCloudTrailDetails', ], 'DnsLogs' => [ 'shape' => 'AwsGuardDutyDetectorDataSourcesDnsLogsDetails', ], 'FlowLogs' => [ 'shape' => 'AwsGuardDutyDetectorDataSourcesFlowLogsDetails', ], 'Kubernetes' => [ 'shape' => 'AwsGuardDutyDetectorDataSourcesKubernetesDetails', ], 'MalwareProtection' => [ 'shape' => 'AwsGuardDutyDetectorDataSourcesMalwareProtectionDetails', ], 'S3Logs' => [ 'shape' => 'AwsGuardDutyDetectorDataSourcesS3LogsDetails', ], ], ], 'AwsGuardDutyDetectorDataSourcesDnsLogsDetails' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsGuardDutyDetectorDataSourcesFlowLogsDetails' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsGuardDutyDetectorDataSourcesKubernetesAuditLogsDetails' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsGuardDutyDetectorDataSourcesKubernetesDetails' => [ 'type' => 'structure', 'members' => [ 'AuditLogs' => [ 'shape' => 'AwsGuardDutyDetectorDataSourcesKubernetesAuditLogsDetails', ], ], ], 'AwsGuardDutyDetectorDataSourcesMalwareProtectionDetails' => [ 'type' => 'structure', 'members' => [ 'ScanEc2InstanceWithFindings' => [ 'shape' => 'AwsGuardDutyDetectorDataSourcesMalwareProtectionScanEc2InstanceWithFindingsDetails', ], 'ServiceRole' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsGuardDutyDetectorDataSourcesMalwareProtectionScanEc2InstanceWithFindingsDetails' => [ 'type' => 'structure', 'members' => [ 'EbsVolumes' => [ 'shape' => 'AwsGuardDutyDetectorDataSourcesMalwareProtectionScanEc2InstanceWithFindingsEbsVolumesDetails', ], ], ], 'AwsGuardDutyDetectorDataSourcesMalwareProtectionScanEc2InstanceWithFindingsEbsVolumesDetails' => [ 'type' => 'structure', 'members' => [ 'Reason' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsGuardDutyDetectorDataSourcesS3LogsDetails' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsGuardDutyDetectorDetails' => [ 'type' => 'structure', 'members' => [ 'DataSources' => [ 'shape' => 'AwsGuardDutyDetectorDataSourcesDetails', ], 'Features' => [ 'shape' => 'AwsGuardDutyDetectorFeaturesList', ], 'FindingPublishingFrequency' => [ 'shape' => 'NonEmptyString', ], 'ServiceRole' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsGuardDutyDetectorFeaturesDetails' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsGuardDutyDetectorFeaturesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsGuardDutyDetectorFeaturesDetails', ], ], 'AwsIamAccessKeyDetails' => [ 'type' => 'structure', 'members' => [ 'UserName' => [ 'shape' => 'NonEmptyString', 'deprecated' => true, 'deprecatedMessage' => 'This filter is deprecated. Instead, use PrincipalName.', ], 'Status' => [ 'shape' => 'AwsIamAccessKeyStatus', ], 'CreatedAt' => [ 'shape' => 'NonEmptyString', ], 'PrincipalId' => [ 'shape' => 'NonEmptyString', ], 'PrincipalType' => [ 'shape' => 'NonEmptyString', ], 'PrincipalName' => [ 'shape' => 'NonEmptyString', ], 'AccountId' => [ 'shape' => 'NonEmptyString', ], 'AccessKeyId' => [ 'shape' => 'NonEmptyString', ], 'SessionContext' => [ 'shape' => 'AwsIamAccessKeySessionContext', ], ], ], 'AwsIamAccessKeySessionContext' => [ 'type' => 'structure', 'members' => [ 'Attributes' => [ 'shape' => 'AwsIamAccessKeySessionContextAttributes', ], 'SessionIssuer' => [ 'shape' => 'AwsIamAccessKeySessionContextSessionIssuer', ], ], ], 'AwsIamAccessKeySessionContextAttributes' => [ 'type' => 'structure', 'members' => [ 'MfaAuthenticated' => [ 'shape' => 'Boolean', ], 'CreationDate' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsIamAccessKeySessionContextSessionIssuer' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'NonEmptyString', ], 'PrincipalId' => [ 'shape' => 'NonEmptyString', ], 'Arn' => [ 'shape' => 'NonEmptyString', ], 'AccountId' => [ 'shape' => 'NonEmptyString', ], 'UserName' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsIamAccessKeyStatus' => [ 'type' => 'string', 'enum' => [ 'Active', 'Inactive', ], ], 'AwsIamAttachedManagedPolicy' => [ 'type' => 'structure', 'members' => [ 'PolicyName' => [ 'shape' => 'NonEmptyString', ], 'PolicyArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsIamAttachedManagedPolicyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsIamAttachedManagedPolicy', ], ], 'AwsIamGroupDetails' => [ 'type' => 'structure', 'members' => [ 'AttachedManagedPolicies' => [ 'shape' => 'AwsIamAttachedManagedPolicyList', ], 'CreateDate' => [ 'shape' => 'NonEmptyString', ], 'GroupId' => [ 'shape' => 'NonEmptyString', ], 'GroupName' => [ 'shape' => 'NonEmptyString', ], 'GroupPolicyList' => [ 'shape' => 'AwsIamGroupPolicyList', ], 'Path' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsIamGroupPolicy' => [ 'type' => 'structure', 'members' => [ 'PolicyName' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsIamGroupPolicyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsIamGroupPolicy', ], ], 'AwsIamInstanceProfile' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'NonEmptyString', ], 'CreateDate' => [ 'shape' => 'NonEmptyString', ], 'InstanceProfileId' => [ 'shape' => 'NonEmptyString', ], 'InstanceProfileName' => [ 'shape' => 'NonEmptyString', ], 'Path' => [ 'shape' => 'NonEmptyString', ], 'Roles' => [ 'shape' => 'AwsIamInstanceProfileRoles', ], ], ], 'AwsIamInstanceProfileList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsIamInstanceProfile', ], ], 'AwsIamInstanceProfileRole' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'NonEmptyString', ], 'AssumeRolePolicyDocument' => [ 'shape' => 'AwsIamRoleAssumeRolePolicyDocument', ], 'CreateDate' => [ 'shape' => 'NonEmptyString', ], 'Path' => [ 'shape' => 'NonEmptyString', ], 'RoleId' => [ 'shape' => 'NonEmptyString', ], 'RoleName' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsIamInstanceProfileRoles' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsIamInstanceProfileRole', ], ], 'AwsIamPermissionsBoundary' => [ 'type' => 'structure', 'members' => [ 'PermissionsBoundaryArn' => [ 'shape' => 'NonEmptyString', ], 'PermissionsBoundaryType' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsIamPolicyDetails' => [ 'type' => 'structure', 'members' => [ 'AttachmentCount' => [ 'shape' => 'Integer', ], 'CreateDate' => [ 'shape' => 'NonEmptyString', ], 'DefaultVersionId' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'IsAttachable' => [ 'shape' => 'Boolean', ], 'Path' => [ 'shape' => 'NonEmptyString', ], 'PermissionsBoundaryUsageCount' => [ 'shape' => 'Integer', ], 'PolicyId' => [ 'shape' => 'NonEmptyString', ], 'PolicyName' => [ 'shape' => 'NonEmptyString', ], 'PolicyVersionList' => [ 'shape' => 'AwsIamPolicyVersionList', ], 'UpdateDate' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsIamPolicyVersion' => [ 'type' => 'structure', 'members' => [ 'VersionId' => [ 'shape' => 'NonEmptyString', ], 'IsDefaultVersion' => [ 'shape' => 'Boolean', ], 'CreateDate' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsIamPolicyVersionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsIamPolicyVersion', ], ], 'AwsIamRoleAssumeRolePolicyDocument' => [ 'type' => 'string', 'max' => 131072, 'min' => 1, 'pattern' => '[\\u0009\\u000A\\u000D\\u0020-\\u007E\\u00A1-\\u00FF]+', ], 'AwsIamRoleDetails' => [ 'type' => 'structure', 'members' => [ 'AssumeRolePolicyDocument' => [ 'shape' => 'AwsIamRoleAssumeRolePolicyDocument', ], 'AttachedManagedPolicies' => [ 'shape' => 'AwsIamAttachedManagedPolicyList', ], 'CreateDate' => [ 'shape' => 'NonEmptyString', ], 'InstanceProfileList' => [ 'shape' => 'AwsIamInstanceProfileList', ], 'PermissionsBoundary' => [ 'shape' => 'AwsIamPermissionsBoundary', ], 'RoleId' => [ 'shape' => 'NonEmptyString', ], 'RoleName' => [ 'shape' => 'NonEmptyString', ], 'RolePolicyList' => [ 'shape' => 'AwsIamRolePolicyList', ], 'MaxSessionDuration' => [ 'shape' => 'Integer', ], 'Path' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsIamRolePolicy' => [ 'type' => 'structure', 'members' => [ 'PolicyName' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsIamRolePolicyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsIamRolePolicy', ], ], 'AwsIamUserDetails' => [ 'type' => 'structure', 'members' => [ 'AttachedManagedPolicies' => [ 'shape' => 'AwsIamAttachedManagedPolicyList', ], 'CreateDate' => [ 'shape' => 'NonEmptyString', ], 'GroupList' => [ 'shape' => 'StringList', ], 'Path' => [ 'shape' => 'NonEmptyString', ], 'PermissionsBoundary' => [ 'shape' => 'AwsIamPermissionsBoundary', ], 'UserId' => [ 'shape' => 'NonEmptyString', ], 'UserName' => [ 'shape' => 'NonEmptyString', ], 'UserPolicyList' => [ 'shape' => 'AwsIamUserPolicyList', ], ], ], 'AwsIamUserPolicy' => [ 'type' => 'structure', 'members' => [ 'PolicyName' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsIamUserPolicyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsIamUserPolicy', ], ], 'AwsKinesisStreamDetails' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'Arn' => [ 'shape' => 'NonEmptyString', ], 'StreamEncryption' => [ 'shape' => 'AwsKinesisStreamStreamEncryptionDetails', ], 'ShardCount' => [ 'shape' => 'Integer', ], 'RetentionPeriodHours' => [ 'shape' => 'Integer', ], ], ], 'AwsKinesisStreamStreamEncryptionDetails' => [ 'type' => 'structure', 'members' => [ 'EncryptionType' => [ 'shape' => 'NonEmptyString', ], 'KeyId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsKmsKeyDetails' => [ 'type' => 'structure', 'members' => [ 'AWSAccountId' => [ 'shape' => 'NonEmptyString', ], 'CreationDate' => [ 'shape' => 'Double', ], 'KeyId' => [ 'shape' => 'NonEmptyString', ], 'KeyManager' => [ 'shape' => 'NonEmptyString', ], 'KeyState' => [ 'shape' => 'NonEmptyString', ], 'Origin' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'KeyRotationStatus' => [ 'shape' => 'Boolean', ], ], ], 'AwsLambdaFunctionCode' => [ 'type' => 'structure', 'members' => [ 'S3Bucket' => [ 'shape' => 'NonEmptyString', ], 'S3Key' => [ 'shape' => 'NonEmptyString', ], 'S3ObjectVersion' => [ 'shape' => 'NonEmptyString', ], 'ZipFile' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsLambdaFunctionDeadLetterConfig' => [ 'type' => 'structure', 'members' => [ 'TargetArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsLambdaFunctionDetails' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'AwsLambdaFunctionCode', ], 'CodeSha256' => [ 'shape' => 'NonEmptyString', ], 'DeadLetterConfig' => [ 'shape' => 'AwsLambdaFunctionDeadLetterConfig', ], 'Environment' => [ 'shape' => 'AwsLambdaFunctionEnvironment', ], 'FunctionName' => [ 'shape' => 'NonEmptyString', ], 'Handler' => [ 'shape' => 'NonEmptyString', ], 'KmsKeyArn' => [ 'shape' => 'NonEmptyString', ], 'LastModified' => [ 'shape' => 'NonEmptyString', ], 'Layers' => [ 'shape' => 'AwsLambdaFunctionLayerList', ], 'MasterArn' => [ 'shape' => 'NonEmptyString', ], 'MemorySize' => [ 'shape' => 'Integer', ], 'RevisionId' => [ 'shape' => 'NonEmptyString', ], 'Role' => [ 'shape' => 'NonEmptyString', ], 'Runtime' => [ 'shape' => 'NonEmptyString', ], 'Timeout' => [ 'shape' => 'Integer', ], 'TracingConfig' => [ 'shape' => 'AwsLambdaFunctionTracingConfig', ], 'VpcConfig' => [ 'shape' => 'AwsLambdaFunctionVpcConfig', ], 'Version' => [ 'shape' => 'NonEmptyString', ], 'Architectures' => [ 'shape' => 'NonEmptyStringList', ], 'PackageType' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsLambdaFunctionEnvironment' => [ 'type' => 'structure', 'members' => [ 'Variables' => [ 'shape' => 'FieldMap', ], 'Error' => [ 'shape' => 'AwsLambdaFunctionEnvironmentError', ], ], ], 'AwsLambdaFunctionEnvironmentError' => [ 'type' => 'structure', 'members' => [ 'ErrorCode' => [ 'shape' => 'NonEmptyString', ], 'Message' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsLambdaFunctionLayer' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'NonEmptyString', ], 'CodeSize' => [ 'shape' => 'Integer', ], ], ], 'AwsLambdaFunctionLayerList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsLambdaFunctionLayer', ], ], 'AwsLambdaFunctionTracingConfig' => [ 'type' => 'structure', 'members' => [ 'Mode' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsLambdaFunctionVpcConfig' => [ 'type' => 'structure', 'members' => [ 'SecurityGroupIds' => [ 'shape' => 'NonEmptyStringList', ], 'SubnetIds' => [ 'shape' => 'NonEmptyStringList', ], 'VpcId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsLambdaLayerVersionDetails' => [ 'type' => 'structure', 'members' => [ 'Version' => [ 'shape' => 'AwsLambdaLayerVersionNumber', ], 'CompatibleRuntimes' => [ 'shape' => 'NonEmptyStringList', ], 'CreatedDate' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsLambdaLayerVersionNumber' => [ 'type' => 'long', ], 'AwsMountPoint' => [ 'type' => 'structure', 'members' => [ 'SourceVolume' => [ 'shape' => 'NonEmptyString', ], 'ContainerPath' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsMountPointList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsMountPoint', ], ], 'AwsMskClusterClusterInfoClientAuthenticationDetails' => [ 'type' => 'structure', 'members' => [ 'Sasl' => [ 'shape' => 'AwsMskClusterClusterInfoClientAuthenticationSaslDetails', ], 'Unauthenticated' => [ 'shape' => 'AwsMskClusterClusterInfoClientAuthenticationUnauthenticatedDetails', ], 'Tls' => [ 'shape' => 'AwsMskClusterClusterInfoClientAuthenticationTlsDetails', ], ], ], 'AwsMskClusterClusterInfoClientAuthenticationSaslDetails' => [ 'type' => 'structure', 'members' => [ 'Iam' => [ 'shape' => 'AwsMskClusterClusterInfoClientAuthenticationSaslIamDetails', ], 'Scram' => [ 'shape' => 'AwsMskClusterClusterInfoClientAuthenticationSaslScramDetails', ], ], ], 'AwsMskClusterClusterInfoClientAuthenticationSaslIamDetails' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], ], ], 'AwsMskClusterClusterInfoClientAuthenticationSaslScramDetails' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], ], ], 'AwsMskClusterClusterInfoClientAuthenticationTlsDetails' => [ 'type' => 'structure', 'members' => [ 'CertificateAuthorityArnList' => [ 'shape' => 'StringList', ], 'Enabled' => [ 'shape' => 'Boolean', ], ], ], 'AwsMskClusterClusterInfoClientAuthenticationUnauthenticatedDetails' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], ], ], 'AwsMskClusterClusterInfoDetails' => [ 'type' => 'structure', 'members' => [ 'EncryptionInfo' => [ 'shape' => 'AwsMskClusterClusterInfoEncryptionInfoDetails', ], 'CurrentVersion' => [ 'shape' => 'NonEmptyString', ], 'NumberOfBrokerNodes' => [ 'shape' => 'Integer', ], 'ClusterName' => [ 'shape' => 'NonEmptyString', ], 'ClientAuthentication' => [ 'shape' => 'AwsMskClusterClusterInfoClientAuthenticationDetails', ], 'EnhancedMonitoring' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsMskClusterClusterInfoEncryptionInfoDetails' => [ 'type' => 'structure', 'members' => [ 'EncryptionInTransit' => [ 'shape' => 'AwsMskClusterClusterInfoEncryptionInfoEncryptionInTransitDetails', ], 'EncryptionAtRest' => [ 'shape' => 'AwsMskClusterClusterInfoEncryptionInfoEncryptionAtRestDetails', ], ], ], 'AwsMskClusterClusterInfoEncryptionInfoEncryptionAtRestDetails' => [ 'type' => 'structure', 'members' => [ 'DataVolumeKMSKeyId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsMskClusterClusterInfoEncryptionInfoEncryptionInTransitDetails' => [ 'type' => 'structure', 'members' => [ 'InCluster' => [ 'shape' => 'Boolean', ], 'ClientBroker' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsMskClusterDetails' => [ 'type' => 'structure', 'members' => [ 'ClusterInfo' => [ 'shape' => 'AwsMskClusterClusterInfoDetails', ], ], ], 'AwsNetworkFirewallFirewallDetails' => [ 'type' => 'structure', 'members' => [ 'DeleteProtection' => [ 'shape' => 'Boolean', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'FirewallArn' => [ 'shape' => 'NonEmptyString', ], 'FirewallId' => [ 'shape' => 'NonEmptyString', ], 'FirewallName' => [ 'shape' => 'NonEmptyString', ], 'FirewallPolicyArn' => [ 'shape' => 'NonEmptyString', ], 'FirewallPolicyChangeProtection' => [ 'shape' => 'Boolean', ], 'SubnetChangeProtection' => [ 'shape' => 'Boolean', ], 'SubnetMappings' => [ 'shape' => 'AwsNetworkFirewallFirewallSubnetMappingsList', ], 'VpcId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsNetworkFirewallFirewallPolicyDetails' => [ 'type' => 'structure', 'members' => [ 'FirewallPolicy' => [ 'shape' => 'FirewallPolicyDetails', ], 'FirewallPolicyArn' => [ 'shape' => 'NonEmptyString', ], 'FirewallPolicyId' => [ 'shape' => 'NonEmptyString', ], 'FirewallPolicyName' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsNetworkFirewallFirewallSubnetMappingsDetails' => [ 'type' => 'structure', 'members' => [ 'SubnetId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsNetworkFirewallFirewallSubnetMappingsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsNetworkFirewallFirewallSubnetMappingsDetails', ], ], 'AwsNetworkFirewallRuleGroupDetails' => [ 'type' => 'structure', 'members' => [ 'Capacity' => [ 'shape' => 'Integer', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'RuleGroup' => [ 'shape' => 'RuleGroupDetails', ], 'RuleGroupArn' => [ 'shape' => 'NonEmptyString', ], 'RuleGroupId' => [ 'shape' => 'NonEmptyString', ], 'RuleGroupName' => [ 'shape' => 'NonEmptyString', ], 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsOpenSearchServiceDomainAdvancedSecurityOptionsDetails' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], 'InternalUserDatabaseEnabled' => [ 'shape' => 'Boolean', ], 'MasterUserOptions' => [ 'shape' => 'AwsOpenSearchServiceDomainMasterUserOptionsDetails', ], ], ], 'AwsOpenSearchServiceDomainClusterConfigDetails' => [ 'type' => 'structure', 'members' => [ 'InstanceCount' => [ 'shape' => 'Integer', ], 'WarmEnabled' => [ 'shape' => 'Boolean', ], 'WarmCount' => [ 'shape' => 'Integer', ], 'DedicatedMasterEnabled' => [ 'shape' => 'Boolean', ], 'ZoneAwarenessConfig' => [ 'shape' => 'AwsOpenSearchServiceDomainClusterConfigZoneAwarenessConfigDetails', ], 'DedicatedMasterCount' => [ 'shape' => 'Integer', ], 'InstanceType' => [ 'shape' => 'NonEmptyString', ], 'WarmType' => [ 'shape' => 'NonEmptyString', ], 'ZoneAwarenessEnabled' => [ 'shape' => 'Boolean', ], 'DedicatedMasterType' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsOpenSearchServiceDomainClusterConfigZoneAwarenessConfigDetails' => [ 'type' => 'structure', 'members' => [ 'AvailabilityZoneCount' => [ 'shape' => 'Integer', ], ], ], 'AwsOpenSearchServiceDomainDetails' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'NonEmptyString', ], 'AccessPolicies' => [ 'shape' => 'NonEmptyString', ], 'DomainName' => [ 'shape' => 'NonEmptyString', ], 'Id' => [ 'shape' => 'NonEmptyString', ], 'DomainEndpoint' => [ 'shape' => 'NonEmptyString', ], 'EngineVersion' => [ 'shape' => 'NonEmptyString', ], 'EncryptionAtRestOptions' => [ 'shape' => 'AwsOpenSearchServiceDomainEncryptionAtRestOptionsDetails', ], 'NodeToNodeEncryptionOptions' => [ 'shape' => 'AwsOpenSearchServiceDomainNodeToNodeEncryptionOptionsDetails', ], 'ServiceSoftwareOptions' => [ 'shape' => 'AwsOpenSearchServiceDomainServiceSoftwareOptionsDetails', ], 'ClusterConfig' => [ 'shape' => 'AwsOpenSearchServiceDomainClusterConfigDetails', ], 'DomainEndpointOptions' => [ 'shape' => 'AwsOpenSearchServiceDomainDomainEndpointOptionsDetails', ], 'VpcOptions' => [ 'shape' => 'AwsOpenSearchServiceDomainVpcOptionsDetails', ], 'LogPublishingOptions' => [ 'shape' => 'AwsOpenSearchServiceDomainLogPublishingOptionsDetails', ], 'DomainEndpoints' => [ 'shape' => 'FieldMap', ], 'AdvancedSecurityOptions' => [ 'shape' => 'AwsOpenSearchServiceDomainAdvancedSecurityOptionsDetails', ], ], ], 'AwsOpenSearchServiceDomainDomainEndpointOptionsDetails' => [ 'type' => 'structure', 'members' => [ 'CustomEndpointCertificateArn' => [ 'shape' => 'NonEmptyString', ], 'CustomEndpointEnabled' => [ 'shape' => 'Boolean', ], 'EnforceHTTPS' => [ 'shape' => 'Boolean', ], 'CustomEndpoint' => [ 'shape' => 'NonEmptyString', ], 'TLSSecurityPolicy' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsOpenSearchServiceDomainEncryptionAtRestOptionsDetails' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], 'KmsKeyId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsOpenSearchServiceDomainLogPublishingOption' => [ 'type' => 'structure', 'members' => [ 'CloudWatchLogsLogGroupArn' => [ 'shape' => 'NonEmptyString', ], 'Enabled' => [ 'shape' => 'Boolean', ], ], ], 'AwsOpenSearchServiceDomainLogPublishingOptionsDetails' => [ 'type' => 'structure', 'members' => [ 'IndexSlowLogs' => [ 'shape' => 'AwsOpenSearchServiceDomainLogPublishingOption', ], 'SearchSlowLogs' => [ 'shape' => 'AwsOpenSearchServiceDomainLogPublishingOption', ], 'AuditLogs' => [ 'shape' => 'AwsOpenSearchServiceDomainLogPublishingOption', ], ], ], 'AwsOpenSearchServiceDomainMasterUserOptionsDetails' => [ 'type' => 'structure', 'members' => [ 'MasterUserArn' => [ 'shape' => 'NonEmptyString', ], 'MasterUserName' => [ 'shape' => 'NonEmptyString', ], 'MasterUserPassword' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsOpenSearchServiceDomainNodeToNodeEncryptionOptionsDetails' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], ], ], 'AwsOpenSearchServiceDomainServiceSoftwareOptionsDetails' => [ 'type' => 'structure', 'members' => [ 'AutomatedUpdateDate' => [ 'shape' => 'NonEmptyString', ], 'Cancellable' => [ 'shape' => 'Boolean', ], 'CurrentVersion' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'NewVersion' => [ 'shape' => 'NonEmptyString', ], 'UpdateAvailable' => [ 'shape' => 'Boolean', ], 'UpdateStatus' => [ 'shape' => 'NonEmptyString', ], 'OptionalDeployment' => [ 'shape' => 'Boolean', ], ], ], 'AwsOpenSearchServiceDomainVpcOptionsDetails' => [ 'type' => 'structure', 'members' => [ 'SecurityGroupIds' => [ 'shape' => 'NonEmptyStringList', ], 'SubnetIds' => [ 'shape' => 'NonEmptyStringList', ], ], ], 'AwsRdsDbClusterAssociatedRole' => [ 'type' => 'structure', 'members' => [ 'RoleArn' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRdsDbClusterAssociatedRoles' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsRdsDbClusterAssociatedRole', ], ], 'AwsRdsDbClusterDetails' => [ 'type' => 'structure', 'members' => [ 'AllocatedStorage' => [ 'shape' => 'Integer', ], 'AvailabilityZones' => [ 'shape' => 'StringList', ], 'BackupRetentionPeriod' => [ 'shape' => 'Integer', ], 'DatabaseName' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], 'Endpoint' => [ 'shape' => 'NonEmptyString', ], 'ReaderEndpoint' => [ 'shape' => 'NonEmptyString', ], 'CustomEndpoints' => [ 'shape' => 'StringList', ], 'MultiAz' => [ 'shape' => 'Boolean', ], 'Engine' => [ 'shape' => 'NonEmptyString', ], 'EngineVersion' => [ 'shape' => 'NonEmptyString', ], 'Port' => [ 'shape' => 'Integer', ], 'MasterUsername' => [ 'shape' => 'NonEmptyString', ], 'PreferredBackupWindow' => [ 'shape' => 'NonEmptyString', ], 'PreferredMaintenanceWindow' => [ 'shape' => 'NonEmptyString', ], 'ReadReplicaIdentifiers' => [ 'shape' => 'StringList', ], 'VpcSecurityGroups' => [ 'shape' => 'AwsRdsDbInstanceVpcSecurityGroups', ], 'HostedZoneId' => [ 'shape' => 'NonEmptyString', ], 'StorageEncrypted' => [ 'shape' => 'Boolean', ], 'KmsKeyId' => [ 'shape' => 'NonEmptyString', ], 'DbClusterResourceId' => [ 'shape' => 'NonEmptyString', ], 'AssociatedRoles' => [ 'shape' => 'AwsRdsDbClusterAssociatedRoles', ], 'ClusterCreateTime' => [ 'shape' => 'NonEmptyString', ], 'EnabledCloudWatchLogsExports' => [ 'shape' => 'StringList', ], 'EngineMode' => [ 'shape' => 'NonEmptyString', ], 'DeletionProtection' => [ 'shape' => 'Boolean', ], 'HttpEndpointEnabled' => [ 'shape' => 'Boolean', ], 'ActivityStreamStatus' => [ 'shape' => 'NonEmptyString', ], 'CopyTagsToSnapshot' => [ 'shape' => 'Boolean', ], 'CrossAccountClone' => [ 'shape' => 'Boolean', ], 'DomainMemberships' => [ 'shape' => 'AwsRdsDbDomainMemberships', ], 'DbClusterParameterGroup' => [ 'shape' => 'NonEmptyString', ], 'DbSubnetGroup' => [ 'shape' => 'NonEmptyString', ], 'DbClusterOptionGroupMemberships' => [ 'shape' => 'AwsRdsDbClusterOptionGroupMemberships', ], 'DbClusterIdentifier' => [ 'shape' => 'NonEmptyString', ], 'DbClusterMembers' => [ 'shape' => 'AwsRdsDbClusterMembers', ], 'IamDatabaseAuthenticationEnabled' => [ 'shape' => 'Boolean', ], 'AutoMinorVersionUpgrade' => [ 'shape' => 'Boolean', ], ], ], 'AwsRdsDbClusterMember' => [ 'type' => 'structure', 'members' => [ 'IsClusterWriter' => [ 'shape' => 'Boolean', ], 'PromotionTier' => [ 'shape' => 'Integer', ], 'DbInstanceIdentifier' => [ 'shape' => 'NonEmptyString', ], 'DbClusterParameterGroupStatus' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRdsDbClusterMembers' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsRdsDbClusterMember', ], ], 'AwsRdsDbClusterOptionGroupMembership' => [ 'type' => 'structure', 'members' => [ 'DbClusterOptionGroupName' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRdsDbClusterOptionGroupMemberships' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsRdsDbClusterOptionGroupMembership', ], ], 'AwsRdsDbClusterSnapshotDbClusterSnapshotAttribute' => [ 'type' => 'structure', 'members' => [ 'AttributeName' => [ 'shape' => 'NonEmptyString', ], 'AttributeValues' => [ 'shape' => 'NonEmptyStringList', ], ], ], 'AwsRdsDbClusterSnapshotDbClusterSnapshotAttributes' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsRdsDbClusterSnapshotDbClusterSnapshotAttribute', ], ], 'AwsRdsDbClusterSnapshotDetails' => [ 'type' => 'structure', 'members' => [ 'AvailabilityZones' => [ 'shape' => 'StringList', ], 'SnapshotCreateTime' => [ 'shape' => 'NonEmptyString', ], 'Engine' => [ 'shape' => 'NonEmptyString', ], 'AllocatedStorage' => [ 'shape' => 'Integer', ], 'Status' => [ 'shape' => 'NonEmptyString', ], 'Port' => [ 'shape' => 'Integer', ], 'VpcId' => [ 'shape' => 'NonEmptyString', ], 'ClusterCreateTime' => [ 'shape' => 'NonEmptyString', ], 'MasterUsername' => [ 'shape' => 'NonEmptyString', ], 'EngineVersion' => [ 'shape' => 'NonEmptyString', ], 'LicenseModel' => [ 'shape' => 'NonEmptyString', ], 'SnapshotType' => [ 'shape' => 'NonEmptyString', ], 'PercentProgress' => [ 'shape' => 'Integer', ], 'StorageEncrypted' => [ 'shape' => 'Boolean', ], 'KmsKeyId' => [ 'shape' => 'NonEmptyString', ], 'DbClusterIdentifier' => [ 'shape' => 'NonEmptyString', ], 'DbClusterSnapshotIdentifier' => [ 'shape' => 'NonEmptyString', ], 'IamDatabaseAuthenticationEnabled' => [ 'shape' => 'Boolean', ], 'DbClusterSnapshotAttributes' => [ 'shape' => 'AwsRdsDbClusterSnapshotDbClusterSnapshotAttributes', ], ], ], 'AwsRdsDbDomainMembership' => [ 'type' => 'structure', 'members' => [ 'Domain' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], 'Fqdn' => [ 'shape' => 'NonEmptyString', ], 'IamRoleName' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRdsDbDomainMemberships' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsRdsDbDomainMembership', ], ], 'AwsRdsDbInstanceAssociatedRole' => [ 'type' => 'structure', 'members' => [ 'RoleArn' => [ 'shape' => 'NonEmptyString', ], 'FeatureName' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRdsDbInstanceAssociatedRoles' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsRdsDbInstanceAssociatedRole', ], ], 'AwsRdsDbInstanceDetails' => [ 'type' => 'structure', 'members' => [ 'AssociatedRoles' => [ 'shape' => 'AwsRdsDbInstanceAssociatedRoles', ], 'CACertificateIdentifier' => [ 'shape' => 'NonEmptyString', ], 'DBClusterIdentifier' => [ 'shape' => 'NonEmptyString', ], 'DBInstanceIdentifier' => [ 'shape' => 'NonEmptyString', ], 'DBInstanceClass' => [ 'shape' => 'NonEmptyString', ], 'DbInstancePort' => [ 'shape' => 'Integer', ], 'DbiResourceId' => [ 'shape' => 'NonEmptyString', ], 'DBName' => [ 'shape' => 'NonEmptyString', ], 'DeletionProtection' => [ 'shape' => 'Boolean', ], 'Endpoint' => [ 'shape' => 'AwsRdsDbInstanceEndpoint', ], 'Engine' => [ 'shape' => 'NonEmptyString', ], 'EngineVersion' => [ 'shape' => 'NonEmptyString', ], 'IAMDatabaseAuthenticationEnabled' => [ 'shape' => 'Boolean', ], 'InstanceCreateTime' => [ 'shape' => 'NonEmptyString', ], 'KmsKeyId' => [ 'shape' => 'NonEmptyString', ], 'PubliclyAccessible' => [ 'shape' => 'Boolean', ], 'StorageEncrypted' => [ 'shape' => 'Boolean', ], 'TdeCredentialArn' => [ 'shape' => 'NonEmptyString', ], 'VpcSecurityGroups' => [ 'shape' => 'AwsRdsDbInstanceVpcSecurityGroups', ], 'MultiAz' => [ 'shape' => 'Boolean', ], 'EnhancedMonitoringResourceArn' => [ 'shape' => 'NonEmptyString', ], 'DbInstanceStatus' => [ 'shape' => 'NonEmptyString', ], 'MasterUsername' => [ 'shape' => 'NonEmptyString', ], 'AllocatedStorage' => [ 'shape' => 'Integer', ], 'PreferredBackupWindow' => [ 'shape' => 'NonEmptyString', ], 'BackupRetentionPeriod' => [ 'shape' => 'Integer', ], 'DbSecurityGroups' => [ 'shape' => 'StringList', ], 'DbParameterGroups' => [ 'shape' => 'AwsRdsDbParameterGroups', ], 'AvailabilityZone' => [ 'shape' => 'NonEmptyString', ], 'DbSubnetGroup' => [ 'shape' => 'AwsRdsDbSubnetGroup', ], 'PreferredMaintenanceWindow' => [ 'shape' => 'NonEmptyString', ], 'PendingModifiedValues' => [ 'shape' => 'AwsRdsDbPendingModifiedValues', ], 'LatestRestorableTime' => [ 'shape' => 'NonEmptyString', ], 'AutoMinorVersionUpgrade' => [ 'shape' => 'Boolean', ], 'ReadReplicaSourceDBInstanceIdentifier' => [ 'shape' => 'NonEmptyString', ], 'ReadReplicaDBInstanceIdentifiers' => [ 'shape' => 'StringList', ], 'ReadReplicaDBClusterIdentifiers' => [ 'shape' => 'StringList', ], 'LicenseModel' => [ 'shape' => 'NonEmptyString', ], 'Iops' => [ 'shape' => 'Integer', ], 'OptionGroupMemberships' => [ 'shape' => 'AwsRdsDbOptionGroupMemberships', ], 'CharacterSetName' => [ 'shape' => 'NonEmptyString', ], 'SecondaryAvailabilityZone' => [ 'shape' => 'NonEmptyString', ], 'StatusInfos' => [ 'shape' => 'AwsRdsDbStatusInfos', ], 'StorageType' => [ 'shape' => 'NonEmptyString', ], 'DomainMemberships' => [ 'shape' => 'AwsRdsDbDomainMemberships', ], 'CopyTagsToSnapshot' => [ 'shape' => 'Boolean', ], 'MonitoringInterval' => [ 'shape' => 'Integer', ], 'MonitoringRoleArn' => [ 'shape' => 'NonEmptyString', ], 'PromotionTier' => [ 'shape' => 'Integer', ], 'Timezone' => [ 'shape' => 'NonEmptyString', ], 'PerformanceInsightsEnabled' => [ 'shape' => 'Boolean', ], 'PerformanceInsightsKmsKeyId' => [ 'shape' => 'NonEmptyString', ], 'PerformanceInsightsRetentionPeriod' => [ 'shape' => 'Integer', ], 'EnabledCloudWatchLogsExports' => [ 'shape' => 'StringList', ], 'ProcessorFeatures' => [ 'shape' => 'AwsRdsDbProcessorFeatures', ], 'ListenerEndpoint' => [ 'shape' => 'AwsRdsDbInstanceEndpoint', ], 'MaxAllocatedStorage' => [ 'shape' => 'Integer', ], ], ], 'AwsRdsDbInstanceEndpoint' => [ 'type' => 'structure', 'members' => [ 'Address' => [ 'shape' => 'NonEmptyString', ], 'Port' => [ 'shape' => 'Integer', ], 'HostedZoneId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRdsDbInstanceVpcSecurityGroup' => [ 'type' => 'structure', 'members' => [ 'VpcSecurityGroupId' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRdsDbInstanceVpcSecurityGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsRdsDbInstanceVpcSecurityGroup', ], ], 'AwsRdsDbOptionGroupMembership' => [ 'type' => 'structure', 'members' => [ 'OptionGroupName' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRdsDbOptionGroupMemberships' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsRdsDbOptionGroupMembership', ], ], 'AwsRdsDbParameterGroup' => [ 'type' => 'structure', 'members' => [ 'DbParameterGroupName' => [ 'shape' => 'NonEmptyString', ], 'ParameterApplyStatus' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRdsDbParameterGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsRdsDbParameterGroup', ], ], 'AwsRdsDbPendingModifiedValues' => [ 'type' => 'structure', 'members' => [ 'DbInstanceClass' => [ 'shape' => 'NonEmptyString', ], 'AllocatedStorage' => [ 'shape' => 'Integer', ], 'MasterUserPassword' => [ 'shape' => 'NonEmptyString', ], 'Port' => [ 'shape' => 'Integer', ], 'BackupRetentionPeriod' => [ 'shape' => 'Integer', ], 'MultiAZ' => [ 'shape' => 'Boolean', ], 'EngineVersion' => [ 'shape' => 'NonEmptyString', ], 'LicenseModel' => [ 'shape' => 'NonEmptyString', ], 'Iops' => [ 'shape' => 'Integer', ], 'DbInstanceIdentifier' => [ 'shape' => 'NonEmptyString', ], 'StorageType' => [ 'shape' => 'NonEmptyString', ], 'CaCertificateIdentifier' => [ 'shape' => 'NonEmptyString', ], 'DbSubnetGroupName' => [ 'shape' => 'NonEmptyString', ], 'PendingCloudWatchLogsExports' => [ 'shape' => 'AwsRdsPendingCloudWatchLogsExports', ], 'ProcessorFeatures' => [ 'shape' => 'AwsRdsDbProcessorFeatures', ], ], ], 'AwsRdsDbProcessorFeature' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'Value' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRdsDbProcessorFeatures' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsRdsDbProcessorFeature', ], ], 'AwsRdsDbSecurityGroupDetails' => [ 'type' => 'structure', 'members' => [ 'DbSecurityGroupArn' => [ 'shape' => 'NonEmptyString', ], 'DbSecurityGroupDescription' => [ 'shape' => 'NonEmptyString', ], 'DbSecurityGroupName' => [ 'shape' => 'NonEmptyString', ], 'Ec2SecurityGroups' => [ 'shape' => 'AwsRdsDbSecurityGroupEc2SecurityGroups', ], 'IpRanges' => [ 'shape' => 'AwsRdsDbSecurityGroupIpRanges', ], 'OwnerId' => [ 'shape' => 'NonEmptyString', ], 'VpcId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRdsDbSecurityGroupEc2SecurityGroup' => [ 'type' => 'structure', 'members' => [ 'Ec2SecurityGroupId' => [ 'shape' => 'NonEmptyString', ], 'Ec2SecurityGroupName' => [ 'shape' => 'NonEmptyString', ], 'Ec2SecurityGroupOwnerId' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRdsDbSecurityGroupEc2SecurityGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsRdsDbSecurityGroupEc2SecurityGroup', ], ], 'AwsRdsDbSecurityGroupIpRange' => [ 'type' => 'structure', 'members' => [ 'CidrIp' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRdsDbSecurityGroupIpRanges' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsRdsDbSecurityGroupIpRange', ], ], 'AwsRdsDbSnapshotDetails' => [ 'type' => 'structure', 'members' => [ 'DbSnapshotIdentifier' => [ 'shape' => 'NonEmptyString', ], 'DbInstanceIdentifier' => [ 'shape' => 'NonEmptyString', ], 'SnapshotCreateTime' => [ 'shape' => 'NonEmptyString', ], 'Engine' => [ 'shape' => 'NonEmptyString', ], 'AllocatedStorage' => [ 'shape' => 'Integer', ], 'Status' => [ 'shape' => 'NonEmptyString', ], 'Port' => [ 'shape' => 'Integer', ], 'AvailabilityZone' => [ 'shape' => 'NonEmptyString', ], 'VpcId' => [ 'shape' => 'NonEmptyString', ], 'InstanceCreateTime' => [ 'shape' => 'NonEmptyString', ], 'MasterUsername' => [ 'shape' => 'NonEmptyString', ], 'EngineVersion' => [ 'shape' => 'NonEmptyString', ], 'LicenseModel' => [ 'shape' => 'NonEmptyString', ], 'SnapshotType' => [ 'shape' => 'NonEmptyString', ], 'Iops' => [ 'shape' => 'Integer', ], 'OptionGroupName' => [ 'shape' => 'NonEmptyString', ], 'PercentProgress' => [ 'shape' => 'Integer', ], 'SourceRegion' => [ 'shape' => 'NonEmptyString', ], 'SourceDbSnapshotIdentifier' => [ 'shape' => 'NonEmptyString', ], 'StorageType' => [ 'shape' => 'NonEmptyString', ], 'TdeCredentialArn' => [ 'shape' => 'NonEmptyString', ], 'Encrypted' => [ 'shape' => 'Boolean', ], 'KmsKeyId' => [ 'shape' => 'NonEmptyString', ], 'Timezone' => [ 'shape' => 'NonEmptyString', ], 'IamDatabaseAuthenticationEnabled' => [ 'shape' => 'Boolean', ], 'ProcessorFeatures' => [ 'shape' => 'AwsRdsDbProcessorFeatures', ], 'DbiResourceId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRdsDbStatusInfo' => [ 'type' => 'structure', 'members' => [ 'StatusType' => [ 'shape' => 'NonEmptyString', ], 'Normal' => [ 'shape' => 'Boolean', ], 'Status' => [ 'shape' => 'NonEmptyString', ], 'Message' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRdsDbStatusInfos' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsRdsDbStatusInfo', ], ], 'AwsRdsDbSubnetGroup' => [ 'type' => 'structure', 'members' => [ 'DbSubnetGroupName' => [ 'shape' => 'NonEmptyString', ], 'DbSubnetGroupDescription' => [ 'shape' => 'NonEmptyString', ], 'VpcId' => [ 'shape' => 'NonEmptyString', ], 'SubnetGroupStatus' => [ 'shape' => 'NonEmptyString', ], 'Subnets' => [ 'shape' => 'AwsRdsDbSubnetGroupSubnets', ], 'DbSubnetGroupArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRdsDbSubnetGroupSubnet' => [ 'type' => 'structure', 'members' => [ 'SubnetIdentifier' => [ 'shape' => 'NonEmptyString', ], 'SubnetAvailabilityZone' => [ 'shape' => 'AwsRdsDbSubnetGroupSubnetAvailabilityZone', ], 'SubnetStatus' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRdsDbSubnetGroupSubnetAvailabilityZone' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRdsDbSubnetGroupSubnets' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsRdsDbSubnetGroupSubnet', ], ], 'AwsRdsEventSubscriptionDetails' => [ 'type' => 'structure', 'members' => [ 'CustSubscriptionId' => [ 'shape' => 'NonEmptyString', ], 'CustomerAwsId' => [ 'shape' => 'NonEmptyString', ], 'Enabled' => [ 'shape' => 'Boolean', ], 'EventCategoriesList' => [ 'shape' => 'NonEmptyStringList', ], 'EventSubscriptionArn' => [ 'shape' => 'NonEmptyString', ], 'SnsTopicArn' => [ 'shape' => 'NonEmptyString', ], 'SourceIdsList' => [ 'shape' => 'NonEmptyStringList', ], 'SourceType' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], 'SubscriptionCreationTime' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRdsPendingCloudWatchLogsExports' => [ 'type' => 'structure', 'members' => [ 'LogTypesToEnable' => [ 'shape' => 'StringList', ], 'LogTypesToDisable' => [ 'shape' => 'StringList', ], ], ], 'AwsRedshiftClusterClusterNode' => [ 'type' => 'structure', 'members' => [ 'NodeRole' => [ 'shape' => 'NonEmptyString', ], 'PrivateIpAddress' => [ 'shape' => 'NonEmptyString', ], 'PublicIpAddress' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRedshiftClusterClusterNodes' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsRedshiftClusterClusterNode', ], ], 'AwsRedshiftClusterClusterParameterGroup' => [ 'type' => 'structure', 'members' => [ 'ClusterParameterStatusList' => [ 'shape' => 'AwsRedshiftClusterClusterParameterStatusList', ], 'ParameterApplyStatus' => [ 'shape' => 'NonEmptyString', ], 'ParameterGroupName' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRedshiftClusterClusterParameterGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsRedshiftClusterClusterParameterGroup', ], ], 'AwsRedshiftClusterClusterParameterStatus' => [ 'type' => 'structure', 'members' => [ 'ParameterName' => [ 'shape' => 'NonEmptyString', ], 'ParameterApplyStatus' => [ 'shape' => 'NonEmptyString', ], 'ParameterApplyErrorDescription' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRedshiftClusterClusterParameterStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsRedshiftClusterClusterParameterStatus', ], ], 'AwsRedshiftClusterClusterSecurityGroup' => [ 'type' => 'structure', 'members' => [ 'ClusterSecurityGroupName' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRedshiftClusterClusterSecurityGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsRedshiftClusterClusterSecurityGroup', ], ], 'AwsRedshiftClusterClusterSnapshotCopyStatus' => [ 'type' => 'structure', 'members' => [ 'DestinationRegion' => [ 'shape' => 'NonEmptyString', ], 'ManualSnapshotRetentionPeriod' => [ 'shape' => 'Integer', ], 'RetentionPeriod' => [ 'shape' => 'Integer', ], 'SnapshotCopyGrantName' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRedshiftClusterDeferredMaintenanceWindow' => [ 'type' => 'structure', 'members' => [ 'DeferMaintenanceEndTime' => [ 'shape' => 'NonEmptyString', ], 'DeferMaintenanceIdentifier' => [ 'shape' => 'NonEmptyString', ], 'DeferMaintenanceStartTime' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRedshiftClusterDeferredMaintenanceWindows' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsRedshiftClusterDeferredMaintenanceWindow', ], ], 'AwsRedshiftClusterDetails' => [ 'type' => 'structure', 'members' => [ 'AllowVersionUpgrade' => [ 'shape' => 'Boolean', ], 'AutomatedSnapshotRetentionPeriod' => [ 'shape' => 'Integer', ], 'AvailabilityZone' => [ 'shape' => 'NonEmptyString', ], 'ClusterAvailabilityStatus' => [ 'shape' => 'NonEmptyString', ], 'ClusterCreateTime' => [ 'shape' => 'NonEmptyString', ], 'ClusterIdentifier' => [ 'shape' => 'NonEmptyString', ], 'ClusterNodes' => [ 'shape' => 'AwsRedshiftClusterClusterNodes', ], 'ClusterParameterGroups' => [ 'shape' => 'AwsRedshiftClusterClusterParameterGroups', ], 'ClusterPublicKey' => [ 'shape' => 'NonEmptyString', ], 'ClusterRevisionNumber' => [ 'shape' => 'NonEmptyString', ], 'ClusterSecurityGroups' => [ 'shape' => 'AwsRedshiftClusterClusterSecurityGroups', ], 'ClusterSnapshotCopyStatus' => [ 'shape' => 'AwsRedshiftClusterClusterSnapshotCopyStatus', ], 'ClusterStatus' => [ 'shape' => 'NonEmptyString', ], 'ClusterSubnetGroupName' => [ 'shape' => 'NonEmptyString', ], 'ClusterVersion' => [ 'shape' => 'NonEmptyString', ], 'DBName' => [ 'shape' => 'NonEmptyString', ], 'DeferredMaintenanceWindows' => [ 'shape' => 'AwsRedshiftClusterDeferredMaintenanceWindows', ], 'ElasticIpStatus' => [ 'shape' => 'AwsRedshiftClusterElasticIpStatus', ], 'ElasticResizeNumberOfNodeOptions' => [ 'shape' => 'NonEmptyString', ], 'Encrypted' => [ 'shape' => 'Boolean', ], 'Endpoint' => [ 'shape' => 'AwsRedshiftClusterEndpoint', ], 'EnhancedVpcRouting' => [ 'shape' => 'Boolean', ], 'ExpectedNextSnapshotScheduleTime' => [ 'shape' => 'NonEmptyString', ], 'ExpectedNextSnapshotScheduleTimeStatus' => [ 'shape' => 'NonEmptyString', ], 'HsmStatus' => [ 'shape' => 'AwsRedshiftClusterHsmStatus', ], 'IamRoles' => [ 'shape' => 'AwsRedshiftClusterIamRoles', ], 'KmsKeyId' => [ 'shape' => 'NonEmptyString', ], 'MaintenanceTrackName' => [ 'shape' => 'NonEmptyString', ], 'ManualSnapshotRetentionPeriod' => [ 'shape' => 'Integer', ], 'MasterUsername' => [ 'shape' => 'NonEmptyString', ], 'NextMaintenanceWindowStartTime' => [ 'shape' => 'NonEmptyString', ], 'NodeType' => [ 'shape' => 'NonEmptyString', ], 'NumberOfNodes' => [ 'shape' => 'Integer', ], 'PendingActions' => [ 'shape' => 'StringList', ], 'PendingModifiedValues' => [ 'shape' => 'AwsRedshiftClusterPendingModifiedValues', ], 'PreferredMaintenanceWindow' => [ 'shape' => 'NonEmptyString', ], 'PubliclyAccessible' => [ 'shape' => 'Boolean', ], 'ResizeInfo' => [ 'shape' => 'AwsRedshiftClusterResizeInfo', ], 'RestoreStatus' => [ 'shape' => 'AwsRedshiftClusterRestoreStatus', ], 'SnapshotScheduleIdentifier' => [ 'shape' => 'NonEmptyString', ], 'SnapshotScheduleState' => [ 'shape' => 'NonEmptyString', ], 'VpcId' => [ 'shape' => 'NonEmptyString', ], 'VpcSecurityGroups' => [ 'shape' => 'AwsRedshiftClusterVpcSecurityGroups', ], 'LoggingStatus' => [ 'shape' => 'AwsRedshiftClusterLoggingStatus', ], ], ], 'AwsRedshiftClusterElasticIpStatus' => [ 'type' => 'structure', 'members' => [ 'ElasticIp' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRedshiftClusterEndpoint' => [ 'type' => 'structure', 'members' => [ 'Address' => [ 'shape' => 'NonEmptyString', ], 'Port' => [ 'shape' => 'Integer', ], ], ], 'AwsRedshiftClusterHsmStatus' => [ 'type' => 'structure', 'members' => [ 'HsmClientCertificateIdentifier' => [ 'shape' => 'NonEmptyString', ], 'HsmConfigurationIdentifier' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRedshiftClusterIamRole' => [ 'type' => 'structure', 'members' => [ 'ApplyStatus' => [ 'shape' => 'NonEmptyString', ], 'IamRoleArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRedshiftClusterIamRoles' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsRedshiftClusterIamRole', ], ], 'AwsRedshiftClusterLoggingStatus' => [ 'type' => 'structure', 'members' => [ 'BucketName' => [ 'shape' => 'NonEmptyString', ], 'LastFailureMessage' => [ 'shape' => 'NonEmptyString', ], 'LastFailureTime' => [ 'shape' => 'NonEmptyString', ], 'LastSuccessfulDeliveryTime' => [ 'shape' => 'NonEmptyString', ], 'LoggingEnabled' => [ 'shape' => 'Boolean', ], 'S3KeyPrefix' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRedshiftClusterPendingModifiedValues' => [ 'type' => 'structure', 'members' => [ 'AutomatedSnapshotRetentionPeriod' => [ 'shape' => 'Integer', ], 'ClusterIdentifier' => [ 'shape' => 'NonEmptyString', ], 'ClusterType' => [ 'shape' => 'NonEmptyString', ], 'ClusterVersion' => [ 'shape' => 'NonEmptyString', ], 'EncryptionType' => [ 'shape' => 'NonEmptyString', ], 'EnhancedVpcRouting' => [ 'shape' => 'Boolean', ], 'MaintenanceTrackName' => [ 'shape' => 'NonEmptyString', ], 'MasterUserPassword' => [ 'shape' => 'NonEmptyString', ], 'NodeType' => [ 'shape' => 'NonEmptyString', ], 'NumberOfNodes' => [ 'shape' => 'Integer', ], 'PubliclyAccessible' => [ 'shape' => 'Boolean', ], ], ], 'AwsRedshiftClusterResizeInfo' => [ 'type' => 'structure', 'members' => [ 'AllowCancelResize' => [ 'shape' => 'Boolean', ], 'ResizeType' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRedshiftClusterRestoreStatus' => [ 'type' => 'structure', 'members' => [ 'CurrentRestoreRateInMegaBytesPerSecond' => [ 'shape' => 'Double', ], 'ElapsedTimeInSeconds' => [ 'shape' => 'Long', ], 'EstimatedTimeToCompletionInSeconds' => [ 'shape' => 'Long', ], 'ProgressInMegaBytes' => [ 'shape' => 'Long', ], 'SnapshotSizeInMegaBytes' => [ 'shape' => 'Long', ], 'Status' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRedshiftClusterVpcSecurityGroup' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'NonEmptyString', ], 'VpcSecurityGroupId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRedshiftClusterVpcSecurityGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsRedshiftClusterVpcSecurityGroup', ], ], 'AwsRoute53HostedZoneConfigDetails' => [ 'type' => 'structure', 'members' => [ 'Comment' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRoute53HostedZoneDetails' => [ 'type' => 'structure', 'members' => [ 'HostedZone' => [ 'shape' => 'AwsRoute53HostedZoneObjectDetails', ], 'Vpcs' => [ 'shape' => 'AwsRoute53HostedZoneVpcsList', ], 'NameServers' => [ 'shape' => 'AwsRoute53HostedZoneNameServersList', ], 'QueryLoggingConfig' => [ 'shape' => 'AwsRoute53QueryLoggingConfigDetails', ], ], ], 'AwsRoute53HostedZoneNameServersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], ], 'AwsRoute53HostedZoneObjectDetails' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'NonEmptyString', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'Config' => [ 'shape' => 'AwsRoute53HostedZoneConfigDetails', ], ], ], 'AwsRoute53HostedZoneVpcDetails' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'NonEmptyString', ], 'Region' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRoute53HostedZoneVpcsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsRoute53HostedZoneVpcDetails', ], ], 'AwsRoute53QueryLoggingConfigDetails' => [ 'type' => 'structure', 'members' => [ 'CloudWatchLogsLogGroupArn' => [ 'shape' => 'CloudWatchLogsLogGroupArnConfigDetails', ], ], ], 'AwsS3AccessPointDetails' => [ 'type' => 'structure', 'members' => [ 'AccessPointArn' => [ 'shape' => 'NonEmptyString', ], 'Alias' => [ 'shape' => 'NonEmptyString', ], 'Bucket' => [ 'shape' => 'NonEmptyString', ], 'BucketAccountId' => [ 'shape' => 'NonEmptyString', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'NetworkOrigin' => [ 'shape' => 'NonEmptyString', ], 'PublicAccessBlockConfiguration' => [ 'shape' => 'AwsS3AccountPublicAccessBlockDetails', ], 'VpcConfiguration' => [ 'shape' => 'AwsS3AccessPointVpcConfigurationDetails', ], ], ], 'AwsS3AccessPointVpcConfigurationDetails' => [ 'type' => 'structure', 'members' => [ 'VpcId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsS3AccountPublicAccessBlockDetails' => [ 'type' => 'structure', 'members' => [ 'BlockPublicAcls' => [ 'shape' => 'Boolean', ], 'BlockPublicPolicy' => [ 'shape' => 'Boolean', ], 'IgnorePublicAcls' => [ 'shape' => 'Boolean', ], 'RestrictPublicBuckets' => [ 'shape' => 'Boolean', ], ], ], 'AwsS3BucketBucketLifecycleConfigurationDetails' => [ 'type' => 'structure', 'members' => [ 'Rules' => [ 'shape' => 'AwsS3BucketBucketLifecycleConfigurationRulesList', ], ], ], 'AwsS3BucketBucketLifecycleConfigurationRulesAbortIncompleteMultipartUploadDetails' => [ 'type' => 'structure', 'members' => [ 'DaysAfterInitiation' => [ 'shape' => 'Integer', ], ], ], 'AwsS3BucketBucketLifecycleConfigurationRulesDetails' => [ 'type' => 'structure', 'members' => [ 'AbortIncompleteMultipartUpload' => [ 'shape' => 'AwsS3BucketBucketLifecycleConfigurationRulesAbortIncompleteMultipartUploadDetails', ], 'ExpirationDate' => [ 'shape' => 'NonEmptyString', ], 'ExpirationInDays' => [ 'shape' => 'Integer', ], 'ExpiredObjectDeleteMarker' => [ 'shape' => 'Boolean', ], 'Filter' => [ 'shape' => 'AwsS3BucketBucketLifecycleConfigurationRulesFilterDetails', ], 'ID' => [ 'shape' => 'NonEmptyString', ], 'NoncurrentVersionExpirationInDays' => [ 'shape' => 'Integer', ], 'NoncurrentVersionTransitions' => [ 'shape' => 'AwsS3BucketBucketLifecycleConfigurationRulesNoncurrentVersionTransitionsList', ], 'Prefix' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], 'Transitions' => [ 'shape' => 'AwsS3BucketBucketLifecycleConfigurationRulesTransitionsList', ], ], ], 'AwsS3BucketBucketLifecycleConfigurationRulesFilterDetails' => [ 'type' => 'structure', 'members' => [ 'Predicate' => [ 'shape' => 'AwsS3BucketBucketLifecycleConfigurationRulesFilterPredicateDetails', ], ], ], 'AwsS3BucketBucketLifecycleConfigurationRulesFilterPredicateDetails' => [ 'type' => 'structure', 'members' => [ 'Operands' => [ 'shape' => 'AwsS3BucketBucketLifecycleConfigurationRulesFilterPredicateOperandsList', ], 'Prefix' => [ 'shape' => 'NonEmptyString', ], 'Tag' => [ 'shape' => 'AwsS3BucketBucketLifecycleConfigurationRulesFilterPredicateTagDetails', ], 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsS3BucketBucketLifecycleConfigurationRulesFilterPredicateOperandsDetails' => [ 'type' => 'structure', 'members' => [ 'Prefix' => [ 'shape' => 'NonEmptyString', ], 'Tag' => [ 'shape' => 'AwsS3BucketBucketLifecycleConfigurationRulesFilterPredicateOperandsTagDetails', ], 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsS3BucketBucketLifecycleConfigurationRulesFilterPredicateOperandsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsS3BucketBucketLifecycleConfigurationRulesFilterPredicateOperandsDetails', ], ], 'AwsS3BucketBucketLifecycleConfigurationRulesFilterPredicateOperandsTagDetails' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'NonEmptyString', ], 'Value' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsS3BucketBucketLifecycleConfigurationRulesFilterPredicateTagDetails' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'NonEmptyString', ], 'Value' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsS3BucketBucketLifecycleConfigurationRulesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsS3BucketBucketLifecycleConfigurationRulesDetails', ], ], 'AwsS3BucketBucketLifecycleConfigurationRulesNoncurrentVersionTransitionsDetails' => [ 'type' => 'structure', 'members' => [ 'Days' => [ 'shape' => 'Integer', ], 'StorageClass' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsS3BucketBucketLifecycleConfigurationRulesNoncurrentVersionTransitionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsS3BucketBucketLifecycleConfigurationRulesNoncurrentVersionTransitionsDetails', ], ], 'AwsS3BucketBucketLifecycleConfigurationRulesTransitionsDetails' => [ 'type' => 'structure', 'members' => [ 'Date' => [ 'shape' => 'NonEmptyString', ], 'Days' => [ 'shape' => 'Integer', ], 'StorageClass' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsS3BucketBucketLifecycleConfigurationRulesTransitionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsS3BucketBucketLifecycleConfigurationRulesTransitionsDetails', ], ], 'AwsS3BucketBucketVersioningConfiguration' => [ 'type' => 'structure', 'members' => [ 'IsMfaDeleteEnabled' => [ 'shape' => 'Boolean', ], 'Status' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsS3BucketDetails' => [ 'type' => 'structure', 'members' => [ 'OwnerId' => [ 'shape' => 'NonEmptyString', ], 'OwnerName' => [ 'shape' => 'NonEmptyString', ], 'OwnerAccountId' => [ 'shape' => 'NonEmptyString', ], 'CreatedAt' => [ 'shape' => 'NonEmptyString', ], 'ServerSideEncryptionConfiguration' => [ 'shape' => 'AwsS3BucketServerSideEncryptionConfiguration', ], 'BucketLifecycleConfiguration' => [ 'shape' => 'AwsS3BucketBucketLifecycleConfigurationDetails', ], 'PublicAccessBlockConfiguration' => [ 'shape' => 'AwsS3AccountPublicAccessBlockDetails', ], 'AccessControlList' => [ 'shape' => 'NonEmptyString', ], 'BucketLoggingConfiguration' => [ 'shape' => 'AwsS3BucketLoggingConfiguration', ], 'BucketWebsiteConfiguration' => [ 'shape' => 'AwsS3BucketWebsiteConfiguration', ], 'BucketNotificationConfiguration' => [ 'shape' => 'AwsS3BucketNotificationConfiguration', ], 'BucketVersioningConfiguration' => [ 'shape' => 'AwsS3BucketBucketVersioningConfiguration', ], 'ObjectLockConfiguration' => [ 'shape' => 'AwsS3BucketObjectLockConfiguration', ], 'Name' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsS3BucketLoggingConfiguration' => [ 'type' => 'structure', 'members' => [ 'DestinationBucketName' => [ 'shape' => 'NonEmptyString', ], 'LogFilePrefix' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsS3BucketNotificationConfiguration' => [ 'type' => 'structure', 'members' => [ 'Configurations' => [ 'shape' => 'AwsS3BucketNotificationConfigurationDetails', ], ], ], 'AwsS3BucketNotificationConfigurationDetail' => [ 'type' => 'structure', 'members' => [ 'Events' => [ 'shape' => 'AwsS3BucketNotificationConfigurationEvents', ], 'Filter' => [ 'shape' => 'AwsS3BucketNotificationConfigurationFilter', ], 'Destination' => [ 'shape' => 'NonEmptyString', ], 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsS3BucketNotificationConfigurationDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsS3BucketNotificationConfigurationDetail', ], ], 'AwsS3BucketNotificationConfigurationEvents' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], ], 'AwsS3BucketNotificationConfigurationFilter' => [ 'type' => 'structure', 'members' => [ 'S3KeyFilter' => [ 'shape' => 'AwsS3BucketNotificationConfigurationS3KeyFilter', ], ], ], 'AwsS3BucketNotificationConfigurationS3KeyFilter' => [ 'type' => 'structure', 'members' => [ 'FilterRules' => [ 'shape' => 'AwsS3BucketNotificationConfigurationS3KeyFilterRules', ], ], ], 'AwsS3BucketNotificationConfigurationS3KeyFilterRule' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'AwsS3BucketNotificationConfigurationS3KeyFilterRuleName', ], 'Value' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsS3BucketNotificationConfigurationS3KeyFilterRuleName' => [ 'type' => 'string', 'enum' => [ 'Prefix', 'Suffix', ], ], 'AwsS3BucketNotificationConfigurationS3KeyFilterRules' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsS3BucketNotificationConfigurationS3KeyFilterRule', ], ], 'AwsS3BucketObjectLockConfiguration' => [ 'type' => 'structure', 'members' => [ 'ObjectLockEnabled' => [ 'shape' => 'NonEmptyString', ], 'Rule' => [ 'shape' => 'AwsS3BucketObjectLockConfigurationRuleDetails', ], ], ], 'AwsS3BucketObjectLockConfigurationRuleDefaultRetentionDetails' => [ 'type' => 'structure', 'members' => [ 'Days' => [ 'shape' => 'Integer', ], 'Mode' => [ 'shape' => 'NonEmptyString', ], 'Years' => [ 'shape' => 'Integer', ], ], ], 'AwsS3BucketObjectLockConfigurationRuleDetails' => [ 'type' => 'structure', 'members' => [ 'DefaultRetention' => [ 'shape' => 'AwsS3BucketObjectLockConfigurationRuleDefaultRetentionDetails', ], ], ], 'AwsS3BucketServerSideEncryptionByDefault' => [ 'type' => 'structure', 'members' => [ 'SSEAlgorithm' => [ 'shape' => 'NonEmptyString', ], 'KMSMasterKeyID' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsS3BucketServerSideEncryptionConfiguration' => [ 'type' => 'structure', 'members' => [ 'Rules' => [ 'shape' => 'AwsS3BucketServerSideEncryptionRules', ], ], ], 'AwsS3BucketServerSideEncryptionRule' => [ 'type' => 'structure', 'members' => [ 'ApplyServerSideEncryptionByDefault' => [ 'shape' => 'AwsS3BucketServerSideEncryptionByDefault', ], ], ], 'AwsS3BucketServerSideEncryptionRules' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsS3BucketServerSideEncryptionRule', ], ], 'AwsS3BucketWebsiteConfiguration' => [ 'type' => 'structure', 'members' => [ 'ErrorDocument' => [ 'shape' => 'NonEmptyString', ], 'IndexDocumentSuffix' => [ 'shape' => 'NonEmptyString', ], 'RedirectAllRequestsTo' => [ 'shape' => 'AwsS3BucketWebsiteConfigurationRedirectTo', ], 'RoutingRules' => [ 'shape' => 'AwsS3BucketWebsiteConfigurationRoutingRules', ], ], ], 'AwsS3BucketWebsiteConfigurationRedirectTo' => [ 'type' => 'structure', 'members' => [ 'Hostname' => [ 'shape' => 'NonEmptyString', ], 'Protocol' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsS3BucketWebsiteConfigurationRoutingRule' => [ 'type' => 'structure', 'members' => [ 'Condition' => [ 'shape' => 'AwsS3BucketWebsiteConfigurationRoutingRuleCondition', ], 'Redirect' => [ 'shape' => 'AwsS3BucketWebsiteConfigurationRoutingRuleRedirect', ], ], ], 'AwsS3BucketWebsiteConfigurationRoutingRuleCondition' => [ 'type' => 'structure', 'members' => [ 'HttpErrorCodeReturnedEquals' => [ 'shape' => 'NonEmptyString', ], 'KeyPrefixEquals' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsS3BucketWebsiteConfigurationRoutingRuleRedirect' => [ 'type' => 'structure', 'members' => [ 'Hostname' => [ 'shape' => 'NonEmptyString', ], 'HttpRedirectCode' => [ 'shape' => 'NonEmptyString', ], 'Protocol' => [ 'shape' => 'NonEmptyString', ], 'ReplaceKeyPrefixWith' => [ 'shape' => 'NonEmptyString', ], 'ReplaceKeyWith' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsS3BucketWebsiteConfigurationRoutingRules' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsS3BucketWebsiteConfigurationRoutingRule', ], ], 'AwsS3ObjectDetails' => [ 'type' => 'structure', 'members' => [ 'LastModified' => [ 'shape' => 'NonEmptyString', ], 'ETag' => [ 'shape' => 'NonEmptyString', ], 'VersionId' => [ 'shape' => 'NonEmptyString', ], 'ContentType' => [ 'shape' => 'NonEmptyString', ], 'ServerSideEncryption' => [ 'shape' => 'NonEmptyString', ], 'SSEKMSKeyId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsSageMakerNotebookInstanceDetails' => [ 'type' => 'structure', 'members' => [ 'AcceleratorTypes' => [ 'shape' => 'NonEmptyStringList', ], 'AdditionalCodeRepositories' => [ 'shape' => 'NonEmptyStringList', ], 'DefaultCodeRepository' => [ 'shape' => 'NonEmptyString', ], 'DirectInternetAccess' => [ 'shape' => 'NonEmptyString', ], 'FailureReason' => [ 'shape' => 'NonEmptyString', ], 'InstanceMetadataServiceConfiguration' => [ 'shape' => 'AwsSageMakerNotebookInstanceMetadataServiceConfigurationDetails', ], 'InstanceType' => [ 'shape' => 'NonEmptyString', ], 'KmsKeyId' => [ 'shape' => 'NonEmptyString', ], 'NetworkInterfaceId' => [ 'shape' => 'NonEmptyString', ], 'NotebookInstanceArn' => [ 'shape' => 'NonEmptyString', ], 'NotebookInstanceLifecycleConfigName' => [ 'shape' => 'NonEmptyString', ], 'NotebookInstanceName' => [ 'shape' => 'NonEmptyString', ], 'NotebookInstanceStatus' => [ 'shape' => 'NonEmptyString', ], 'PlatformIdentifier' => [ 'shape' => 'NonEmptyString', ], 'RoleArn' => [ 'shape' => 'NonEmptyString', ], 'RootAccess' => [ 'shape' => 'NonEmptyString', ], 'SecurityGroups' => [ 'shape' => 'NonEmptyStringList', ], 'SubnetId' => [ 'shape' => 'NonEmptyString', ], 'Url' => [ 'shape' => 'NonEmptyString', ], 'VolumeSizeInGB' => [ 'shape' => 'Integer', ], ], ], 'AwsSageMakerNotebookInstanceMetadataServiceConfigurationDetails' => [ 'type' => 'structure', 'members' => [ 'MinimumInstanceMetadataServiceVersion' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsSecretsManagerSecretDetails' => [ 'type' => 'structure', 'members' => [ 'RotationRules' => [ 'shape' => 'AwsSecretsManagerSecretRotationRules', ], 'RotationOccurredWithinFrequency' => [ 'shape' => 'Boolean', ], 'KmsKeyId' => [ 'shape' => 'NonEmptyString', ], 'RotationEnabled' => [ 'shape' => 'Boolean', ], 'RotationLambdaArn' => [ 'shape' => 'NonEmptyString', ], 'Deleted' => [ 'shape' => 'Boolean', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsSecretsManagerSecretRotationRules' => [ 'type' => 'structure', 'members' => [ 'AutomaticallyAfterDays' => [ 'shape' => 'Integer', ], ], ], 'AwsSecurityFinding' => [ 'type' => 'structure', 'required' => [ 'SchemaVersion', 'Id', 'ProductArn', 'GeneratorId', 'AwsAccountId', 'CreatedAt', 'UpdatedAt', 'Title', 'Description', 'Resources', ], 'members' => [ 'SchemaVersion' => [ 'shape' => 'NonEmptyString', ], 'Id' => [ 'shape' => 'NonEmptyString', ], 'ProductArn' => [ 'shape' => 'NonEmptyString', ], 'ProductName' => [ 'shape' => 'NonEmptyString', ], 'CompanyName' => [ 'shape' => 'NonEmptyString', ], 'Region' => [ 'shape' => 'NonEmptyString', ], 'GeneratorId' => [ 'shape' => 'NonEmptyString', ], 'AwsAccountId' => [ 'shape' => 'NonEmptyString', ], 'Types' => [ 'shape' => 'TypeList', ], 'FirstObservedAt' => [ 'shape' => 'NonEmptyString', ], 'LastObservedAt' => [ 'shape' => 'NonEmptyString', ], 'CreatedAt' => [ 'shape' => 'NonEmptyString', ], 'UpdatedAt' => [ 'shape' => 'NonEmptyString', ], 'Severity' => [ 'shape' => 'Severity', ], 'Confidence' => [ 'shape' => 'Integer', ], 'Criticality' => [ 'shape' => 'Integer', ], 'Title' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'Remediation' => [ 'shape' => 'Remediation', ], 'SourceUrl' => [ 'shape' => 'NonEmptyString', ], 'ProductFields' => [ 'shape' => 'FieldMap', ], 'UserDefinedFields' => [ 'shape' => 'FieldMap', ], 'Malware' => [ 'shape' => 'MalwareList', ], 'Network' => [ 'shape' => 'Network', ], 'NetworkPath' => [ 'shape' => 'NetworkPathList', ], 'Process' => [ 'shape' => 'ProcessDetails', ], 'Threats' => [ 'shape' => 'ThreatList', ], 'ThreatIntelIndicators' => [ 'shape' => 'ThreatIntelIndicatorList', ], 'Resources' => [ 'shape' => 'ResourceList', ], 'Compliance' => [ 'shape' => 'Compliance', ], 'VerificationState' => [ 'shape' => 'VerificationState', ], 'WorkflowState' => [ 'shape' => 'WorkflowState', ], 'Workflow' => [ 'shape' => 'Workflow', ], 'RecordState' => [ 'shape' => 'RecordState', ], 'RelatedFindings' => [ 'shape' => 'RelatedFindingList', ], 'Note' => [ 'shape' => 'Note', ], 'Vulnerabilities' => [ 'shape' => 'VulnerabilityList', ], 'PatchSummary' => [ 'shape' => 'PatchSummary', ], 'Action' => [ 'shape' => 'Action', ], 'FindingProviderFields' => [ 'shape' => 'FindingProviderFields', ], 'Sample' => [ 'shape' => 'Boolean', ], 'GeneratorDetails' => [ 'shape' => 'GeneratorDetails', ], 'ProcessedAt' => [ 'shape' => 'NonEmptyString', ], 'AwsAccountName' => [ 'shape' => 'NonEmptyString', ], 'Detection' => [ 'shape' => 'Detection', ], ], ], 'AwsSecurityFindingFilters' => [ 'type' => 'structure', 'members' => [ 'ProductArn' => [ 'shape' => 'StringFilterList', ], 'AwsAccountId' => [ 'shape' => 'StringFilterList', ], 'Id' => [ 'shape' => 'StringFilterList', ], 'GeneratorId' => [ 'shape' => 'StringFilterList', ], 'Region' => [ 'shape' => 'StringFilterList', ], 'Type' => [ 'shape' => 'StringFilterList', ], 'FirstObservedAt' => [ 'shape' => 'DateFilterList', ], 'LastObservedAt' => [ 'shape' => 'DateFilterList', ], 'CreatedAt' => [ 'shape' => 'DateFilterList', ], 'UpdatedAt' => [ 'shape' => 'DateFilterList', ], 'SeverityProduct' => [ 'shape' => 'NumberFilterList', 'deprecated' => true, 'deprecatedMessage' => 'This filter is deprecated. Instead, use FindingProviderSeverityOriginal.', ], 'SeverityNormalized' => [ 'shape' => 'NumberFilterList', 'deprecated' => true, 'deprecatedMessage' => 'This filter is deprecated. Instead, use SeverityLabel or FindingProviderFieldsSeverityLabel.', ], 'SeverityLabel' => [ 'shape' => 'StringFilterList', ], 'Confidence' => [ 'shape' => 'NumberFilterList', ], 'Criticality' => [ 'shape' => 'NumberFilterList', ], 'Title' => [ 'shape' => 'StringFilterList', ], 'Description' => [ 'shape' => 'StringFilterList', ], 'RecommendationText' => [ 'shape' => 'StringFilterList', ], 'SourceUrl' => [ 'shape' => 'StringFilterList', ], 'ProductFields' => [ 'shape' => 'MapFilterList', ], 'ProductName' => [ 'shape' => 'StringFilterList', ], 'CompanyName' => [ 'shape' => 'StringFilterList', ], 'UserDefinedFields' => [ 'shape' => 'MapFilterList', ], 'MalwareName' => [ 'shape' => 'StringFilterList', ], 'MalwareType' => [ 'shape' => 'StringFilterList', ], 'MalwarePath' => [ 'shape' => 'StringFilterList', ], 'MalwareState' => [ 'shape' => 'StringFilterList', ], 'NetworkDirection' => [ 'shape' => 'StringFilterList', ], 'NetworkProtocol' => [ 'shape' => 'StringFilterList', ], 'NetworkSourceIpV4' => [ 'shape' => 'IpFilterList', ], 'NetworkSourceIpV6' => [ 'shape' => 'IpFilterList', ], 'NetworkSourcePort' => [ 'shape' => 'NumberFilterList', ], 'NetworkSourceDomain' => [ 'shape' => 'StringFilterList', ], 'NetworkSourceMac' => [ 'shape' => 'StringFilterList', ], 'NetworkDestinationIpV4' => [ 'shape' => 'IpFilterList', ], 'NetworkDestinationIpV6' => [ 'shape' => 'IpFilterList', ], 'NetworkDestinationPort' => [ 'shape' => 'NumberFilterList', ], 'NetworkDestinationDomain' => [ 'shape' => 'StringFilterList', ], 'ProcessName' => [ 'shape' => 'StringFilterList', ], 'ProcessPath' => [ 'shape' => 'StringFilterList', ], 'ProcessPid' => [ 'shape' => 'NumberFilterList', ], 'ProcessParentPid' => [ 'shape' => 'NumberFilterList', ], 'ProcessLaunchedAt' => [ 'shape' => 'DateFilterList', ], 'ProcessTerminatedAt' => [ 'shape' => 'DateFilterList', ], 'ThreatIntelIndicatorType' => [ 'shape' => 'StringFilterList', ], 'ThreatIntelIndicatorValue' => [ 'shape' => 'StringFilterList', ], 'ThreatIntelIndicatorCategory' => [ 'shape' => 'StringFilterList', ], 'ThreatIntelIndicatorLastObservedAt' => [ 'shape' => 'DateFilterList', ], 'ThreatIntelIndicatorSource' => [ 'shape' => 'StringFilterList', ], 'ThreatIntelIndicatorSourceUrl' => [ 'shape' => 'StringFilterList', ], 'ResourceType' => [ 'shape' => 'StringFilterList', ], 'ResourceId' => [ 'shape' => 'StringFilterList', ], 'ResourcePartition' => [ 'shape' => 'StringFilterList', ], 'ResourceRegion' => [ 'shape' => 'StringFilterList', ], 'ResourceTags' => [ 'shape' => 'MapFilterList', ], 'ResourceAwsEc2InstanceType' => [ 'shape' => 'StringFilterList', ], 'ResourceAwsEc2InstanceImageId' => [ 'shape' => 'StringFilterList', ], 'ResourceAwsEc2InstanceIpV4Addresses' => [ 'shape' => 'IpFilterList', ], 'ResourceAwsEc2InstanceIpV6Addresses' => [ 'shape' => 'IpFilterList', ], 'ResourceAwsEc2InstanceKeyName' => [ 'shape' => 'StringFilterList', ], 'ResourceAwsEc2InstanceIamInstanceProfileArn' => [ 'shape' => 'StringFilterList', ], 'ResourceAwsEc2InstanceVpcId' => [ 'shape' => 'StringFilterList', ], 'ResourceAwsEc2InstanceSubnetId' => [ 'shape' => 'StringFilterList', ], 'ResourceAwsEc2InstanceLaunchedAt' => [ 'shape' => 'DateFilterList', ], 'ResourceAwsS3BucketOwnerId' => [ 'shape' => 'StringFilterList', ], 'ResourceAwsS3BucketOwnerName' => [ 'shape' => 'StringFilterList', ], 'ResourceAwsIamAccessKeyUserName' => [ 'shape' => 'StringFilterList', 'deprecated' => true, 'deprecatedMessage' => 'This filter is deprecated. Instead, use ResourceAwsIamAccessKeyPrincipalName.', ], 'ResourceAwsIamAccessKeyPrincipalName' => [ 'shape' => 'StringFilterList', ], 'ResourceAwsIamAccessKeyStatus' => [ 'shape' => 'StringFilterList', ], 'ResourceAwsIamAccessKeyCreatedAt' => [ 'shape' => 'DateFilterList', ], 'ResourceAwsIamUserUserName' => [ 'shape' => 'StringFilterList', ], 'ResourceContainerName' => [ 'shape' => 'StringFilterList', ], 'ResourceContainerImageId' => [ 'shape' => 'StringFilterList', ], 'ResourceContainerImageName' => [ 'shape' => 'StringFilterList', ], 'ResourceContainerLaunchedAt' => [ 'shape' => 'DateFilterList', ], 'ResourceDetailsOther' => [ 'shape' => 'MapFilterList', ], 'ComplianceStatus' => [ 'shape' => 'StringFilterList', ], 'VerificationState' => [ 'shape' => 'StringFilterList', ], 'WorkflowState' => [ 'shape' => 'StringFilterList', ], 'WorkflowStatus' => [ 'shape' => 'StringFilterList', ], 'RecordState' => [ 'shape' => 'StringFilterList', ], 'RelatedFindingsProductArn' => [ 'shape' => 'StringFilterList', ], 'RelatedFindingsId' => [ 'shape' => 'StringFilterList', ], 'NoteText' => [ 'shape' => 'StringFilterList', ], 'NoteUpdatedAt' => [ 'shape' => 'DateFilterList', ], 'NoteUpdatedBy' => [ 'shape' => 'StringFilterList', ], 'Keyword' => [ 'shape' => 'KeywordFilterList', 'deprecated' => true, 'deprecatedMessage' => 'The Keyword property is deprecated.', ], 'FindingProviderFieldsConfidence' => [ 'shape' => 'NumberFilterList', ], 'FindingProviderFieldsCriticality' => [ 'shape' => 'NumberFilterList', ], 'FindingProviderFieldsRelatedFindingsId' => [ 'shape' => 'StringFilterList', ], 'FindingProviderFieldsRelatedFindingsProductArn' => [ 'shape' => 'StringFilterList', ], 'FindingProviderFieldsSeverityLabel' => [ 'shape' => 'StringFilterList', ], 'FindingProviderFieldsSeverityOriginal' => [ 'shape' => 'StringFilterList', ], 'FindingProviderFieldsTypes' => [ 'shape' => 'StringFilterList', ], 'Sample' => [ 'shape' => 'BooleanFilterList', ], 'ComplianceSecurityControlId' => [ 'shape' => 'StringFilterList', ], 'ComplianceAssociatedStandardsId' => [ 'shape' => 'StringFilterList', ], 'VulnerabilitiesExploitAvailable' => [ 'shape' => 'StringFilterList', ], 'VulnerabilitiesFixAvailable' => [ 'shape' => 'StringFilterList', ], 'ComplianceSecurityControlParametersName' => [ 'shape' => 'StringFilterList', ], 'ComplianceSecurityControlParametersValue' => [ 'shape' => 'StringFilterList', ], 'AwsAccountName' => [ 'shape' => 'StringFilterList', ], 'ResourceApplicationName' => [ 'shape' => 'StringFilterList', ], 'ResourceApplicationArn' => [ 'shape' => 'StringFilterList', ], ], ], 'AwsSecurityFindingIdentifier' => [ 'type' => 'structure', 'required' => [ 'Id', 'ProductArn', ], 'members' => [ 'Id' => [ 'shape' => 'NonEmptyString', ], 'ProductArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsSecurityFindingIdentifierList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsSecurityFindingIdentifier', ], ], 'AwsSecurityFindingList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsSecurityFinding', ], ], 'AwsSnsTopicDetails' => [ 'type' => 'structure', 'members' => [ 'KmsMasterKeyId' => [ 'shape' => 'NonEmptyString', ], 'Subscription' => [ 'shape' => 'AwsSnsTopicSubscriptionList', ], 'TopicName' => [ 'shape' => 'NonEmptyString', ], 'Owner' => [ 'shape' => 'NonEmptyString', ], 'SqsSuccessFeedbackRoleArn' => [ 'shape' => 'NonEmptyString', ], 'SqsFailureFeedbackRoleArn' => [ 'shape' => 'NonEmptyString', ], 'ApplicationSuccessFeedbackRoleArn' => [ 'shape' => 'NonEmptyString', ], 'FirehoseSuccessFeedbackRoleArn' => [ 'shape' => 'NonEmptyString', ], 'FirehoseFailureFeedbackRoleArn' => [ 'shape' => 'NonEmptyString', ], 'HttpSuccessFeedbackRoleArn' => [ 'shape' => 'NonEmptyString', ], 'HttpFailureFeedbackRoleArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsSnsTopicSubscription' => [ 'type' => 'structure', 'members' => [ 'Endpoint' => [ 'shape' => 'NonEmptyString', ], 'Protocol' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsSnsTopicSubscriptionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsSnsTopicSubscription', ], ], 'AwsSqsQueueDetails' => [ 'type' => 'structure', 'members' => [ 'KmsDataKeyReusePeriodSeconds' => [ 'shape' => 'Integer', ], 'KmsMasterKeyId' => [ 'shape' => 'NonEmptyString', ], 'QueueName' => [ 'shape' => 'NonEmptyString', ], 'DeadLetterTargetArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsSsmComplianceSummary' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'NonEmptyString', ], 'CompliantCriticalCount' => [ 'shape' => 'Integer', ], 'CompliantHighCount' => [ 'shape' => 'Integer', ], 'CompliantMediumCount' => [ 'shape' => 'Integer', ], 'ExecutionType' => [ 'shape' => 'NonEmptyString', ], 'NonCompliantCriticalCount' => [ 'shape' => 'Integer', ], 'CompliantInformationalCount' => [ 'shape' => 'Integer', ], 'NonCompliantInformationalCount' => [ 'shape' => 'Integer', ], 'CompliantUnspecifiedCount' => [ 'shape' => 'Integer', ], 'NonCompliantLowCount' => [ 'shape' => 'Integer', ], 'NonCompliantHighCount' => [ 'shape' => 'Integer', ], 'CompliantLowCount' => [ 'shape' => 'Integer', ], 'ComplianceType' => [ 'shape' => 'NonEmptyString', ], 'PatchBaselineId' => [ 'shape' => 'NonEmptyString', ], 'OverallSeverity' => [ 'shape' => 'NonEmptyString', ], 'NonCompliantMediumCount' => [ 'shape' => 'Integer', ], 'NonCompliantUnspecifiedCount' => [ 'shape' => 'Integer', ], 'PatchGroup' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsSsmPatch' => [ 'type' => 'structure', 'members' => [ 'ComplianceSummary' => [ 'shape' => 'AwsSsmComplianceSummary', ], ], ], 'AwsSsmPatchComplianceDetails' => [ 'type' => 'structure', 'members' => [ 'Patch' => [ 'shape' => 'AwsSsmPatch', ], ], ], 'AwsStepFunctionStateMachineDetails' => [ 'type' => 'structure', 'members' => [ 'Label' => [ 'shape' => 'NonEmptyString', ], 'LoggingConfiguration' => [ 'shape' => 'AwsStepFunctionStateMachineLoggingConfigurationDetails', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'RoleArn' => [ 'shape' => 'NonEmptyString', ], 'StateMachineArn' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], 'TracingConfiguration' => [ 'shape' => 'AwsStepFunctionStateMachineTracingConfigurationDetails', ], 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsStepFunctionStateMachineLoggingConfigurationDestinationsCloudWatchLogsLogGroupDetails' => [ 'type' => 'structure', 'members' => [ 'LogGroupArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsStepFunctionStateMachineLoggingConfigurationDestinationsDetails' => [ 'type' => 'structure', 'members' => [ 'CloudWatchLogsLogGroup' => [ 'shape' => 'AwsStepFunctionStateMachineLoggingConfigurationDestinationsCloudWatchLogsLogGroupDetails', ], ], ], 'AwsStepFunctionStateMachineLoggingConfigurationDestinationsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsStepFunctionStateMachineLoggingConfigurationDestinationsDetails', ], ], 'AwsStepFunctionStateMachineLoggingConfigurationDetails' => [ 'type' => 'structure', 'members' => [ 'Destinations' => [ 'shape' => 'AwsStepFunctionStateMachineLoggingConfigurationDestinationsList', ], 'IncludeExecutionData' => [ 'shape' => 'Boolean', ], 'Level' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsStepFunctionStateMachineTracingConfigurationDetails' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], ], ], 'AwsWafRateBasedRuleDetails' => [ 'type' => 'structure', 'members' => [ 'MetricName' => [ 'shape' => 'NonEmptyString', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'RateKey' => [ 'shape' => 'NonEmptyString', ], 'RateLimit' => [ 'shape' => 'Long', ], 'RuleId' => [ 'shape' => 'NonEmptyString', ], 'MatchPredicates' => [ 'shape' => 'AwsWafRateBasedRuleMatchPredicateList', ], ], ], 'AwsWafRateBasedRuleMatchPredicate' => [ 'type' => 'structure', 'members' => [ 'DataId' => [ 'shape' => 'NonEmptyString', ], 'Negated' => [ 'shape' => 'Boolean', ], 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsWafRateBasedRuleMatchPredicateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsWafRateBasedRuleMatchPredicate', ], ], 'AwsWafRegionalRateBasedRuleDetails' => [ 'type' => 'structure', 'members' => [ 'MetricName' => [ 'shape' => 'NonEmptyString', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'RateKey' => [ 'shape' => 'NonEmptyString', ], 'RateLimit' => [ 'shape' => 'Long', ], 'RuleId' => [ 'shape' => 'NonEmptyString', ], 'MatchPredicates' => [ 'shape' => 'AwsWafRegionalRateBasedRuleMatchPredicateList', ], ], ], 'AwsWafRegionalRateBasedRuleMatchPredicate' => [ 'type' => 'structure', 'members' => [ 'DataId' => [ 'shape' => 'NonEmptyString', ], 'Negated' => [ 'shape' => 'Boolean', ], 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsWafRegionalRateBasedRuleMatchPredicateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsWafRegionalRateBasedRuleMatchPredicate', ], ], 'AwsWafRegionalRuleDetails' => [ 'type' => 'structure', 'members' => [ 'MetricName' => [ 'shape' => 'NonEmptyString', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'PredicateList' => [ 'shape' => 'AwsWafRegionalRulePredicateList', ], 'RuleId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsWafRegionalRuleGroupDetails' => [ 'type' => 'structure', 'members' => [ 'MetricName' => [ 'shape' => 'NonEmptyString', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'RuleGroupId' => [ 'shape' => 'NonEmptyString', ], 'Rules' => [ 'shape' => 'AwsWafRegionalRuleGroupRulesList', ], ], ], 'AwsWafRegionalRuleGroupRulesActionDetails' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsWafRegionalRuleGroupRulesDetails' => [ 'type' => 'structure', 'members' => [ 'Action' => [ 'shape' => 'AwsWafRegionalRuleGroupRulesActionDetails', ], 'Priority' => [ 'shape' => 'Integer', ], 'RuleId' => [ 'shape' => 'NonEmptyString', ], 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsWafRegionalRuleGroupRulesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsWafRegionalRuleGroupRulesDetails', ], ], 'AwsWafRegionalRulePredicateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsWafRegionalRulePredicateListDetails', ], ], 'AwsWafRegionalRulePredicateListDetails' => [ 'type' => 'structure', 'members' => [ 'DataId' => [ 'shape' => 'NonEmptyString', ], 'Negated' => [ 'shape' => 'Boolean', ], 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsWafRegionalWebAclDetails' => [ 'type' => 'structure', 'members' => [ 'DefaultAction' => [ 'shape' => 'NonEmptyString', ], 'MetricName' => [ 'shape' => 'NonEmptyString', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'RulesList' => [ 'shape' => 'AwsWafRegionalWebAclRulesList', ], 'WebAclId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsWafRegionalWebAclRulesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsWafRegionalWebAclRulesListDetails', ], ], 'AwsWafRegionalWebAclRulesListActionDetails' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsWafRegionalWebAclRulesListDetails' => [ 'type' => 'structure', 'members' => [ 'Action' => [ 'shape' => 'AwsWafRegionalWebAclRulesListActionDetails', ], 'OverrideAction' => [ 'shape' => 'AwsWafRegionalWebAclRulesListOverrideActionDetails', ], 'Priority' => [ 'shape' => 'Integer', ], 'RuleId' => [ 'shape' => 'NonEmptyString', ], 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsWafRegionalWebAclRulesListOverrideActionDetails' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsWafRuleDetails' => [ 'type' => 'structure', 'members' => [ 'MetricName' => [ 'shape' => 'NonEmptyString', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'PredicateList' => [ 'shape' => 'AwsWafRulePredicateList', ], 'RuleId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsWafRuleGroupDetails' => [ 'type' => 'structure', 'members' => [ 'MetricName' => [ 'shape' => 'NonEmptyString', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'RuleGroupId' => [ 'shape' => 'NonEmptyString', ], 'Rules' => [ 'shape' => 'AwsWafRuleGroupRulesList', ], ], ], 'AwsWafRuleGroupRulesActionDetails' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsWafRuleGroupRulesDetails' => [ 'type' => 'structure', 'members' => [ 'Action' => [ 'shape' => 'AwsWafRuleGroupRulesActionDetails', ], 'Priority' => [ 'shape' => 'Integer', ], 'RuleId' => [ 'shape' => 'NonEmptyString', ], 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsWafRuleGroupRulesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsWafRuleGroupRulesDetails', ], ], 'AwsWafRulePredicateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsWafRulePredicateListDetails', ], ], 'AwsWafRulePredicateListDetails' => [ 'type' => 'structure', 'members' => [ 'DataId' => [ 'shape' => 'NonEmptyString', ], 'Negated' => [ 'shape' => 'Boolean', ], 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsWafWebAclDetails' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'DefaultAction' => [ 'shape' => 'NonEmptyString', ], 'Rules' => [ 'shape' => 'AwsWafWebAclRuleList', ], 'WebAclId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsWafWebAclRule' => [ 'type' => 'structure', 'members' => [ 'Action' => [ 'shape' => 'WafAction', ], 'ExcludedRules' => [ 'shape' => 'WafExcludedRuleList', ], 'OverrideAction' => [ 'shape' => 'WafOverrideAction', ], 'Priority' => [ 'shape' => 'Integer', ], 'RuleId' => [ 'shape' => 'NonEmptyString', ], 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsWafWebAclRuleList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsWafWebAclRule', ], ], 'AwsWafv2ActionAllowDetails' => [ 'type' => 'structure', 'members' => [ 'CustomRequestHandling' => [ 'shape' => 'AwsWafv2CustomRequestHandlingDetails', ], ], ], 'AwsWafv2ActionBlockDetails' => [ 'type' => 'structure', 'members' => [ 'CustomResponse' => [ 'shape' => 'AwsWafv2CustomResponseDetails', ], ], ], 'AwsWafv2CustomHttpHeader' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'Value' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsWafv2CustomRequestHandlingDetails' => [ 'type' => 'structure', 'members' => [ 'InsertHeaders' => [ 'shape' => 'AwsWafv2InsertHeadersList', ], ], ], 'AwsWafv2CustomResponseDetails' => [ 'type' => 'structure', 'members' => [ 'CustomResponseBodyKey' => [ 'shape' => 'NonEmptyString', ], 'ResponseCode' => [ 'shape' => 'Integer', ], 'ResponseHeaders' => [ 'shape' => 'AwsWafv2InsertHeadersList', ], ], ], 'AwsWafv2InsertHeadersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsWafv2CustomHttpHeader', ], ], 'AwsWafv2RuleGroupDetails' => [ 'type' => 'structure', 'members' => [ 'Capacity' => [ 'shape' => 'Long', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'Id' => [ 'shape' => 'NonEmptyString', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'Arn' => [ 'shape' => 'NonEmptyString', ], 'Rules' => [ 'shape' => 'AwsWafv2RulesList', ], 'Scope' => [ 'shape' => 'NonEmptyString', ], 'VisibilityConfig' => [ 'shape' => 'AwsWafv2VisibilityConfigDetails', ], ], ], 'AwsWafv2RulesActionCaptchaDetails' => [ 'type' => 'structure', 'members' => [ 'CustomRequestHandling' => [ 'shape' => 'AwsWafv2CustomRequestHandlingDetails', ], ], ], 'AwsWafv2RulesActionCountDetails' => [ 'type' => 'structure', 'members' => [ 'CustomRequestHandling' => [ 'shape' => 'AwsWafv2CustomRequestHandlingDetails', ], ], ], 'AwsWafv2RulesActionDetails' => [ 'type' => 'structure', 'members' => [ 'Allow' => [ 'shape' => 'AwsWafv2ActionAllowDetails', ], 'Block' => [ 'shape' => 'AwsWafv2ActionBlockDetails', ], 'Captcha' => [ 'shape' => 'AwsWafv2RulesActionCaptchaDetails', ], 'Count' => [ 'shape' => 'AwsWafv2RulesActionCountDetails', ], ], ], 'AwsWafv2RulesDetails' => [ 'type' => 'structure', 'members' => [ 'Action' => [ 'shape' => 'AwsWafv2RulesActionDetails', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'OverrideAction' => [ 'shape' => 'NonEmptyString', ], 'Priority' => [ 'shape' => 'Integer', ], 'VisibilityConfig' => [ 'shape' => 'AwsWafv2VisibilityConfigDetails', ], ], ], 'AwsWafv2RulesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsWafv2RulesDetails', ], ], 'AwsWafv2VisibilityConfigDetails' => [ 'type' => 'structure', 'members' => [ 'CloudWatchMetricsEnabled' => [ 'shape' => 'Boolean', ], 'MetricName' => [ 'shape' => 'NonEmptyString', ], 'SampledRequestsEnabled' => [ 'shape' => 'Boolean', ], ], ], 'AwsWafv2WebAclActionDetails' => [ 'type' => 'structure', 'members' => [ 'Allow' => [ 'shape' => 'AwsWafv2ActionAllowDetails', ], 'Block' => [ 'shape' => 'AwsWafv2ActionBlockDetails', ], ], ], 'AwsWafv2WebAclCaptchaConfigDetails' => [ 'type' => 'structure', 'members' => [ 'ImmunityTimeProperty' => [ 'shape' => 'AwsWafv2WebAclCaptchaConfigImmunityTimePropertyDetails', ], ], ], 'AwsWafv2WebAclCaptchaConfigImmunityTimePropertyDetails' => [ 'type' => 'structure', 'members' => [ 'ImmunityTime' => [ 'shape' => 'Long', ], ], ], 'AwsWafv2WebAclDetails' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'Arn' => [ 'shape' => 'NonEmptyString', ], 'ManagedbyFirewallManager' => [ 'shape' => 'Boolean', ], 'Id' => [ 'shape' => 'NonEmptyString', ], 'Capacity' => [ 'shape' => 'Long', ], 'CaptchaConfig' => [ 'shape' => 'AwsWafv2WebAclCaptchaConfigDetails', ], 'DefaultAction' => [ 'shape' => 'AwsWafv2WebAclActionDetails', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'Rules' => [ 'shape' => 'AwsWafv2RulesList', ], 'VisibilityConfig' => [ 'shape' => 'AwsWafv2VisibilityConfigDetails', ], ], ], 'AwsXrayEncryptionConfigDetails' => [ 'type' => 'structure', 'members' => [ 'KeyId' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'BatchDeleteAutomationRulesRequest' => [ 'type' => 'structure', 'required' => [ 'AutomationRulesArns', ], 'members' => [ 'AutomationRulesArns' => [ 'shape' => 'AutomationRulesArnsList', ], ], ], 'BatchDeleteAutomationRulesResponse' => [ 'type' => 'structure', 'members' => [ 'ProcessedAutomationRules' => [ 'shape' => 'AutomationRulesArnsList', ], 'UnprocessedAutomationRules' => [ 'shape' => 'UnprocessedAutomationRulesList', ], ], ], 'BatchDisableStandardsRequest' => [ 'type' => 'structure', 'required' => [ 'StandardsSubscriptionArns', ], 'members' => [ 'StandardsSubscriptionArns' => [ 'shape' => 'StandardsSubscriptionArns', ], ], ], 'BatchDisableStandardsResponse' => [ 'type' => 'structure', 'members' => [ 'StandardsSubscriptions' => [ 'shape' => 'StandardsSubscriptions', ], ], ], 'BatchEnableStandardsRequest' => [ 'type' => 'structure', 'required' => [ 'StandardsSubscriptionRequests', ], 'members' => [ 'StandardsSubscriptionRequests' => [ 'shape' => 'StandardsSubscriptionRequests', ], ], ], 'BatchEnableStandardsResponse' => [ 'type' => 'structure', 'members' => [ 'StandardsSubscriptions' => [ 'shape' => 'StandardsSubscriptions', ], ], ], 'BatchGetAutomationRulesRequest' => [ 'type' => 'structure', 'required' => [ 'AutomationRulesArns', ], 'members' => [ 'AutomationRulesArns' => [ 'shape' => 'AutomationRulesArnsList', ], ], ], 'BatchGetAutomationRulesResponse' => [ 'type' => 'structure', 'members' => [ 'Rules' => [ 'shape' => 'AutomationRulesConfigList', ], 'UnprocessedAutomationRules' => [ 'shape' => 'UnprocessedAutomationRulesList', ], ], ], 'BatchGetConfigurationPolicyAssociationsRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigurationPolicyAssociationIdentifiers', ], 'members' => [ 'ConfigurationPolicyAssociationIdentifiers' => [ 'shape' => 'ConfigurationPolicyAssociationsList', ], ], ], 'BatchGetConfigurationPolicyAssociationsResponse' => [ 'type' => 'structure', 'members' => [ 'ConfigurationPolicyAssociations' => [ 'shape' => 'ConfigurationPolicyAssociationList', ], 'UnprocessedConfigurationPolicyAssociations' => [ 'shape' => 'UnprocessedConfigurationPolicyAssociationList', ], ], ], 'BatchGetSecurityControlsRequest' => [ 'type' => 'structure', 'required' => [ 'SecurityControlIds', ], 'members' => [ 'SecurityControlIds' => [ 'shape' => 'StringList', ], ], ], 'BatchGetSecurityControlsResponse' => [ 'type' => 'structure', 'required' => [ 'SecurityControls', ], 'members' => [ 'SecurityControls' => [ 'shape' => 'SecurityControls', ], 'UnprocessedIds' => [ 'shape' => 'UnprocessedSecurityControls', ], ], ], 'BatchGetStandardsControlAssociationsRequest' => [ 'type' => 'structure', 'required' => [ 'StandardsControlAssociationIds', ], 'members' => [ 'StandardsControlAssociationIds' => [ 'shape' => 'StandardsControlAssociationIds', ], ], ], 'BatchGetStandardsControlAssociationsResponse' => [ 'type' => 'structure', 'required' => [ 'StandardsControlAssociationDetails', ], 'members' => [ 'StandardsControlAssociationDetails' => [ 'shape' => 'StandardsControlAssociationDetails', ], 'UnprocessedAssociations' => [ 'shape' => 'UnprocessedStandardsControlAssociations', ], ], ], 'BatchImportFindingsRequest' => [ 'type' => 'structure', 'required' => [ 'Findings', ], 'members' => [ 'Findings' => [ 'shape' => 'BatchImportFindingsRequestFindingList', ], ], ], 'BatchImportFindingsRequestFindingList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsSecurityFinding', ], 'max' => 100, 'min' => 1, ], 'BatchImportFindingsResponse' => [ 'type' => 'structure', 'required' => [ 'FailedCount', 'SuccessCount', ], 'members' => [ 'FailedCount' => [ 'shape' => 'Integer', ], 'SuccessCount' => [ 'shape' => 'Integer', ], 'FailedFindings' => [ 'shape' => 'ImportFindingsErrorList', ], ], ], 'BatchUpdateAutomationRulesRequest' => [ 'type' => 'structure', 'required' => [ 'UpdateAutomationRulesRequestItems', ], 'members' => [ 'UpdateAutomationRulesRequestItems' => [ 'shape' => 'UpdateAutomationRulesRequestItemsList', ], ], ], 'BatchUpdateAutomationRulesResponse' => [ 'type' => 'structure', 'members' => [ 'ProcessedAutomationRules' => [ 'shape' => 'AutomationRulesArnsList', ], 'UnprocessedAutomationRules' => [ 'shape' => 'UnprocessedAutomationRulesList', ], ], ], 'BatchUpdateFindingsRequest' => [ 'type' => 'structure', 'required' => [ 'FindingIdentifiers', ], 'members' => [ 'FindingIdentifiers' => [ 'shape' => 'AwsSecurityFindingIdentifierList', ], 'Note' => [ 'shape' => 'NoteUpdate', ], 'Severity' => [ 'shape' => 'SeverityUpdate', ], 'VerificationState' => [ 'shape' => 'VerificationState', ], 'Confidence' => [ 'shape' => 'RatioScale', ], 'Criticality' => [ 'shape' => 'RatioScale', ], 'Types' => [ 'shape' => 'TypeList', ], 'UserDefinedFields' => [ 'shape' => 'FieldMap', ], 'Workflow' => [ 'shape' => 'WorkflowUpdate', ], 'RelatedFindings' => [ 'shape' => 'RelatedFindingList', ], ], ], 'BatchUpdateFindingsResponse' => [ 'type' => 'structure', 'required' => [ 'ProcessedFindings', 'UnprocessedFindings', ], 'members' => [ 'ProcessedFindings' => [ 'shape' => 'AwsSecurityFindingIdentifierList', ], 'UnprocessedFindings' => [ 'shape' => 'BatchUpdateFindingsUnprocessedFindingsList', ], ], ], 'BatchUpdateFindingsUnprocessedFinding' => [ 'type' => 'structure', 'required' => [ 'FindingIdentifier', 'ErrorCode', 'ErrorMessage', ], 'members' => [ 'FindingIdentifier' => [ 'shape' => 'AwsSecurityFindingIdentifier', ], 'ErrorCode' => [ 'shape' => 'NonEmptyString', ], 'ErrorMessage' => [ 'shape' => 'NonEmptyString', ], ], ], 'BatchUpdateFindingsUnprocessedFindingsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchUpdateFindingsUnprocessedFinding', ], ], 'BatchUpdateFindingsV2ProcessedFinding' => [ 'type' => 'structure', 'members' => [ 'FindingIdentifier' => [ 'shape' => 'OcsfFindingIdentifier', ], 'MetadataUid' => [ 'shape' => 'NonEmptyString', ], ], ], 'BatchUpdateFindingsV2ProcessedFindingsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchUpdateFindingsV2ProcessedFinding', ], ], 'BatchUpdateFindingsV2Request' => [ 'type' => 'structure', 'members' => [ 'MetadataUids' => [ 'shape' => 'MetadataUidList', ], 'FindingIdentifiers' => [ 'shape' => 'OcsfFindingIdentifierList', ], 'Comment' => [ 'shape' => 'NonEmptyString', ], 'SeverityId' => [ 'shape' => 'Integer', ], 'StatusId' => [ 'shape' => 'Integer', ], ], ], 'BatchUpdateFindingsV2Response' => [ 'type' => 'structure', 'required' => [ 'ProcessedFindings', 'UnprocessedFindings', ], 'members' => [ 'ProcessedFindings' => [ 'shape' => 'BatchUpdateFindingsV2ProcessedFindingsList', ], 'UnprocessedFindings' => [ 'shape' => 'BatchUpdateFindingsV2UnprocessedFindingsList', ], ], ], 'BatchUpdateFindingsV2UnprocessedFinding' => [ 'type' => 'structure', 'members' => [ 'FindingIdentifier' => [ 'shape' => 'OcsfFindingIdentifier', ], 'MetadataUid' => [ 'shape' => 'NonEmptyString', ], 'ErrorCode' => [ 'shape' => 'BatchUpdateFindingsV2UnprocessedFindingErrorCode', ], 'ErrorMessage' => [ 'shape' => 'NonEmptyString', ], ], ], 'BatchUpdateFindingsV2UnprocessedFindingErrorCode' => [ 'type' => 'string', 'enum' => [ 'ResourceNotFoundException', 'ValidationException', 'InternalServerException', 'ConflictException', ], ], 'BatchUpdateFindingsV2UnprocessedFindingsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchUpdateFindingsV2UnprocessedFinding', ], ], 'BatchUpdateStandardsControlAssociationsRequest' => [ 'type' => 'structure', 'required' => [ 'StandardsControlAssociationUpdates', ], 'members' => [ 'StandardsControlAssociationUpdates' => [ 'shape' => 'StandardsControlAssociationUpdates', ], ], ], 'BatchUpdateStandardsControlAssociationsResponse' => [ 'type' => 'structure', 'members' => [ 'UnprocessedAssociationUpdates' => [ 'shape' => 'UnprocessedStandardsControlAssociationUpdates', ], ], ], 'Boolean' => [ 'type' => 'boolean', ], 'BooleanConfigurationOptions' => [ 'type' => 'structure', 'members' => [ 'DefaultValue' => [ 'shape' => 'Boolean', ], ], ], 'BooleanFilter' => [ 'type' => 'structure', 'members' => [ 'Value' => [ 'shape' => 'Boolean', ], ], ], 'BooleanFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BooleanFilter', ], ], 'CategoryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], ], 'Cell' => [ 'type' => 'structure', 'members' => [ 'Column' => [ 'shape' => 'Long', ], 'Row' => [ 'shape' => 'Long', ], 'ColumnName' => [ 'shape' => 'NonEmptyString', ], 'CellReference' => [ 'shape' => 'NonEmptyString', ], ], ], 'Cells' => [ 'type' => 'list', 'member' => [ 'shape' => 'Cell', ], ], 'CidrBlockAssociation' => [ 'type' => 'structure', 'members' => [ 'AssociationId' => [ 'shape' => 'NonEmptyString', ], 'CidrBlock' => [ 'shape' => 'NonEmptyString', ], 'CidrBlockState' => [ 'shape' => 'NonEmptyString', ], ], ], 'CidrBlockAssociationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CidrBlockAssociation', ], ], 'City' => [ 'type' => 'structure', 'members' => [ 'CityName' => [ 'shape' => 'NonEmptyString', ], ], ], 'ClassificationResult' => [ 'type' => 'structure', 'members' => [ 'MimeType' => [ 'shape' => 'NonEmptyString', ], 'SizeClassified' => [ 'shape' => 'Long', ], 'AdditionalOccurrences' => [ 'shape' => 'Boolean', ], 'Status' => [ 'shape' => 'ClassificationStatus', ], 'SensitiveData' => [ 'shape' => 'SensitiveDataResultList', ], 'CustomDataIdentifiers' => [ 'shape' => 'CustomDataIdentifiersResult', ], ], ], 'ClassificationStatus' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'NonEmptyString', ], 'Reason' => [ 'shape' => 'NonEmptyString', ], ], ], 'ClientToken' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '^[\\x21-\\x7E]{1,64}$', ], 'CloudWatchLogsLogGroupArnConfigDetails' => [ 'type' => 'structure', 'members' => [ 'CloudWatchLogsLogGroupArn' => [ 'shape' => 'NonEmptyString', ], 'HostedZoneId' => [ 'shape' => 'NonEmptyString', ], 'Id' => [ 'shape' => 'NonEmptyString', ], ], ], 'CodeVulnerabilitiesFilePath' => [ 'type' => 'structure', 'members' => [ 'EndLine' => [ 'shape' => 'Integer', ], 'FileName' => [ 'shape' => 'NonEmptyString', ], 'FilePath' => [ 'shape' => 'NonEmptyString', ], 'StartLine' => [ 'shape' => 'Integer', ], ], ], 'Compliance' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'ComplianceStatus', ], 'RelatedRequirements' => [ 'shape' => 'RelatedRequirementsList', ], 'StatusReasons' => [ 'shape' => 'StatusReasonsList', ], 'SecurityControlId' => [ 'shape' => 'NonEmptyString', ], 'AssociatedStandards' => [ 'shape' => 'AssociatedStandardsList', ], 'SecurityControlParameters' => [ 'shape' => 'SecurityControlParametersList', ], ], ], 'ComplianceStatus' => [ 'type' => 'string', 'enum' => [ 'PASSED', 'WARNING', 'FAILED', 'NOT_AVAILABLE', ], ], 'CompositeFilter' => [ 'type' => 'structure', 'members' => [ 'StringFilters' => [ 'shape' => 'OcsfStringFilterList', ], 'DateFilters' => [ 'shape' => 'OcsfDateFilterList', ], 'BooleanFilters' => [ 'shape' => 'OcsfBooleanFilterList', ], 'NumberFilters' => [ 'shape' => 'OcsfNumberFilterList', ], 'MapFilters' => [ 'shape' => 'OcsfMapFilterList', ], 'Operator' => [ 'shape' => 'AllowedOperators', ], ], ], 'CompositeFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CompositeFilter', ], ], 'ConfigurationOptions' => [ 'type' => 'structure', 'members' => [ 'Integer' => [ 'shape' => 'IntegerConfigurationOptions', ], 'IntegerList' => [ 'shape' => 'IntegerListConfigurationOptions', ], 'Double' => [ 'shape' => 'DoubleConfigurationOptions', ], 'String' => [ 'shape' => 'StringConfigurationOptions', ], 'StringList' => [ 'shape' => 'StringListConfigurationOptions', ], 'Boolean' => [ 'shape' => 'BooleanConfigurationOptions', ], 'Enum' => [ 'shape' => 'EnumConfigurationOptions', ], 'EnumList' => [ 'shape' => 'EnumListConfigurationOptions', ], ], 'union' => true, ], 'ConfigurationPolicyAssociation' => [ 'type' => 'structure', 'members' => [ 'Target' => [ 'shape' => 'Target', ], ], ], 'ConfigurationPolicyAssociationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfigurationPolicyAssociationSummary', ], ], 'ConfigurationPolicyAssociationStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'SUCCESS', 'FAILED', ], ], 'ConfigurationPolicyAssociationSummary' => [ 'type' => 'structure', 'members' => [ 'ConfigurationPolicyId' => [ 'shape' => 'NonEmptyString', ], 'TargetId' => [ 'shape' => 'NonEmptyString', ], 'TargetType' => [ 'shape' => 'TargetType', ], 'AssociationType' => [ 'shape' => 'AssociationType', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], 'AssociationStatus' => [ 'shape' => 'ConfigurationPolicyAssociationStatus', ], 'AssociationStatusMessage' => [ 'shape' => 'NonEmptyString', ], ], ], 'ConfigurationPolicyAssociationSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfigurationPolicyAssociationSummary', ], ], 'ConfigurationPolicyAssociationsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfigurationPolicyAssociation', ], ], 'ConfigurationPolicySummary' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'NonEmptyString', ], 'Id' => [ 'shape' => 'NonEmptyString', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], 'ServiceEnabled' => [ 'shape' => 'Boolean', ], ], ], 'ConfigurationPolicySummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfigurationPolicySummary', ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'NonEmptyString', ], 'Code' => [ 'shape' => 'NonEmptyString', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ConnectionDirection' => [ 'type' => 'string', 'enum' => [ 'INBOUND', 'OUTBOUND', ], ], 'ConnectorAuthStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'FAILED', ], ], 'ConnectorProviderName' => [ 'type' => 'string', 'enum' => [ 'JIRA_CLOUD', 'SERVICENOW', ], ], 'ConnectorRegistrationsV2Request' => [ 'type' => 'structure', 'required' => [ 'AuthCode', 'AuthState', ], 'members' => [ 'AuthCode' => [ 'shape' => 'NonEmptyString', ], 'AuthState' => [ 'shape' => 'NonEmptyString', ], ], ], 'ConnectorRegistrationsV2Response' => [ 'type' => 'structure', 'required' => [ 'ConnectorId', ], 'members' => [ 'ConnectorArn' => [ 'shape' => 'NonEmptyString', ], 'ConnectorId' => [ 'shape' => 'NonEmptyString', ], ], ], 'ConnectorStatus' => [ 'type' => 'string', 'enum' => [ 'CONNECTED', 'FAILED_TO_CONNECT', 'PENDING_CONFIGURATION', 'PENDING_AUTHORIZATION', ], ], 'ConnectorSummary' => [ 'type' => 'structure', 'required' => [ 'ConnectorId', 'Name', 'ProviderSummary', 'CreatedAt', ], 'members' => [ 'ConnectorArn' => [ 'shape' => 'NonEmptyString', ], 'ConnectorId' => [ 'shape' => 'NonEmptyString', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'ProviderSummary' => [ 'shape' => 'ProviderSummary', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'ConnectorSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConnectorSummary', ], ], 'ContainerDetails' => [ 'type' => 'structure', 'members' => [ 'ContainerRuntime' => [ 'shape' => 'NonEmptyString', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'ImageId' => [ 'shape' => 'NonEmptyString', ], 'ImageName' => [ 'shape' => 'NonEmptyString', ], 'LaunchedAt' => [ 'shape' => 'NonEmptyString', ], 'VolumeMounts' => [ 'shape' => 'VolumeMountList', ], 'Privileged' => [ 'shape' => 'Boolean', ], ], ], 'ControlFindingGenerator' => [ 'type' => 'string', 'enum' => [ 'STANDARD_CONTROL', 'SECURITY_CONTROL', ], ], 'ControlStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'Country' => [ 'type' => 'structure', 'members' => [ 'CountryCode' => [ 'shape' => 'NonEmptyString', ], 'CountryName' => [ 'shape' => 'NonEmptyString', ], ], ], 'CreateActionTargetRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Description', 'Id', ], 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'Id' => [ 'shape' => 'NonEmptyString', ], ], ], 'CreateActionTargetResponse' => [ 'type' => 'structure', 'required' => [ 'ActionTargetArn', ], 'members' => [ 'ActionTargetArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'CreateAggregatorV2Request' => [ 'type' => 'structure', 'required' => [ 'RegionLinkingMode', ], 'members' => [ 'RegionLinkingMode' => [ 'shape' => 'NonEmptyString', ], 'LinkedRegions' => [ 'shape' => 'StringList', ], 'Tags' => [ 'shape' => 'TagMap', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CreateAggregatorV2Response' => [ 'type' => 'structure', 'members' => [ 'AggregatorV2Arn' => [ 'shape' => 'NonEmptyString', ], 'AggregationRegion' => [ 'shape' => 'NonEmptyString', ], 'RegionLinkingMode' => [ 'shape' => 'NonEmptyString', ], 'LinkedRegions' => [ 'shape' => 'StringList', ], ], ], 'CreateAutomationRuleRequest' => [ 'type' => 'structure', 'required' => [ 'RuleOrder', 'RuleName', 'Description', 'Criteria', 'Actions', ], 'members' => [ 'Tags' => [ 'shape' => 'TagMap', ], 'RuleStatus' => [ 'shape' => 'RuleStatus', ], 'RuleOrder' => [ 'shape' => 'RuleOrderValue', ], 'RuleName' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'IsTerminal' => [ 'shape' => 'Boolean', ], 'Criteria' => [ 'shape' => 'AutomationRulesFindingFilters', ], 'Actions' => [ 'shape' => 'ActionList', ], ], ], 'CreateAutomationRuleResponse' => [ 'type' => 'structure', 'members' => [ 'RuleArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'CreateAutomationRuleV2Request' => [ 'type' => 'structure', 'required' => [ 'RuleName', 'Description', 'RuleOrder', 'Criteria', 'Actions', ], 'members' => [ 'RuleName' => [ 'shape' => 'NonEmptyString', ], 'RuleStatus' => [ 'shape' => 'RuleStatusV2', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'RuleOrder' => [ 'shape' => 'RuleOrderValueV2', ], 'Criteria' => [ 'shape' => 'Criteria', ], 'Actions' => [ 'shape' => 'AutomationRulesActionListV2', ], 'Tags' => [ 'shape' => 'TagMap', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CreateAutomationRuleV2Response' => [ 'type' => 'structure', 'members' => [ 'RuleArn' => [ 'shape' => 'NonEmptyString', ], 'RuleId' => [ 'shape' => 'NonEmptyString', ], ], ], 'CreateConfigurationPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'ConfigurationPolicy', ], 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'ConfigurationPolicy' => [ 'shape' => 'Policy', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateConfigurationPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'NonEmptyString', ], 'Id' => [ 'shape' => 'NonEmptyString', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'ConfigurationPolicy' => [ 'shape' => 'Policy', ], ], ], 'CreateConnectorV2Request' => [ 'type' => 'structure', 'required' => [ 'Name', 'Provider', ], 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'Provider' => [ 'shape' => 'ProviderConfiguration', ], 'KmsKeyArn' => [ 'shape' => 'NonEmptyString', ], 'Tags' => [ 'shape' => 'TagMap', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CreateConnectorV2Response' => [ 'type' => 'structure', 'required' => [ 'ConnectorId', ], 'members' => [ 'ConnectorArn' => [ 'shape' => 'NonEmptyString', ], 'ConnectorId' => [ 'shape' => 'NonEmptyString', ], 'AuthUrl' => [ 'shape' => 'NonEmptyString', ], ], ], 'CreateFindingAggregatorRequest' => [ 'type' => 'structure', 'required' => [ 'RegionLinkingMode', ], 'members' => [ 'RegionLinkingMode' => [ 'shape' => 'NonEmptyString', ], 'Regions' => [ 'shape' => 'StringList', ], ], ], 'CreateFindingAggregatorResponse' => [ 'type' => 'structure', 'members' => [ 'FindingAggregatorArn' => [ 'shape' => 'NonEmptyString', ], 'FindingAggregationRegion' => [ 'shape' => 'NonEmptyString', ], 'RegionLinkingMode' => [ 'shape' => 'NonEmptyString', ], 'Regions' => [ 'shape' => 'StringList', ], ], ], 'CreateInsightRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Filters', 'GroupByAttribute', ], 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'Filters' => [ 'shape' => 'AwsSecurityFindingFilters', ], 'GroupByAttribute' => [ 'shape' => 'NonEmptyString', ], ], ], 'CreateInsightResponse' => [ 'type' => 'structure', 'required' => [ 'InsightArn', ], 'members' => [ 'InsightArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'CreateMembersRequest' => [ 'type' => 'structure', 'required' => [ 'AccountDetails', ], 'members' => [ 'AccountDetails' => [ 'shape' => 'AccountDetailsList', ], ], ], 'CreateMembersResponse' => [ 'type' => 'structure', 'members' => [ 'UnprocessedAccounts' => [ 'shape' => 'ResultList', ], ], ], 'CreateTicketV2Request' => [ 'type' => 'structure', 'required' => [ 'ConnectorId', 'FindingMetadataUid', ], 'members' => [ 'ConnectorId' => [ 'shape' => 'NonEmptyString', ], 'FindingMetadataUid' => [ 'shape' => 'NonEmptyString', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CreateTicketV2Response' => [ 'type' => 'structure', 'required' => [ 'TicketId', ], 'members' => [ 'TicketId' => [ 'shape' => 'NonEmptyString', ], 'TicketSrcUrl' => [ 'shape' => 'NonEmptyString', ], ], ], 'Criteria' => [ 'type' => 'structure', 'members' => [ 'OcsfFindingCriteria' => [ 'shape' => 'OcsfFindingFilters', ], ], 'union' => true, ], 'CrossAccountMaxResults' => [ 'type' => 'integer', 'max' => 50, 'min' => 1, ], 'CustomDataIdentifiersDetections' => [ 'type' => 'structure', 'members' => [ 'Count' => [ 'shape' => 'Long', ], 'Arn' => [ 'shape' => 'NonEmptyString', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'Occurrences' => [ 'shape' => 'Occurrences', ], ], ], 'CustomDataIdentifiersDetectionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CustomDataIdentifiersDetections', ], ], 'CustomDataIdentifiersResult' => [ 'type' => 'structure', 'members' => [ 'Detections' => [ 'shape' => 'CustomDataIdentifiersDetectionsList', ], 'TotalCount' => [ 'shape' => 'Long', ], ], ], 'CustomizableProperties' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityControlProperty', ], ], 'Cvss' => [ 'type' => 'structure', 'members' => [ 'Version' => [ 'shape' => 'NonEmptyString', ], 'BaseScore' => [ 'shape' => 'Double', ], 'BaseVector' => [ 'shape' => 'NonEmptyString', ], 'Source' => [ 'shape' => 'NonEmptyString', ], 'Adjustments' => [ 'shape' => 'AdjustmentList', ], ], ], 'CvssList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Cvss', ], ], 'DataClassificationDetails' => [ 'type' => 'structure', 'members' => [ 'DetailedResultsLocation' => [ 'shape' => 'NonEmptyString', ], 'Result' => [ 'shape' => 'ClassificationResult', ], ], ], 'DateFilter' => [ 'type' => 'structure', 'members' => [ 'Start' => [ 'shape' => 'NonEmptyString', ], 'End' => [ 'shape' => 'NonEmptyString', ], 'DateRange' => [ 'shape' => 'DateRange', ], ], ], 'DateFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DateFilter', ], ], 'DateRange' => [ 'type' => 'structure', 'members' => [ 'Value' => [ 'shape' => 'Integer', ], 'Unit' => [ 'shape' => 'DateRangeUnit', ], ], ], 'DateRangeUnit' => [ 'type' => 'string', 'enum' => [ 'DAYS', ], ], 'DeclineInvitationsRequest' => [ 'type' => 'structure', 'required' => [ 'AccountIds', ], 'members' => [ 'AccountIds' => [ 'shape' => 'AccountIdList', ], ], ], 'DeclineInvitationsResponse' => [ 'type' => 'structure', 'members' => [ 'UnprocessedAccounts' => [ 'shape' => 'ResultList', ], ], ], 'DeleteActionTargetRequest' => [ 'type' => 'structure', 'required' => [ 'ActionTargetArn', ], 'members' => [ 'ActionTargetArn' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'ActionTargetArn', ], ], ], 'DeleteActionTargetResponse' => [ 'type' => 'structure', 'required' => [ 'ActionTargetArn', ], 'members' => [ 'ActionTargetArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'DeleteAggregatorV2Request' => [ 'type' => 'structure', 'required' => [ 'AggregatorV2Arn', ], 'members' => [ 'AggregatorV2Arn' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'AggregatorV2Arn', ], ], ], 'DeleteAggregatorV2Response' => [ 'type' => 'structure', 'members' => [], ], 'DeleteAutomationRuleV2Request' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'Identifier', ], ], ], 'DeleteAutomationRuleV2Response' => [ 'type' => 'structure', 'members' => [], ], 'DeleteConfigurationPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'Identifier', ], ], ], 'DeleteConfigurationPolicyResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteConnectorV2Request' => [ 'type' => 'structure', 'required' => [ 'ConnectorId', ], 'members' => [ 'ConnectorId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'ConnectorId', ], ], ], 'DeleteConnectorV2Response' => [ 'type' => 'structure', 'members' => [], ], 'DeleteFindingAggregatorRequest' => [ 'type' => 'structure', 'required' => [ 'FindingAggregatorArn', ], 'members' => [ 'FindingAggregatorArn' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'FindingAggregatorArn', ], ], ], 'DeleteFindingAggregatorResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteInsightRequest' => [ 'type' => 'structure', 'required' => [ 'InsightArn', ], 'members' => [ 'InsightArn' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'InsightArn', ], ], ], 'DeleteInsightResponse' => [ 'type' => 'structure', 'required' => [ 'InsightArn', ], 'members' => [ 'InsightArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'DeleteInvitationsRequest' => [ 'type' => 'structure', 'required' => [ 'AccountIds', ], 'members' => [ 'AccountIds' => [ 'shape' => 'AccountIdList', ], ], ], 'DeleteInvitationsResponse' => [ 'type' => 'structure', 'members' => [ 'UnprocessedAccounts' => [ 'shape' => 'ResultList', ], ], ], 'DeleteMembersRequest' => [ 'type' => 'structure', 'required' => [ 'AccountIds', ], 'members' => [ 'AccountIds' => [ 'shape' => 'AccountIdList', ], ], ], 'DeleteMembersResponse' => [ 'type' => 'structure', 'members' => [ 'UnprocessedAccounts' => [ 'shape' => 'ResultList', ], ], ], 'DescribeActionTargetsRequest' => [ 'type' => 'structure', 'members' => [ 'ActionTargetArns' => [ 'shape' => 'ArnList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'DescribeActionTargetsResponse' => [ 'type' => 'structure', 'required' => [ 'ActionTargets', ], 'members' => [ 'ActionTargets' => [ 'shape' => 'ActionTargetList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeHubRequest' => [ 'type' => 'structure', 'members' => [ 'HubArn' => [ 'shape' => 'NonEmptyString', 'location' => 'querystring', 'locationName' => 'HubArn', ], ], ], 'DescribeHubResponse' => [ 'type' => 'structure', 'members' => [ 'HubArn' => [ 'shape' => 'NonEmptyString', ], 'SubscribedAt' => [ 'shape' => 'NonEmptyString', ], 'AutoEnableControls' => [ 'shape' => 'Boolean', ], 'ControlFindingGenerator' => [ 'shape' => 'ControlFindingGenerator', ], ], ], 'DescribeOrganizationConfigurationRequest' => [ 'type' => 'structure', 'members' => [], ], 'DescribeOrganizationConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'AutoEnable' => [ 'shape' => 'Boolean', ], 'MemberAccountLimitReached' => [ 'shape' => 'Boolean', ], 'AutoEnableStandards' => [ 'shape' => 'AutoEnableStandards', ], 'OrganizationConfiguration' => [ 'shape' => 'OrganizationConfiguration', ], ], ], 'DescribeProductsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'ProductArn' => [ 'shape' => 'NonEmptyString', 'location' => 'querystring', 'locationName' => 'ProductArn', ], ], ], 'DescribeProductsResponse' => [ 'type' => 'structure', 'required' => [ 'Products', ], 'members' => [ 'Products' => [ 'shape' => 'ProductsList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeProductsV2Request' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], ], ], 'DescribeProductsV2Response' => [ 'type' => 'structure', 'required' => [ 'ProductsV2', ], 'members' => [ 'ProductsV2' => [ 'shape' => 'ProductsV2List', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeSecurityHubV2Request' => [ 'type' => 'structure', 'members' => [], ], 'DescribeSecurityHubV2Response' => [ 'type' => 'structure', 'members' => [ 'HubV2Arn' => [ 'shape' => 'NonEmptyString', ], 'SubscribedAt' => [ 'shape' => 'NonEmptyString', ], ], ], 'DescribeStandardsControlsRequest' => [ 'type' => 'structure', 'required' => [ 'StandardsSubscriptionArn', ], 'members' => [ 'StandardsSubscriptionArn' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'StandardsSubscriptionArn', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], ], ], 'DescribeStandardsControlsResponse' => [ 'type' => 'structure', 'members' => [ 'Controls' => [ 'shape' => 'StandardsControls', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeStandardsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], ], ], 'DescribeStandardsResponse' => [ 'type' => 'structure', 'members' => [ 'Standards' => [ 'shape' => 'Standards', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'Detection' => [ 'type' => 'structure', 'members' => [ 'Sequence' => [ 'shape' => 'Sequence', ], ], ], 'DisableImportFindingsForProductRequest' => [ 'type' => 'structure', 'required' => [ 'ProductSubscriptionArn', ], 'members' => [ 'ProductSubscriptionArn' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'ProductSubscriptionArn', ], ], ], 'DisableImportFindingsForProductResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisableOrganizationAdminAccountRequest' => [ 'type' => 'structure', 'required' => [ 'AdminAccountId', ], 'members' => [ 'AdminAccountId' => [ 'shape' => 'NonEmptyString', ], 'Feature' => [ 'shape' => 'SecurityHubFeature', ], ], ], 'DisableOrganizationAdminAccountResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisableSecurityHubRequest' => [ 'type' => 'structure', 'members' => [], ], 'DisableSecurityHubResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisableSecurityHubV2Request' => [ 'type' => 'structure', 'members' => [], ], 'DisableSecurityHubV2Response' => [ 'type' => 'structure', 'members' => [], ], 'DisabledSecurityControlIdentifierList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], ], 'DisassociateFromAdministratorAccountRequest' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateFromAdministratorAccountResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateFromMasterAccountRequest' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateFromMasterAccountResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateMembersRequest' => [ 'type' => 'structure', 'required' => [ 'AccountIds', ], 'members' => [ 'AccountIds' => [ 'shape' => 'AccountIdList', ], ], ], 'DisassociateMembersResponse' => [ 'type' => 'structure', 'members' => [], ], 'DnsRequestAction' => [ 'type' => 'structure', 'members' => [ 'Domain' => [ 'shape' => 'NonEmptyString', ], 'Protocol' => [ 'shape' => 'NonEmptyString', ], 'Blocked' => [ 'shape' => 'Boolean', ], ], ], 'Double' => [ 'type' => 'double', ], 'DoubleConfigurationOptions' => [ 'type' => 'structure', 'members' => [ 'DefaultValue' => [ 'shape' => 'Double', ], 'Min' => [ 'shape' => 'Double', ], 'Max' => [ 'shape' => 'Double', ], ], ], 'EnableImportFindingsForProductRequest' => [ 'type' => 'structure', 'required' => [ 'ProductArn', ], 'members' => [ 'ProductArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'EnableImportFindingsForProductResponse' => [ 'type' => 'structure', 'members' => [ 'ProductSubscriptionArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'EnableOrganizationAdminAccountRequest' => [ 'type' => 'structure', 'required' => [ 'AdminAccountId', ], 'members' => [ 'AdminAccountId' => [ 'shape' => 'NonEmptyString', ], 'Feature' => [ 'shape' => 'SecurityHubFeature', ], ], ], 'EnableOrganizationAdminAccountResponse' => [ 'type' => 'structure', 'members' => [ 'AdminAccountId' => [ 'shape' => 'NonEmptyString', ], 'Feature' => [ 'shape' => 'SecurityHubFeature', ], ], ], 'EnableSecurityHubRequest' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagMap', ], 'EnableDefaultStandards' => [ 'shape' => 'Boolean', ], 'ControlFindingGenerator' => [ 'shape' => 'ControlFindingGenerator', ], ], ], 'EnableSecurityHubResponse' => [ 'type' => 'structure', 'members' => [], ], 'EnableSecurityHubV2Request' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'EnableSecurityHubV2Response' => [ 'type' => 'structure', 'members' => [ 'HubV2Arn' => [ 'shape' => 'NonEmptyString', ], ], ], 'EnabledSecurityControlIdentifierList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], ], 'EnabledStandardIdentifierList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], ], 'EnumConfigurationOptions' => [ 'type' => 'structure', 'members' => [ 'DefaultValue' => [ 'shape' => 'NonEmptyString', ], 'AllowedValues' => [ 'shape' => 'StringList', ], ], ], 'EnumListConfigurationOptions' => [ 'type' => 'structure', 'members' => [ 'DefaultValue' => [ 'shape' => 'StringList', ], 'MaxItems' => [ 'shape' => 'Integer', ], 'AllowedValues' => [ 'shape' => 'StringList', ], ], ], 'ExternalIntegrationConfiguration' => [ 'type' => 'structure', 'members' => [ 'ConnectorArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'FieldMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'NonEmptyString', ], 'value' => [ 'shape' => 'NonEmptyString', ], ], 'FilePathList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FilePaths', ], ], 'FilePaths' => [ 'type' => 'structure', 'members' => [ 'FilePath' => [ 'shape' => 'NonEmptyString', ], 'FileName' => [ 'shape' => 'NonEmptyString', ], 'ResourceId' => [ 'shape' => 'NonEmptyString', ], 'Hash' => [ 'shape' => 'NonEmptyString', ], ], ], 'FindingAggregator' => [ 'type' => 'structure', 'members' => [ 'FindingAggregatorArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'FindingAggregatorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FindingAggregator', ], ], 'FindingHistoryRecord' => [ 'type' => 'structure', 'members' => [ 'FindingIdentifier' => [ 'shape' => 'AwsSecurityFindingIdentifier', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], 'FindingCreated' => [ 'shape' => 'Boolean', ], 'UpdateSource' => [ 'shape' => 'FindingHistoryUpdateSource', ], 'Updates' => [ 'shape' => 'FindingHistoryUpdatesList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'FindingHistoryRecordList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FindingHistoryRecord', ], ], 'FindingHistoryUpdate' => [ 'type' => 'structure', 'members' => [ 'UpdatedField' => [ 'shape' => 'NonEmptyString', ], 'OldValue' => [ 'shape' => 'NonEmptyString', ], 'NewValue' => [ 'shape' => 'NonEmptyString', ], ], ], 'FindingHistoryUpdateSource' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'FindingHistoryUpdateSourceType', ], 'Identity' => [ 'shape' => 'NonEmptyString', ], ], ], 'FindingHistoryUpdateSourceType' => [ 'type' => 'string', 'enum' => [ 'BATCH_UPDATE_FINDINGS', 'BATCH_IMPORT_FINDINGS', ], ], 'FindingHistoryUpdatesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FindingHistoryUpdate', ], ], 'FindingProviderFields' => [ 'type' => 'structure', 'members' => [ 'Confidence' => [ 'shape' => 'RatioScale', ], 'Criticality' => [ 'shape' => 'RatioScale', ], 'RelatedFindings' => [ 'shape' => 'RelatedFindingList', ], 'Severity' => [ 'shape' => 'FindingProviderSeverity', ], 'Types' => [ 'shape' => 'TypeList', ], ], ], 'FindingProviderSeverity' => [ 'type' => 'structure', 'members' => [ 'Label' => [ 'shape' => 'SeverityLabel', ], 'Original' => [ 'shape' => 'NonEmptyString', ], ], ], 'FirewallPolicyDetails' => [ 'type' => 'structure', 'members' => [ 'StatefulRuleGroupReferences' => [ 'shape' => 'FirewallPolicyStatefulRuleGroupReferencesList', ], 'StatelessCustomActions' => [ 'shape' => 'FirewallPolicyStatelessCustomActionsList', ], 'StatelessDefaultActions' => [ 'shape' => 'NonEmptyStringList', ], 'StatelessFragmentDefaultActions' => [ 'shape' => 'NonEmptyStringList', ], 'StatelessRuleGroupReferences' => [ 'shape' => 'FirewallPolicyStatelessRuleGroupReferencesList', ], ], ], 'FirewallPolicyStatefulRuleGroupReferencesDetails' => [ 'type' => 'structure', 'members' => [ 'ResourceArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'FirewallPolicyStatefulRuleGroupReferencesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FirewallPolicyStatefulRuleGroupReferencesDetails', ], ], 'FirewallPolicyStatelessCustomActionsDetails' => [ 'type' => 'structure', 'members' => [ 'ActionDefinition' => [ 'shape' => 'StatelessCustomActionDefinition', ], 'ActionName' => [ 'shape' => 'NonEmptyString', ], ], ], 'FirewallPolicyStatelessCustomActionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FirewallPolicyStatelessCustomActionsDetails', ], ], 'FirewallPolicyStatelessRuleGroupReferencesDetails' => [ 'type' => 'structure', 'members' => [ 'Priority' => [ 'shape' => 'Integer', ], 'ResourceArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'FirewallPolicyStatelessRuleGroupReferencesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FirewallPolicyStatelessRuleGroupReferencesDetails', ], ], 'GeneratorDetails' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'Labels' => [ 'shape' => 'TypeList', ], ], ], 'GeoLocation' => [ 'type' => 'structure', 'members' => [ 'Lon' => [ 'shape' => 'Double', ], 'Lat' => [ 'shape' => 'Double', ], ], ], 'GetAdministratorAccountRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetAdministratorAccountResponse' => [ 'type' => 'structure', 'members' => [ 'Administrator' => [ 'shape' => 'Invitation', ], ], ], 'GetAggregatorV2Request' => [ 'type' => 'structure', 'required' => [ 'AggregatorV2Arn', ], 'members' => [ 'AggregatorV2Arn' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'AggregatorV2Arn', ], ], ], 'GetAggregatorV2Response' => [ 'type' => 'structure', 'members' => [ 'AggregatorV2Arn' => [ 'shape' => 'NonEmptyString', ], 'AggregationRegion' => [ 'shape' => 'NonEmptyString', ], 'RegionLinkingMode' => [ 'shape' => 'NonEmptyString', ], 'LinkedRegions' => [ 'shape' => 'StringList', ], ], ], 'GetAutomationRuleV2Request' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'Identifier', ], ], ], 'GetAutomationRuleV2Response' => [ 'type' => 'structure', 'members' => [ 'RuleArn' => [ 'shape' => 'NonEmptyString', ], 'RuleId' => [ 'shape' => 'NonEmptyString', ], 'RuleOrder' => [ 'shape' => 'RuleOrderValueV2', ], 'RuleName' => [ 'shape' => 'NonEmptyString', ], 'RuleStatus' => [ 'shape' => 'RuleStatusV2', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'Criteria' => [ 'shape' => 'Criteria', ], 'Actions' => [ 'shape' => 'AutomationRulesActionListV2', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'GetConfigurationPolicyAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'Target', ], 'members' => [ 'Target' => [ 'shape' => 'Target', ], ], ], 'GetConfigurationPolicyAssociationResponse' => [ 'type' => 'structure', 'members' => [ 'ConfigurationPolicyId' => [ 'shape' => 'NonEmptyString', ], 'TargetId' => [ 'shape' => 'NonEmptyString', ], 'TargetType' => [ 'shape' => 'TargetType', ], 'AssociationType' => [ 'shape' => 'AssociationType', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], 'AssociationStatus' => [ 'shape' => 'ConfigurationPolicyAssociationStatus', ], 'AssociationStatusMessage' => [ 'shape' => 'NonEmptyString', ], ], ], 'GetConfigurationPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'Identifier', ], ], ], 'GetConfigurationPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'NonEmptyString', ], 'Id' => [ 'shape' => 'NonEmptyString', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'ConfigurationPolicy' => [ 'shape' => 'Policy', ], ], ], 'GetConnectorV2Request' => [ 'type' => 'structure', 'required' => [ 'ConnectorId', ], 'members' => [ 'ConnectorId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'ConnectorId', ], ], ], 'GetConnectorV2Response' => [ 'type' => 'structure', 'required' => [ 'ConnectorId', 'Name', 'CreatedAt', 'LastUpdatedAt', 'Health', 'ProviderDetail', ], 'members' => [ 'ConnectorArn' => [ 'shape' => 'NonEmptyString', ], 'ConnectorId' => [ 'shape' => 'NonEmptyString', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'KmsKeyArn' => [ 'shape' => 'NonEmptyString', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'LastUpdatedAt' => [ 'shape' => 'Timestamp', ], 'Health' => [ 'shape' => 'HealthCheck', ], 'ProviderDetail' => [ 'shape' => 'ProviderDetail', ], ], ], 'GetEnabledStandardsRequest' => [ 'type' => 'structure', 'members' => [ 'StandardsSubscriptionArns' => [ 'shape' => 'StandardsSubscriptionArns', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'GetEnabledStandardsResponse' => [ 'type' => 'structure', 'members' => [ 'StandardsSubscriptions' => [ 'shape' => 'StandardsSubscriptions', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetFindingAggregatorRequest' => [ 'type' => 'structure', 'required' => [ 'FindingAggregatorArn', ], 'members' => [ 'FindingAggregatorArn' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'FindingAggregatorArn', ], ], ], 'GetFindingAggregatorResponse' => [ 'type' => 'structure', 'members' => [ 'FindingAggregatorArn' => [ 'shape' => 'NonEmptyString', ], 'FindingAggregationRegion' => [ 'shape' => 'NonEmptyString', ], 'RegionLinkingMode' => [ 'shape' => 'NonEmptyString', ], 'Regions' => [ 'shape' => 'StringList', ], ], ], 'GetFindingHistoryRequest' => [ 'type' => 'structure', 'required' => [ 'FindingIdentifier', ], 'members' => [ 'FindingIdentifier' => [ 'shape' => 'AwsSecurityFindingIdentifier', ], 'StartTime' => [ 'shape' => 'Timestamp', ], 'EndTime' => [ 'shape' => 'Timestamp', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'GetFindingHistoryResponse' => [ 'type' => 'structure', 'members' => [ 'Records' => [ 'shape' => 'FindingHistoryRecordList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetFindingStatisticsV2Request' => [ 'type' => 'structure', 'required' => [ 'GroupByRules', ], 'members' => [ 'GroupByRules' => [ 'shape' => 'GroupByRules', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], 'MaxStatisticResults' => [ 'shape' => 'MaxStatisticResults', ], ], ], 'GetFindingStatisticsV2Response' => [ 'type' => 'structure', 'members' => [ 'GroupByResults' => [ 'shape' => 'GroupByResults', ], ], ], 'GetFindingsRequest' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'AwsSecurityFindingFilters', ], 'SortCriteria' => [ 'shape' => 'SortCriteria', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'GetFindingsResponse' => [ 'type' => 'structure', 'required' => [ 'Findings', ], 'members' => [ 'Findings' => [ 'shape' => 'AwsSecurityFindingList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetFindingsV2Request' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'OcsfFindingFilters', ], 'SortCriteria' => [ 'shape' => 'SortCriteria', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'GetFindingsV2Response' => [ 'type' => 'structure', 'members' => [ 'Findings' => [ 'shape' => 'OcsfFindingsList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetInsightResultsRequest' => [ 'type' => 'structure', 'required' => [ 'InsightArn', ], 'members' => [ 'InsightArn' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'InsightArn', ], ], ], 'GetInsightResultsResponse' => [ 'type' => 'structure', 'required' => [ 'InsightResults', ], 'members' => [ 'InsightResults' => [ 'shape' => 'InsightResults', ], ], ], 'GetInsightsRequest' => [ 'type' => 'structure', 'members' => [ 'InsightArns' => [ 'shape' => 'ArnList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'GetInsightsResponse' => [ 'type' => 'structure', 'required' => [ 'Insights', ], 'members' => [ 'Insights' => [ 'shape' => 'InsightList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetInvitationsCountRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetInvitationsCountResponse' => [ 'type' => 'structure', 'members' => [ 'InvitationsCount' => [ 'shape' => 'Integer', ], ], ], 'GetMasterAccountRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetMasterAccountResponse' => [ 'type' => 'structure', 'members' => [ 'Master' => [ 'shape' => 'Invitation', ], ], ], 'GetMembersRequest' => [ 'type' => 'structure', 'required' => [ 'AccountIds', ], 'members' => [ 'AccountIds' => [ 'shape' => 'AccountIdList', ], ], ], 'GetMembersResponse' => [ 'type' => 'structure', 'members' => [ 'Members' => [ 'shape' => 'MemberList', ], 'UnprocessedAccounts' => [ 'shape' => 'ResultList', ], ], ], 'GetResourcesStatisticsV2Request' => [ 'type' => 'structure', 'required' => [ 'GroupByRules', ], 'members' => [ 'GroupByRules' => [ 'shape' => 'ResourceGroupByRules', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], 'MaxStatisticResults' => [ 'shape' => 'MaxStatisticResults', ], ], ], 'GetResourcesStatisticsV2Response' => [ 'type' => 'structure', 'required' => [ 'GroupByResults', ], 'members' => [ 'GroupByResults' => [ 'shape' => 'GroupByResults', ], ], ], 'GetResourcesV2Request' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'ResourcesFilters', ], 'SortCriteria' => [ 'shape' => 'SortCriteria', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'GetResourcesV2Response' => [ 'type' => 'structure', 'required' => [ 'Resources', ], 'members' => [ 'Resources' => [ 'shape' => 'Resources', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetSecurityControlDefinitionRequest' => [ 'type' => 'structure', 'required' => [ 'SecurityControlId', ], 'members' => [ 'SecurityControlId' => [ 'shape' => 'NonEmptyString', 'location' => 'querystring', 'locationName' => 'SecurityControlId', ], ], ], 'GetSecurityControlDefinitionResponse' => [ 'type' => 'structure', 'required' => [ 'SecurityControlDefinition', ], 'members' => [ 'SecurityControlDefinition' => [ 'shape' => 'SecurityControlDefinition', ], ], ], 'GroupByField' => [ 'type' => 'string', 'enum' => [ 'activity_name', 'cloud.account.uid', 'cloud.provider', 'cloud.region', 'compliance.assessments.name', 'compliance.status', 'compliance.control', 'finding_info.title', 'finding_info.types', 'metadata.product.name', 'metadata.product.uid', 'resources.type', 'resources.uid', 'severity', 'status', 'vulnerabilities.fix_coverage', 'class_name', ], ], 'GroupByResult' => [ 'type' => 'structure', 'members' => [ 'GroupByField' => [ 'shape' => 'NonEmptyString', ], 'GroupByValues' => [ 'shape' => 'GroupByValues', ], ], ], 'GroupByResults' => [ 'type' => 'list', 'member' => [ 'shape' => 'GroupByResult', ], ], 'GroupByRule' => [ 'type' => 'structure', 'required' => [ 'GroupByField', ], 'members' => [ 'Filters' => [ 'shape' => 'OcsfFindingFilters', ], 'GroupByField' => [ 'shape' => 'GroupByField', ], ], ], 'GroupByRules' => [ 'type' => 'list', 'member' => [ 'shape' => 'GroupByRule', ], ], 'GroupByValue' => [ 'type' => 'structure', 'members' => [ 'FieldValue' => [ 'shape' => 'NonEmptyString', ], 'Count' => [ 'shape' => 'Integer', ], ], ], 'GroupByValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'GroupByValue', ], ], 'HealthCheck' => [ 'type' => 'structure', 'required' => [ 'ConnectorStatus', 'LastCheckedAt', ], 'members' => [ 'ConnectorStatus' => [ 'shape' => 'ConnectorStatus', ], 'Message' => [ 'shape' => 'NonEmptyString', ], 'LastCheckedAt' => [ 'shape' => 'Timestamp', ], ], ], 'IcmpTypeCode' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'Integer', ], 'Type' => [ 'shape' => 'Integer', ], ], ], 'ImportFindingsError' => [ 'type' => 'structure', 'required' => [ 'Id', 'ErrorCode', 'ErrorMessage', ], 'members' => [ 'Id' => [ 'shape' => 'NonEmptyString', ], 'ErrorCode' => [ 'shape' => 'NonEmptyString', ], 'ErrorMessage' => [ 'shape' => 'NonEmptyString', ], ], ], 'ImportFindingsErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImportFindingsError', ], ], 'Indicator' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'NonEmptyString', ], 'Values' => [ 'shape' => 'NonEmptyStringList', ], 'Title' => [ 'shape' => 'NonEmptyString', ], 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'IndicatorsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Indicator', ], 'max' => 100, 'min' => 0, ], 'Insight' => [ 'type' => 'structure', 'required' => [ 'InsightArn', 'Name', 'Filters', 'GroupByAttribute', ], 'members' => [ 'InsightArn' => [ 'shape' => 'NonEmptyString', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'Filters' => [ 'shape' => 'AwsSecurityFindingFilters', ], 'GroupByAttribute' => [ 'shape' => 'NonEmptyString', ], ], ], 'InsightList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Insight', ], ], 'InsightResultValue' => [ 'type' => 'structure', 'required' => [ 'GroupByAttributeValue', 'Count', ], 'members' => [ 'GroupByAttributeValue' => [ 'shape' => 'NonEmptyString', ], 'Count' => [ 'shape' => 'Integer', ], ], ], 'InsightResultValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InsightResultValue', ], ], 'InsightResults' => [ 'type' => 'structure', 'required' => [ 'InsightArn', 'GroupByAttribute', 'ResultValues', ], 'members' => [ 'InsightArn' => [ 'shape' => 'NonEmptyString', ], 'GroupByAttribute' => [ 'shape' => 'NonEmptyString', ], 'ResultValues' => [ 'shape' => 'InsightResultValueList', ], ], ], 'Integer' => [ 'type' => 'integer', ], 'IntegerConfigurationOptions' => [ 'type' => 'structure', 'members' => [ 'DefaultValue' => [ 'shape' => 'Integer', ], 'Min' => [ 'shape' => 'Integer', ], 'Max' => [ 'shape' => 'Integer', ], ], ], 'IntegerList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Integer', ], ], 'IntegerListConfigurationOptions' => [ 'type' => 'structure', 'members' => [ 'DefaultValue' => [ 'shape' => 'IntegerList', ], 'Min' => [ 'shape' => 'Integer', ], 'Max' => [ 'shape' => 'Integer', ], 'MaxItems' => [ 'shape' => 'Integer', ], ], ], 'IntegrationType' => [ 'type' => 'string', 'enum' => [ 'SEND_FINDINGS_TO_SECURITY_HUB', 'RECEIVE_FINDINGS_FROM_SECURITY_HUB', 'UPDATE_FINDINGS_IN_SECURITY_HUB', ], ], 'IntegrationTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IntegrationType', ], ], 'IntegrationV2Type' => [ 'type' => 'string', 'enum' => [ 'SEND_FINDINGS_TO_SECURITY_HUB', 'RECEIVE_FINDINGS_FROM_SECURITY_HUB', 'UPDATE_FINDINGS_IN_SECURITY_HUB', ], ], 'IntegrationV2TypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IntegrationV2Type', ], ], 'InternalException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'NonEmptyString', ], 'Code' => [ 'shape' => 'NonEmptyString', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'NonEmptyString', ], 'Code' => [ 'shape' => 'NonEmptyString', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, ], 'InvalidAccessException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'NonEmptyString', ], 'Code' => [ 'shape' => 'NonEmptyString', ], ], 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], 'InvalidInputException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'NonEmptyString', ], 'Code' => [ 'shape' => 'NonEmptyString', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'Invitation' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'InvitationId' => [ 'shape' => 'NonEmptyString', ], 'InvitedAt' => [ 'shape' => 'Timestamp', ], 'MemberStatus' => [ 'shape' => 'NonEmptyString', ], ], ], 'InvitationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Invitation', ], ], 'InviteMembersRequest' => [ 'type' => 'structure', 'required' => [ 'AccountIds', ], 'members' => [ 'AccountIds' => [ 'shape' => 'AccountIdList', ], ], ], 'InviteMembersResponse' => [ 'type' => 'structure', 'members' => [ 'UnprocessedAccounts' => [ 'shape' => 'ResultList', ], ], ], 'IpFilter' => [ 'type' => 'structure', 'members' => [ 'Cidr' => [ 'shape' => 'NonEmptyString', ], ], ], 'IpFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IpFilter', ], ], 'IpOrganizationDetails' => [ 'type' => 'structure', 'members' => [ 'Asn' => [ 'shape' => 'Integer', ], 'AsnOrg' => [ 'shape' => 'NonEmptyString', ], 'Isp' => [ 'shape' => 'NonEmptyString', ], 'Org' => [ 'shape' => 'NonEmptyString', ], ], ], 'Ipv6CidrBlockAssociation' => [ 'type' => 'structure', 'members' => [ 'AssociationId' => [ 'shape' => 'NonEmptyString', ], 'Ipv6CidrBlock' => [ 'shape' => 'NonEmptyString', ], 'CidrBlockState' => [ 'shape' => 'NonEmptyString', ], ], ], 'Ipv6CidrBlockAssociationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Ipv6CidrBlockAssociation', ], ], 'JiraCloudDetail' => [ 'type' => 'structure', 'members' => [ 'CloudId' => [ 'shape' => 'NonEmptyString', ], 'ProjectKey' => [ 'shape' => 'NonEmptyString', ], 'Domain' => [ 'shape' => 'NonEmptyString', ], 'AuthUrl' => [ 'shape' => 'NonEmptyString', ], 'AuthStatus' => [ 'shape' => 'ConnectorAuthStatus', ], ], ], 'JiraCloudProviderConfiguration' => [ 'type' => 'structure', 'members' => [ 'ProjectKey' => [ 'shape' => 'NonEmptyString', ], ], ], 'JiraCloudUpdateConfiguration' => [ 'type' => 'structure', 'required' => [ 'ProjectKey', ], 'members' => [ 'ProjectKey' => [ 'shape' => 'NonEmptyString', ], ], ], 'KeywordFilter' => [ 'type' => 'structure', 'members' => [ 'Value' => [ 'shape' => 'NonEmptyString', ], ], ], 'KeywordFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'KeywordFilter', ], ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'NonEmptyString', ], 'Code' => [ 'shape' => 'NonEmptyString', ], ], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], 'ListAggregatorsV2Request' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], ], ], 'ListAggregatorsV2Response' => [ 'type' => 'structure', 'members' => [ 'AggregatorsV2' => [ 'shape' => 'AggregatorV2List', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAutomationRulesRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], ], ], 'ListAutomationRulesResponse' => [ 'type' => 'structure', 'members' => [ 'AutomationRulesMetadata' => [ 'shape' => 'AutomationRulesMetadataList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAutomationRulesV2Request' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], ], ], 'ListAutomationRulesV2Response' => [ 'type' => 'structure', 'members' => [ 'Rules' => [ 'shape' => 'AutomationRulesMetadataListV2', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListConfigurationPoliciesRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], ], ], 'ListConfigurationPoliciesResponse' => [ 'type' => 'structure', 'members' => [ 'ConfigurationPolicySummaries' => [ 'shape' => 'ConfigurationPolicySummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListConfigurationPolicyAssociationsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'Filters' => [ 'shape' => 'AssociationFilters', ], ], ], 'ListConfigurationPolicyAssociationsResponse' => [ 'type' => 'structure', 'members' => [ 'ConfigurationPolicyAssociationSummaries' => [ 'shape' => 'ConfigurationPolicyAssociationSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListConnectorsV2Request' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'ProviderName' => [ 'shape' => 'ConnectorProviderName', 'location' => 'querystring', 'locationName' => 'ProviderName', ], 'ConnectorStatus' => [ 'shape' => 'ConnectorStatus', 'location' => 'querystring', 'locationName' => 'ConnectorStatus', ], ], ], 'ListConnectorsV2Response' => [ 'type' => 'structure', 'required' => [ 'Connectors', ], 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'Connectors' => [ 'shape' => 'ConnectorSummaryList', ], ], ], 'ListEnabledProductsForImportRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], ], ], 'ListEnabledProductsForImportResponse' => [ 'type' => 'structure', 'members' => [ 'ProductSubscriptions' => [ 'shape' => 'ProductSubscriptionArnList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListFindingAggregatorsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], ], ], 'ListFindingAggregatorsResponse' => [ 'type' => 'structure', 'members' => [ 'FindingAggregators' => [ 'shape' => 'FindingAggregatorList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListInvitationsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'CrossAccountMaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], ], ], 'ListInvitationsResponse' => [ 'type' => 'structure', 'members' => [ 'Invitations' => [ 'shape' => 'InvitationList', ], 'NextToken' => [ 'shape' => 'NonEmptyString', ], ], ], 'ListMembersRequest' => [ 'type' => 'structure', 'members' => [ 'OnlyAssociated' => [ 'shape' => 'Boolean', 'location' => 'querystring', 'locationName' => 'OnlyAssociated', ], 'MaxResults' => [ 'shape' => 'CrossAccountMaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], ], ], 'ListMembersResponse' => [ 'type' => 'structure', 'members' => [ 'Members' => [ 'shape' => 'MemberList', ], 'NextToken' => [ 'shape' => 'NonEmptyString', ], ], ], 'ListOrganizationAdminAccountsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'AdminsMaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'Feature' => [ 'shape' => 'SecurityHubFeature', 'location' => 'querystring', 'locationName' => 'Feature', ], ], ], 'ListOrganizationAdminAccountsResponse' => [ 'type' => 'structure', 'members' => [ 'AdminAccounts' => [ 'shape' => 'AdminAccounts', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'Feature' => [ 'shape' => 'SecurityHubFeature', ], ], ], 'ListSecurityControlDefinitionsRequest' => [ 'type' => 'structure', 'members' => [ 'StandardsArn' => [ 'shape' => 'NonEmptyString', 'location' => 'querystring', 'locationName' => 'StandardsArn', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], ], ], 'ListSecurityControlDefinitionsResponse' => [ 'type' => 'structure', 'required' => [ 'SecurityControlDefinitions', ], 'members' => [ 'SecurityControlDefinitions' => [ 'shape' => 'SecurityControlDefinitions', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListStandardsControlAssociationsRequest' => [ 'type' => 'structure', 'required' => [ 'SecurityControlId', ], 'members' => [ 'SecurityControlId' => [ 'shape' => 'NonEmptyString', 'location' => 'querystring', 'locationName' => 'SecurityControlId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], ], ], 'ListStandardsControlAssociationsResponse' => [ 'type' => 'structure', 'required' => [ 'StandardsControlAssociationSummaries', ], 'members' => [ 'StandardsControlAssociationSummaries' => [ 'shape' => 'StandardsControlAssociationSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'ResourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'LoadBalancerState' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'NonEmptyString', ], 'Reason' => [ 'shape' => 'NonEmptyString', ], ], ], 'Long' => [ 'type' => 'long', ], 'Malware' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'Type' => [ 'shape' => 'MalwareType', ], 'Path' => [ 'shape' => 'NonEmptyString', ], 'State' => [ 'shape' => 'MalwareState', ], ], ], 'MalwareList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Malware', ], ], 'MalwareState' => [ 'type' => 'string', 'enum' => [ 'OBSERVED', 'REMOVAL_FAILED', 'REMOVED', ], ], 'MalwareType' => [ 'type' => 'string', 'enum' => [ 'ADWARE', 'BLENDED_THREAT', 'BOTNET_AGENT', 'COIN_MINER', 'EXPLOIT_KIT', 'KEYLOGGER', 'MACRO', 'POTENTIALLY_UNWANTED', 'SPYWARE', 'RANSOMWARE', 'REMOTE_ACCESS', 'ROOTKIT', 'TROJAN', 'VIRUS', 'WORM', ], ], 'MapFilter' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'NonEmptyString', ], 'Value' => [ 'shape' => 'NonEmptyString', ], 'Comparison' => [ 'shape' => 'MapFilterComparison', ], ], ], 'MapFilterComparison' => [ 'type' => 'string', 'enum' => [ 'EQUALS', 'NOT_EQUALS', 'CONTAINS', 'NOT_CONTAINS', ], ], 'MapFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MapFilter', ], ], 'MaxResults' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'MaxStatisticResults' => [ 'type' => 'integer', 'max' => 400, 'min' => 1, ], 'Member' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'Email' => [ 'shape' => 'NonEmptyString', ], 'MasterId' => [ 'shape' => 'NonEmptyString', 'deprecated' => true, 'deprecatedMessage' => 'This field is deprecated, use AdministratorId instead.', ], 'AdministratorId' => [ 'shape' => 'NonEmptyString', ], 'MemberStatus' => [ 'shape' => 'NonEmptyString', ], 'InvitedAt' => [ 'shape' => 'Timestamp', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'MemberList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Member', ], ], 'MetadataUidList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], 'max' => 100, 'min' => 0, ], 'Network' => [ 'type' => 'structure', 'members' => [ 'Direction' => [ 'shape' => 'NetworkDirection', ], 'Protocol' => [ 'shape' => 'NonEmptyString', ], 'OpenPortRange' => [ 'shape' => 'PortRange', ], 'SourceIpV4' => [ 'shape' => 'NonEmptyString', ], 'SourceIpV6' => [ 'shape' => 'NonEmptyString', ], 'SourcePort' => [ 'shape' => 'Integer', ], 'SourceDomain' => [ 'shape' => 'NonEmptyString', ], 'SourceMac' => [ 'shape' => 'NonEmptyString', ], 'DestinationIpV4' => [ 'shape' => 'NonEmptyString', ], 'DestinationIpV6' => [ 'shape' => 'NonEmptyString', ], 'DestinationPort' => [ 'shape' => 'Integer', ], 'DestinationDomain' => [ 'shape' => 'NonEmptyString', ], ], ], 'NetworkAutonomousSystem' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'Number' => [ 'shape' => 'Integer', ], ], ], 'NetworkConnection' => [ 'type' => 'structure', 'members' => [ 'Direction' => [ 'shape' => 'ConnectionDirection', ], ], ], 'NetworkConnectionAction' => [ 'type' => 'structure', 'members' => [ 'ConnectionDirection' => [ 'shape' => 'NonEmptyString', ], 'RemoteIpDetails' => [ 'shape' => 'ActionRemoteIpDetails', ], 'RemotePortDetails' => [ 'shape' => 'ActionRemotePortDetails', ], 'LocalPortDetails' => [ 'shape' => 'ActionLocalPortDetails', ], 'Protocol' => [ 'shape' => 'NonEmptyString', ], 'Blocked' => [ 'shape' => 'Boolean', ], ], ], 'NetworkDirection' => [ 'type' => 'string', 'enum' => [ 'IN', 'OUT', ], ], 'NetworkEndpoint' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'NonEmptyString', ], 'Ip' => [ 'shape' => 'NonEmptyString', ], 'Domain' => [ 'shape' => 'NonEmptyString', ], 'Port' => [ 'shape' => 'Integer', ], 'Location' => [ 'shape' => 'NetworkGeoLocation', ], 'AutonomousSystem' => [ 'shape' => 'NetworkAutonomousSystem', ], 'Connection' => [ 'shape' => 'NetworkConnection', ], ], ], 'NetworkEndpointsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NetworkEndpoint', ], 'max' => 10, 'min' => 0, ], 'NetworkGeoLocation' => [ 'type' => 'structure', 'members' => [ 'City' => [ 'shape' => 'NonEmptyString', ], 'Country' => [ 'shape' => 'NonEmptyString', ], 'Lat' => [ 'shape' => 'Double', ], 'Lon' => [ 'shape' => 'Double', ], ], ], 'NetworkHeader' => [ 'type' => 'structure', 'members' => [ 'Protocol' => [ 'shape' => 'NonEmptyString', ], 'Destination' => [ 'shape' => 'NetworkPathComponentDetails', ], 'Source' => [ 'shape' => 'NetworkPathComponentDetails', ], ], ], 'NetworkPathComponent' => [ 'type' => 'structure', 'members' => [ 'ComponentId' => [ 'shape' => 'NonEmptyString', ], 'ComponentType' => [ 'shape' => 'NonEmptyString', ], 'Egress' => [ 'shape' => 'NetworkHeader', ], 'Ingress' => [ 'shape' => 'NetworkHeader', ], ], ], 'NetworkPathComponentDetails' => [ 'type' => 'structure', 'members' => [ 'Address' => [ 'shape' => 'StringList', ], 'PortRanges' => [ 'shape' => 'PortRangeList', ], ], ], 'NetworkPathList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NetworkPathComponent', ], ], 'NextToken' => [ 'type' => 'string', ], 'NonEmptyString' => [ 'type' => 'string', 'pattern' => '.*\\S.*', ], 'NonEmptyStringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], ], 'Note' => [ 'type' => 'structure', 'required' => [ 'Text', 'UpdatedBy', 'UpdatedAt', ], 'members' => [ 'Text' => [ 'shape' => 'NonEmptyString', ], 'UpdatedBy' => [ 'shape' => 'NonEmptyString', ], 'UpdatedAt' => [ 'shape' => 'NonEmptyString', ], ], ], 'NoteUpdate' => [ 'type' => 'structure', 'required' => [ 'Text', 'UpdatedBy', ], 'members' => [ 'Text' => [ 'shape' => 'NonEmptyString', ], 'UpdatedBy' => [ 'shape' => 'NonEmptyString', ], ], ], 'NumberFilter' => [ 'type' => 'structure', 'members' => [ 'Gte' => [ 'shape' => 'Double', ], 'Lte' => [ 'shape' => 'Double', ], 'Eq' => [ 'shape' => 'Double', ], 'Gt' => [ 'shape' => 'Double', ], 'Lt' => [ 'shape' => 'Double', ], ], ], 'NumberFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NumberFilter', ], ], 'Occurrences' => [ 'type' => 'structure', 'members' => [ 'LineRanges' => [ 'shape' => 'Ranges', ], 'OffsetRanges' => [ 'shape' => 'Ranges', ], 'Pages' => [ 'shape' => 'Pages', ], 'Records' => [ 'shape' => 'Records', ], 'Cells' => [ 'shape' => 'Cells', ], ], ], 'OcsfBooleanField' => [ 'type' => 'string', 'enum' => [ 'compliance.assessments.meets_criteria', 'vulnerabilities.is_exploit_available', 'vulnerabilities.is_fix_available', ], ], 'OcsfBooleanFilter' => [ 'type' => 'structure', 'members' => [ 'FieldName' => [ 'shape' => 'OcsfBooleanField', ], 'Filter' => [ 'shape' => 'BooleanFilter', ], ], ], 'OcsfBooleanFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OcsfBooleanFilter', ], ], 'OcsfDateField' => [ 'type' => 'string', 'enum' => [ 'finding_info.created_time_dt', 'finding_info.first_seen_time_dt', 'finding_info.last_seen_time_dt', 'finding_info.modified_time_dt', ], ], 'OcsfDateFilter' => [ 'type' => 'structure', 'members' => [ 'FieldName' => [ 'shape' => 'OcsfDateField', ], 'Filter' => [ 'shape' => 'DateFilter', ], ], ], 'OcsfDateFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OcsfDateFilter', ], ], 'OcsfFinding' => [ 'type' => 'structure', 'members' => [], 'document' => true, ], 'OcsfFindingFilters' => [ 'type' => 'structure', 'members' => [ 'CompositeFilters' => [ 'shape' => 'CompositeFilterList', ], 'CompositeOperator' => [ 'shape' => 'AllowedOperators', ], ], ], 'OcsfFindingIdentifier' => [ 'type' => 'structure', 'required' => [ 'CloudAccountUid', 'FindingInfoUid', 'MetadataProductUid', ], 'members' => [ 'CloudAccountUid' => [ 'shape' => 'NonEmptyString', ], 'FindingInfoUid' => [ 'shape' => 'NonEmptyString', ], 'MetadataProductUid' => [ 'shape' => 'NonEmptyString', ], ], ], 'OcsfFindingIdentifierList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OcsfFindingIdentifier', ], 'max' => 100, 'min' => 0, ], 'OcsfFindingsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OcsfFinding', ], ], 'OcsfMapField' => [ 'type' => 'string', 'enum' => [ 'resources.tags', ], ], 'OcsfMapFilter' => [ 'type' => 'structure', 'members' => [ 'FieldName' => [ 'shape' => 'OcsfMapField', ], 'Filter' => [ 'shape' => 'MapFilter', ], ], ], 'OcsfMapFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OcsfMapFilter', ], ], 'OcsfNumberField' => [ 'type' => 'string', 'enum' => [ 'activity_id', 'compliance.status_id', 'confidence_score', 'severity_id', 'status_id', 'finding_info.related_events_count', ], ], 'OcsfNumberFilter' => [ 'type' => 'structure', 'members' => [ 'FieldName' => [ 'shape' => 'OcsfNumberField', ], 'Filter' => [ 'shape' => 'NumberFilter', ], ], ], 'OcsfNumberFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OcsfNumberFilter', ], ], 'OcsfStringField' => [ 'type' => 'string', 'enum' => [ 'metadata.uid', 'activity_name', 'cloud.account.uid', 'cloud.provider', 'cloud.region', 'compliance.assessments.category', 'compliance.assessments.name', 'compliance.control', 'compliance.status', 'compliance.standards', 'finding_info.desc', 'finding_info.src_url', 'finding_info.title', 'finding_info.types', 'finding_info.uid', 'finding_info.related_events.uid', 'finding_info.related_events.product.uid', 'finding_info.related_events.title', 'metadata.product.name', 'metadata.product.uid', 'metadata.product.vendor_name', 'remediation.desc', 'remediation.references', 'resources.cloud_partition', 'resources.region', 'resources.type', 'resources.uid', 'severity', 'status', 'comment', 'vulnerabilities.fix_coverage', 'class_name', ], ], 'OcsfStringFilter' => [ 'type' => 'structure', 'members' => [ 'FieldName' => [ 'shape' => 'OcsfStringField', ], 'Filter' => [ 'shape' => 'StringFilter', ], ], ], 'OcsfStringFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OcsfStringFilter', ], ], 'OrganizationConfiguration' => [ 'type' => 'structure', 'members' => [ 'ConfigurationType' => [ 'shape' => 'OrganizationConfigurationConfigurationType', ], 'Status' => [ 'shape' => 'OrganizationConfigurationStatus', ], 'StatusMessage' => [ 'shape' => 'NonEmptyString', ], ], ], 'OrganizationConfigurationConfigurationType' => [ 'type' => 'string', 'enum' => [ 'CENTRAL', 'LOCAL', ], ], 'OrganizationConfigurationStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'ENABLED', 'FAILED', ], ], 'Page' => [ 'type' => 'structure', 'members' => [ 'PageNumber' => [ 'shape' => 'Long', ], 'LineRange' => [ 'shape' => 'Range', ], 'OffsetRange' => [ 'shape' => 'Range', ], ], ], 'Pages' => [ 'type' => 'list', 'member' => [ 'shape' => 'Page', ], ], 'ParameterConfiguration' => [ 'type' => 'structure', 'required' => [ 'ValueType', ], 'members' => [ 'ValueType' => [ 'shape' => 'ParameterValueType', ], 'Value' => [ 'shape' => 'ParameterValue', ], ], ], 'ParameterDefinition' => [ 'type' => 'structure', 'required' => [ 'Description', 'ConfigurationOptions', ], 'members' => [ 'Description' => [ 'shape' => 'NonEmptyString', ], 'ConfigurationOptions' => [ 'shape' => 'ConfigurationOptions', ], ], ], 'ParameterDefinitions' => [ 'type' => 'map', 'key' => [ 'shape' => 'NonEmptyString', ], 'value' => [ 'shape' => 'ParameterDefinition', ], ], 'ParameterValue' => [ 'type' => 'structure', 'members' => [ 'Integer' => [ 'shape' => 'Integer', ], 'IntegerList' => [ 'shape' => 'IntegerList', ], 'Double' => [ 'shape' => 'Double', ], 'String' => [ 'shape' => 'NonEmptyString', ], 'StringList' => [ 'shape' => 'StringList', ], 'Boolean' => [ 'shape' => 'Boolean', ], 'Enum' => [ 'shape' => 'NonEmptyString', ], 'EnumList' => [ 'shape' => 'StringList', ], ], 'union' => true, ], 'ParameterValueType' => [ 'type' => 'string', 'enum' => [ 'DEFAULT', 'CUSTOM', ], ], 'Parameters' => [ 'type' => 'map', 'key' => [ 'shape' => 'NonEmptyString', ], 'value' => [ 'shape' => 'ParameterConfiguration', ], ], 'Partition' => [ 'type' => 'string', 'enum' => [ 'aws', 'aws-cn', 'aws-us-gov', ], ], 'PatchSummary' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'NonEmptyString', ], 'InstalledCount' => [ 'shape' => 'Integer', ], 'MissingCount' => [ 'shape' => 'Integer', ], 'FailedCount' => [ 'shape' => 'Integer', ], 'InstalledOtherCount' => [ 'shape' => 'Integer', ], 'InstalledRejectedCount' => [ 'shape' => 'Integer', ], 'InstalledPendingReboot' => [ 'shape' => 'Integer', ], 'OperationStartTime' => [ 'shape' => 'NonEmptyString', ], 'OperationEndTime' => [ 'shape' => 'NonEmptyString', ], 'RebootOption' => [ 'shape' => 'NonEmptyString', ], 'Operation' => [ 'shape' => 'NonEmptyString', ], ], ], 'Policy' => [ 'type' => 'structure', 'members' => [ 'SecurityHub' => [ 'shape' => 'SecurityHubPolicy', ], ], 'union' => true, ], 'PortProbeAction' => [ 'type' => 'structure', 'members' => [ 'PortProbeDetails' => [ 'shape' => 'PortProbeDetailList', ], 'Blocked' => [ 'shape' => 'Boolean', ], ], ], 'PortProbeDetail' => [ 'type' => 'structure', 'members' => [ 'LocalPortDetails' => [ 'shape' => 'ActionLocalPortDetails', ], 'LocalIpDetails' => [ 'shape' => 'ActionLocalIpDetails', ], 'RemoteIpDetails' => [ 'shape' => 'ActionRemoteIpDetails', ], ], ], 'PortProbeDetailList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PortProbeDetail', ], ], 'PortRange' => [ 'type' => 'structure', 'members' => [ 'Begin' => [ 'shape' => 'Integer', ], 'End' => [ 'shape' => 'Integer', ], ], ], 'PortRangeFromTo' => [ 'type' => 'structure', 'members' => [ 'From' => [ 'shape' => 'Integer', ], 'To' => [ 'shape' => 'Integer', ], ], ], 'PortRangeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PortRange', ], ], 'ProcessDetails' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'Path' => [ 'shape' => 'NonEmptyString', ], 'Pid' => [ 'shape' => 'Integer', ], 'ParentPid' => [ 'shape' => 'Integer', ], 'LaunchedAt' => [ 'shape' => 'NonEmptyString', ], 'TerminatedAt' => [ 'shape' => 'NonEmptyString', ], ], ], 'Product' => [ 'type' => 'structure', 'required' => [ 'ProductArn', ], 'members' => [ 'ProductArn' => [ 'shape' => 'NonEmptyString', ], 'ProductName' => [ 'shape' => 'NonEmptyString', ], 'CompanyName' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'Categories' => [ 'shape' => 'CategoryList', ], 'IntegrationTypes' => [ 'shape' => 'IntegrationTypeList', ], 'MarketplaceUrl' => [ 'shape' => 'NonEmptyString', ], 'ActivationUrl' => [ 'shape' => 'NonEmptyString', ], 'ProductSubscriptionResourcePolicy' => [ 'shape' => 'NonEmptyString', ], ], ], 'ProductSubscriptionArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], ], 'ProductV2' => [ 'type' => 'structure', 'members' => [ 'ProductV2Name' => [ 'shape' => 'NonEmptyString', ], 'CompanyName' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'Categories' => [ 'shape' => 'CategoryList', ], 'IntegrationV2Types' => [ 'shape' => 'IntegrationV2TypeList', ], 'MarketplaceUrl' => [ 'shape' => 'NonEmptyString', ], 'ActivationUrl' => [ 'shape' => 'NonEmptyString', ], ], ], 'ProductsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Product', ], ], 'ProductsV2List' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProductV2', ], ], 'PropagatingVgwSetDetails' => [ 'type' => 'structure', 'members' => [ 'GatewayId' => [ 'shape' => 'NonEmptyString', ], ], ], 'PropagatingVgwSetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PropagatingVgwSetDetails', ], ], 'ProviderConfiguration' => [ 'type' => 'structure', 'members' => [ 'JiraCloud' => [ 'shape' => 'JiraCloudProviderConfiguration', ], 'ServiceNow' => [ 'shape' => 'ServiceNowProviderConfiguration', ], ], 'union' => true, ], 'ProviderDetail' => [ 'type' => 'structure', 'members' => [ 'JiraCloud' => [ 'shape' => 'JiraCloudDetail', ], 'ServiceNow' => [ 'shape' => 'ServiceNowDetail', ], ], 'union' => true, ], 'ProviderSummary' => [ 'type' => 'structure', 'members' => [ 'ProviderName' => [ 'shape' => 'ConnectorProviderName', ], 'ConnectorStatus' => [ 'shape' => 'ConnectorStatus', ], ], ], 'ProviderUpdateConfiguration' => [ 'type' => 'structure', 'members' => [ 'JiraCloud' => [ 'shape' => 'JiraCloudUpdateConfiguration', ], ], 'union' => true, ], 'Range' => [ 'type' => 'structure', 'members' => [ 'Start' => [ 'shape' => 'Long', ], 'End' => [ 'shape' => 'Long', ], 'StartColumn' => [ 'shape' => 'Long', ], ], ], 'Ranges' => [ 'type' => 'list', 'member' => [ 'shape' => 'Range', ], ], 'RatioScale' => [ 'type' => 'integer', 'max' => 100, 'min' => 0, ], 'Recommendation' => [ 'type' => 'structure', 'members' => [ 'Text' => [ 'shape' => 'NonEmptyString', ], 'Url' => [ 'shape' => 'NonEmptyString', ], ], ], 'Record' => [ 'type' => 'structure', 'members' => [ 'JsonPath' => [ 'shape' => 'NonEmptyString', ], 'RecordIndex' => [ 'shape' => 'Long', ], ], ], 'RecordState' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'ARCHIVED', ], ], 'Records' => [ 'type' => 'list', 'member' => [ 'shape' => 'Record', ], ], 'RegionAvailabilityStatus' => [ 'type' => 'string', 'enum' => [ 'AVAILABLE', 'UNAVAILABLE', ], ], 'RelatedFinding' => [ 'type' => 'structure', 'required' => [ 'ProductArn', 'Id', ], 'members' => [ 'ProductArn' => [ 'shape' => 'NonEmptyString', ], 'Id' => [ 'shape' => 'NonEmptyString', ], ], ], 'RelatedFindingList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RelatedFinding', ], ], 'RelatedRequirementsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], ], 'Remediation' => [ 'type' => 'structure', 'members' => [ 'Recommendation' => [ 'shape' => 'Recommendation', ], ], ], 'Resource' => [ 'type' => 'structure', 'required' => [ 'Type', 'Id', ], 'members' => [ 'Type' => [ 'shape' => 'NonEmptyString', ], 'Id' => [ 'shape' => 'NonEmptyString', ], 'Partition' => [ 'shape' => 'Partition', ], 'Region' => [ 'shape' => 'NonEmptyString', ], 'ResourceRole' => [ 'shape' => 'NonEmptyString', ], 'Tags' => [ 'shape' => 'FieldMap', ], 'DataClassification' => [ 'shape' => 'DataClassificationDetails', ], 'Details' => [ 'shape' => 'ResourceDetails', ], 'ApplicationName' => [ 'shape' => 'NonEmptyString', ], 'ApplicationArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'ResourceArn' => [ 'type' => 'string', 'pattern' => '^arn:aws:securityhub:.*', ], 'ResourceCategory' => [ 'type' => 'string', 'enum' => [ 'Compute', 'Database', 'Storage', 'Code', 'AI/ML', 'Identity', 'Network', 'Other', ], ], 'ResourceConfig' => [ 'type' => 'structure', 'members' => [], 'document' => true, ], 'ResourceConflictException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'NonEmptyString', ], 'Code' => [ 'shape' => 'NonEmptyString', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ResourceDetails' => [ 'type' => 'structure', 'members' => [ 'AwsAutoScalingAutoScalingGroup' => [ 'shape' => 'AwsAutoScalingAutoScalingGroupDetails', ], 'AwsCodeBuildProject' => [ 'shape' => 'AwsCodeBuildProjectDetails', ], 'AwsCloudFrontDistribution' => [ 'shape' => 'AwsCloudFrontDistributionDetails', ], 'AwsEc2Instance' => [ 'shape' => 'AwsEc2InstanceDetails', ], 'AwsEc2NetworkInterface' => [ 'shape' => 'AwsEc2NetworkInterfaceDetails', ], 'AwsEc2SecurityGroup' => [ 'shape' => 'AwsEc2SecurityGroupDetails', ], 'AwsEc2Volume' => [ 'shape' => 'AwsEc2VolumeDetails', ], 'AwsEc2Vpc' => [ 'shape' => 'AwsEc2VpcDetails', ], 'AwsEc2Eip' => [ 'shape' => 'AwsEc2EipDetails', ], 'AwsEc2Subnet' => [ 'shape' => 'AwsEc2SubnetDetails', ], 'AwsEc2NetworkAcl' => [ 'shape' => 'AwsEc2NetworkAclDetails', ], 'AwsElbv2LoadBalancer' => [ 'shape' => 'AwsElbv2LoadBalancerDetails', ], 'AwsElasticBeanstalkEnvironment' => [ 'shape' => 'AwsElasticBeanstalkEnvironmentDetails', ], 'AwsElasticsearchDomain' => [ 'shape' => 'AwsElasticsearchDomainDetails', ], 'AwsS3Bucket' => [ 'shape' => 'AwsS3BucketDetails', ], 'AwsS3AccountPublicAccessBlock' => [ 'shape' => 'AwsS3AccountPublicAccessBlockDetails', ], 'AwsS3Object' => [ 'shape' => 'AwsS3ObjectDetails', ], 'AwsSecretsManagerSecret' => [ 'shape' => 'AwsSecretsManagerSecretDetails', ], 'AwsIamAccessKey' => [ 'shape' => 'AwsIamAccessKeyDetails', ], 'AwsIamUser' => [ 'shape' => 'AwsIamUserDetails', ], 'AwsIamPolicy' => [ 'shape' => 'AwsIamPolicyDetails', ], 'AwsApiGatewayV2Stage' => [ 'shape' => 'AwsApiGatewayV2StageDetails', ], 'AwsApiGatewayV2Api' => [ 'shape' => 'AwsApiGatewayV2ApiDetails', ], 'AwsDynamoDbTable' => [ 'shape' => 'AwsDynamoDbTableDetails', ], 'AwsApiGatewayStage' => [ 'shape' => 'AwsApiGatewayStageDetails', ], 'AwsApiGatewayRestApi' => [ 'shape' => 'AwsApiGatewayRestApiDetails', ], 'AwsCloudTrailTrail' => [ 'shape' => 'AwsCloudTrailTrailDetails', ], 'AwsSsmPatchCompliance' => [ 'shape' => 'AwsSsmPatchComplianceDetails', ], 'AwsCertificateManagerCertificate' => [ 'shape' => 'AwsCertificateManagerCertificateDetails', ], 'AwsRedshiftCluster' => [ 'shape' => 'AwsRedshiftClusterDetails', ], 'AwsElbLoadBalancer' => [ 'shape' => 'AwsElbLoadBalancerDetails', ], 'AwsIamGroup' => [ 'shape' => 'AwsIamGroupDetails', ], 'AwsIamRole' => [ 'shape' => 'AwsIamRoleDetails', ], 'AwsKmsKey' => [ 'shape' => 'AwsKmsKeyDetails', ], 'AwsLambdaFunction' => [ 'shape' => 'AwsLambdaFunctionDetails', ], 'AwsLambdaLayerVersion' => [ 'shape' => 'AwsLambdaLayerVersionDetails', ], 'AwsRdsDbInstance' => [ 'shape' => 'AwsRdsDbInstanceDetails', ], 'AwsSnsTopic' => [ 'shape' => 'AwsSnsTopicDetails', ], 'AwsSqsQueue' => [ 'shape' => 'AwsSqsQueueDetails', ], 'AwsWafWebAcl' => [ 'shape' => 'AwsWafWebAclDetails', ], 'AwsRdsDbSnapshot' => [ 'shape' => 'AwsRdsDbSnapshotDetails', ], 'AwsRdsDbClusterSnapshot' => [ 'shape' => 'AwsRdsDbClusterSnapshotDetails', ], 'AwsRdsDbCluster' => [ 'shape' => 'AwsRdsDbClusterDetails', ], 'AwsEcsCluster' => [ 'shape' => 'AwsEcsClusterDetails', ], 'AwsEcsContainer' => [ 'shape' => 'AwsEcsContainerDetails', ], 'AwsEcsTaskDefinition' => [ 'shape' => 'AwsEcsTaskDefinitionDetails', ], 'Container' => [ 'shape' => 'ContainerDetails', ], 'Other' => [ 'shape' => 'FieldMap', ], 'AwsRdsEventSubscription' => [ 'shape' => 'AwsRdsEventSubscriptionDetails', ], 'AwsEcsService' => [ 'shape' => 'AwsEcsServiceDetails', ], 'AwsAutoScalingLaunchConfiguration' => [ 'shape' => 'AwsAutoScalingLaunchConfigurationDetails', ], 'AwsEc2VpnConnection' => [ 'shape' => 'AwsEc2VpnConnectionDetails', ], 'AwsEcrContainerImage' => [ 'shape' => 'AwsEcrContainerImageDetails', ], 'AwsOpenSearchServiceDomain' => [ 'shape' => 'AwsOpenSearchServiceDomainDetails', ], 'AwsEc2VpcEndpointService' => [ 'shape' => 'AwsEc2VpcEndpointServiceDetails', ], 'AwsXrayEncryptionConfig' => [ 'shape' => 'AwsXrayEncryptionConfigDetails', ], 'AwsWafRateBasedRule' => [ 'shape' => 'AwsWafRateBasedRuleDetails', ], 'AwsWafRegionalRateBasedRule' => [ 'shape' => 'AwsWafRegionalRateBasedRuleDetails', ], 'AwsEcrRepository' => [ 'shape' => 'AwsEcrRepositoryDetails', ], 'AwsEksCluster' => [ 'shape' => 'AwsEksClusterDetails', ], 'AwsNetworkFirewallFirewallPolicy' => [ 'shape' => 'AwsNetworkFirewallFirewallPolicyDetails', ], 'AwsNetworkFirewallFirewall' => [ 'shape' => 'AwsNetworkFirewallFirewallDetails', ], 'AwsNetworkFirewallRuleGroup' => [ 'shape' => 'AwsNetworkFirewallRuleGroupDetails', ], 'AwsRdsDbSecurityGroup' => [ 'shape' => 'AwsRdsDbSecurityGroupDetails', ], 'AwsKinesisStream' => [ 'shape' => 'AwsKinesisStreamDetails', ], 'AwsEc2TransitGateway' => [ 'shape' => 'AwsEc2TransitGatewayDetails', ], 'AwsEfsAccessPoint' => [ 'shape' => 'AwsEfsAccessPointDetails', ], 'AwsCloudFormationStack' => [ 'shape' => 'AwsCloudFormationStackDetails', ], 'AwsCloudWatchAlarm' => [ 'shape' => 'AwsCloudWatchAlarmDetails', ], 'AwsEc2VpcPeeringConnection' => [ 'shape' => 'AwsEc2VpcPeeringConnectionDetails', ], 'AwsWafRegionalRuleGroup' => [ 'shape' => 'AwsWafRegionalRuleGroupDetails', ], 'AwsWafRegionalRule' => [ 'shape' => 'AwsWafRegionalRuleDetails', ], 'AwsWafRegionalWebAcl' => [ 'shape' => 'AwsWafRegionalWebAclDetails', ], 'AwsWafRule' => [ 'shape' => 'AwsWafRuleDetails', ], 'AwsWafRuleGroup' => [ 'shape' => 'AwsWafRuleGroupDetails', ], 'AwsEcsTask' => [ 'shape' => 'AwsEcsTaskDetails', ], 'AwsBackupBackupVault' => [ 'shape' => 'AwsBackupBackupVaultDetails', ], 'AwsBackupBackupPlan' => [ 'shape' => 'AwsBackupBackupPlanDetails', ], 'AwsBackupRecoveryPoint' => [ 'shape' => 'AwsBackupRecoveryPointDetails', ], 'AwsEc2LaunchTemplate' => [ 'shape' => 'AwsEc2LaunchTemplateDetails', ], 'AwsSageMakerNotebookInstance' => [ 'shape' => 'AwsSageMakerNotebookInstanceDetails', ], 'AwsWafv2WebAcl' => [ 'shape' => 'AwsWafv2WebAclDetails', ], 'AwsWafv2RuleGroup' => [ 'shape' => 'AwsWafv2RuleGroupDetails', ], 'AwsEc2RouteTable' => [ 'shape' => 'AwsEc2RouteTableDetails', ], 'AwsAmazonMqBroker' => [ 'shape' => 'AwsAmazonMqBrokerDetails', ], 'AwsAppSyncGraphQlApi' => [ 'shape' => 'AwsAppSyncGraphQlApiDetails', ], 'AwsEventSchemasRegistry' => [ 'shape' => 'AwsEventSchemasRegistryDetails', ], 'AwsGuardDutyDetector' => [ 'shape' => 'AwsGuardDutyDetectorDetails', ], 'AwsStepFunctionStateMachine' => [ 'shape' => 'AwsStepFunctionStateMachineDetails', ], 'AwsAthenaWorkGroup' => [ 'shape' => 'AwsAthenaWorkGroupDetails', ], 'AwsEventsEventbus' => [ 'shape' => 'AwsEventsEventbusDetails', ], 'AwsDmsEndpoint' => [ 'shape' => 'AwsDmsEndpointDetails', ], 'AwsEventsEndpoint' => [ 'shape' => 'AwsEventsEndpointDetails', ], 'AwsDmsReplicationTask' => [ 'shape' => 'AwsDmsReplicationTaskDetails', ], 'AwsDmsReplicationInstance' => [ 'shape' => 'AwsDmsReplicationInstanceDetails', ], 'AwsRoute53HostedZone' => [ 'shape' => 'AwsRoute53HostedZoneDetails', ], 'AwsMskCluster' => [ 'shape' => 'AwsMskClusterDetails', ], 'AwsS3AccessPoint' => [ 'shape' => 'AwsS3AccessPointDetails', ], 'AwsEc2ClientVpnEndpoint' => [ 'shape' => 'AwsEc2ClientVpnEndpointDetails', ], ], ], 'ResourceFindingsSummary' => [ 'type' => 'structure', 'required' => [ 'FindingType', 'ProductName', 'TotalFindings', ], 'members' => [ 'FindingType' => [ 'shape' => 'NonEmptyString', ], 'ProductName' => [ 'shape' => 'NonEmptyString', ], 'TotalFindings' => [ 'shape' => 'Integer', ], 'Severities' => [ 'shape' => 'ResourceSeverityBreakdown', ], ], ], 'ResourceFindingsSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceFindingsSummary', ], ], 'ResourceGroupByField' => [ 'type' => 'string', 'enum' => [ 'account_id', 'region', 'resource_category', 'resource_type', 'resource_name', 'findings_summary.finding_type', ], ], 'ResourceGroupByRule' => [ 'type' => 'structure', 'required' => [ 'GroupByField', ], 'members' => [ 'GroupByField' => [ 'shape' => 'ResourceGroupByField', ], 'Filters' => [ 'shape' => 'ResourcesFilters', ], ], ], 'ResourceGroupByRules' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceGroupByRule', ], ], 'ResourceInUseException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'NonEmptyString', ], 'Code' => [ 'shape' => 'NonEmptyString', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'ResourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Resource', ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'NonEmptyString', ], 'Code' => [ 'shape' => 'NonEmptyString', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'ResourceResult' => [ 'type' => 'structure', 'required' => [ 'ResourceId', 'AccountId', 'Region', 'ResourceDetailCaptureTimeDt', 'ResourceConfig', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'NonEmptyString', ], 'ResourceId' => [ 'shape' => 'NonEmptyString', ], 'AccountId' => [ 'shape' => 'NonEmptyString', ], 'Region' => [ 'shape' => 'NonEmptyString', ], 'ResourceCategory' => [ 'shape' => 'ResourceCategory', ], 'ResourceType' => [ 'shape' => 'NonEmptyString', ], 'ResourceName' => [ 'shape' => 'NonEmptyString', ], 'ResourceCreationTimeDt' => [ 'shape' => 'NonEmptyString', ], 'ResourceDetailCaptureTimeDt' => [ 'shape' => 'NonEmptyString', ], 'FindingsSummary' => [ 'shape' => 'ResourceFindingsSummaryList', ], 'ResourceTags' => [ 'shape' => 'ResourceTagList', ], 'ResourceConfig' => [ 'shape' => 'ResourceConfig', ], ], ], 'ResourceSeverityBreakdown' => [ 'type' => 'structure', 'members' => [ 'Other' => [ 'shape' => 'Integer', ], 'Fatal' => [ 'shape' => 'Integer', ], 'Critical' => [ 'shape' => 'Integer', ], 'High' => [ 'shape' => 'Integer', ], 'Medium' => [ 'shape' => 'Integer', ], 'Low' => [ 'shape' => 'Integer', ], 'Informational' => [ 'shape' => 'Integer', ], 'Unknown' => [ 'shape' => 'Integer', ], ], ], 'ResourceTag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'NonEmptyString', ], 'Value' => [ 'shape' => 'NonEmptyString', ], ], ], 'ResourceTagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceTag', ], ], 'Resources' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceResult', ], ], 'ResourcesCompositeFilter' => [ 'type' => 'structure', 'members' => [ 'StringFilters' => [ 'shape' => 'ResourcesStringFilterList', ], 'DateFilters' => [ 'shape' => 'ResourcesDateFilterList', ], 'NumberFilters' => [ 'shape' => 'ResourcesNumberFilterList', ], 'MapFilters' => [ 'shape' => 'ResourcesMapFilterList', ], 'Operator' => [ 'shape' => 'AllowedOperators', ], ], ], 'ResourcesCompositeFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourcesCompositeFilter', ], ], 'ResourcesDateField' => [ 'type' => 'string', 'enum' => [ 'resource_detail_capture_time_dt', 'resource_creation_time_dt', ], ], 'ResourcesDateFilter' => [ 'type' => 'structure', 'members' => [ 'FieldName' => [ 'shape' => 'ResourcesDateField', ], 'Filter' => [ 'shape' => 'DateFilter', ], ], ], 'ResourcesDateFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourcesDateFilter', ], ], 'ResourcesFilters' => [ 'type' => 'structure', 'members' => [ 'CompositeFilters' => [ 'shape' => 'ResourcesCompositeFilterList', ], 'CompositeOperator' => [ 'shape' => 'AllowedOperators', ], ], ], 'ResourcesMapField' => [ 'type' => 'string', 'enum' => [ 'tags', ], ], 'ResourcesMapFilter' => [ 'type' => 'structure', 'members' => [ 'FieldName' => [ 'shape' => 'ResourcesMapField', ], 'Filter' => [ 'shape' => 'MapFilter', ], ], ], 'ResourcesMapFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourcesMapFilter', ], ], 'ResourcesNumberField' => [ 'type' => 'string', 'enum' => [ 'findings_summary.total_findings', 'findings_summary.severities.other', 'findings_summary.severities.fatal', 'findings_summary.severities.critical', 'findings_summary.severities.high', 'findings_summary.severities.medium', 'findings_summary.severities.low', 'findings_summary.severities.informational', 'findings_summary.severities.unknown', ], ], 'ResourcesNumberFilter' => [ 'type' => 'structure', 'members' => [ 'FieldName' => [ 'shape' => 'ResourcesNumberField', ], 'Filter' => [ 'shape' => 'NumberFilter', ], ], ], 'ResourcesNumberFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourcesNumberFilter', ], ], 'ResourcesStringField' => [ 'type' => 'string', 'enum' => [ 'resource_arn', 'resource_id', 'account_id', 'region', 'resource_category', 'resource_type', 'resource_name', 'findings_summary.finding_type', 'findings_summary.product_name', ], ], 'ResourcesStringFilter' => [ 'type' => 'structure', 'members' => [ 'FieldName' => [ 'shape' => 'ResourcesStringField', ], 'Filter' => [ 'shape' => 'StringFilter', ], ], ], 'ResourcesStringFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourcesStringFilter', ], ], 'Result' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'ProcessingResult' => [ 'shape' => 'NonEmptyString', ], ], ], 'ResultList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Result', ], ], 'RouteSetDetails' => [ 'type' => 'structure', 'members' => [ 'CarrierGatewayId' => [ 'shape' => 'NonEmptyString', ], 'CoreNetworkArn' => [ 'shape' => 'NonEmptyString', ], 'DestinationCidrBlock' => [ 'shape' => 'NonEmptyString', ], 'DestinationIpv6CidrBlock' => [ 'shape' => 'NonEmptyString', ], 'DestinationPrefixListId' => [ 'shape' => 'NonEmptyString', ], 'EgressOnlyInternetGatewayId' => [ 'shape' => 'NonEmptyString', ], 'GatewayId' => [ 'shape' => 'NonEmptyString', ], 'InstanceId' => [ 'shape' => 'NonEmptyString', ], 'InstanceOwnerId' => [ 'shape' => 'NonEmptyString', ], 'LocalGatewayId' => [ 'shape' => 'NonEmptyString', ], 'NatGatewayId' => [ 'shape' => 'NonEmptyString', ], 'NetworkInterfaceId' => [ 'shape' => 'NonEmptyString', ], 'Origin' => [ 'shape' => 'NonEmptyString', ], 'State' => [ 'shape' => 'NonEmptyString', ], 'TransitGatewayId' => [ 'shape' => 'NonEmptyString', ], 'VpcPeeringConnectionId' => [ 'shape' => 'NonEmptyString', ], ], ], 'RouteSetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RouteSetDetails', ], ], 'RuleGroupDetails' => [ 'type' => 'structure', 'members' => [ 'RuleVariables' => [ 'shape' => 'RuleGroupVariables', ], 'RulesSource' => [ 'shape' => 'RuleGroupSource', ], ], ], 'RuleGroupSource' => [ 'type' => 'structure', 'members' => [ 'RulesSourceList' => [ 'shape' => 'RuleGroupSourceListDetails', ], 'RulesString' => [ 'shape' => 'NonEmptyString', ], 'StatefulRules' => [ 'shape' => 'RuleGroupSourceStatefulRulesList', ], 'StatelessRulesAndCustomActions' => [ 'shape' => 'RuleGroupSourceStatelessRulesAndCustomActionsDetails', ], ], ], 'RuleGroupSourceCustomActionsDetails' => [ 'type' => 'structure', 'members' => [ 'ActionDefinition' => [ 'shape' => 'StatelessCustomActionDefinition', ], 'ActionName' => [ 'shape' => 'NonEmptyString', ], ], ], 'RuleGroupSourceCustomActionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RuleGroupSourceCustomActionsDetails', ], ], 'RuleGroupSourceListDetails' => [ 'type' => 'structure', 'members' => [ 'GeneratedRulesType' => [ 'shape' => 'NonEmptyString', ], 'TargetTypes' => [ 'shape' => 'NonEmptyStringList', ], 'Targets' => [ 'shape' => 'NonEmptyStringList', ], ], ], 'RuleGroupSourceStatefulRulesDetails' => [ 'type' => 'structure', 'members' => [ 'Action' => [ 'shape' => 'NonEmptyString', ], 'Header' => [ 'shape' => 'RuleGroupSourceStatefulRulesHeaderDetails', ], 'RuleOptions' => [ 'shape' => 'RuleGroupSourceStatefulRulesOptionsList', ], ], ], 'RuleGroupSourceStatefulRulesHeaderDetails' => [ 'type' => 'structure', 'members' => [ 'Destination' => [ 'shape' => 'NonEmptyString', ], 'DestinationPort' => [ 'shape' => 'NonEmptyString', ], 'Direction' => [ 'shape' => 'NonEmptyString', ], 'Protocol' => [ 'shape' => 'NonEmptyString', ], 'Source' => [ 'shape' => 'NonEmptyString', ], 'SourcePort' => [ 'shape' => 'NonEmptyString', ], ], ], 'RuleGroupSourceStatefulRulesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RuleGroupSourceStatefulRulesDetails', ], ], 'RuleGroupSourceStatefulRulesOptionsDetails' => [ 'type' => 'structure', 'members' => [ 'Keyword' => [ 'shape' => 'NonEmptyString', ], 'Settings' => [ 'shape' => 'RuleGroupSourceStatefulRulesRuleOptionsSettingsList', ], ], ], 'RuleGroupSourceStatefulRulesOptionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RuleGroupSourceStatefulRulesOptionsDetails', ], ], 'RuleGroupSourceStatefulRulesRuleOptionsSettingsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], ], 'RuleGroupSourceStatelessRuleDefinition' => [ 'type' => 'structure', 'members' => [ 'Actions' => [ 'shape' => 'NonEmptyStringList', ], 'MatchAttributes' => [ 'shape' => 'RuleGroupSourceStatelessRuleMatchAttributes', ], ], ], 'RuleGroupSourceStatelessRuleMatchAttributes' => [ 'type' => 'structure', 'members' => [ 'DestinationPorts' => [ 'shape' => 'RuleGroupSourceStatelessRuleMatchAttributesDestinationPortsList', ], 'Destinations' => [ 'shape' => 'RuleGroupSourceStatelessRuleMatchAttributesDestinationsList', ], 'Protocols' => [ 'shape' => 'RuleGroupSourceStatelessRuleMatchAttributesProtocolsList', ], 'SourcePorts' => [ 'shape' => 'RuleGroupSourceStatelessRuleMatchAttributesSourcePortsList', ], 'Sources' => [ 'shape' => 'RuleGroupSourceStatelessRuleMatchAttributesSourcesList', ], 'TcpFlags' => [ 'shape' => 'RuleGroupSourceStatelessRuleMatchAttributesTcpFlagsList', ], ], ], 'RuleGroupSourceStatelessRuleMatchAttributesDestinationPorts' => [ 'type' => 'structure', 'members' => [ 'FromPort' => [ 'shape' => 'Integer', ], 'ToPort' => [ 'shape' => 'Integer', ], ], ], 'RuleGroupSourceStatelessRuleMatchAttributesDestinationPortsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RuleGroupSourceStatelessRuleMatchAttributesDestinationPorts', ], ], 'RuleGroupSourceStatelessRuleMatchAttributesDestinations' => [ 'type' => 'structure', 'members' => [ 'AddressDefinition' => [ 'shape' => 'NonEmptyString', ], ], ], 'RuleGroupSourceStatelessRuleMatchAttributesDestinationsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RuleGroupSourceStatelessRuleMatchAttributesDestinations', ], ], 'RuleGroupSourceStatelessRuleMatchAttributesProtocolsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Integer', ], ], 'RuleGroupSourceStatelessRuleMatchAttributesSourcePorts' => [ 'type' => 'structure', 'members' => [ 'FromPort' => [ 'shape' => 'Integer', ], 'ToPort' => [ 'shape' => 'Integer', ], ], ], 'RuleGroupSourceStatelessRuleMatchAttributesSourcePortsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RuleGroupSourceStatelessRuleMatchAttributesSourcePorts', ], ], 'RuleGroupSourceStatelessRuleMatchAttributesSources' => [ 'type' => 'structure', 'members' => [ 'AddressDefinition' => [ 'shape' => 'NonEmptyString', ], ], ], 'RuleGroupSourceStatelessRuleMatchAttributesSourcesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RuleGroupSourceStatelessRuleMatchAttributesSources', ], ], 'RuleGroupSourceStatelessRuleMatchAttributesTcpFlags' => [ 'type' => 'structure', 'members' => [ 'Flags' => [ 'shape' => 'NonEmptyStringList', ], 'Masks' => [ 'shape' => 'NonEmptyStringList', ], ], ], 'RuleGroupSourceStatelessRuleMatchAttributesTcpFlagsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RuleGroupSourceStatelessRuleMatchAttributesTcpFlags', ], ], 'RuleGroupSourceStatelessRulesAndCustomActionsDetails' => [ 'type' => 'structure', 'members' => [ 'CustomActions' => [ 'shape' => 'RuleGroupSourceCustomActionsList', ], 'StatelessRules' => [ 'shape' => 'RuleGroupSourceStatelessRulesList', ], ], ], 'RuleGroupSourceStatelessRulesDetails' => [ 'type' => 'structure', 'members' => [ 'Priority' => [ 'shape' => 'Integer', ], 'RuleDefinition' => [ 'shape' => 'RuleGroupSourceStatelessRuleDefinition', ], ], ], 'RuleGroupSourceStatelessRulesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RuleGroupSourceStatelessRulesDetails', ], ], 'RuleGroupVariables' => [ 'type' => 'structure', 'members' => [ 'IpSets' => [ 'shape' => 'RuleGroupVariablesIpSetsDetails', ], 'PortSets' => [ 'shape' => 'RuleGroupVariablesPortSetsDetails', ], ], ], 'RuleGroupVariablesIpSetsDetails' => [ 'type' => 'structure', 'members' => [ 'Definition' => [ 'shape' => 'NonEmptyStringList', ], ], ], 'RuleGroupVariablesPortSetsDetails' => [ 'type' => 'structure', 'members' => [ 'Definition' => [ 'shape' => 'NonEmptyStringList', ], ], ], 'RuleOrderValue' => [ 'type' => 'integer', 'max' => 1000, 'min' => 1, ], 'RuleOrderValueV2' => [ 'type' => 'float', 'max' => 1000.0, 'min' => 1.0, ], 'RuleStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'RuleStatusV2' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'SecurityControl' => [ 'type' => 'structure', 'required' => [ 'SecurityControlId', 'SecurityControlArn', 'Title', 'Description', 'RemediationUrl', 'SeverityRating', 'SecurityControlStatus', ], 'members' => [ 'SecurityControlId' => [ 'shape' => 'NonEmptyString', ], 'SecurityControlArn' => [ 'shape' => 'NonEmptyString', ], 'Title' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'RemediationUrl' => [ 'shape' => 'NonEmptyString', ], 'SeverityRating' => [ 'shape' => 'SeverityRating', ], 'SecurityControlStatus' => [ 'shape' => 'ControlStatus', ], 'UpdateStatus' => [ 'shape' => 'UpdateStatus', ], 'Parameters' => [ 'shape' => 'Parameters', ], 'LastUpdateReason' => [ 'shape' => 'AlphaNumericNonEmptyString', ], ], ], 'SecurityControlCustomParameter' => [ 'type' => 'structure', 'members' => [ 'SecurityControlId' => [ 'shape' => 'NonEmptyString', ], 'Parameters' => [ 'shape' => 'Parameters', ], ], ], 'SecurityControlCustomParametersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityControlCustomParameter', ], ], 'SecurityControlDefinition' => [ 'type' => 'structure', 'required' => [ 'SecurityControlId', 'Title', 'Description', 'RemediationUrl', 'SeverityRating', 'CurrentRegionAvailability', ], 'members' => [ 'SecurityControlId' => [ 'shape' => 'NonEmptyString', ], 'Title' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'RemediationUrl' => [ 'shape' => 'NonEmptyString', ], 'SeverityRating' => [ 'shape' => 'SeverityRating', ], 'CurrentRegionAvailability' => [ 'shape' => 'RegionAvailabilityStatus', ], 'CustomizableProperties' => [ 'shape' => 'CustomizableProperties', ], 'ParameterDefinitions' => [ 'shape' => 'ParameterDefinitions', ], ], ], 'SecurityControlDefinitions' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityControlDefinition', ], ], 'SecurityControlParameter' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'Value' => [ 'shape' => 'TypeList', ], ], ], 'SecurityControlParametersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityControlParameter', ], ], 'SecurityControlProperty' => [ 'type' => 'string', 'enum' => [ 'Parameters', ], ], 'SecurityControls' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityControl', ], ], 'SecurityControlsConfiguration' => [ 'type' => 'structure', 'members' => [ 'EnabledSecurityControlIdentifiers' => [ 'shape' => 'EnabledSecurityControlIdentifierList', ], 'DisabledSecurityControlIdentifiers' => [ 'shape' => 'DisabledSecurityControlIdentifierList', ], 'SecurityControlCustomParameters' => [ 'shape' => 'SecurityControlCustomParametersList', ], ], ], 'SecurityGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], ], 'SecurityHubFeature' => [ 'type' => 'string', 'enum' => [ 'SecurityHub', 'SecurityHubV2', ], ], 'SecurityHubPolicy' => [ 'type' => 'structure', 'members' => [ 'ServiceEnabled' => [ 'shape' => 'Boolean', ], 'EnabledStandardIdentifiers' => [ 'shape' => 'EnabledStandardIdentifierList', ], 'SecurityControlsConfiguration' => [ 'shape' => 'SecurityControlsConfiguration', ], ], ], 'SensitiveDataDetections' => [ 'type' => 'structure', 'members' => [ 'Count' => [ 'shape' => 'Long', ], 'Type' => [ 'shape' => 'NonEmptyString', ], 'Occurrences' => [ 'shape' => 'Occurrences', ], ], ], 'SensitiveDataDetectionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SensitiveDataDetections', ], ], 'SensitiveDataResult' => [ 'type' => 'structure', 'members' => [ 'Category' => [ 'shape' => 'NonEmptyString', ], 'Detections' => [ 'shape' => 'SensitiveDataDetectionsList', ], 'TotalCount' => [ 'shape' => 'Long', ], ], ], 'SensitiveDataResultList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SensitiveDataResult', ], ], 'SensitiveNonEmptyString' => [ 'type' => 'string', 'pattern' => '.*\\S.*', 'sensitive' => true, ], 'Sequence' => [ 'type' => 'structure', 'members' => [ 'Uid' => [ 'shape' => 'NonEmptyString', ], 'Actors' => [ 'shape' => 'ActorsList', ], 'Endpoints' => [ 'shape' => 'NetworkEndpointsList', ], 'Signals' => [ 'shape' => 'SignalsList', ], 'SequenceIndicators' => [ 'shape' => 'IndicatorsList', ], ], ], 'ServiceNowDetail' => [ 'type' => 'structure', 'required' => [ 'AuthStatus', ], 'members' => [ 'InstanceName' => [ 'shape' => 'NonEmptyString', ], 'ClientId' => [ 'shape' => 'NonEmptyString', ], 'AuthStatus' => [ 'shape' => 'ConnectorAuthStatus', ], ], ], 'ServiceNowProviderConfiguration' => [ 'type' => 'structure', 'required' => [ 'InstanceName', 'ClientId', 'ClientSecret', ], 'members' => [ 'InstanceName' => [ 'shape' => 'NonEmptyString', ], 'ClientId' => [ 'shape' => 'NonEmptyString', ], 'ClientSecret' => [ 'shape' => 'SensitiveNonEmptyString', ], ], ], 'Severity' => [ 'type' => 'structure', 'members' => [ 'Product' => [ 'shape' => 'Double', ], 'Label' => [ 'shape' => 'SeverityLabel', ], 'Normalized' => [ 'shape' => 'Integer', ], 'Original' => [ 'shape' => 'NonEmptyString', ], ], ], 'SeverityLabel' => [ 'type' => 'string', 'enum' => [ 'INFORMATIONAL', 'LOW', 'MEDIUM', 'HIGH', 'CRITICAL', ], ], 'SeverityRating' => [ 'type' => 'string', 'enum' => [ 'LOW', 'MEDIUM', 'HIGH', 'CRITICAL', ], ], 'SeverityUpdate' => [ 'type' => 'structure', 'members' => [ 'Normalized' => [ 'shape' => 'RatioScale', ], 'Product' => [ 'shape' => 'Double', ], 'Label' => [ 'shape' => 'SeverityLabel', ], ], ], 'Signal' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'NonEmptyString', ], 'Id' => [ 'shape' => 'NonEmptyString', ], 'Title' => [ 'shape' => 'NonEmptyString', ], 'ProductArn' => [ 'shape' => 'NonEmptyString', ], 'ResourceIds' => [ 'shape' => 'NonEmptyStringList', ], 'SignalIndicators' => [ 'shape' => 'IndicatorsList', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'CreatedAt' => [ 'shape' => 'Long', ], 'UpdatedAt' => [ 'shape' => 'Long', ], 'FirstSeenAt' => [ 'shape' => 'Long', ], 'LastSeenAt' => [ 'shape' => 'Long', ], 'Severity' => [ 'shape' => 'Double', ], 'Count' => [ 'shape' => 'Integer', ], 'ActorIds' => [ 'shape' => 'NonEmptyStringList', ], 'EndpointIds' => [ 'shape' => 'NonEmptyStringList', ], ], ], 'SignalsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Signal', ], 'max' => 100, 'min' => 1, ], 'SizeBytes' => [ 'type' => 'long', ], 'SoftwarePackage' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'Version' => [ 'shape' => 'NonEmptyString', ], 'Epoch' => [ 'shape' => 'NonEmptyString', ], 'Release' => [ 'shape' => 'NonEmptyString', ], 'Architecture' => [ 'shape' => 'NonEmptyString', ], 'PackageManager' => [ 'shape' => 'NonEmptyString', ], 'FilePath' => [ 'shape' => 'NonEmptyString', ], 'FixedInVersion' => [ 'shape' => 'NonEmptyString', ], 'Remediation' => [ 'shape' => 'NonEmptyString', ], 'SourceLayerHash' => [ 'shape' => 'NonEmptyString', ], 'SourceLayerArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'SoftwarePackageList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SoftwarePackage', ], ], 'SortCriteria' => [ 'type' => 'list', 'member' => [ 'shape' => 'SortCriterion', ], ], 'SortCriterion' => [ 'type' => 'structure', 'members' => [ 'Field' => [ 'shape' => 'NonEmptyString', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'SortOrder' => [ 'type' => 'string', 'enum' => [ 'asc', 'desc', ], ], 'Standard' => [ 'type' => 'structure', 'members' => [ 'StandardsArn' => [ 'shape' => 'NonEmptyString', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'EnabledByDefault' => [ 'shape' => 'Boolean', ], 'StandardsManagedBy' => [ 'shape' => 'StandardsManagedBy', ], ], ], 'Standards' => [ 'type' => 'list', 'member' => [ 'shape' => 'Standard', ], ], 'StandardsControl' => [ 'type' => 'structure', 'members' => [ 'StandardsControlArn' => [ 'shape' => 'NonEmptyString', ], 'ControlStatus' => [ 'shape' => 'ControlStatus', ], 'DisabledReason' => [ 'shape' => 'NonEmptyString', ], 'ControlStatusUpdatedAt' => [ 'shape' => 'Timestamp', ], 'ControlId' => [ 'shape' => 'NonEmptyString', ], 'Title' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'RemediationUrl' => [ 'shape' => 'NonEmptyString', ], 'SeverityRating' => [ 'shape' => 'SeverityRating', ], 'RelatedRequirements' => [ 'shape' => 'RelatedRequirementsList', ], ], ], 'StandardsControlArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], ], 'StandardsControlAssociationDetail' => [ 'type' => 'structure', 'required' => [ 'StandardsArn', 'SecurityControlId', 'SecurityControlArn', 'AssociationStatus', ], 'members' => [ 'StandardsArn' => [ 'shape' => 'NonEmptyString', ], 'SecurityControlId' => [ 'shape' => 'NonEmptyString', ], 'SecurityControlArn' => [ 'shape' => 'NonEmptyString', ], 'AssociationStatus' => [ 'shape' => 'AssociationStatus', ], 'RelatedRequirements' => [ 'shape' => 'RelatedRequirementsList', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], 'UpdatedReason' => [ 'shape' => 'NonEmptyString', ], 'StandardsControlTitle' => [ 'shape' => 'NonEmptyString', ], 'StandardsControlDescription' => [ 'shape' => 'NonEmptyString', ], 'StandardsControlArns' => [ 'shape' => 'StandardsControlArnList', ], ], ], 'StandardsControlAssociationDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'StandardsControlAssociationDetail', ], ], 'StandardsControlAssociationId' => [ 'type' => 'structure', 'required' => [ 'SecurityControlId', 'StandardsArn', ], 'members' => [ 'SecurityControlId' => [ 'shape' => 'NonEmptyString', ], 'StandardsArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'StandardsControlAssociationIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'StandardsControlAssociationId', ], ], 'StandardsControlAssociationSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'StandardsControlAssociationSummary', ], ], 'StandardsControlAssociationSummary' => [ 'type' => 'structure', 'required' => [ 'StandardsArn', 'SecurityControlId', 'SecurityControlArn', 'AssociationStatus', ], 'members' => [ 'StandardsArn' => [ 'shape' => 'NonEmptyString', ], 'SecurityControlId' => [ 'shape' => 'NonEmptyString', ], 'SecurityControlArn' => [ 'shape' => 'NonEmptyString', ], 'AssociationStatus' => [ 'shape' => 'AssociationStatus', ], 'RelatedRequirements' => [ 'shape' => 'RelatedRequirementsList', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], 'UpdatedReason' => [ 'shape' => 'NonEmptyString', ], 'StandardsControlTitle' => [ 'shape' => 'NonEmptyString', ], 'StandardsControlDescription' => [ 'shape' => 'NonEmptyString', ], ], ], 'StandardsControlAssociationUpdate' => [ 'type' => 'structure', 'required' => [ 'StandardsArn', 'SecurityControlId', 'AssociationStatus', ], 'members' => [ 'StandardsArn' => [ 'shape' => 'NonEmptyString', ], 'SecurityControlId' => [ 'shape' => 'NonEmptyString', ], 'AssociationStatus' => [ 'shape' => 'AssociationStatus', ], 'UpdatedReason' => [ 'shape' => 'NonEmptyString', ], ], ], 'StandardsControlAssociationUpdates' => [ 'type' => 'list', 'member' => [ 'shape' => 'StandardsControlAssociationUpdate', ], ], 'StandardsControls' => [ 'type' => 'list', 'member' => [ 'shape' => 'StandardsControl', ], ], 'StandardsControlsUpdatable' => [ 'type' => 'string', 'enum' => [ 'READY_FOR_UPDATES', 'NOT_READY_FOR_UPDATES', ], ], 'StandardsInputParameterMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'NonEmptyString', ], 'value' => [ 'shape' => 'NonEmptyString', ], ], 'StandardsManagedBy' => [ 'type' => 'structure', 'members' => [ 'Company' => [ 'shape' => 'NonEmptyString', ], 'Product' => [ 'shape' => 'NonEmptyString', ], ], ], 'StandardsStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'READY', 'FAILED', 'DELETING', 'INCOMPLETE', ], ], 'StandardsStatusReason' => [ 'type' => 'structure', 'required' => [ 'StatusReasonCode', ], 'members' => [ 'StatusReasonCode' => [ 'shape' => 'StatusReasonCode', ], ], ], 'StandardsSubscription' => [ 'type' => 'structure', 'required' => [ 'StandardsSubscriptionArn', 'StandardsArn', 'StandardsInput', 'StandardsStatus', ], 'members' => [ 'StandardsSubscriptionArn' => [ 'shape' => 'NonEmptyString', ], 'StandardsArn' => [ 'shape' => 'NonEmptyString', ], 'StandardsInput' => [ 'shape' => 'StandardsInputParameterMap', ], 'StandardsStatus' => [ 'shape' => 'StandardsStatus', ], 'StandardsControlsUpdatable' => [ 'shape' => 'StandardsControlsUpdatable', ], 'StandardsStatusReason' => [ 'shape' => 'StandardsStatusReason', ], ], ], 'StandardsSubscriptionArns' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], 'max' => 25, 'min' => 1, ], 'StandardsSubscriptionRequest' => [ 'type' => 'structure', 'required' => [ 'StandardsArn', ], 'members' => [ 'StandardsArn' => [ 'shape' => 'NonEmptyString', ], 'StandardsInput' => [ 'shape' => 'StandardsInputParameterMap', ], ], ], 'StandardsSubscriptionRequests' => [ 'type' => 'list', 'member' => [ 'shape' => 'StandardsSubscriptionRequest', ], 'max' => 25, 'min' => 1, ], 'StandardsSubscriptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'StandardsSubscription', ], ], 'StartConfigurationPolicyAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigurationPolicyIdentifier', 'Target', ], 'members' => [ 'ConfigurationPolicyIdentifier' => [ 'shape' => 'NonEmptyString', ], 'Target' => [ 'shape' => 'Target', ], ], ], 'StartConfigurationPolicyAssociationResponse' => [ 'type' => 'structure', 'members' => [ 'ConfigurationPolicyId' => [ 'shape' => 'NonEmptyString', ], 'TargetId' => [ 'shape' => 'NonEmptyString', ], 'TargetType' => [ 'shape' => 'TargetType', ], 'AssociationType' => [ 'shape' => 'AssociationType', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], 'AssociationStatus' => [ 'shape' => 'ConfigurationPolicyAssociationStatus', ], 'AssociationStatusMessage' => [ 'shape' => 'NonEmptyString', ], ], ], 'StartConfigurationPolicyDisassociationRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigurationPolicyIdentifier', ], 'members' => [ 'Target' => [ 'shape' => 'Target', ], 'ConfigurationPolicyIdentifier' => [ 'shape' => 'NonEmptyString', ], ], ], 'StartConfigurationPolicyDisassociationResponse' => [ 'type' => 'structure', 'members' => [], ], 'StatelessCustomActionDefinition' => [ 'type' => 'structure', 'members' => [ 'PublishMetricAction' => [ 'shape' => 'StatelessCustomPublishMetricAction', ], ], ], 'StatelessCustomPublishMetricAction' => [ 'type' => 'structure', 'members' => [ 'Dimensions' => [ 'shape' => 'StatelessCustomPublishMetricActionDimensionsList', ], ], ], 'StatelessCustomPublishMetricActionDimension' => [ 'type' => 'structure', 'members' => [ 'Value' => [ 'shape' => 'NonEmptyString', ], ], ], 'StatelessCustomPublishMetricActionDimensionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StatelessCustomPublishMetricActionDimension', ], ], 'StatusReason' => [ 'type' => 'structure', 'required' => [ 'ReasonCode', ], 'members' => [ 'ReasonCode' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], ], ], 'StatusReasonCode' => [ 'type' => 'string', 'enum' => [ 'NO_AVAILABLE_CONFIGURATION_RECORDER', 'MAXIMUM_NUMBER_OF_CONFIG_RULES_EXCEEDED', 'INTERNAL_ERROR', ], ], 'StatusReasonsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StatusReason', ], ], 'StringConfigurationOptions' => [ 'type' => 'structure', 'members' => [ 'DefaultValue' => [ 'shape' => 'NonEmptyString', ], 'Re2Expression' => [ 'shape' => 'NonEmptyString', ], 'ExpressionDescription' => [ 'shape' => 'NonEmptyString', ], ], ], 'StringFilter' => [ 'type' => 'structure', 'members' => [ 'Value' => [ 'shape' => 'NonEmptyString', ], 'Comparison' => [ 'shape' => 'StringFilterComparison', ], ], ], 'StringFilterComparison' => [ 'type' => 'string', 'enum' => [ 'EQUALS', 'PREFIX', 'NOT_EQUALS', 'PREFIX_NOT_EQUALS', 'CONTAINS', 'NOT_CONTAINS', 'CONTAINS_WORD', ], ], 'StringFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StringFilter', ], ], 'StringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], ], 'StringListConfigurationOptions' => [ 'type' => 'structure', 'members' => [ 'DefaultValue' => [ 'shape' => 'StringList', ], 'Re2Expression' => [ 'shape' => 'NonEmptyString', ], 'MaxItems' => [ 'shape' => 'Integer', ], 'ExpressionDescription' => [ 'shape' => 'NonEmptyString', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^(?!aws:)[a-zA-Z+-=._:/]+$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 1, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 50, 'min' => 1, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, ], 'Target' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', ], 'OrganizationalUnitId' => [ 'shape' => 'NonEmptyString', ], 'RootId' => [ 'shape' => 'NonEmptyString', ], ], 'union' => true, ], 'TargetType' => [ 'type' => 'string', 'enum' => [ 'ACCOUNT', 'ORGANIZATIONAL_UNIT', 'ROOT', ], ], 'Threat' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'Severity' => [ 'shape' => 'NonEmptyString', ], 'ItemCount' => [ 'shape' => 'Integer', ], 'FilePaths' => [ 'shape' => 'FilePathList', ], ], ], 'ThreatIntelIndicator' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'ThreatIntelIndicatorType', ], 'Value' => [ 'shape' => 'NonEmptyString', ], 'Category' => [ 'shape' => 'ThreatIntelIndicatorCategory', ], 'LastObservedAt' => [ 'shape' => 'NonEmptyString', ], 'Source' => [ 'shape' => 'NonEmptyString', ], 'SourceUrl' => [ 'shape' => 'NonEmptyString', ], ], ], 'ThreatIntelIndicatorCategory' => [ 'type' => 'string', 'enum' => [ 'BACKDOOR', 'CARD_STEALER', 'COMMAND_AND_CONTROL', 'DROP_SITE', 'EXPLOIT_SITE', 'KEYLOGGER', ], ], 'ThreatIntelIndicatorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ThreatIntelIndicator', ], ], 'ThreatIntelIndicatorType' => [ 'type' => 'string', 'enum' => [ 'DOMAIN', 'EMAIL_ADDRESS', 'HASH_MD5', 'HASH_SHA1', 'HASH_SHA256', 'HASH_SHA512', 'IPV4_ADDRESS', 'IPV6_ADDRESS', 'MUTEX', 'PROCESS', 'URL', ], ], 'ThreatList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Threat', ], ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'NonEmptyString', ], 'Code' => [ 'shape' => 'NonEmptyString', ], ], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], 'Timestamp' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'TypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], ], 'UnprocessedAutomationRule' => [ 'type' => 'structure', 'members' => [ 'RuleArn' => [ 'shape' => 'NonEmptyString', ], 'ErrorCode' => [ 'shape' => 'Integer', ], 'ErrorMessage' => [ 'shape' => 'NonEmptyString', ], ], ], 'UnprocessedAutomationRulesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UnprocessedAutomationRule', ], ], 'UnprocessedConfigurationPolicyAssociation' => [ 'type' => 'structure', 'members' => [ 'ConfigurationPolicyAssociationIdentifiers' => [ 'shape' => 'ConfigurationPolicyAssociation', ], 'ErrorCode' => [ 'shape' => 'NonEmptyString', ], 'ErrorReason' => [ 'shape' => 'NonEmptyString', ], ], ], 'UnprocessedConfigurationPolicyAssociationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UnprocessedConfigurationPolicyAssociation', ], ], 'UnprocessedErrorCode' => [ 'type' => 'string', 'enum' => [ 'INVALID_INPUT', 'ACCESS_DENIED', 'NOT_FOUND', 'LIMIT_EXCEEDED', ], ], 'UnprocessedSecurityControl' => [ 'type' => 'structure', 'required' => [ 'SecurityControlId', 'ErrorCode', ], 'members' => [ 'SecurityControlId' => [ 'shape' => 'NonEmptyString', ], 'ErrorCode' => [ 'shape' => 'UnprocessedErrorCode', ], 'ErrorReason' => [ 'shape' => 'NonEmptyString', ], ], ], 'UnprocessedSecurityControls' => [ 'type' => 'list', 'member' => [ 'shape' => 'UnprocessedSecurityControl', ], ], 'UnprocessedStandardsControlAssociation' => [ 'type' => 'structure', 'required' => [ 'StandardsControlAssociationId', 'ErrorCode', ], 'members' => [ 'StandardsControlAssociationId' => [ 'shape' => 'StandardsControlAssociationId', ], 'ErrorCode' => [ 'shape' => 'UnprocessedErrorCode', ], 'ErrorReason' => [ 'shape' => 'NonEmptyString', ], ], ], 'UnprocessedStandardsControlAssociationUpdate' => [ 'type' => 'structure', 'required' => [ 'StandardsControlAssociationUpdate', 'ErrorCode', ], 'members' => [ 'StandardsControlAssociationUpdate' => [ 'shape' => 'StandardsControlAssociationUpdate', ], 'ErrorCode' => [ 'shape' => 'UnprocessedErrorCode', ], 'ErrorReason' => [ 'shape' => 'NonEmptyString', ], ], ], 'UnprocessedStandardsControlAssociationUpdates' => [ 'type' => 'list', 'member' => [ 'shape' => 'UnprocessedStandardsControlAssociationUpdate', ], ], 'UnprocessedStandardsControlAssociations' => [ 'type' => 'list', 'member' => [ 'shape' => 'UnprocessedStandardsControlAssociation', ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'TagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateActionTargetRequest' => [ 'type' => 'structure', 'required' => [ 'ActionTargetArn', ], 'members' => [ 'ActionTargetArn' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'ActionTargetArn', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], ], ], 'UpdateActionTargetResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateAggregatorV2Request' => [ 'type' => 'structure', 'required' => [ 'AggregatorV2Arn', 'RegionLinkingMode', ], 'members' => [ 'AggregatorV2Arn' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'AggregatorV2Arn', ], 'RegionLinkingMode' => [ 'shape' => 'NonEmptyString', ], 'LinkedRegions' => [ 'shape' => 'StringList', ], ], ], 'UpdateAggregatorV2Response' => [ 'type' => 'structure', 'members' => [ 'AggregatorV2Arn' => [ 'shape' => 'NonEmptyString', ], 'AggregationRegion' => [ 'shape' => 'NonEmptyString', ], 'RegionLinkingMode' => [ 'shape' => 'NonEmptyString', ], 'LinkedRegions' => [ 'shape' => 'StringList', ], ], ], 'UpdateAutomationRuleV2Request' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'Identifier', ], 'RuleStatus' => [ 'shape' => 'RuleStatusV2', ], 'RuleOrder' => [ 'shape' => 'RuleOrderValueV2', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'RuleName' => [ 'shape' => 'NonEmptyString', ], 'Criteria' => [ 'shape' => 'Criteria', ], 'Actions' => [ 'shape' => 'AutomationRulesActionListV2', ], ], ], 'UpdateAutomationRuleV2Response' => [ 'type' => 'structure', 'members' => [], ], 'UpdateAutomationRulesRequestItem' => [ 'type' => 'structure', 'required' => [ 'RuleArn', ], 'members' => [ 'RuleArn' => [ 'shape' => 'NonEmptyString', ], 'RuleStatus' => [ 'shape' => 'RuleStatus', ], 'RuleOrder' => [ 'shape' => 'RuleOrderValue', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'RuleName' => [ 'shape' => 'NonEmptyString', ], 'IsTerminal' => [ 'shape' => 'Boolean', ], 'Criteria' => [ 'shape' => 'AutomationRulesFindingFilters', ], 'Actions' => [ 'shape' => 'ActionList', ], ], ], 'UpdateAutomationRulesRequestItemsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UpdateAutomationRulesRequestItem', ], 'max' => 100, 'min' => 1, ], 'UpdateConfigurationPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'Identifier', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'UpdatedReason' => [ 'shape' => 'NonEmptyString', ], 'ConfigurationPolicy' => [ 'shape' => 'Policy', ], ], ], 'UpdateConfigurationPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'NonEmptyString', ], 'Id' => [ 'shape' => 'NonEmptyString', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'ConfigurationPolicy' => [ 'shape' => 'Policy', ], ], ], 'UpdateConnectorV2Request' => [ 'type' => 'structure', 'required' => [ 'ConnectorId', ], 'members' => [ 'ConnectorId' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'ConnectorId', ], 'ClientSecret' => [ 'shape' => 'SensitiveNonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'Provider' => [ 'shape' => 'ProviderUpdateConfiguration', ], ], ], 'UpdateConnectorV2Response' => [ 'type' => 'structure', 'members' => [], ], 'UpdateFindingAggregatorRequest' => [ 'type' => 'structure', 'required' => [ 'FindingAggregatorArn', 'RegionLinkingMode', ], 'members' => [ 'FindingAggregatorArn' => [ 'shape' => 'NonEmptyString', ], 'RegionLinkingMode' => [ 'shape' => 'NonEmptyString', ], 'Regions' => [ 'shape' => 'StringList', ], ], ], 'UpdateFindingAggregatorResponse' => [ 'type' => 'structure', 'members' => [ 'FindingAggregatorArn' => [ 'shape' => 'NonEmptyString', ], 'FindingAggregationRegion' => [ 'shape' => 'NonEmptyString', ], 'RegionLinkingMode' => [ 'shape' => 'NonEmptyString', ], 'Regions' => [ 'shape' => 'StringList', ], ], ], 'UpdateFindingsRequest' => [ 'type' => 'structure', 'required' => [ 'Filters', ], 'members' => [ 'Filters' => [ 'shape' => 'AwsSecurityFindingFilters', ], 'Note' => [ 'shape' => 'NoteUpdate', ], 'RecordState' => [ 'shape' => 'RecordState', ], ], ], 'UpdateFindingsResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateInsightRequest' => [ 'type' => 'structure', 'required' => [ 'InsightArn', ], 'members' => [ 'InsightArn' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'InsightArn', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'Filters' => [ 'shape' => 'AwsSecurityFindingFilters', ], 'GroupByAttribute' => [ 'shape' => 'NonEmptyString', ], ], ], 'UpdateInsightResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateOrganizationConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'AutoEnable', ], 'members' => [ 'AutoEnable' => [ 'shape' => 'Boolean', ], 'AutoEnableStandards' => [ 'shape' => 'AutoEnableStandards', ], 'OrganizationConfiguration' => [ 'shape' => 'OrganizationConfiguration', ], ], ], 'UpdateOrganizationConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateSecurityControlRequest' => [ 'type' => 'structure', 'required' => [ 'SecurityControlId', 'Parameters', ], 'members' => [ 'SecurityControlId' => [ 'shape' => 'NonEmptyString', ], 'Parameters' => [ 'shape' => 'Parameters', ], 'LastUpdateReason' => [ 'shape' => 'AlphaNumericNonEmptyString', ], ], ], 'UpdateSecurityControlResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateSecurityHubConfigurationRequest' => [ 'type' => 'structure', 'members' => [ 'AutoEnableControls' => [ 'shape' => 'Boolean', ], 'ControlFindingGenerator' => [ 'shape' => 'ControlFindingGenerator', ], ], ], 'UpdateSecurityHubConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateStandardsControlRequest' => [ 'type' => 'structure', 'required' => [ 'StandardsControlArn', ], 'members' => [ 'StandardsControlArn' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'StandardsControlArn', ], 'ControlStatus' => [ 'shape' => 'ControlStatus', ], 'DisabledReason' => [ 'shape' => 'NonEmptyString', ], ], ], 'UpdateStandardsControlResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateStatus' => [ 'type' => 'string', 'enum' => [ 'READY', 'UPDATING', ], ], 'UserAccount' => [ 'type' => 'structure', 'members' => [ 'Uid' => [ 'shape' => 'NonEmptyString', ], 'Name' => [ 'shape' => 'NonEmptyString', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'NonEmptyString', ], 'Code' => [ 'shape' => 'NonEmptyString', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'VerificationState' => [ 'type' => 'string', 'enum' => [ 'UNKNOWN', 'TRUE_POSITIVE', 'FALSE_POSITIVE', 'BENIGN_POSITIVE', ], ], 'VolumeMount' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'MountPath' => [ 'shape' => 'NonEmptyString', ], ], ], 'VolumeMountList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VolumeMount', ], ], 'VpcInfoCidrBlockSetDetails' => [ 'type' => 'structure', 'members' => [ 'CidrBlock' => [ 'shape' => 'NonEmptyString', ], ], ], 'VpcInfoCidrBlockSetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VpcInfoCidrBlockSetDetails', ], ], 'VpcInfoIpv6CidrBlockSetDetails' => [ 'type' => 'structure', 'members' => [ 'Ipv6CidrBlock' => [ 'shape' => 'NonEmptyString', ], ], ], 'VpcInfoIpv6CidrBlockSetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VpcInfoIpv6CidrBlockSetDetails', ], ], 'VpcInfoPeeringOptionsDetails' => [ 'type' => 'structure', 'members' => [ 'AllowDnsResolutionFromRemoteVpc' => [ 'shape' => 'Boolean', ], 'AllowEgressFromLocalClassicLinkToRemoteVpc' => [ 'shape' => 'Boolean', ], 'AllowEgressFromLocalVpcToRemoteClassicLink' => [ 'shape' => 'Boolean', ], ], ], 'Vulnerability' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'NonEmptyString', ], 'VulnerablePackages' => [ 'shape' => 'SoftwarePackageList', ], 'Cvss' => [ 'shape' => 'CvssList', ], 'RelatedVulnerabilities' => [ 'shape' => 'StringList', ], 'Vendor' => [ 'shape' => 'VulnerabilityVendor', ], 'ReferenceUrls' => [ 'shape' => 'StringList', ], 'FixAvailable' => [ 'shape' => 'VulnerabilityFixAvailable', ], 'EpssScore' => [ 'shape' => 'Double', ], 'ExploitAvailable' => [ 'shape' => 'VulnerabilityExploitAvailable', ], 'LastKnownExploitAt' => [ 'shape' => 'NonEmptyString', ], 'CodeVulnerabilities' => [ 'shape' => 'VulnerabilityCodeVulnerabilitiesList', ], ], ], 'VulnerabilityCodeVulnerabilities' => [ 'type' => 'structure', 'members' => [ 'Cwes' => [ 'shape' => 'TypeList', ], 'FilePath' => [ 'shape' => 'CodeVulnerabilitiesFilePath', ], 'SourceArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'VulnerabilityCodeVulnerabilitiesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VulnerabilityCodeVulnerabilities', ], ], 'VulnerabilityExploitAvailable' => [ 'type' => 'string', 'enum' => [ 'YES', 'NO', ], ], 'VulnerabilityFixAvailable' => [ 'type' => 'string', 'enum' => [ 'YES', 'NO', 'PARTIAL', ], ], 'VulnerabilityList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Vulnerability', ], ], 'VulnerabilityVendor' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'Url' => [ 'shape' => 'NonEmptyString', ], 'VendorSeverity' => [ 'shape' => 'NonEmptyString', ], 'VendorCreatedAt' => [ 'shape' => 'NonEmptyString', ], 'VendorUpdatedAt' => [ 'shape' => 'NonEmptyString', ], ], ], 'WafAction' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'WafExcludedRule' => [ 'type' => 'structure', 'members' => [ 'RuleId' => [ 'shape' => 'NonEmptyString', ], ], ], 'WafExcludedRuleList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WafExcludedRule', ], ], 'WafOverrideAction' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'Workflow' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'WorkflowStatus', ], ], ], 'WorkflowState' => [ 'type' => 'string', 'deprecated' => true, 'deprecatedMessage' => 'This filter is deprecated. Instead, use SeverityLabel or FindingProviderFieldsSeverityLabel.', 'enum' => [ 'NEW', 'ASSIGNED', 'IN_PROGRESS', 'DEFERRED', 'RESOLVED', ], ], 'WorkflowStatus' => [ 'type' => 'string', 'enum' => [ 'NEW', 'NOTIFIED', 'RESOLVED', 'SUPPRESSED', ], ], 'WorkflowUpdate' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'WorkflowStatus', ], ], ], ],];
